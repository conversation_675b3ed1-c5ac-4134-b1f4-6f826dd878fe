[{"artifact": "nav", "type": "HAR", "version": "1.3.21", "group": "taobao-ohos"}, {"artifact": "dinamicx", "type": "HAR", "version": "1.1.57", "group": "taobao-ohos"}, {"artifact": "riverlogger", "type": "HAR", "version": "3.1.0-rc.55", "group": "taobao-ohos"}, {"artifact": "qking", "type": "HAR", "version": "0.21.1-ohos", "group": "taobao-ohos"}, {"artifact": "weex-ability", "type": "HAR", "version": "0.21.2-ohos", "group": "taobao-ohos"}, {"artifact": "unicorn", "type": "HAR", "version": "0.21.8-ohos", "group": "taobao-ohos"}, {"artifact": "weex_framework", "type": "HAR", "version": "0.21.8-framework-ohos", "group": "taobao-ohos"}, {"artifact": "pie", "type": "HAR", "version": "1.1.53", "group": "taobao-ohos"}, {"artifact": "megability_interface", "type": "HAR", "version": "1.8.16", "group": "taobao-ohos"}, {"artifact": "taobaoavsdk", "type": "HAR", "version": "1.1.0", "group": "taobao-ohos"}, {"artifact": "megability_idl_interface", "type": "HAR", "version": "1.8.69", "group": "taobao-ohos"}, {"artifact": "megability_kit", "type": "HAR", "version": "1.8.34", "group": "taobao-ohos"}, {"artifact": "megability_hub", "type": "HAR", "version": "1.8.52", "group": "taobao-ohos"}, {"artifact": "taobao_runtime", "type": "HAR", "version": "1.0.97", "group": "taobao-ohos"}, {"artifact": "account", "type": "HAR", "version": "1.5.121", "group": "taobao-ohos"}, {"artifact": "account_ui", "type": "HAR", "version": "1.5.121", "group": "taobao-ohos"}, {"artifact": "address", "type": "HAR", "version": "1.0.42", "group": "taobao-ohos"}, {"artifact": "tbrest", "type": "HAR", "version": "0.1.19", "group": "taobao-ohos"}, {"artifact": "xnet_next", "type": "HAR", "version": "0.9.33", "group": "taobao-ohos"}, {"artifact": "mtop_next", "type": "HAR", "version": "0.3.60", "group": "taobao-ohos"}, {"artifact": "orange_next", "type": "HAR", "version": "0.1.29", "group": "taobao-ohos"}, {"artifact": "uikit", "type": "HAR", "version": "1.0.41", "group": "taobao-ohos"}, {"artifact": "themis_container", "type": "HAR", "version": "1.1.381", "group": "taobao-ohos"}, {"artifact": "themis_kernel", "type": "HAR", "version": "1.1.381", "group": "taobao-ohos"}, {"artifact": "themis_ability_basic", "type": "HAR", "version": "1.1.381", "group": "taobao-ohos"}, {"artifact": "rp_verify", "type": "HAR", "version": "0.0.10", "group": "cro-ohos"}, {"artifact": "webview", "type": "HAR", "version": "1.1.96", "group": "taobao-ohos"}, {"artifact": "zstd", "type": "HAR", "version": "1.52.2", "group": "taobao-ohos"}, {"artifact": "tlog", "type": "HAR", "version": "1.0.10", "group": "taobao-ohos"}, {"artifact": "tlog_engine", "type": "HAR", "version": "1.1.4", "group": "taobao-ohos"}, {"artifact": "stdpop", "type": "HAR", "version": "1.0.69", "group": "taobao-ohos"}, {"artifact": "themis_inside", "type": "HAR", "version": "1.1.381", "group": "taobao-ohos"}, {"artifact": "hmcrash", "type": "HAR", "version": "1.1.34", "group": "taobao-ohos"}, {"artifact": "account_mega", "type": "HAR", "version": "1.5.121", "group": "taobao-ohos"}, {"artifact": "ut_analytics_sdk", "type": "HAR", "version": "1.2.19", "group": "taobao-ohos"}, {"artifact": "utdid_sdk", "type": "HAR", "version": "1.2.4", "group": "taobao-ohos"}, {"artifact": "ut_kernel", "type": "HAR", "version": "1.2.19", "group": "taobao-ohos"}, {"artifact": "image_kit", "type": "HAR", "version": "0.2.18", "group": "taobao-ohos"}, {"artifact": "tnet", "type": "HAR", "version": "1.2.1", "group": "taobao-ohos"}, {"artifact": "zcache", "type": "HAR", "version": "10.10.0-rc.57", "group": "taobao-ohos"}, {"artifact": "munion_harmony", "type": "HAR", "version": "1.0.31", "group": "taobao-ohos"}, {"artifact": "commonutils", "type": "HAR", "version": "1.0.16", "group": "fliggy-ohos"}, {"artifact": "jsbridge", "type": "HAR", "version": "1.0.92", "group": "fliggy-ohos"}, {"artifact": "logger", "type": "HAR", "version": "1.0.9", "group": "fliggy-ohos"}, {"artifact": "accs_ohos", "type": "HAR", "version": "0.0.60", "group": "taobao-ohos"}, {"artifact": "appcompat", "type": "HAR", "version": "1.0.8", "group": "fliggy-ohos"}, {"artifact": "marvel", "type": "HAR", "version": "1.0.8-ohos-ta<PERSON><PERSON>", "group": "taobao-ohos"}, {"artifact": "configcenter", "type": "HAR", "version": "1.0.2", "group": "fliggy-ohos"}, {"artifact": "common<PERSON>", "type": "HAR", "version": "1.0.6", "group": "fliggy-ohos"}, {"artifact": "iconfont", "type": "HAR", "version": "1.0.3", "group": "fliggy-ohos"}, {"artifact": "launcher", "type": "HAR", "version": "1.0.1", "group": "fliggy-ohos"}, {"artifact": "fperformancekit", "type": "HAR", "version": "1.0.4", "group": "fliggy-ohos"}, {"artifact": "titlebar", "type": "HAR", "version": "1.0.23", "group": "fliggy-ohos"}, {"artifact": "fmmkv", "type": "HAR", "version": "1.0.1", "group": "fliggy-ohos"}, {"artifact": "fliggykv", "type": "HAR", "version": "1.0.1", "group": "fliggy-ohos"}, {"artifact": "env", "type": "HAR", "version": "1.0.11", "group": "fliggy-ohos"}, {"artifact": "tracker", "type": "HAR", "version": "1.0.12", "group": "fliggy-ohos"}, {"artifact": "login", "type": "HAR", "version": "1.0.42", "group": "fliggy-ohos"}, {"artifact": "mtop", "type": "HAR", "version": "1.0.10", "group": "fliggy-ohos"}, {"artifact": "router", "type": "HAR", "version": "1.0.43", "group": "fliggy-ohos"}, {"artifact": "permission", "type": "HAR", "version": "1.0.14", "group": "fliggy-ohos"}, {"artifact": "location", "type": "HAR", "version": "1.0.4", "group": "fliggy-ohos"}, {"artifact": "player", "type": "HAR", "version": "1.0.4", "group": "fliggy-ohos"}, {"artifact": "scancode", "type": "HAR", "version": "1.0.15", "group": "fliggy-ohos"}, {"artifact": "fcache", "type": "HAR", "version": "1.0.4", "group": "fliggy-ohos"}, {"artifact": "unicorn", "type": "HAR", "version": "1.0.73", "group": "fliggy-ohos"}, {"artifact": "flutter_container", "type": "HAR", "version": "1.0.25", "group": "fliggy-ohos"}, {"artifact": "homepage", "type": "HAR", "version": "1.0.37", "group": "fliggy-ohos"}, {"artifact": "train", "type": "HAR", "version": "1.0.8", "group": "fliggy-ohos"}, {"artifact": "launcherimpl", "type": "HAR", "version": "1.0.38", "group": "fliggy-ohos"}, {"artifact": "taoliveroom", "type": "HAR", "version": "2.1.93-fliggyfix", "group": "taobao-ohos"}, {"artifact": "multi_screen", "type": "HAR", "version": "1.0.22", "group": "taobao-ohos"}, {"artifact": "powermsg_ohos", "type": "HAR", "version": "1.0.55", "group": "taobao-ohos"}, {"artifact": "account_auth", "type": "HAR", "version": "1.5.121", "group": "taobao-ohos"}, {"artifact": "ut_mega_sdk", "type": "HAR", "version": "0.1.9", "group": "taobao-ohos"}, {"artifact": "tlgoods", "type": "HAR", "version": "1.1.134-fliggy", "group": "taobao-ohos"}, {"artifact": "tlog_uploader", "type": "HAR", "version": "1.0.6", "group": "taobao-ohos"}, {"artifact": "mega_powermsg_abilities", "type": "HAR", "version": "1.0.16", "group": "taobao-ohos"}, {"artifact": "mega_mtop_abilities_ohos", "type": "HAR", "version": "0.0.22", "group": "taobao-ohos"}, {"artifact": "pay", "type": "HAR", "version": "1.0.18", "group": "fliggy-ohos"}, {"artifact": "aus", "type": "HAR", "version": "0.2.18", "group": "taobao-ohos"}, {"artifact": "boringssl", "type": "HAR", "version": "0.1.2", "group": "taobao-ohos"}, {"artifact": "megability_registry", "type": "HAR", "version": "1.5.63", "group": "taobao-ohos"}, {"artifact": "themis_web", "type": "HAR", "version": "1.1.381", "group": "taobao-ohos"}, {"artifact": "themis_uniapp", "type": "HAR", "version": "1.1.381", "group": "taobao-ohos"}, {"artifact": "mtop_ssr", "type": "HAR", "version": "0.0.19", "group": "taobao-ohos"}, {"artifact": "themis_weex2", "type": "HAR", "version": "1.1.381", "group": "taobao-ohos"}, {"artifact": "<PERSON>z<PERSON><PERSON>", "type": "HAR", "version": "0.1.18", "group": "taobao-ohos"}, {"artifact": "verifyidentity", "type": "HAR", "version": "1.1.3", "group": "alipay"}, {"artifact": "tblive_ability", "type": "HAR", "version": "1.0.32", "group": "taobao-ohos"}, {"artifact": "securityguardsdk", "type": "HAR", "version": "6.6.241201", "group": "mobilesec-ohos"}, {"artifact": "protodb", "type": "HAR", "version": "4.2.2", "group": "taobao-ohos"}, {"artifact": "abtest", "type": "HAR", "version": "0.0.38", "group": "taobao-ohos"}, {"artifact": "taobao_content_playcontrol", "type": "HAR", "version": "1.0.81", "group": "taobao-ohos"}, {"artifact": "dynamicrouter", "type": "HAR", "version": "1.0.12", "group": "fliggy-ohos"}, {"artifact": "megability_for_taobao", "type": "HAR", "version": "1.8.13", "group": "taobao-ohos"}, {"artifact": "xlite", "type": "HAR", "version": "1.0.17", "group": "taobao-ohos"}, {"artifact": "mega_broadcast_ability", "type": "HAR", "version": "1.8.34", "group": "taobao-ohos"}, {"artifact": "mega_navigator_ability", "type": "HAR", "version": "1.8.34", "group": "taobao-ohos"}, {"artifact": "antui", "type": "HAR", "version": "1.0.14", "group": "alipay"}, {"artifact": "ucc", "type": "HAR", "version": "1.5.121", "group": "taobao-ohos"}, {"artifact": "applicationmonitor", "type": "HAR", "version": "1.0.44", "group": "taobao-ohos"}, {"artifact": "cashier_harmony_app_taobao", "type": "HAR", "version": "24.1.107", "group": "alipay"}, {"artifact": "cashier_core", "type": "HAR", "version": "1.0.241224144737", "group": "alipay"}, {"artifact": "cashier_platform", "type": "HAR", "version": "24.1.106", "group": "alipay"}, {"artifact": "product_biometric_adapt", "type": "HAR", "version": "1.0.8", "group": "alipay"}, {"artifact": "product_biometric", "type": "HAR", "version": "1.0.241113113110", "group": "alipay"}, {"artifact": "taobao_ffmpeg", "type": "HAR", "version": "1.0.6-ohos", "group": "taobao-ohos"}, {"artifact": "tbglobalmenu", "type": "HAR", "version": "1.0.5", "group": "taobao-ohos"}, {"artifact": "taob<PERSON><PERSON><PERSON>_next_arch", "type": "HAR", "version": "1.0.199", "group": "taobao-ohos"}, {"artifact": "html5parser", "type": "HAR", "version": "1.0.0", "group": "alipay"}, {"artifact": "taolive-kmp", "type": "HAR", "version": "1.0.122", "group": "taobao-ohos"}, {"artifact": "mpaas_spmtracker", "type": "HAR", "version": "1.0.250110105910", "group": "alipay"}, {"artifact": "mpaas_netsdkextdepend", "type": "HAR", "version": "1.0.241108191147", "group": "alipay"}, {"artifact": "kmp_api_wrapper", "type": "HAR", "version": "0.0.6", "group": "taobao-ohos"}, {"artifact": "zcache_ability", "type": "HAR", "version": "1.0.3-rc.4", "group": "taobao-ohos"}, {"artifact": "harmony_transport", "type": "HAR", "version": "1.0.241108190852", "group": "alipay"}, {"artifact": "mpaas_flybird", "type": "HAR", "version": "1.0.19", "group": "alipay"}, {"artifact": "blueshieldsdk", "type": "HAR", "version": "1.0.26", "group": "alipay"}, {"artifact": "mpaas_flybird_adapter", "type": "HAR", "version": "1.0.25", "group": "alipay"}, {"artifact": "blueshieldres", "type": "HAR", "version": "1.0.1", "group": "alipay"}, {"artifact": "network", "type": "HAR", "version": "1.0.************", "group": "alipay"}, {"artifact": "artc_engine", "type": "HAR", "version": "1.1.1", "group": "taobao-ohos"}, {"artifact": "account_sso", "type": "HAR", "version": "1.5.121", "group": "taobao-ohos"}, {"artifact": "flow_customs", "type": "HAR", "version": "1.0.15", "group": "taobao-ohos"}, {"artifact": "launcher_interface", "type": "HAR", "version": "1.0.13", "group": "taobao-ohos"}, {"artifact": "detail_platform", "type": "HAR", "version": "1.0.258", "group": "taobao-ohos"}, {"artifact": "detail_tb_app", "type": "HAR", "version": "1.0.258", "group": "taobao-ohos"}, {"artifact": "detail_tb_adapter", "type": "HAR", "version": "1.0.258", "group": "taobao-ohos"}, {"artifact": "afservicesdk", "type": "HAR", "version": "1.0.2", "group": "alipay"}, {"artifact": "ucc_mega", "type": "HAR", "version": "1.5.121", "group": "taobao-ohos"}, {"artifact": "common_utils_wrapper", "type": "HAR", "version": "0.0.32", "group": "taobao-ohos"}, {"artifact": "bifrost", "type": "HAR", "version": "1.0.************", "group": "alipay"}, {"artifact": "tbdinamicx", "type": "HAR", "version": "1.1.53", "group": "taobao-ohos"}, {"artifact": "data_provider", "type": "HAR", "version": "1.0.10", "group": "taobao-ohos"}, {"artifact": "preloader", "type": "HAR", "version": "1.0.22", "group": "taobao-ohos"}, {"artifact": "share", "type": "HAR", "version": "1.0.10", "group": "fliggy-ohos"}, {"artifact": "mega_executor_ability", "type": "HAR", "version": "1.8.34", "group": "taobao-ohos"}, {"artifact": "kmp_mega_wrapper", "type": "HAR", "version": "0.0.5", "group": "taobao-ohos"}, {"artifact": "commonbiz", "type": "HAR", "version": "1.0.8", "group": "fliggy-ohos"}, {"artifact": "flutter_module", "type": "HAR", "version": "1.0.141", "group": "fliggy-ohos"}, {"artifact": "tbkmp_wrapper", "type": "HAR", "version": "0.2.114-cmp", "group": "taobao-ohos"}, {"artifact": "detail_media_swiper", "type": "HAR", "version": "1.0.258", "group": "taobao-ohos"}, {"artifact": "megability_c_interface", "type": "HAR", "version": "1.7.5", "group": "taobao-ohos"}, {"artifact": "libcxx-shared", "type": "HAR", "version": "1.0.2", "group": "taobao-ohos"}, {"artifact": "fliggylive", "type": "HAR", "version": "1.0.6", "group": "fliggy-ohos"}]