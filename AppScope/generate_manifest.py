import hashlib
import sys
import json

target_dir = sys.argv[2]

with open(target_dir+"/artifact_data.json", "r") as file:
    data = json.load(file)


def calculate_sha256(file_path):
    sha256_hash = hashlib.sha256()

    with open(file_path, "rb") as file:
        # Read and update hash string value in blocks of 4K
        for byte_block in iter(lambda: file.read(4096), b""):
            sha256_hash.update(byte_block)

    return sha256_hash.hexdigest()


version_name = sys.argv[1]

with open(target_dir+"/app.json5", "r") as file:
    app_json = json.load(file)

version_name = app_json['app']['versionName']
version_code = app_json['app']['versionCode']

target_artifacts = ['entry-default-signed.hap',"app_icon.png"]
package_artifacts = ['entry-default-signed.hap']
artifact_url = {}
artifact_hash = {}
for item in data:
    if item['fileName'] in target_artifacts:
        artifact_url[item['fileName']] = item['ossFullUrl'] + "/" + item['fileName']
    if item['fileName'] in package_artifacts:
        artifact_hash[item['fileName']] = calculate_sha256(item['fileFullPath'])

manifest = {
    "app": {
        "bundleName": "com.fliggy.hmos.beta",
        "bundleType": "app",
        "versionCode": version_code,
        "versionName": version_name,
        "label": "飞猪beta版",
        "deployDomain": "mtl4.alibaba-inc.com",
        "icons": {
            "normal": artifact_url["app_icon.png"],
            "large": artifact_url["app_icon.png"],
        },
        "minAPIVersion": "5.0.0(12)",
        "targetAPIVersion": "5.0.0(12)",
        "modules": [
            {
                "name": "entry",
                "type": "entry",
                "deviceTypes": [
                    "phone",
                    "tablet"
                ],
                "packageUrl": artifact_url["entry-default-signed.hap"],
                "packageHash": artifact_hash["entry-default-signed.hap"],
            }
        ]
    }
}

with open(target_dir+"/appmanifest.json5", "w",encoding='utf-8') as manifest_file:
    manifest_file.write(json.dumps(manifest,ensure_ascii=False))
