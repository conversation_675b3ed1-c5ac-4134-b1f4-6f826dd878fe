import qrcode
import sys
import json

datafile_path = sys.argv[1]
qrcode_file_name = "demodownload.png"

with open(datafile_path+"/html_data.json", "r") as file:
    data = json.load(file)

for item in data:
    if item["fileName"] == "in-house_download.html":
        html_url = item["ossFullUrl"] + "/" + item["fileName"]

if not html_url:
    print("Invalid url")
    sys.exit(-1)
# 生成二维码
qr = qrcode.QRCode(
    version=1,
    error_correction=qrcode.constants.ERROR_CORRECT_L,
    box_size=10,
    border=4,
)
qr.add_data(html_url)
qr.make(fit=True)

img = qr.make_image(fill_color="black", back_color="white")
img.save(datafile_path+"/"+qrcode_file_name)