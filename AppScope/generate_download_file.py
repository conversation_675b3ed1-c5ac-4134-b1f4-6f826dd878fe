import hashlib
import json
import sys

target_dir = sys.argv[1]
input_file_name = target_dir+"/manifest_data.json"
template_file_name = target_dir+"/demodownload_template.html"
output_file_name = target_dir+"/in-house_download.html"


with open(input_file_name, "r") as file:
    data = json.load(file)


def get_md5_of_file(file_path):
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()


# 遍历数据并打印每个对象的"fileName"和"ossUrl"
filename = "fileName"
ossurl = "ossUrl"
manifest_url = ""
for item in data:
    if item[filename] == "appmanifest.json5":
        manifest_url = item["ossFullUrl"] + "/" + item[filename]

if not manifest_url:
    print("No manifest url detected!")
    exit(-1)

with open(template_file_name, 'r') as f:
    html_string = f.read()
    html_string = html_string.replace('{download_link}', manifest_url)

with open(output_file_name, 'w') as f:
    f.write(html_string)

extra_main_artifact = []

file_path = output_file_name
md5_hash = get_md5_of_file(file_path)

meta_info = {
        "md5": md5_hash,
        "metadata": {
           "isMainArtifact": True
        },
        "name": "in-house_download.html"
}
extra_main_artifact.append(meta_info)

with open("user_defined_info.json", 'w') as f:
    json.dump(extra_main_artifact, f,ensure_ascii=False)