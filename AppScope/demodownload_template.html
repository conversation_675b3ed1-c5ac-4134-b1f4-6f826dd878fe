<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover"
    />
    <title>飞猪旅行鸿蒙版</title>
    <style>
        body,
        html {
          width: 100%;
          height: 100%;
          margin: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
        }

        body {
          background-image: url(https://gw.alicdn.com/imgextra/i1/O1CN01rQbHSe1Eh0J6t0HhZ_!!6000000000382-0-tps-1080-2504.jpg);
          background-size: cover;
          backdrop-filter: blur(20px);
        }

        #button_download {
          height: 40px;
          color: rgb(255, 255, 255);
          display: block;
          position: fixed;
          left: 50%;
          bottom: 50px;
          padding: 0 15px;
          font-size: 14px;
          transform: translate(-50%);
          border: none;
          box-shadow: rgba(255, 255, 255, 0.25) 0px 2px 5px;
          background-color: rgb(255, 80, 0);
          border-radius: 40px;
          text-align: center;
          align-items: center;
          z-index: 1000000;
          font-family: Helvetica, sans-serif;
          text-decoration: none;
          user-select: none;
        }
    </style>
    <script>
        document.addEventListener("DOMContentLoaded", function () {
          // 获取按钮元素
          var button = document.getElementById("button_download");

          // 模拟点击事件
          if (button) {
            console.log("Button found");
            button.click();
          } else {
            console.log("No button");
          }
        });
        function openDeepLink() {
          let url = "store://enterprise/manifest?url={download_link}";
          window.open(url, "_parent");
        }
    </script>
</head>
<body>
<img
        class="logo"
        src="https://gw.alicdn.com/imgextra/i4/O1CN01s99pQu1aRoik96RYZ_!!6000000003327-0-tps-216-216.jpg"
/>
<button id="button_download" onclick="openDeepLink()">
    下载安装飞猪旅行鸿蒙内部测试版
</button>
</body>
</html>
