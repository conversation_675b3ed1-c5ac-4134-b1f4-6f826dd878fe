{
  "modelVersion": "5.0.0",
  "license": "",
  "devDependencies": {
    "@ohos/hypium": "1.0.6"
  },
  "author": "",
  "name": "apptemplate",
  "description": "Please describe the basic information.",
  "main": "",
  "version": "1.0.0",
  "overrides": {
    // dingtalk start
    "@dingtalk/dingpaassdk": "3.2.2-pub-SNAPSHOT",
    "@dingtalk/dingpaasaim": "3.2.2-pub-SNAPSHOT",
    // dingtalk end
    "cpp_nexus": "file:./cpp_nexus",
    "@mobilesec-ohos/securityguardsdk": "6.6.241201",
    "@taobao-ohos/nav": "1.3.21",
    "@taobao-ohos/mtop_next": "0.3.60",
    "@taobao-ohos/utdid_sdk": "1.2.4",
    "@taobao-ohos/account": "1.5.121",
    "@taobao-ohos/account_mega": "1.5.121",
    "@taobao-ohos/account_auth": "1.5.121",
    "@taobao-ohos/account_sso": "1.5.121",
    "@taobao-ohos/account_ui": "1.5.121",
    "@taobao-ohos/ucc": "1.5.121",
    "@taobao-ohos/ucc_mega": "1.5.121",
    "@taobao-ohos/ut_analytics_sdk": "1.2.19",
    "@taobao-ohos/ut_kernel": "1.2.19",
    "@taobao-ohos/riverlogger": "3.1.0-rc.55",
    "@taobao-ohos/zstd": "1.52.2",
    "@taobao-ohos/dinamicx": "1.1.57",
    "@taobao-ohos/tlog": "1.0.10",
    "@taobao-ohos/tlog_engine": "1.1.4",
    "@taobao-ohos/tlog_uploader": "1.0.6",
    "@taobao-ohos/xnet_next": "0.9.33",
    "@taobao-ohos/taobao_runtime": "1.0.97",
    "@taobao-ohos/protodb": "4.2.2",
    "@taobao-ohos/mtop_ssr": "0.0.19",
    "@taobao-ohos/taobao_ffmpeg": "1.0.6-ohos",
    "@taobao-ohos/taobaoavsdk": "1.1.0",
    "@taobao-ohos/uikit": "1.0.41",
    "@taobao-ohos/image_kit": "0.2.18",
    "@taobao-ohos/orange_next": "0.1.29",
    "@taobao-ohos/zcache": "10.10.0-rc.57",
    "@taobao-ohos/preloader": "1.0.22",
    "@taobao-ohos/pie": "1.1.53",
    "@taobao-ohos/aus": "0.2.18",
    "@taobao-ohos/tnet": "1.2.1",
    "@taobao-ohos/boringssl": "0.1.2",
    "@taobao-ohos/ezmock": "0.1.18",
    "@taobao-ohos/hmcrash": "1.1.34",
    "@taobao-ohos/accs_ohos": "0.0.60",
    "@taobao-ohos/tbrest": "0.1.19",
    "@taobao-ohos/multi_screen": "1.0.22",
    "@taobao-ohos/munion_harmony": "1.0.31",
    "@taobao-ohos/craft": "1.0.10",
    "@taobao-ohos/tbdinamicx": "1.1.53",
    "@taobao-ohos/dinamicx_devtools": "1.1.26",
    "@ohos/lottie": "2.0.12",
    "@ohos/multinavigation": "1.0.4",
    "long": "5.2.1",
    "@ohos/protobufjs": "2.0.1",
    "pako": "2.1.0",
    "@ohos/flutter_ohos": "2.0.0-fliggy-ohos-2224d83466",
    "@fliggy-ohos/aion_sdk": "1.0.138",
    "@fliggy-ohos/sqflite": "1.0.138",
    "@fliggy-ohos/flutter_boost": "1.0.138",
    "@fliggy-ohos/path_provider_ohos": "1.0.138",
    "@fliggy-ohos/permission_handler_ohos": "1.0.138",
    "@fliggy-ohos/device_info_plus": "1.0.138",
    "@fliggy-ohos/package_info_plus": "1.0.138",
    "@fliggy-ohos/shared_preferences_ohos": "1.0.138",
    "@fliggy-ohos/url_launcher_ohos": "1.0.138",
    "@fliggy-ohos/high_available": "1.0.138",
    "@fliggy-ohos/wpk_uploader": "1.0.138",
    "@fliggy-ohos/flutter_module": "1.0.138",
//    "@ohos/flutter_ohos": "file:./localdeps/flutter.har",
//    "@fliggy-ohos/aion_sdk": "file:./localdeps/aion_sdk.har",
//    "@fliggy-ohos/sqflite": "file:./localdeps/sqflite.har",
//    "@fliggy-ohos/flutter_boost": "file:./localdeps/flutter_boost.har",
//    "@fliggy-ohos/path_provider_ohos": "file:./localdeps/path_provider_ohos.har",
//    "@fliggy-ohos/permission_handler_ohos": "file:./localdeps/permission_handler_ohos.har",
//    "@fliggy-ohos/device_info_plus": "file:./localdeps/device_info_plus.har",
//    "@fliggy-ohos/package_info_plus": "file:./localdeps/package_info_plus.har",
//    "@fliggy-ohos/shared_preferences_ohos": "file:./localdeps/shared_preferences_ohos.har",
//    "@fliggy-ohos/url_launcher_ohos": "file:./localdeps/url_launcher_ohos.har",
//    "@fliggy-ohos/high_available": "file:./localdeps/high_available.har",
//    "@fliggy-ohos/wpk_uploader": "file:./localdeps/wpk_uploader.har",
//    "@fliggy-ohos/flutter_module": "file:./localdeps/flutter_module.har",
    "@fliggy-ohos/jsbridge": "1.0.92",
//    "@fliggy-ohos/jsbridge": "file:./localdeps/jsbridge.har",
    "@fliggy-ohos/scancode": "1.0.15",
    "@fliggy-ohos/logger": "1.0.9",
    "@fliggy-ohos/login": "1.0.40",
    "@fliggy-ohos/titlebar": "1.0.23",
    "@fliggy-ohos/pay": "1.0.18",
    "@fliggy-ohos/launcherimpl": "1.0.38",
    "@fliggy-ohos/commonutils": "1.0.16",
    "@fliggy-ohos/permission": "1.0.14",
    "@fliggy-ohos/fperformancekit": "1.0.4",
    "@fliggy-ohos/iconfont": "1.0.3",
    "@fliggy-ohos/fmmkv": "1.0.1",
    "@fliggy-ohos/tracker": "1.0.12",
    "@fliggy-ohos/homepage": "file:./localdeps/homepage.har",
    "@fliggy-ohos/fliggy_im": "file:./localdeps/fliggy_im.har",
    "@fliggy-ohos/train": "1.0.8",
    "@fliggy-ohos/location": "1.0.4",
    "@fliggy-ohos/fliggykv": "1.0.1",
    "@fliggy-ohos/mtop": "1.0.10",
    "@fliggy-ohos/fcache": "1.0.4",
    "@fliggy-ohos/configcenter": "1.0.2",
    "@fliggy-ohos/dynamicrouter": "1.0.12",
    "@fliggy-ohos/player": "1.0.4",
    "@fliggy-ohos/commonui": "1.0.6",
    "@fliggy-ohos/unicorn": "1.0.71",
    "@fliggy-ohos/router": "1.0.43",
    "@fliggy-ohos/env": "1.0.11",
    "@fliggy-ohos/appcompat": "1.0.8",
    "@fliggy-ohos/launcher": "1.0.1",
    "@fliggy-ohos/flutter_container": "1.0.23",
    "@fliggy-ohos/share": "1.0.10",
    "@fliggy-ohos/commonbiz": "1.0.8",
    "@taobao-ohos/data_provider": "1.0.10",
    "@taobao-ohos/xlite": "1.0.17",
    "@ohos/gpu_transform": "1.0.1",
    "@ohos/crypto-js": "2.0.0",
    "@ohos/axios": "2.2.4",
    "@ohos/disklrucache": "2.0.2-rc.3",
    "@taobao-ohos/launcher_interface": "1.0.13",
    "@taobao-ohos/webview": "1.1.96",
    "@taobao-ohos/themis_inside": "1.1.381",
    "@taobao-ohos/themis_ability_basic": "1.1.381",
    "@taobao-ohos/themis_kernel": "1.1.381",
    "@taobao-ohos/themis_container": "1.1.381",
    "@taobao-ohos/themis_web": "1.1.381",
    "@taobao-ohos/themis_uniapp": "1.1.381",
    "@taobao-ohos/artc_engine": "1.1.1",
    "@taobao-ohos/marvel": "1.0.8-ohos-taobao",
    "@taobao-ohos/megability_idl_interface": "1.8.69",
    "@taobao-ohos/megability_interface": "1.8.16",
    "@taobao-ohos/megability_kit": "1.8.34",
    "@taobao-ohos/megability_hub": "1.8.52",
    "@taobao-ohos/mega_broadcast_ability": "1.8.34",
    "@taobao-ohos/mega_executor_ability": "1.8.34",
    "@taobao-ohos/mega_device_ability": "1.8.34",
    "@taobao-ohos/mega_accelerometer_ability": "1.8.34",
    "@taobao-ohos/mega_app_ability": "1.8.34",
    "@taobao-ohos/mega_clipboard_ability": "1.8.34",
    "@taobao-ohos/mega_contacts_ability": "1.8.34",
    "@taobao-ohos/mega_haptics_engine_ability": "1.8.34",
    "@taobao-ohos/mega_kv_storage_ability": "1.8.34",
    "@taobao-ohos/mega_log_ability": "1.8.34",
    "@taobao-ohos/mega_mem_kv_storage_ability": "1.8.34",
    "@taobao-ohos/mega_screen_ability": "1.8.34",
    "@taobao-ohos/mega_security_ability": "1.8.34",
    "@taobao-ohos/mega_system_ability": "1.8.34",
    "@taobao-ohos/mega_tel_ability": "1.8.34",
    "@taobao-ohos/mega_toast_ability": "1.8.34",
    "@taobao-ohos/mega_uploader_ability": "1.8.34",
    "@taobao-ohos/mega_user_kv_storage_ability": "1.8.34",
    "@taobao-ohos/mega_utils": "1.8.34",
    "@taobao-ohos/mega_file_ability": "1.8.34",
    "@taobao-ohos/mega_ali_upload_service_ability": "1.8.34",
    "@taobao-ohos/mega_websocket_ability": "1.8.34",
    "@taobao-ohos/mega_audio_context_ability": "1.8.34",
    "@taobao-ohos/mega_http_request_ability": "1.8.34",
    "@taobao-ohos/mega_orientation_ability": "1.8.34",
    "@taobao-ohos/mega_navigator_ability": "1.8.34",
    "@taobao-ohos/mega_mtop_abilities_ohos": "0.0.22",
    "@taobao-ohos/mega_orange_abilities_ohos": "0.0.7",
    "@taobao-ohos/ut_mega_sdk": "0.1.9",
    "@taobao-ohos/abtest": "0.0.38",
    "@taobao-ohos/flow_customs": "1.0.15",
    "@taobao-ohos/libcxx-shared": "1.0.2",
    "@taobao-ohos/taobaooauthsdk": "1.5.118",
    "reflect-metadata": "0.1.13",
    "@cro-ohos/rp_verify": "0.0.10",
    "@protobufjs/utf8": "1.1.0",
    "@protobufjs/eventemitter": "1.1.0",
    "@types/node": "18.15.11",
    "@protobufjs/path": "1.1.2",
    "@protobufjs/float": "1.0.2",
    "@protobufjs/pool": "1.1.0",
    "@protobufjs/base64": "1.1.2",
    "@protobufjs/aspromise": "1.1.2",
    "@cainiao-ohos/bifrost-engine": "1.0.27",
    "@ohos/aki": "1.2.7",
    "@taobao-ohos/megability_for_taobao": "1.8.13",
    "@taobao-ohos/account_taobao": "1.5.118",
    "@taobao-ohos/themis_weex2": "1.1.381",
    "@taobao-ohos/account_playground": "1.5.118",
    "@taobao-ohos/qking": "0.21.1-ohos",
    "@taobao-ohos/weex_framework": "0.21.8-framework-ohos",
    "@taobao-ohos/unicorn": "0.21.8-ohos",
    "@taobao-ohos/weex-ability": "0.21.2-ohos",
    "@taobao-ohos/stdpop": "1.0.69",
    "@taobao-ohos/address": "1.0.42",
    "@alipay/cashier_core": "1.0.************",
    "@alipay/cashier_platform": "24.1.106",
    "@alipay/cashier_harmony_app_taobao": "24.1.107",
    "@alipay/html5parser": "1.0.0",
    "@alipay/mpaas_flybird": "1.0.19",
    "@alipay/mpaas_flybird_adapter": "1.0.25",
    "@alipay/verifyidentity": "1.1.3",
    "@alipay/bifrost": "1.0.************",
    "@alipay/network": "1.0.************",
    "@alipay/mpaas_netsdkextdepend": "1.0.************",
    "@alipay/harmony_transport": "1.0.************",
    "@alipay/blueshieldsdk": "1.0.26",
    "@alipay/blueshieldres": "1.0.1",
    "@alipay/antui": "1.0.14",
    "@alipay/product_biometric_adapt": "1.0.8",
    "@alipay/product_biometric": "1.0.241113113110",
    "@alipay/mpaas_spmtracker": "1.0.250110105910",
    "@alipay/afservicesdk": "1.0.2",
    "@fliggy-ohos/fliggylive": "1.0.6",
    "@taobao-ohos/tbglobalmenu": "1.0.5",
    "@taobao-ohos/taobao_content_playcontrol": "1.0.81",
    "@taobao-ohos/kmp_api_wrapper": "0.0.6",
    "@taobao-ohos/common_utils_wrapper": "0.0.32",
    "@taobao-ohos/tlgoods": "1.1.134-fliggy",
    "@taobao-ohos/powermsg_ohos": "1.0.55",
    "@taobao-ohos/mega_powermsg_abilities": "1.0.16",
    "@taobao-ohos/taolive-kmp": "1.0.122",
    "@taobao-ohos/taobaolive_next_arch": "1.0.199",
    "@taobao-ohos/tbkmp_wrapper": "0.2.114-cmp",
    "@taobao-ohos/megability_c_interface": "1.7.5",
    "@taobao-ohos/kmp_mega_wrapper": "0.0.5",
    "@taobao-ohos/taoliveroom": "2.1.93-fliggyfix",
    "@taobao-ohos/mega_design": "1.0.19",
    "@taobao-ohos/detail_media_swiper": "1.0.258",
    "@taobao-ohos/detail_platform": "1.0.258",
    "@taobao-ohos/detail_tb_adapter": "1.0.258",
    "@taobao-ohos/detail_tb_app": "1.0.258",
    "@taobao-ohos/megability_registry": "1.5.63",
    "@taobao-ohos/tblive_ability": "1.0.32",
    "@taobao-ohos/applicationmonitor": "1.0.44",
    "@taobao-ohos/zcache_ability": "1.0.3-rc.4"
  }
}