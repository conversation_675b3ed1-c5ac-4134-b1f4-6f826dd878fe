import sys
import json
import base64
import urllib.request
from json5 import loads, dumps
from packaging.version import Version

def dependencies_from(ref):
    url = f"http://gitlab.alibaba-inc.com/api/v3/projects/2784618/repository/files?private_token=Ns6d9FmRvR9HVnNCEDAf&file_path=dependencies.json&ref={ref}"

    try:
        response = urllib.request.urlopen(url)
        data = loads(response.read().decode())
        if 'content' in data:
            dependency_list = json.loads(base64.b64decode(data['content']).decode())
            dependencies = {}
            for dependency_item in dependency_list:
                key = f"@{dependency_item['group']}/{dependency_item['artifact']}"
                dependencies[key] = dependency_item['version']
            return dependencies
        else:
            return {}
    except Exception as e:
        print(f"Error fetching dependencies from {url}: {e}")
        return {}

if len(sys.argv) < 2:
    print('缺少参数，可传入tag或者分支名。eg: python sync_dependency_from_taobao.py v10.42.10')
    sys.exit(1)

ref = sys.argv[1]
taobao_dependencies = dependencies_from(ref)

errorList = []

with open('oh-package.json5', 'r') as file:
    oh_package = loads(file.read())

for dependency, version in taobao_dependencies.items():
    if not oh_package['overrides'].get(dependency, '').startswith('file:'):
        current_version = oh_package['overrides'].get(dependency)
        if current_version:
            try:
                taobao_ver = Version(version)
                feizhu_ver = Version(current_version)
                if taobao_ver > feizhu_ver:
                    oh_package['overrides'][dependency] = version
                else:
                    print(f"{dependency}版本比当前低，无需更新。当前: {current_version}; 手淘版本: {version}")
            except:
                print(f"版本号解析失败，强制使用手淘版本: {dependency}: 当前: {current_version}; 手淘版本: {version}")
                oh_package['overrides'][dependency] = version
                errorList.append(f"{dependency}: 当前: {current_version}; 手淘版本: {version}")

        else:
            oh_package['overrides'][dependency] = version

with open('oh-package.json5', 'w') as file:
    file.write(dumps(oh_package, indent=2))

import subprocess
subprocess.run(['ohpm', 'clean'], check=True)
subprocess.run(['ohpm', 'install', '--all'], check=True)

lock_dependency_names = set()
with open('entry/oh-package-lock.json5', 'r') as file:
    package_lock = loads(file.read())
    for key, value in package_lock['packages'].items():
        lock_dependency_names.add(value['name'])

with open('oh-package.json5', 'r') as file:
    oh_package = loads(file.read())
    oh_package['overrides'] = {k: v for k, v in oh_package['overrides'].items()
                               if k in lock_dependency_names}

with open('oh-package.json5', 'w') as file:
    file.write(dumps(oh_package, indent=2))

print("未overrides的库:")
with open('entry/oh-package-lock.json5', 'r') as file:
    package_lock = loads(file.read())
    for key, value in package_lock['packages'].items():
        dependency = value['name']
        resolved_url = value['resolved']
        if resolved_url.startswith('http') and dependency not in oh_package['overrides']:
            print(dependency)

print(f"版本号解析失败，强制使用手淘版本库：")
for dependency in errorList:
    print(dependency)