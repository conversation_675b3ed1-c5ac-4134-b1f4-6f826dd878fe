/*
* Copyright (c) 2023 Hunan OpenValley Digital Industry Development Co., Ltd.
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

// Script for compiling build behavior. It is built in the build plug-in and cannot be modified currently.
// export { appTasks } from '@ohos/hvigor-ohos-plugin';
import { appTasks } from '@ohos/hvigor-ohos-plugin';
import {mtl_dependencies_sync,mtl_hap_plugin_list,mtl_deal_version} from '@ali/fliggy-mtl-protocol-parse'

function pluginsList() {
  // mtl构建
  // mtl构建flutter产物选择规则：变更单，同mtl_dependencies_sync默认值情况；集成单和发布单用release产物。
  console.log('pluginsList')
  if(process.env.MUPP_PROJECT_ID != undefined) {
    return mtl_hap_plugin_list();
  }
  // end mtl构建

  /*
   * @integrateAreaId：本地依赖同步，将integrateAreaId对应的value替换为你想要的变更单或者集成区的依赖，同步一次之后注释掉改代码，否则会每次sync都同步依赖
   * */
  // return [mtl_dependencies_sync({"integrateAreaId": 1189168})];
  return []
}

export default {
  system: appTasks,
  plugins: [...pluginsList()],
}