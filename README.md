# 本地联编文档
注意！！！使用本地联编会改动oh-package.json5文件，请勿提交改动！！！

注意！！！使用本地联编会改动oh-package.json5文件，请勿提交改动！！！

注意！！！使用本地联编会改动oh-package.json5文件，请勿提交改动！！！

## 原理
由于overrides到本地目录的方案不生效，因此我们自行实现了鸿蒙本地联编。具体操作原理是：
- 在壳工程的oh-package.json5文件中写入overrides，目标是到localdeps目录下的子模块har文件
 ![](https://gw.alicdn.com/imgextra/i2/O1CN013yw1CD1swC1C9Warg_!!6000000005830-0-tps-3996-1206.jpg)

- 编译子模块，生成har文件，执行 ./fk-ohos assemble可以自动完成
- 将子模块的har文件复制到壳工程的localdeps/目录下(./fk-ohos assemble可以自动完成)
- 更新依赖，打包壳工程的hap
- 安装hap，启动应用

## 使用
portal/ohos目录下有一个可执行文件fk-ohos(取fake-ohos之意，假装是一个ohos的官方命令)，该命令行工具支持以下功能：

### 清理
```shell
./fk-ohos clean
```
这个命令会清理localdeps/目录下的本地产物、entry的构建目录以及oh-package.json5中的以@fliggy-ohos开头的本地overrides。
一般在打包发现有缓存、新功能一直不生效时使用。

### 构建
```shell
./fk-ohos assemble jsbridge commonui
```
这个命令会构建子模块并打包、安装应用。后面跟需要构建的子模块名称，有一个前提条件是需要jsbridge、commonui等子模块的文件路径和portal壳工程平级。

### 调试
```shell
./fk-ohos debug
```
以调试模式运行应用，类似Android的./xxx.sh debug，可以断点调试启动阶段的代码

断点的位置需要在portal的包里

![](https://gw.alicdn.com/imgextra/i3/O1CN01ncB5XI1TuwXaCxgXQ_!!6000000002443-0-tps-2880-1920.jpg)


## 代码更新

每次代码更新一些新库时，到portal的entry路径下执行 ohpm update
然后sync工程，确保依赖都拉取到
![](https://gw.alicdn.com/imgextra/i1/O1CN01cl3RSe1VwR3vFptbj_!!6000000002717-0-tps-980-238.jpg)

## FAQ
1. 如果报错 json.decoder.JSONDecodeError: Expecting property name enclosed in double quotes: line 47 column 3 (char 1749)

这种情况一般是oh-package.json5中，使用了单引号或者末尾有多余的逗号，修改成标准的json格式即可

2. 使用./fk-ohos assemble  TestModule 报错： ERROR: Unable to find 'hwsdk.dir' in 'local.properties' or 'HOS_SDK_HOME' in the system environment path. at TestModule/local.properties
  - 配置 HOS_SDK_HOME，指向 hwsdk.dir=/Users/<USER>/tools/harmony/X86SDK
  - 在TestModule项目的local.properties中，配置hwsdk.dir （推荐）
  ![图片](https://gw.alicdn.com/imgextra/i1/O1CN01676QiY1nxxSoIcGgY_!!6000000005157-0-tps-2388-1742.jpg)


3. 本地模块版本不生效，确认portal的localdeps文件里是否有本地文件，可以查看entry/oh-package-lock.json5看看锁定的版本，如果不是可以在portal/ohos/entry 里执行 ohpm update更新下依赖
  ![](https://gw.alicdn.com/imgextra/i3/O1CN01Wlb3Jw1auDDbCv6bs_!!6000000003389-0-tps-3274-1294.jpg)

4. IDE有自己的独立缓存，执行完assemble之后，可以点击 File -> Sync and Refresh Project 刷新IDE的模块缓存
5. 调试只能在portal/ohos中进行，是在oh_modules/目录下找到你的模块以及对应的代码，下断点，attach进程即可；如果需要debug启动阶段的功能，同样下好断点，执行./fk-ohos debug，应用启动之后，在IDE里attach
6. 报错 Cannot find module '@ohos/flutter_boost' or its corresponding type declarations.
- 在IDE中点击 Build -> Clean Project 清理项目缓存
- 如果还不行，删除~/.hvigor/project_caches
7. 摩天轮新构建模块的代码，在本地不生效
- IDE构建默认不会更新snapshot依赖，因此需要到模块目录下执行ohpm update。如果使用fk-ohos脚本，不会存在这个问题
8. 依赖的库版本一直更新不了
- 到entry的oh-package-lock.json5里找到对应库的lock版本，先移除后再sync拉取。
  ![](https://gw.alicdn.com/imgextra/i1/O1CN01SpKp1n1KJXesyVGED_!!6000000001143-0-tps-3542-1500.jpg)