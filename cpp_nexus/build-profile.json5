{
  "apiType": "stageMode",
  "buildOption": {
    "externalNativeOptions": {
      "path": "./CMakeLists.txt",
      "arguments": "-DOHOS_STL=c++_shared",
      "abiFilters": [
        "arm64-v8a"
      ],
      "cppFlags": "-std=c++20",
    },
    "napiLibFilterOption": {
      "excludes" : ["**/libc++_shared.so", "**/libweex_framework.so", "**/libquickjs.so"]
    },
  },
  "buildOptionSet": [
    {
      "name": "debug",
      "externalNativeOptions": {
        "arguments": "-DOHOS_STL=c++_shared -DCMAKE_BUILD_TYPE=Debug",
      }
    },
    {
      "name": "debugWithSymbol",
      "externalNativeOptions": {
        "arguments": "-DOHOS_STL=c++_shared -DCMAKE_BUILD_TYPE=Debug"
      },
      "nativeLib": {
        "debugSymbol": {
          "strip": false
        }
      }
    },
    {
      "name": "release",
      "externalNativeOptions": {
        "arguments": "-DOHOS_STL=c++_shared -DCMAKE_BUILD_TYPE=Release",
      }
    },
    {
      "name": "releaseWithSymbol",
      "externalNativeOptions": {
        "arguments": "-DOHOS_STL=c++_shared -DCMAKE_BUILD_TYPE=RelWithDebInfo"
      }
    }
  ],
  "buildModeBinder": [
    {
      "buildModeName": "debugWithSymbol",
      "mappings": [
        {
          "buildOptionName": "debugWithSymbol",
          "targetName": "default"
        }
      ]
    },
    {
      "buildModeName": "releaseWithSymbol",
      "mappings": [
        {
          "buildOptionName": "releaseWithSymbol",
          "targetName": "default"
        }
      ]
    }
  ],
  "targets": [
    {
      "name": "default",
      "runtimeOS": "HarmonyOS"
    }
  ]
}