// Copyright (c) 2025 Fliggy Team. All rights reserved.
// Created by guyan on 2025-08-06.
// FIM NAPI Bridge Implementation for HarmonyOS - Standard NAPI Implementation

#include "napi/native_api.h"
// C++标准库头文件
#include <string>
#include <cstdio>
#include <atomic>

//#include <cstddef>
//#include <cstdlib>
//#include <map>
//#include <mutex>
//#include <ctime>
//#include <functional>
//#include <thread>
//#include <chrono>
//#include <memory>
// 使用HarmonyOS官方NAPI头文件
// 暂时使用printf，后续优化为HarmonyOS日志
// #include "hilog/log.h"

// 项目头文件
//#include "fim_napi_bridge.h"

using namespace std;

// 调试专用：FIM Engine相关结构体和错误定义
// 这些是为了调试目的，与fliggy_im保持一致的接口结构

enum class FIMErrorDomain {
    FIM_ERR_DOMAIN_FIMSDK = 0,
    FIM_ERR_DOMAIN_DPS = 1,
    FIM_ERR_DOMAIN_NETWORK = 2
};

enum class FIMErrorCode {
    FIM_ERR_NO_ENGINE = 1001,
    FIM_ERR_SETTING_SERVICE = 1002,
    FIM_ERR_NO_DEVICE_ID = 1003,
    FIM_ERR_NO_APP_KEY = 1004,
    FIM_ERR_ENGINE_START_FAILED = 1005
};

struct FIMError {
    FIMErrorDomain domain;
    FIMErrorCode code;
    std::string developer_message;
    std::string reason;
    std::string extra_info;
    std::string scope;

    // 构造函数，接受四个参数
    FIMError(FIMErrorDomain domain_, FIMErrorCode code_, const std::string& developer_message_,
             const std::string& reason_)
        : domain(domain_), code(code_), developer_message(developer_message_),
          reason(reason_), extra_info(""), scope("") {}

    // 构造函数，接受三个参数
    FIMError(FIMErrorCode code_, const std::string& developer_message_,
            const std::string& reason_)
        : domain(FIMErrorDomain::FIM_ERR_DOMAIN_FIMSDK),
        code(code_),
        developer_message(developer_message_),
        reason(reason_),
        extra_info(""), scope("") {}

    // 默认构造函数
    FIMError()
        : domain(FIMErrorDomain::FIM_ERR_DOMAIN_FIMSDK), code(FIMErrorCode::FIM_ERR_ENGINE_START_FAILED),
          developer_message(""), reason(""), extra_info(""), scope("") {}
};

// 前向声明
struct FIMEngineStartContext;

// Promise结果数据结构
struct PromiseResult {
    std::string result;
    bool isSuccess;
    FIMError error;
    FIMEngineStartContext* context;  // 添加context指针

    PromiseResult(FIMEngineStartContext* ctx, const std::string& res)
        : context(ctx), result(res), isSuccess(true) {}
    PromiseResult(FIMEngineStartContext* ctx, const FIMError& err)
        : context(ctx), error(err), isSuccess(false) {}
};

// Promise上下文结构体，用于FIMEngine::Start异步回调
struct FIMEngineStartContext {
    napi_env env;
    napi_deferred deferred;
    napi_threadsafe_function tsfn;
    std::atomic<bool> resolved;

    FIMEngineStartContext(napi_env e, napi_deferred d, napi_threadsafe_function t)
        : env(e), deferred(d), tsfn(t), resolved(false) {}

    ~FIMEngineStartContext() {
        if (tsfn) {
            napi_release_threadsafe_function(tsfn, napi_tsfn_release);
        }
    }
};

// 将FIMError转换为JavaScript对象
static napi_value CreateErrorObject(napi_env env, const FIMError& error) {
    napi_value errorObj;
    napi_create_object(env, &errorObj);

    // 添加domain字段
    napi_value domainValue;
    napi_create_int32(env, static_cast<int32_t>(error.domain), &domainValue);
    napi_set_named_property(env, errorObj, "domain", domainValue);

    // 添加code字段
    napi_value codeValue;
    napi_create_int32(env, static_cast<int32_t>(error.code), &codeValue);
    napi_set_named_property(env, errorObj, "code", codeValue);

    // 添加developer_message字段
    napi_value messageValue;
    napi_create_string_utf8(env, error.developer_message.c_str(), NAPI_AUTO_LENGTH, &messageValue);
    napi_set_named_property(env, errorObj, "developer_message", messageValue);

    // 添加reason字段
    napi_value reasonValue;
    napi_create_string_utf8(env, error.reason.c_str(), NAPI_AUTO_LENGTH, &reasonValue);
    napi_set_named_property(env, errorObj, "reason", reasonValue);

    // 添加extra_info字段
    napi_value extraInfoValue;
    napi_create_string_utf8(env, error.extra_info.c_str(), NAPI_AUTO_LENGTH, &extraInfoValue);
    napi_set_named_property(env, errorObj, "extra_info", extraInfoValue);

    // 添加scope字段
    napi_value scopeValue;
    napi_create_string_utf8(env, error.scope.c_str(), NAPI_AUTO_LENGTH, &scopeValue);
    napi_set_named_property(env, errorObj, "scope", scopeValue);

    return errorObj;
}

// 线程安全函数的回调，在主线程中执行
static void ThreadSafeFunctionCallback(napi_env env, napi_value js_callback, void* context, void* data) {
    (void)js_callback; // 不使用JavaScript回调
    printf("[DEBUG] ThreadSafeFunctionCallback called - Main thread\n");

    PromiseResult* result = static_cast<PromiseResult*>(data);
    printf("[DEBUG] PromiseResult pointer: %p\n", (void*)result);

    if (result && result->context) {
        printf("[DEBUG] PromiseResult is valid, context: %p\n", (void*)result->context);
        FIMEngineStartContext* ctx = result->context;
        // 检查是否已经解析过
        bool expected = false;
        if (ctx->resolved.compare_exchange_strong(expected, true)) {
            printf("[DEBUG] Promise not yet resolved, proceeding...\n");
            if (result->isSuccess) {
                // 解析成功
                printf("[DEBUG] Resolving promise with success: %s\n", result->result.c_str());
                napi_value napiResult;
                napi_status status = napi_create_string_utf8(env, result->result.c_str(), NAPI_AUTO_LENGTH, &napiResult);
                printf("[DEBUG] napi_create_string_utf8 status: %d\n", status);
                status = napi_resolve_deferred(env, ctx->deferred, napiResult);
                printf("[DEBUG] napi_resolve_deferred status: %d\n", status);
            } else {
                // 解析失败
                printf("[DEBUG] Rejecting promise with error\n");
                napi_value errorObj = CreateErrorObject(env, result->error);
                napi_reject_deferred(env, ctx->deferred, errorObj);
            }
        } else {
            printf("[DEBUG] Promise already resolved, skipping\n");
        }
    } else {
        printf("[DEBUG] Invalid PromiseResult or context\n");
    }

    // 清理数据
    delete result;
    printf("[DEBUG] ThreadSafeFunctionCallback completed\n");
}

// 线程安全的Promise解析函数
static void resolvePromiseThreadSafe(FIMEngineStartContext* context, const std::string& result) {
    printf("[DEBUG] resolvePromiseThreadSafe called with result: %s\n", result.c_str());
    printf("[DEBUG] Context pointer: %p\n", (void*)context);

    if (context && context->tsfn) {
        printf("[DEBUG] Context and tsfn are valid\n");
        PromiseResult* data = new PromiseResult(context, result);
        printf("[DEBUG] Created PromiseResult: %p\n", (void*)data);

        napi_status status = napi_call_threadsafe_function(context->tsfn, data, napi_tsfn_blocking);
        printf("[DEBUG] napi_call_threadsafe_function status: %d\n", status);

        if (status != napi_ok) {
            printf("[DEBUG] napi_call_threadsafe_function failed, deleting data\n");
            delete data;
        } else {
            printf("[DEBUG] napi_call_threadsafe_function succeeded\n");
        }
    } else {
        printf("[DEBUG] Invalid context or tsfn\n");
    }
}

// 线程安全的Promise拒绝函数
static void rejectPromiseThreadSafe(FIMEngineStartContext* context, const FIMError& error) {
    if (context && context->tsfn) {
        PromiseResult* data = new PromiseResult(context, error);
        napi_status status = napi_call_threadsafe_function(context->tsfn, data, napi_tsfn_blocking);
        if (status != napi_ok) {
            delete data;
        }
    }
}





//// 调试专用：FIMEngine类实现 - 用于调试线程安全问题
//namespace alibaba {
//namespace fim {
//
//class DebugFIMEngine {
//public:
//    // 获取单例对象
//    static DebugFIMEngine& GetInstance() {
//        static DebugFIMEngine instance;
//        return instance;
//    }
//
//    // 确保不可拷贝和不可移动
//    DebugFIMEngine(const DebugFIMEngine&) = delete;
//    DebugFIMEngine& operator=(const DebugFIMEngine&) = delete;
//
//    // Start方法 - 调试版本，可以设置断点
//    void Start(const std::function<void()>& OnSuccess,
//               const std::function<void(const FIMError& error)>& OnFailure) {
//
////        printf("[DEBUG] DebugFIMEngine::Start called - Thread ID: %p\n", (void*)std::this_thread::get_id());
//
//        // 在新线程中执行启动逻辑，用于调试异步启动过程
//        std::thread([this, OnSuccess, OnFailure]() {
////            printf("[DEBUG] DebugFIMEngine::Start - Worker thread started, Thread ID: %p\n", (void*)std::this_thread::get_id());
//
//            try {
//                // 调试点1：检查引擎状态
//                printf("[DEBUG] Step 1: Checking engine initialization...\n");
//                if (!engine_initialized_) {
//                    printf("[DEBUG] Engine not initialized, failing...\n");
//                    OnFailure(FIMError(FIMErrorDomain::FIM_ERR_DOMAIN_FIMSDK,
//                                     FIMErrorCode::FIM_ERR_NO_ENGINE,
//                                     "fim engine not start",
//                                     "FIMErrorCode::FIM_ERR_NO_ENGINE"));
//                    return;
//                }
//
//                // 调试点2：检查设置服务
//                printf("[DEBUG] Step 2: Checking setting service...\n");
//                if (!setting_service_created_) {
//                    printf("[DEBUG] Setting service not created, failing...\n");
//                    OnFailure(FIMError(FIMErrorDomain::FIM_ERR_DOMAIN_FIMSDK,
//                                     FIMErrorCode::FIM_ERR_SETTING_SERVICE,
//                                     "fim setting service not create",
//                                     "FIMErrorCode::FIM_ERR_NO_SETTING_SERVICE"));
//                    return;
//                }
//
//                // 调试点3：检查设备ID
//                printf("[DEBUG] Step 3: Checking device ID: %s\n", device_id_.c_str());
//                if (device_id_.empty()) {
//                    printf("[DEBUG] Device ID empty, failing...\n");
//                    OnFailure(FIMError(FIMErrorDomain::FIM_ERR_DOMAIN_FIMSDK,
//                                     FIMErrorCode::FIM_ERR_NO_DEVICE_ID,
//                                     "fim device id not set",
//                                     "FIMErrorCode::FIM_ERR_NO_DEVICE_ID"));
//                    return;
//                }
//
//                // 调试点4：检查AppKey
//                printf("[DEBUG] Step 4: Checking app key: %s\n", app_key_.c_str());
//                if (app_key_.empty()) {
//                    printf("[DEBUG] App key empty, failing...\n");
//                    OnFailure(FIMError(FIMErrorDomain::FIM_ERR_DOMAIN_FIMSDK,
//                                     FIMErrorCode::FIM_ERR_NO_APP_KEY,
//                                     "fim app key not set",
//                                     "FIMErrorCode::FIM_ERR_NO_APP_KEY"));
//                    return;
//                }
//
//                // 调试点5：模拟启动过程
//                printf("[DEBUG] Step 5: Starting engine initialization...\n");
//                std::this_thread::sleep_for(std::chrono::milliseconds(100));
//
//                // 调试点6：标记为已启动
//                printf("[DEBUG] Step 6: Marking engine as started...\n");
//                started_ = true;
//
//                // 调试点7：调用成功回调
////                printf("[DEBUG] Step 7: Calling success callback - Thread ID: %p\n", (void*)std::this_thread::get_id());
//                OnSuccess();
//                printf("[DEBUG] Success callback completed\n");
//
//            } catch (const std::exception& e) {
//                printf("[DEBUG] Exception caught: %s\n", e.what());
//                FIMError error(FIMErrorCode::FIM_ERR_ENGINE_START_FAILED,
//                             "Engine start failed", e.what());
//                OnFailure(error);
//            }
//        }).detach();
//
//        printf("[DEBUG] DebugFIMEngine::Start - Worker thread detached\n");
//    }
//
//    // 其他方法
//    bool IsStarted() {
//        return started_;
//    }
//
//private:
//    // 私有构造和析构函数
//    DebugFIMEngine() : started_(false), engine_initialized_(true), setting_service_created_(true),
//                      device_id_("debug_device_id"), app_key_("debug_app_key") {
//        printf("[DEBUG] DebugFIMEngine instance created\n");
//    }
//    ~DebugFIMEngine() = default;
//
//    bool started_;
//    bool engine_initialized_;
//    bool setting_service_created_;
//    std::string device_id_;
//    std::string app_key_;
//};
//
//} // namespace fim
//} // namespace alibaba
//
//// 调试专用：FIMEngine::Start的NAPI封装函数
//static napi_value DebugFIMEngineStart(napi_env env, napi_callback_info info) {
//    printf("[DEBUG] DebugFIMEngineStart called - Main thread\n");
//
//    // 创建Promise
//    napi_deferred deferred;
//    napi_value promise;
//    napi_status status = napi_create_promise(env, &deferred, &promise);
//    if (status != napi_ok) {
//        printf("[DEBUG] Failed to create promise\n");
//        napi_throw_error(env, nullptr, "Failed to create promise");
//        return nullptr;
//    }
//
//    // 创建threadsafe function
//    napi_threadsafe_function tsfn;
//    napi_value resource_name;
//    napi_create_string_utf8(env, "FIMEngineStartCallback", NAPI_AUTO_LENGTH, &resource_name);
//
//    status = napi_create_threadsafe_function(
//        env,
//        nullptr,  // 不需要JavaScript回调函数
//        nullptr,  // 不需要async resource
//        resource_name,
//        0,        // 最大队列大小，0表示无限制
//        1,        // 初始线程计数
//        nullptr,  // 线程结束回调数据
//        nullptr,  // 线程结束回调函数
//        nullptr,  // 上下文数据，我们将在调用时传递
//        ThreadSafeFunctionCallback,
//        &tsfn
//    );
//
//    if (status != napi_ok) {
//        printf("[DEBUG] Failed to create threadsafe function\n");
//        napi_throw_error(env, nullptr, "Failed to create threadsafe function");
//        return nullptr;
//    }
//
//    // 创建上下文对象
//    auto context = std::make_shared<FIMEngineStartContext>(env, deferred, tsfn);
//    printf("[DEBUG] Promise context created with threadsafe function\n");
//
//    // 定义成功回调 - 使用线程安全的Promise解析
//    auto onSuccess = [context]() {
//        printf("[DEBUG] Success callback called in worker thread\n");
//        resolvePromiseThreadSafe(context.get(), "FIM Engine started successfully");
//        printf("[DEBUG] resolvePromiseThreadSafe called\n");
//    };
//
//    // 定义失败回调 - 使用线程安全的Promise解析
//    auto onFailure = [context](const FIMError& error) {
//        printf("[DEBUG] Failure callback called in worker thread: %s\n", error.developer_message.c_str());
//        rejectPromiseThreadSafe(context.get(), error);
//        printf("[DEBUG] rejectPromiseThreadSafe called\n");
//    };
//
//    // 调用DebugFIMEngine::Start方法
//    try {
//        printf("[DEBUG] Calling DebugFIMEngine::GetInstance().Start()\n");
//        alibaba::fim::DebugFIMEngine::GetInstance().Start(onSuccess, onFailure);
//        printf("[DEBUG] DebugFIMEngine::Start() call completed\n");
//    } catch (const std::exception& e) {
//        printf("[DEBUG] Exception in DebugFIMEngineStart: %s\n", e.what());
//        napi_value errorMsg;
//        napi_create_string_utf8(env, e.what(), NAPI_AUTO_LENGTH, &errorMsg);
//        napi_reject_deferred(env, deferred, errorMsg);
//    }
//
//    printf("[DEBUG] Returning promise\n");
//    return promise;
//}
//
//// FIMEngine类实现 - 与fliggy_im项目保持一致的接口结构
//namespace alibaba {
//namespace fim {
//
//class FIMEngine {
//public:
//    // 获取单例对象
//    static FIMEngine& GetInstance() {
//        static FIMEngine instance;
//        return instance;
//    }
//
//    // 确保不可拷贝和不可移动
//    FIMEngine(const FIMEngine&) = delete;
//    FIMEngine& operator=(const FIMEngine&) = delete;
//    FIMEngine(FIMEngine&&) = delete;
//    FIMEngine& operator=(FIMEngine&&) = delete;
//
//    // Start方法 - 与fliggy_im项目保持一致的签名和逻辑
//    void Start(const std::function<void()>& OnSuccess,
//               const std::function<void(const FIMError& error)>& OnFailure) {
//
//        // 在新线程中执行启动逻辑，模拟真实的异步启动过程
//        std::thread([this, OnSuccess, OnFailure]() {
//            try {
//                // 模拟启动检查过程（与fliggy_im实现类似）
//
//                // 1. 检查引擎状态（模拟DPSPubEngine检查）
//                if (!engine_initialized_) {
//                    OnFailure(FIMError(FIMErrorDomain::FIM_ERR_DOMAIN_FIMSDK,
//                                     FIMErrorCode::FIM_ERR_NO_ENGINE,
//                                     "fim engine not start",
//                                     "FIMErrorCode::FIM_ERR_NO_ENGINE"));
//                    return;
//                }
//
//                // 2. 检查设置服务
//                if (!setting_service_created_) {
//                    OnFailure(FIMError(FIMErrorDomain::FIM_ERR_DOMAIN_FIMSDK,
//                                     FIMErrorCode::FIM_ERR_SETTING_SERVICE,
//                                     "fim setting service not create",
//                                     "FIMErrorCode::FIM_ERR_NO_SETTING_SERVICE"));
//                    return;
//                }
//
//                // 3. 检查设备ID
//                if (device_id_.empty()) {
//                    OnFailure(FIMError(FIMErrorDomain::FIM_ERR_DOMAIN_FIMSDK,
//                                     FIMErrorCode::FIM_ERR_NO_DEVICE_ID,
//                                     "fim device id not set",
//                                     "FIMErrorCode::FIM_ERR_NO_DEVICE_ID"));
//                    return;
//                }
//
//                // 4. 检查AppKey
//                if (app_key_.empty()) {
//                    OnFailure(FIMError(FIMErrorDomain::FIM_ERR_DOMAIN_FIMSDK,
//                                     FIMErrorCode::FIM_ERR_NO_APP_KEY,
//                                     "fim app key not set",
//                                     "FIMErrorCode::FIM_ERR_NO_APP_KEY"));
//                    return;
//                }
//
//                // 模拟启动过程
//                std::this_thread::sleep_for(std::chrono::milliseconds(100));
//
//                // 标记为已启动
//                started_ = true;
//
//                // 启动成功
//                OnSuccess();
//
//            } catch (const std::exception& e) {
//                FIMError error(FIMErrorCode::FIM_ERR_ENGINE_START_FAILED,
//                             "Engine start failed", e.what());
//                OnFailure(error);
//            }
//        }).detach();
//    }
//
//    // 其他方法（保持与fliggy_im一致的接口）
//    bool IsStarted() {
//        return started_;
//    }
//
//private:
//    // 私有构造和析构函数以防止外部创建实例
//    FIMEngine() : started_(false), engine_initialized_(true), setting_service_created_(true),
//                  device_id_("test_device_id"), app_key_("test_app_key") {}
//    ~FIMEngine() = default;
//
//    bool started_;
//    bool engine_initialized_;
//    bool setting_service_created_;
//    std::string device_id_;
//    std::string app_key_;
//};
//
//} // namespace fim
//} // namespace alibaba
//
//// 使用与fliggy_im一致的命名空间
//using namespace alibaba::fim;
//
//// FIMEngine::Start的NAPI封装函数 - 修复线程安全问题
//static napi_value FIMEngineStart(napi_env env, napi_callback_info info) {
//    // 创建Promise
//    napi_deferred deferred;
//    napi_value promise;
//    napi_status status = napi_create_promise(env, &deferred, &promise);
//    if (status != napi_ok) {
//        napi_throw_error(env, nullptr, "Failed to create promise");
//        return nullptr;
//    }
//
//    // 创建threadsafe function
//    napi_threadsafe_function tsfn;
//    napi_value resource_name;
//    napi_create_string_utf8(env, "FIMEngineStartCallback", NAPI_AUTO_LENGTH, &resource_name);
//
//    status = napi_create_threadsafe_function(
//        env,
//        nullptr,  // 不需要JavaScript回调函数
//        nullptr,  // 不需要async resource
//        resource_name,
//        0,        // 最大队列大小，0表示无限制
//        1,        // 初始线程计数
//        nullptr,  // 线程结束回调数据
//        nullptr,  // 线程结束回调函数
//        nullptr,  // 上下文数据，我们将在调用时传递
//        ThreadSafeFunctionCallback,
//        &tsfn
//    );
//
//    if (status != napi_ok) {
//        napi_throw_error(env, nullptr, "Failed to create threadsafe function");
//        return nullptr;
//    }
//
//    // 创建上下文对象
//    auto context = std::make_shared<FIMEngineStartContext>(env, deferred, tsfn);
//
//    // 定义成功回调 - 使用线程安全的Promise解析
//    auto onSuccess = [context]() {
//        printf("[DEBUG] Success callback called in worker thread\n");
//        resolvePromiseThreadSafe(context.get(), "FIM Engine started successfully");
//    };
//
//    // 定义失败回调 - 使用线程安全的Promise解析
//    auto onFailure = [context](const FIMError& error) {
//        printf("[DEBUG] Failure callback called in worker thread: %s\n", error.developer_message.c_str());
//        rejectPromiseThreadSafe(context.get(), error);
//    };
//
//    // 调用FIMEngine::Start方法
//    try {
//        FIMEngine::GetInstance().Start(onSuccess, onFailure);
//    } catch (const std::exception& e) {
//        napi_value errorMsg;
//        napi_create_string_utf8(env, e.what(), NAPI_AUTO_LENGTH, &errorMsg);
//        napi_reject_deferred(env, deferred, errorMsg);
//    }
//
//    return promise;
//}
//
///**\n * @brief 线程安全适配器数据结构\n */
//struct ThreadSafeAdapterData {
//    std::string (*syncFunc)(const std::string&);
//    std::string paramsJson;
//    FIMInvokerResultCallback callback;
//    
//    ThreadSafeAdapterData(std::string (*func)(const std::string&), const std::string& params, FIMInvokerResultCallback cb)
//        : syncFunc(func), paramsJson(params), callback(cb) {}
//};
//
///**\n * @brief 线程函数，用于执行同步函数\n *\n * 在单独的线程中执行同步函数，避免阻塞主线程\n */
//static void* executeSyncFunction(void* arg) {
//    ThreadSafeAdapterData* data = static_cast<ThreadSafeAdapterData*>(arg);
//    
//    // 执行同步函数
//    std::string result = data->syncFunc(data->paramsJson);
//    
//    // 通过回调返回结果
//    data->callback(result);
//    
//    // 清理资源
//    delete data;
//    
//    return nullptr;
//}
//
///**\n * @brief 创建线程安全的适配器函数\n *\n * 为同步函数创建线程安全的适配器，确保在单独的线程中执行\n */
//template<std::string (*SyncFunc)(const std::string&)>
//static void createThreadSafeAdapter(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    // 创建线程数据
//    ThreadSafeAdapterData* data = new ThreadSafeAdapterData(SyncFunc, paramsJson, callback);
//    
//    // 创建新线程执行同步函数
//    pthread_t thread;
//    pthread_create(&thread, nullptr, executeSyncFunction, data);
//    pthread_detach(thread);
//}
//
//// HarmonyOS日志配置
//#undef LOG_DOMAIN
//#undef LOG_TAG
//#define LOG_DOMAIN 0x3200  // 自定义domain
//#define LOG_TAG "FIM_NAPI"
//
///**
// * @brief NAPI Promise上下文结构
// * 用于在业务层回调中resolve Promise
// * 使用线程安全函数来跨线程通信
// */
//struct NAPIPromiseContext {
//    napi_env env;
//    napi_deferred deferred;
//    napi_threadsafe_function tsfn;  // 线程安全函数
//
//    NAPIPromiseContext(napi_env e, napi_deferred d, napi_threadsafe_function t)
//        : env(e), deferred(d), tsfn(t) {}
//};
//
///**
// * @brief 全局上下文映射表，用于关联回调ID和Promise上下文
// * 线程安全的映射表，支持跨线程访问
// */
//static std::map<int, NAPIPromiseContext*> g_promiseContextMap;
//static std::mutex g_contextMapMutex;
//static int g_nextContextId = 1;
//
///**
// * @brief 生成唯一的上下文ID
// */
//static int generateContextId() {
//    std::lock_guard<std::mutex> lock(g_contextMapMutex);
//    return g_nextContextId++;
//}
//
///**
// * @brief 存储Promise上下文
// */
//static int storePromiseContext(NAPIPromiseContext* context) {
//    int contextId = generateContextId();
//    std::lock_guard<std::mutex> lock(g_contextMapMutex);
//    g_promiseContextMap[contextId] = context;
//    return contextId;
//}
//
///**
// * @brief 获取并移除Promise上下文
// */
//static NAPIPromiseContext* retrievePromiseContext(int contextId) {
//    std::lock_guard<std::mutex> lock(g_contextMapMutex);
//    std::map<int, NAPIPromiseContext*>::iterator it = g_promiseContextMap.find(contextId);
//    if (it != g_promiseContextMap.end()) {
//        NAPIPromiseContext* context = it->second;
//        g_promiseContextMap.erase(it);
//        return context;
//    }
//    return nullptr;
//}
//
///**
// * @brief 全局当前Promise上下文ID（简化方案）
// * 注意：这种方案不是线程安全的，仅用于演示
// */
//// static int g_currentPromiseContextId = 0;  // 已废弃，使用线程安全的替代方案
//
///**
// * @brief Promise解析回调函数声明
// */
//static void promiseResolverCallback(const std::string& result);
//
///**
// * @brief 回调函数映射表，用于关联特定的回调函数和Promise上下文
// */
//static std::map<void*, int> g_callbackToContextMap;
//
///**
// * @brief 创建Promise包装回调函数
// * 返回一个特殊的回调函数，它知道如何resolve对应的Promise
// */
//// 线程安全的Promise解析函数
//static void resolvePromiseWithResult(int contextId, const std::string& result) {
//    // 获取Promise上下文
//    NAPIPromiseContext* context = retrievePromiseContext(contextId);
//    if (!context) {
//        // 上下文已被清理或不存在
//        return;
//    }
//
//    // 直接解析Promise
//    napi_value napiResult;
//    napi_status status = napi_create_string_utf8(context->env, result.c_str(), SIZE_MAX, &napiResult);
//    if (status == napi_ok) {
//        napi_resolve_deferred(context->env, context->deferred, napiResult);
//    } else {
//        napi_value error;
//        napi_create_string_utf8(context->env, "Failed to create result value", SIZE_MAX, &error);
//        napi_reject_deferred(context->env, context->deferred, error);
//    }
//    
//    // 清理数据
//    delete context;
//}
//
//// 全局变量存储当前上下文ID
//static thread_local int g_currentContextId = 0;
//
//// C风格回调函数适配器
//static void cStyleCallbackAdapter(const std::string& result) {
//    resolvePromiseWithResult(g_currentContextId, result);
//}
//
//static FIMInvokerResultCallback createPromiseCallback(int contextId) {
//    // 存储contextId到线程局部存储中
//    g_currentContextId = contextId;
//    
//    // 返回C风格的回调函数
//    return cStyleCallbackAdapter;
//}
//
///**
// * @brief 线程安全回调数据结构
// */
//struct ThreadSafeCallbackData {
//    std::string result;
//    int contextId;
//
//    ThreadSafeCallbackData(const std::string& r, int id) : result(r), contextId(id) {}
//};
//
///**
// * @brief 线程安全回调函数，在主线程中执行
// * 这个函数会被napi_threadsafe_function调用，确保在主线程中执行
// */
//static void threadSafeCallback(napi_env env, napi_value js_callback, void* context, void* data) {
//    // 避免未使用参数警告
//    (void)js_callback;
//
//    ThreadSafeCallbackData* callbackData = static_cast<ThreadSafeCallbackData*>(data);
//    NAPIPromiseContext* promiseContext = static_cast<NAPIPromiseContext*>(context);
//
//    if (!callbackData || !promiseContext) {
//        return;
//    }
//
//    // 现在我们在主线程中，可以安全地调用NAPI函数
//    napi_value napiResult;
//    napi_status status = napi_create_string_utf8(env, callbackData->result.c_str(), NAPI_AUTO_LENGTH, &napiResult);
//    if (status == napi_ok) {
//        napi_resolve_deferred(env, promiseContext->deferred, napiResult);
//    } else {
//        napi_value error;
//        napi_create_string_utf8(env, "Failed to create result value", SIZE_MAX, &error);
//        napi_reject_deferred(env, promiseContext->deferred, error);
//    }
//
//    // 清理线程安全函数
//    // napi_release_threadsafe_function(promiseContext->tsfn, napi_tsfn_release);
//
//    // 清理数据
//    delete callbackData;
//    delete promiseContext;
//}
//
///**
// * @brief Promise解析回调函数实现
// * 这个函数会在业务层的后台线程中被调用
// * 它直接解析Promise
// */
//// 已废弃的函数，使用线程安全的lambda表达式替代
//// static void promiseResolverCallback(const std::string& result) {
////     // 获取当前的上下文ID
////     int contextId = g_currentPromiseContextId;
//// 
////     // 获取Promise上下文
////     NAPIPromiseContext* context = retrievePromiseContext(contextId);
////     if (!context) {
////         // 上下文已被清理或不存在
////         return;
////     }
//// 
////     // 直接解析Promise
////     napi_value napiResult;
////     napi_status status = napi_create_string_utf8(context->env, result.c_str(), NAPI_AUTO_LENGTH, &napiResult);
////     if (status == napi_ok) {
////         napi_resolve_deferred(context->env, context->deferred, napiResult);
////     } else {
////         napi_value error;
////         napi_create_string_utf8(context->env, "Failed to create result value", NAPI_AUTO_LENGTH, &error);
////         napi_reject_deferred(context->env, context->deferred, error);
////     }
////     
////     // 清理数据
////     delete context;
//// }
//
///**
// * @brief 异步FIM操作上下文结构
// *
// * 用于管理异步NAPI调用的上下文信息，确保线程安全
// */
//struct AsyncFIMContext {
//    // napi_env env;                    // NAPI环境句柄
//    // napi_deferred deferred;          // Promise的deferred对象
//    // napi_threadsafe_function tsfn;   // 线程安全函数
//    string result;                   // 操作结果
//    string error;                    // 错误信息
//    bool success;                    // 操作是否成功
//
//    AsyncFIMContext(/*napi_env e, napi_deferred d*/)
//        : /*env(e), deferred(d), tsfn(nullptr),*/ success(false) {}
//
//    ~AsyncFIMContext() {
//        // 析构时确保线程安全函数被正确释放
//        /*if (tsfn != nullptr) {
//            napi_release_threadsafe_function(tsfn, napi_tsfn_release);
//        }*/
//    }
//};
//
//
///**
// * @brief 统一的FIM方法NAPI封装函数
// *
// * 这个模板函数统一处理所有FIM方法的NAPI封装逻辑：
// * 1. 参数校验（确保只有一个JSON字符串参数）
// * 2. 字符串参数提取和转换
// * 3. 创建Promise对象
// * 4. 调用业务逻辑函数
// * 5. 返回Promise给ArkTS/JS
// *
// * @param env NAPI环境句柄
// * @param info NAPI回调信息
// * @param methodName 方法名称（用于错误信息和调试）
// * @param businessFunc 对应的业务逻辑函数指针
// * @return napi_value Promise对象
// */
//template<typename BusinessFunc>
//static napi_value handleFIMMethodAsync(napi_env env, napi_callback_info info,
//                                       const char* methodName, BusinessFunc businessFunc) {
//    printf("[DEBUG] handleFIMMethodAsync ENTRY: method=%s\n", methodName);
//
//    // 1. 获取并校验参数个数
//    size_t argc = 1;
//    napi_value args[1] = {nullptr};
//    napi_status status = napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);
//
//    if (status != napi_ok) {
//        printf("[DEBUG] Failed to parse arguments, status=%d\n", status);
//        napi_throw_error(env, nullptr, "Failed to parse arguments");
//        return nullptr;
//    }
//
//    printf("[DEBUG] Arguments parsed successfully, argc=%zu\n", argc);
//
//    if (argc != 1) {
//        printf("[DEBUG] Wrong argument count: expected=1, actual=%zu\n", argc);
//        napi_throw_error(env, nullptr, "Expected exactly 1 argument (JSON string)");
//        return nullptr;
//    }
//
//    // 2. 检查参数类型
//    napi_valuetype valuetype;
//    status = napi_typeof(env, args[0], &valuetype);
//    printf("[DEBUG] Argument type check: status=%d, valuetype=%d\n", status, valuetype);
//
//    // 添加调试信息
//    const char* typeNames[] = {"undefined", "null", "boolean", "number", "string", "symbol", "object", "function", "external", "bigint"};
//    const char* actualTypeName = (valuetype < 10) ? typeNames[valuetype] : "unknown";
//
//    if (status != napi_ok) {
//        printf("[DEBUG] Failed to get argument type: status=%d\n", status);
//        std::string errorMsg = std::string(methodName) + " failed to get argument type.";
//        napi_throw_error(env, nullptr, errorMsg.c_str());
//        return nullptr;
//    }
//
//    if (valuetype != napi_string) {
//        printf("[DEBUG] Wrong argument type: expected=string(4), actual=%s(%d)\n", actualTypeName, valuetype);
//        std::string errorMsg = std::string(methodName) + " argument must be a string (JSON). Got type: " + actualTypeName;
//        napi_throw_error(env, nullptr, errorMsg.c_str());
//        return nullptr;
//    }
//
//    printf("[DEBUG] Argument type validation passed: string\n");
//
//    // 3. 提取字符串参数
//    size_t str_size = 0;
//    status = napi_get_value_string_utf8(env, args[0], nullptr, 0, &str_size);
//    if (status != napi_ok) {
//        napi_throw_error(env, nullptr, "Failed to get string size");
//        return nullptr;
//    }
//
//    char* str_buffer = new char[str_size + 1];
//    size_t str_length = 0;
//    status = napi_get_value_string_utf8(env, args[0], str_buffer, str_size + 1, &str_length);
//    if (status != napi_ok) {
//        delete[] str_buffer;
//        napi_throw_error(env, nullptr, "Failed to get string content");
//        return nullptr;
//    }
//
//    std::string jsonStr(str_buffer);
//    delete[] str_buffer;
//
//    // 4. 创建Promise用于真正的异步处理
//    napi_deferred deferred;
//    napi_value promise;
//    napi_status promiseStatus = napi_create_promise(env, &deferred, &promise);
//    if (promiseStatus != napi_ok) {
//        napi_throw_error(env, nullptr, "Failed to create promise");
//        return nullptr;
//    }
//
//    // 5. 不要立即解析Promise，而是让业务逻辑函数通过回调来解析Promise
//    printf("[DEBUG] Promise created for method: %s\n", methodName);
//
//    // 创建Promise上下文
//    NAPIPromiseContext* context = new NAPIPromiseContext(env, deferred, nullptr);
//    int contextId = storePromiseContext(context);
//
//    // 创建Promise回调函数并调用业务逻辑
//    FIMInvokerResultCallback promiseCallback = createPromiseCallback(contextId);
//    
//    // 调用业务逻辑函数（异步）
//    businessFunc(jsonStr, promiseCallback);
//
//    return promise;
//}
//
//
///**
// * @brief NAPI 封装函数：获取会话列表
// * @param env NAPI 环境句柄
// * @param info NAPI 回调信息，包含参数等
// * @return napi_value 返回给 ArkTS/JS 的值
// */
//static napi_value NAPI_fim_get_conversations(napi_env env, napi_callback_info info) {
//    // getConversations 是异步函数，需要使用异步处理
//    return handleFIMMethodAsync(env, info, "fim_get_conversations", fliggy::fim::business::getConversations);
//}
//
//// ============================================================================
//// 异步方法实现 - 优先级P0方法（根据文档要求）
//// ============================================================================
//
//
///**
// * @brief 异步版本：发送消息适配器
// *
// * 为同步的sendMessage函数创建异步适配器
// */
//static void sendMessageAsync(const std::string& messageJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::sendMessage>(messageJson, callback);
//}
//
//
//// ============================================================================
//// 同步方法实现（保持向后兼容）
//// ============================================================================
//
//// ============================================================================
//// 消息相关桥接方法 (14个)
//// ============================================================================
//
///**
// * @brief NAPI 封装函数：发送消息
// */
///**
// * @brief 异步版本：发送消息适配器
// *
// * 为同步的sendMessage函数创建异步适配器
// */
//static void sendMessageAsyncAdapter(const std::string& messageJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::sendMessage>(messageJson, callback);
//}
//
//static napi_value NAPI_fim_send_message(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_send_message", sendMessageAsyncAdapter);
//}
//
///**
// * @brief NAPI 封装函数：获取消息
// */
///**\n * @brief 异步版本：获取消息适配器\n *\n * 为同步的getMessage函数创建异步适配器\n */
//static void getMessageAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::getMessage>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_get_message(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_get_message", getMessageAsync);
//}
//
///**
// * @brief NAPI 封装函数：获取本地消息
// */
///**\n * @brief 异步版本：获取本地消息适配器\n *\n * 为同步的getLocalMessage函数创建异步适配器\n */
//static void getLocalMessageAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::getLocalMessage>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_get_local_message(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_get_local_message", getLocalMessageAsync);
//}
//
///**
// * @brief NAPI 封装函数：删除消息
// */
///**\n * @brief 异步版本：删除消息适配器\n *\n * 为同步的deleteMessage函数创建异步适配器\n */
//static void deleteMessageAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::deleteMessage>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_delete_message(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_delete_message", deleteMessageAsync);
//}
//
///**
// * @brief NAPI 封装函数：删除本地消息
// */
///**\n * @brief 异步版本：删除本地消息适配器\n *\n * 为同步的deleteLocalMessage函数创建异步适配器\n */
//static void deleteLocalMessageAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::deleteLocalMessage>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_delete_local_message(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_delete_local_message", deleteLocalMessageAsync);
//}
//
///**
// * @brief NAPI 封装函数：撤回消息
// */
///**\n * @brief 异步版本：撤回消息适配器\n *\n * 为同步的recallMessage函数创建异步适配器\n */
//static void recallMessageAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::recallMessage>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_recall_message(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_recall_message", recallMessageAsync);
//}
//
///**
// * @brief NAPI 封装函数：重发消息
// */
///**\n * @brief 异步版本：重发消息适配器\n *\n * 为同步的resendMessage函数创建异步适配器\n */
//static void resendMessageAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::resendMessage>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_resend_message(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_resend_message", resendMessageAsync);
//}
//
///**
// * @brief NAPI 封装函数：回复消息
// */
///**\n * @brief 异步版本：回复消息适配器\n *\n * 为同步的replyMessage函数创建异步适配器\n */
//static void replyMessageAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::replyMessage>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_reply_message(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_reply_message", replyMessageAsync);
//}
//
///**
// * @brief NAPI 封装函数：列出之前的本地消息
// */
///**\n * @brief 异步版本：列出之前的本地消息适配器\n *\n * 为同步的listPreviousLocalMsgs函数创建异步适配器\n */
//static void listPreviousLocalMsgsAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::listPreviousLocalMsgs>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_list_previous_local_msgs(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_list_previous_local_msgs", listPreviousLocalMsgsAsync);
//}
//
///**
// * @brief NAPI 封装函数：列出之前的消息
// */
///**\n * @brief 异步版本：列出之前的消息适配器\n *\n * 为同步的listPreviousMsgs函数创建异步适配器\n */
//static void listPreviousMsgsAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::listPreviousMsgs>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_list_previous_msgs(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_list_previous_msgs", listPreviousMsgsAsync);
//}
//
///**
// * @brief NAPI 封装函数：列出之后的本地消息
// */
///**\n * @brief 异步版本：列出之后的本地消息适配器\n *\n * 为同步的listNextLocalMsgs函数创建异步适配器\n */
//static void listNextLocalMsgsAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::listNextLocalMsgs>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_list_next_local_msgs(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_list_next_local_msgs", listNextLocalMsgsAsync);
//}
//
///**
// * @brief NAPI 封装函数：列出之后的消息
// */
///**\n * @brief 异步版本：列出之后的消息适配器\n *\n * 为同步的listNextMsgs函数创建异步适配器\n */
//static void listNextMsgsAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::listNextMsgs>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_list_next_msgs(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_list_next_msgs", listNextMsgsAsync);
//}
//
///**
// * @brief NAPI 封装函数：列出消息读取状态
// */
///**\n * @brief 异步版本：列出消息读取状态适配器\n *\n * 为同步的listMessagesReadStatus函数创建异步适配器\n */
//static void listMessagesReadStatusAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::listMessagesReadStatus>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_list_messages_read_status(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_list_messages_read_status", listMessagesReadStatusAsync);
//}
//
///**
// * @brief NAPI 封装函数：更新消息为已读
// */
///**\n * @brief 异步版本：更新消息为已读适配器\n *\n * 为同步的updateMessageToRead函数创建异步适配器\n */
//static void updateMessageToReadAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::updateMessageToRead>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_update_message_to_read(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_update_message_to_read", updateMessageToReadAsync);
//}
//
//// ============================================================================
//// 会话相关桥接方法 (8个)
//// ============================================================================
//
///**
// * @brief NAPI 封装函数：创建会话
// */
///**\n * @brief 异步版本：创建会话适配器\n *\n * 为同步的createConversation函数创建异步适配器\n */
//static void createConversationAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::createConversation>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_create_conversation(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_create_conversation", createConversationAsync);
//}
//
///**
// * @brief NAPI 封装函数：删除会话
// */
///**\n * @brief 异步版本：删除会话适配器\n *\n * 为同步的deleteConversation函数创建异步适配器\n */
//static void deleteConversationAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::deleteConversation>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_delete_conversation(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_delete_conversation", deleteConversationAsync);
//}
//
///**
// * @brief NAPI 封装函数：更新会话
// */
///**\n * @brief 异步版本：更新会话适配器\n *\n * 为同步的updateConversation函数创建异步适配器\n */
//static void updateConversationAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::updateConversation>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_update_conversation(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_update_conversation", updateConversationAsync);
//}
//
///**
// * @brief NAPI 封装函数：获取会话详情
// */
///**\n * @brief 异步版本：获取会话详情适配器\n *\n * 为同步的getConversationDetail函数创建异步适配器\n */
//static void getConversationDetailAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::getConversationDetail>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_get_conversation_detail(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_get_conversation_detail", getConversationDetailAsync);
//}
//
///**
// * @brief NAPI 封装函数：获取会话成员
// */
///**\n * @brief 异步版本：获取会话成员适配器\n *\n * 为同步的getConversationMembers函数创建异步适配器\n */
//static void getConversationMembersAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::getConversationMembers>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_get_conversation_members(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_get_conversation_members", getConversationMembersAsync);
//}
//
///**
// * @brief NAPI 封装函数：添加会话成员
// */
///**\n * @brief 异步版本：添加会话成员适配器\n *\n * 为同步的addConversationMembers函数创建异步适配器\n */
//static void addConversationMembersAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::addConversationMembers>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_add_conversation_members(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_add_conversation_members", addConversationMembersAsync);
//}
//
///**
// * @brief NAPI 封装函数：移除会话成员
// */
///**\n * @brief 异步版本：移除会话成员适配器\n *\n * 为同步的removeConversationMembers函数创建异步适配器\n */
//static void removeConversationMembersAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::removeConversationMembers>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_remove_conversation_members(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_remove_conversation_members", removeConversationMembersAsync);
//}
//
///**
// * @brief NAPI 封装函数：设置会话管理员
// */
///**\n * @brief 异步版本：设置会话管理员适配器\n *\n * 为同步的setConversationAdmin函数创建异步适配器\n */
//static void setConversationAdminAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::setConversationAdmin>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_set_conversation_admin(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_set_conversation_admin", setConversationAdminAsync);
//}
//
//// ============================================================================
//// 搜索相关桥接方法 (4个)
//// ============================================================================
//
///**
// * @brief NAPI 封装函数：搜索消息
// */
///**\n * @brief 异步版本：搜索消息适配器\n *\n * 为同步的searchMessages函数创建异步适配器\n */
//static void searchMessagesAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::searchMessages>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_search_messages(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_search_messages", searchMessagesAsync);
//}
//
///**
// * @brief NAPI 封装函数：搜索会话
// */
///**\n * @brief 异步版本：搜索会话适配器\n *\n * 为同步的searchConversations函数创建异步适配器\n */
//static void searchConversationsAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::searchConversations>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_search_conversations(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_search_conversations", searchConversationsAsync);
//}
//
///**
// * @brief NAPI 封装函数：搜索联系人
// */
///**\n * @brief 异步版本：搜索联系人适配器\n *\n * 为同步的searchContacts函数创建异步适配器\n */
//static void searchContactsAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::searchContacts>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_search_contacts(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_search_contacts", searchContactsAsync);
//}
//
///**
// * @brief NAPI 封装函数：全局搜索
// */
///**\n * @brief 异步版本：全局搜索适配器\n *\n * 为同步的globalSearch函数创建异步适配器\n */
//static void globalSearchAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::globalSearch>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_global_search(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_global_search", globalSearchAsync);
//}
//
//// ============================================================================
//// 用户和联系人相关桥接方法 (6个)
//// ============================================================================
//
///**
// * @brief NAPI 封装函数：获取用户信息
// */
///**\n * @brief 异步版本：获取用户信息适配器\n *\n * 为同步的getUserInfo函数创建异步适配器\n */
//static void getUserInfoAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::getUserInfo>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_get_user_info(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_get_user_info", getUserInfoAsync);
//}
//
///**
// * @brief NAPI 封装函数：更新用户信息
// */
///**\n * @brief 异步版本：更新用户信息适配器\n *\n * 为同步的updateUserInfo函数创建异步适配器\n */
//static void updateUserInfoAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::updateUserInfo>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_update_user_info(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_update_user_info", updateUserInfoAsync);
//}
//
///**
// * @brief NAPI 封装函数：获取联系人列表
// */
///**\n * @brief 异步版本：获取联系人列表适配器\n *\n * 为同步的getContacts函数创建异步适配器\n */
//static void getContactsAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::getContacts>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_get_contacts(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_get_contacts", getContactsAsync);
//}
//
///**
// * @brief NAPI 封装函数：添加联系人
// */
///**\n * @brief 异步版本：添加联系人适配器\n *\n * 为同步的addContact函数创建异步适配器\n */
//static void addContactAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::addContact>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_add_contact(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_add_contact", addContactAsync);
//}
//
///**
// * @brief NAPI 封装函数：删除联系人
// */
///**\n * @brief 异步版本：删除联系人适配器\n *\n * 为同步的removeContact函数创建异步适配器\n */
//static void removeContactAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::removeContact>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_remove_contact(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_remove_contact", removeContactAsync);
//}
//
///**
// * @brief NAPI 封装函数：更新联系人信息
// */
///**\n * @brief 异步版本：更新联系人信息适配器\n *\n * 为同步的updateContact函数创建异步适配器\n */
//static void updateContactAsync(const std::string& paramsJson, FIMInvokerResultCallback callback) {
//    createThreadSafeAdapter<fliggy::fim::business::updateContact>(paramsJson, callback);
//}
//
//static napi_value NAPI_fim_update_contact(napi_env env, napi_callback_info info) {
//    return handleFIMMethodAsync(env, info, "fim_update_contact", updateContactAsync);
//}

/**
 * @brief 模块初始化函数，注册所有导出函数
 * @param env NAPI环境句柄
 * @param exports 导出对象
 * @return napi_value 返回导出对象
 */
static napi_value Init(napi_env env, napi_value exports) {
    napi_status status;

    // 导出FIM NAPI Bridge函数 - 完整的32个方法 + 异步版本 + 调试版FIM Engine
    napi_property_descriptor desc[] = {
        // 调试版FIM Engine 方法
//        {"fim_engine_start", nullptr, DebugFIMEngineStart, nullptr, nullptr, nullptr, napi_default, nullptr},
//
//        // 消息相关方法 (15个)
//        {"fim_get_conversations", nullptr, NAPI_fim_get_conversations, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_send_message", nullptr, NAPI_fim_send_message, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_get_message", nullptr, NAPI_fim_get_message, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_get_local_message", nullptr, NAPI_fim_get_local_message, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_delete_message", nullptr, NAPI_fim_delete_message, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_delete_local_message", nullptr, NAPI_fim_delete_local_message, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_recall_message", nullptr, NAPI_fim_recall_message, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_resend_message", nullptr, NAPI_fim_resend_message, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_reply_message", nullptr, NAPI_fim_reply_message, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_list_previous_local_msgs", nullptr, NAPI_fim_list_previous_local_msgs, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_list_previous_msgs", nullptr, NAPI_fim_list_previous_msgs, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_list_next_local_msgs", nullptr, NAPI_fim_list_next_local_msgs, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_list_next_msgs", nullptr, NAPI_fim_list_next_msgs, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_list_messages_read_status", nullptr, NAPI_fim_list_messages_read_status, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_update_message_to_read", nullptr, NAPI_fim_update_message_to_read, nullptr, nullptr, nullptr, napi_default, nullptr},
//
//        // 会话相关方法 (8个)
//        {"fim_create_conversation", nullptr, NAPI_fim_create_conversation, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_delete_conversation", nullptr, NAPI_fim_delete_conversation, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_update_conversation", nullptr, NAPI_fim_update_conversation, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_get_conversation_detail", nullptr, NAPI_fim_get_conversation_detail, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_get_conversation_members", nullptr, NAPI_fim_get_conversation_members, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_add_conversation_members", nullptr, NAPI_fim_add_conversation_members, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_remove_conversation_members", nullptr, NAPI_fim_remove_conversation_members, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_set_conversation_admin", nullptr, NAPI_fim_set_conversation_admin, nullptr, nullptr, nullptr, napi_default, nullptr},
//
//        // 搜索相关方法 (4个)
//        {"fim_search_messages", nullptr, NAPI_fim_search_messages, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_search_conversations", nullptr, NAPI_fim_search_conversations, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_search_contacts", nullptr, NAPI_fim_search_contacts, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_global_search", nullptr, NAPI_fim_global_search, nullptr, nullptr, nullptr, napi_default, nullptr},
//
//        // 用户和联系人相关方法 (6个)
//        {"fim_get_user_info", nullptr, NAPI_fim_get_user_info, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_update_user_info", nullptr, NAPI_fim_update_user_info, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_get_contacts", nullptr, NAPI_fim_get_contacts, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_add_contact", nullptr, NAPI_fim_add_contact, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_remove_contact", nullptr, NAPI_fim_remove_contact, nullptr, nullptr, nullptr, napi_default, nullptr},
//        {"fim_update_contact", nullptr, NAPI_fim_update_contact, nullptr, nullptr, nullptr, napi_default, nullptr}
    };
    
    status = napi_define_properties(env, exports, sizeof(desc) / sizeof(desc[0]), desc);
    if (status != napi_ok) {
        napi_throw_error(env, nullptr, "Failed to define properties");
        return nullptr;
    }

    return exports;
}

/**
 * @brief 模块描述符，定义模块名称和初始化函数
 */
static napi_module demoModule = {
    .nm_version = 1,
    .nm_flags = 0,
    .nm_filename = nullptr,
    .nm_register_func = Init,
    .nm_modname = "fim_napi",  // 模块名称，必须与CMakeLists.txt中的库名一致
    .nm_priv = ((void*)0),
    .reserved = { 0 },
};

/**
 * @brief 模块注册函数
 */
extern "C" __attribute__((constructor)) void RegisterHelloModule(void) {
    napi_module_register(&demoModule);
}
