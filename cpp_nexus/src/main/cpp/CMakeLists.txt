# the minimum version of CMake.
cmake_minimum_required(VERSION 3.4.1)
project(cpp_nexus)


set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++20 -fPIC -Os -ffunction-sections -fdata-sections")
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -s")
set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -Wl,--gc-sections")
# 开启额外的警告

#-Werror: 这个标志会将所有警告转化为错误，这样在编译时任何警告都会导致编译失败。这是一种确保代码质量和正确性的方法，因为它迫使开发者解决所有警告。
#-Wshorten-64-to-32: 当编译器检测到可能导致数据丢失的 64 位到 32 位的隐式转换时，它会发出警告。
#-Wno-error=reorder: 尽管成员初始化顺序不正确会发出警告，但这个标志确保不会因为这个警告而导致编译错误。
#-Wno-error=unused-variable: 不将未使用变量的警告当作错误，这样可以防止编译因未使用变量而失败，尽管警告本身仍然会显示。
#-Wno-error=unused-private-field: 提供类中存在未使用的私有字段时，编译器会发出警告，但这个标志会阻止这种警告成为编译错误。
#-Wno-error=unknown-warning-option: 如果编译器不识别某个警告选项，通常会发出警告，但该标志会防止这种警告升级为错误。
#-Wno-error=unused-command-line-argument: 这个标志确保如果命令行中提供了未使用的参数，编译器会发出警告，但不会导致编译错误。
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Werror -Wshorten-64-to-32 -Wno-error=reorder -Wno-error=unused-variable -Wno-error=unused-private-field -Wno-error=unknown-warning-option -Wno-error=unused-command-line-argument -Wno-error=shorten-64-to-32")

# set(PKG_PATH "${CMAKE_CURRENT_LIST_DIR}/../../../oh_modules/@taobao-ohos")
set(DINGTALK_PKG_PATH "${CMAKE_CURRENT_LIST_DIR}/../../../oh_modules/@dingtalk")

message("cpp全源码编译开始")

file(GLOB PKG_LIBS_DIRS "${PKG_PATH}/*")
foreach (entry ${PKG_LIBS_DIRS})
    get_filename_component(folder_name ${entry} NAME)
    if (IS_DIRECTORY ${entry})
        if (EXISTS "${entry}/include")
            include_directories(${entry}/include)
            message("${entry}/include 文件夹添加成功")
        endif ()
        if (IS_DIRECTORY ${entry}/cppSrc)
            include(${entry}/cppSrc/CMakeLists.txt)
            file(GLOB_RECURSE SO_FILES "${entry}/libs/*.so")
            if(SO_FILES)
                file(REMOVE ${SO_FILES})
            endif()
        elseif(EXISTS "${entry}/libs")
                get_filename_component(folder_name ${entry} NAME)
                            message("Folder found: ${folder_name}")
                            message(${entry}/libs/${PLATFORM_NAME})
                            file(GLOB LIBS "${entry}/libs/${CMAKE_OHOS_ARCH_ABI}/*")
                            if (LIBS)
                                message("add libs:${LIBS}")
                                add_library("tcpkg::${folder_name}" INTERFACE IMPORTED)
                                foreach (lib ${LIBS})
                                    get_filename_component(lib_name ${lib} NAME)
                                    if (NOT ${lib_name} STREQUAL "libc++_shared.so")
                                        target_link_libraries("tcpkg::${folder_name}" INTERFACE ${lib})
                                    endif ()
                                endforeach ()
                                message("${PKG_NAME_SPACE}::${folder_name} 添加成功")
                            endif()

        endif()

    endif ()
endforeach ()


foreach (entry ${PKG_LIBS_DIRS})
     if (IS_DIRECTORY ${entry}/cppSrc)
           include(${entry}/cppSrc/link.cmake)
      endif()
endforeach ()

# 添加钉钉SDK支持
#file(GLOB DINGTALK_LIBS_DIRS "${DINGTALK_PKG_PATH}/*")
#foreach (entry ${DINGTALK_LIBS_DIRS})
#    get_filename_component(folder_name ${entry} NAME)
#    if (IS_DIRECTORY ${entry})
#        if (EXISTS "${entry}/include")
#            include_directories(${entry}/include)
#            message("DingTalk ${entry}/include 文件夹添加成功")
#        endif ()
#        if(EXISTS "${entry}/libs")
#            get_filename_component(folder_name ${entry} NAME)
#            message("DingTalk Folder found: ${folder_name}")
#            file(GLOB LIBS "${entry}/libs/${CMAKE_OHOS_ARCH_ABI}/*")
#            if (LIBS)
#                message("add DingTalk libs:${LIBS}")
#                add_library("dingtalk::${folder_name}" INTERFACE IMPORTED)
#                foreach (lib ${LIBS})
#                    get_filename_component(lib_name ${lib} NAME)
#                    if (NOT ${lib_name} STREQUAL "libc++_shared.so")
#                        target_link_libraries("dingtalk::${folder_name}" INTERFACE ${lib})
#                    endif ()
#                endforeach ()
#                message("dingtalk::${folder_name} 添加成功")
#            endif()
#        endif()
#    endif ()
#endforeach ()

# 添加源文件 - 明确指定需要的文件
set(NAPI_SOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/napi_init.cpp"
#    "${CMAKE_CURRENT_SOURCE_DIR}/fim_napi_bridge.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/hello.cpp"
)
#
## 创建共享库 - 使用标准NAPI实现
#add_library(hello SHARED ${NAPI_SOURCES})
#
## 链接必要的库
#target_link_libraries(hello PUBLIC libace_napi.z.so)
#
## 链接钉钉SDK
#target_link_libraries(hello PUBLIC dingtalk::aimsdk)
#
## 设置包含目录
#target_include_directories(hello PRIVATE
#    ${CMAKE_CURRENT_SOURCE_DIR}
#)
#
## 设置编译选项
#target_compile_options(hello PRIVATE
#    -fvisibility=hidden
#    -fexceptions
#)
#
## 设置链接选项
#set_target_properties(hello PROPERTIES
#    CXX_VISIBILITY_PRESET hidden
#    VISIBILITY_INLINES_HIDDEN ON
#)