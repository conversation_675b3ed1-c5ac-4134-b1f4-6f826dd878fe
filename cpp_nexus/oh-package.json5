{
  "license": "Apache-2.0",
  "author": "Taobao Developer",
  "name": "cpp_nexus",
  "description": "Please describe the basic information.",
  "main": "index.ets",
  "version": "1.0.0",
  "dependencies": {
    "@mobilesec-ohos/securityguardsdk": "*",
    "@taobao-ohos/accs_ohos": "*",
    "@taobao-ohos/aus": "*",
    "@taobao-ohos/boringssl": "*",
    "@taobao-ohos/mtop_next": "*",
    "@taobao-ohos/pie": "*",
    "@taobao-ohos/protodb": "*",
    "@taobao-ohos/tlog_engine": "*",
    "@taobao-ohos/tlog_uploader": "*",
    "@taobao-ohos/tnet": "*",
    "@taobao-ohos/xnet_next": "*",
    "@taobao-ohos/utdid_sdk": "*",
    "@taobao-ohos/ut_kernel": "*",
    "@taobao-ohos/ut_analytics_sdk": "*",
    "@taobao-ohos/xlite": "*",
    "@taobao-ohos/zstd": "*",
    "@taobao-ohos/image_kit": "*",
    "@taobao-ohos/orange_next": "*",
    "@taobao-ohos/account": "*",
    "@taobao-ohos/account_auth": "*",
    "@taobao-ohos/account_sso": "*",
    "@taobao-ohos/account_mega": "*",
    "@taobao-ohos/account_ui": "*",
//    "@taobao-ohos/account_playground": "*",
    "@taobao-ohos/dinamicx": "*",
    "@taobao-ohos/tbdinamicx": "*",
//    "@taobao-ohos/dinamicx_devtools": "*",
    "@taobao-ohos/hmcrash": "*",
    "@taobao-ohos/tbrest": "*",
    "@fliggy-ohos/fmmkv": "*",
    "@taobao-ohos/megability_hub": "*",
    "@taobao-ohos/megability_registry": "*",
    "@taobao-ohos/megability_idl_interface": "*",
    "@taobao-ohos/megability_interface": "*",
//    "@taobao-ohos/megability_kit": "*",
    "@taobao-ohos/taobao_runtime": "*",
    "@taobao-ohos/nav": "*",

    // dingtalk start
    "@dingtalk/dingpaassdk": "*",
    "@dingtalk/dingpaasaim": "*",
    // dingtalk end

    // tbmessage_ohos start
//    "@taobao-ohos/tbmessage_ohos": "*",
//    "@taobao-ohos/applicationmonitor": "*",
//    "@taobao-ohos/messagesdk": "*",
//    "@taobao-ohos/peregrine_lite": "*",
//    "@taobao-ohos/pop_layer_sdk": "*",
//    "@taobao-ohos/syncsdk": "*",
//    "@taobao-ohos/fts_engine": "*",
    // "@taobao-ohos/nano_compose": "*", // 临时注释，与pie版本不兼容

//    "@dingtalk/aimsdk": "*",
    // tbmessage_ohos end

    "@fliggy-ohos/commonutils": "*",
    "@fliggy-ohos/appcompat": "*",
    "@fliggy-ohos/commonui": "*",
    "@fliggy-ohos/commonbiz": "*",
    "@fliggy-ohos/unicorn": "*",
    "@fliggy-ohos/fcache": "*",
    "@fliggy-ohos/homepage": "*",
    "@fliggy-ohos/router": "*",
    "@fliggy-ohos/dynamicrouter": "*",
    "@fliggy-ohos/player": "*",
    "@fliggy-ohos/scancode": "*",
    "@fliggy-ohos/flutter_container": "*",
    "@fliggy-ohos/jsbridge": "*",
    "@fliggy-ohos/mtop": "*",
    "@taobao-ohos/ezmock": "*",
    "@taobao-ohos/munion_harmony": "*",
    "@fliggy-ohos/titlebar": "*",
    "@fliggy-ohos/iconfont": "*",
    "@fliggy-ohos/logger": "*",
    "@fliggy-ohos/login": "*",
    "@fliggy-ohos/env": "*",
    "@fliggy-ohos/tracker": "*",
    "@fliggy-ohos/fliggykv": "*",
    "@fliggy-ohos/permission": "*",
    "@fliggy-ohos/launcher": "*",
    "@fliggy-ohos/launcherimpl": "*",
    "@fliggy-ohos/pay": "*",
    "@fliggy-ohos/fliggy_im": "*",
//    "@amap/amap_lbs_common": "*",
//    "@amap/amap_lbs_map3d": "*",
    "@taobao-ohos/tlog": "*",
    "@taobao-ohos/preloader": "*",
    "@ohos/flutter_ohos": "*",
    "@fliggy-ohos/sqflite": "*",
    "@fliggy-ohos/aion_sdk": "*",
    "@fliggy-ohos/flutter_boost": "*",
    "@fliggy-ohos/path_provider_ohos": "*",
    "@fliggy-ohos/permission_handler_ohos": "*",
    "@fliggy-ohos/device_info_plus": "*",
    "@fliggy-ohos/package_info_plus": "*",
    "@fliggy-ohos/shared_preferences_ohos": "*",
    "@fliggy-ohos/url_launcher_ohos": "*",
    "@fliggy-ohos/high_available": "*",
    "@fliggy-ohos/flutter_module": "*",
    "@ohos/multinavigation": "*",
    "@taobao-ohos/riverlogger": "*",
    "@taobao-ohos/mtop_ssr": "*",
    "@taobao-ohos/taobao_ffmpeg": "*",
    "@taobao-ohos/taobaoavsdk": "*",
    "@taobao-ohos/uikit": "*",
    "@taobao-ohos/zcache": "*",
    "@taobao-ohos/webview": "*",
    "@taobao-ohos/multi_screen": "*",
    "@taobao-ohos/craft": "*",
    "@taobao-ohos/launcher_interface": "*",
    "@ohos/lottie": "*",
    "long": "*",
    "@ohos/protobufjs": "*",
    "pako": "*",
    "@taobao-ohos/data_provider": "*",
    "@ohos/gpu_transform": "*",
    "@ohos/crypto-js": "*",
    "@ohos/axios": "*",
    "@ohos/disklrucache": "*",
    "@taobao-ohos/themis_inside": "*",
    "@taobao-ohos/themis_ability_basic": "*",
    "@taobao-ohos/themis_kernel": "*",
    "@taobao-ohos/themis_container": "*",
    "@taobao-ohos/themis_web": "*",
    "@taobao-ohos/themis_uniapp": "*",
//    "@taobao-ohos/unicorn": "*",
    "@taobao-ohos/artc_engine": "*",
    "@taobao-ohos/marvel": "*",
    "@taobao-ohos/mega_broadcast_ability": "*",
    "@taobao-ohos/mega_executor_ability": "*",
    "@taobao-ohos/mega_accelerometer_ability": "*",
//    "@taobao-ohos/mega_action_sheet_ability": "*",
//    "@taobao-ohos/mega_alert_ability": "*",
    "@taobao-ohos/mega_app_ability": "*",
    "@taobao-ohos/mega_clipboard_ability": "*",
    "@taobao-ohos/mega_contacts_ability": "*",
    "@taobao-ohos/mega_haptics_engine_ability": "*",
    "@taobao-ohos/mega_kv_storage_ability": "*",
//    "@taobao-ohos/mega_loading_ability": "*",
//    "@taobao-ohos/mega_location_ability": "*",
    "@taobao-ohos/mega_log_ability": "*",
    "@taobao-ohos/mega_mem_kv_storage_ability": "*",
    "@taobao-ohos/mega_screen_ability": "*",
    "@taobao-ohos/mega_security_ability": "*",
//    "@taobao-ohos/mega_speech_recognition_ability": "*",
    "@taobao-ohos/mega_system_ability": "*",
    "@taobao-ohos/mega_tel_ability": "*",
    "@taobao-ohos/mega_toast_ability": "*",
    "@taobao-ohos/mega_uploader_ability": "*",
    "@taobao-ohos/mega_user_kv_storage_ability": "*",
    "@taobao-ohos/mega_utils": "*",
    "@taobao-ohos/mega_file_ability": "*",
    "@taobao-ohos/mega_ali_upload_service_ability": "*",
//    "@taobao-ohos/mega_photo_ability": "*",
    "@taobao-ohos/mega_websocket_ability": "*",
    "@taobao-ohos/mega_audio_context_ability": "*",
    "@taobao-ohos/mega_http_request_ability": "*",
//    "@taobao-ohos/mega_image_preview_ability": "*",
    "@taobao-ohos/mega_orientation_ability": "*",
    "@taobao-ohos/mega_mtop_abilities_ohos": "*",
    "@taobao-ohos/mega_orange_abilities_ohos": "*",
    "@taobao-ohos/ut_mega_sdk": "*",
    "@taobao-ohos/abtest": "*",
    "@taobao-ohos/megability_for_taobao": "*",
//    "@taobao-ohos/account_taobao": "*",
//    "@taobao-ohos/stdpop": "*",
//    "@taobao-ohos/qking": "*",
    "@taobao-ohos/mega_navigator_ability": "*",
    "@taobao-ohos/ucc": "*",
    "@taobao-ohos/ucc_mega": "*",
//    "@taobao-ohos/flow_customs": "*",
    //    "@taobao-ohos/themis_weex2": "*",
    //    "@taobao-ohos/weex-ability": "*",
    //    "@taobao-ohos/weex_framework": "*",
    //    "@taobao-ohos/address": "*",

    "@cro-ohos/rp_verify": "*",
    "@protobufjs/utf8": "*",
    "@protobufjs/eventemitter": "*",
//    "@types/node": "*",
    "@protobufjs/path": "*",
    "@protobufjs/float": "*",
    "@protobufjs/pool": "*",
    "@protobufjs/base64": "*",
    "@protobufjs/aspromise": "*",
    "@cainiao-ohos/bifrost-engine": "*",
    /// 标准版本支付
//    "@cashier_alipay/cashiersdk": "*",
    /// 授权相关
    "@alipay/afservicesdk": "*",
    /// 专业版本支付
    "@alipay/cashier_core": "*",
    "@alipay/cashier_platform": "*",
    "@alipay/cashier_harmony_app_taobao": "*",
    "@alipay/mpaas_flybird": "*",
    "@alipay/mpaas_flybird_adapter": "*",
    "@alipay/verifyidentity": "*",
    "@alipay/bifrost": "*",
    "@alipay/network": "*",
    "@alipay/mpaas_netsdkextdepend": "*",
    "@alipay/harmony_transport": "*",
    "@alipay/blueshieldsdk": "*",
    "@alipay/blueshieldres": "*",
    "@alipay/antui": "*",
    "@alipay/product_biometric_adapt": "*",
    "@alipay/product_biometric": "*",
    "@alipay/mpaas_spmtracker": "*",
    "@taobao-ohos/powermsg_ohos": "*",
    "@taobao-ohos/mega_powermsg_abilities": "*",
    "@taobao-ohos/taoliveroom": "*",
    "@taobao-ohos/taobaolive_next_arch": "*",
    "@taobao-ohos/tblive_ability": "*",
    "@taobao-ohos/taolive-kmp": "*",
    "@taobao-ohos/kmp_api_wrapper": "*",
    "@taobao-ohos/kmp_mega_wrapper": "*",
    "@taobao-ohos/megability_c_interface": "*",
    "@taobao-ohos/tbkmp_wrapper": "*",
    "@taobao-ohos/common_utils_wrapper": "*",
    "@taobao-ohos/taobao_content_playcontrol": "*",
    "@taobao-ohos/tlgoods": "*",
    "@taobao-ohos/stdpop": "*",
    "@taobao-ohos/zcache_ability": "*"
  }
}