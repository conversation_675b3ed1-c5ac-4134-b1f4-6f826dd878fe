cmake_minimum_required(VERSION 3.4.1)

project(fim_napi)

# include root project
set(CMAKE_CXX_STANDARD 20)

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++20 -fPIC")
if(${CMAKE_BUILD_TYPE} STREQUAL "Debug")
 find_program(CCACHE_FOUND ccache)
 if (CCACHE_FOUND)
     message("cpp_nexus ccache found")
     set(CMAKE_CXX_COMPILER_LAUNCHER "ccache")
 endif()
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -O0")
else()
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Os -ffunction-sections -fdata-sections")
endif()
set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -Wl,--gc-sections")
# 开启额外的警告

#-Werror: 这个标志会将所有警告转化为错误，这样在编译时任何警告都会导致编译失败。这是一种确保代码质量和正确性的方法，因为它迫使开发者解决所有警告。
#-Wshorten-64-to-32: 当编译器检测到可能导致数据丢失的 64 位到 32 位的隐式转换时，它会发出警告。
#-Wno-error=reorder: 尽管成员初始化顺序不正确会发出警告，但这个标志确保不会因为这个警告而导致编译错误。
#-Wno-error=unused-variable: 不将未使用变量的警告当作错误，这样可以防止编译因未使用变量而失败，尽管警告本身仍然会显示。
#-Wno-error=unused-private-field: 提供类中存在未使用的私有字段时，编译器会发出警告，但这个标志会阻止这种警告成为编译错误。
#-Wno-error=unknown-warning-option: 如果编译器不识别某个警告选项，通常会发出警告，但该标志会防止这种警告升级为错误。
#-Wno-error=unused-command-line-argument: 这个标志确保如果命令行中提供了未使用的参数，编译器会发出警告，但不会导致编译错误。
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fvisibility=hidden")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fvisibility=hidden -Werror -Wno-error=reorder -Wno-error=unused-variable -Wno-error=unused-private-field -Wno-error=unknown-warning-option -Wno-error=unused-command-line-argument -Wno-error=c++2b-extensions -Wno-error=format -Wno-error=deprecated-declarations -Wno-error=ambiguous-reversed-operator -Wno-error=macro-redefined")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wno-unused-command-line-argument") # 去除gcc-toolchain垃圾警告

set(PKG_NAME_SPACE "tcpkg")
set(TCPKG_HEADER_TAG ".tcpkg_cache/deploy")

# 定义函数：查找软链接，并将结果返回到调用者的变量中
function(find_symlinks_recursive root_dir return_var)
    # 初始化本地变量以收集软链接
    set(symlinks_list "")

    # 获取当前目录下的所有文件条目
    file(GLOB_RECURSE all_files_following_symlinks LIST_DIRECTORIES true "${root_dir}/*")
    foreach(file_path IN LISTS all_files_following_symlinks)
        # 使用get_filename_component来检查是否是软链接
        get_filename_component(file_path_realpath "${file_path}" REALPATH)
        get_filename_component(file_path_absolute "${file_path}" ABSOLUTE)

        # 比较 REALPATH 与 ABSOLUTE 路径，如果不同，则是软链接
        if(NOT file_path_realpath STREQUAL file_path_absolute)
            list(APPEND symlinks_list "${file_path}")
        endif()
    endforeach()

    # 设置返回变量为软链接列表，使用 PARENT_SCOPE 使得变量在父作用域可用
    set(${return_var} "${symlinks_list}" PARENT_SCOPE)
endfunction()

set(PKG_PATH "${CMAKE_CURRENT_LIST_DIR}/oh_modules/")

message("cpp全源码编译开始")
include(${PACKAGE_FIND_FILE})

find_symlinks_recursive("${PKG_PATH}" PKG_LIBS_DIRS)
foreach (entry ${PKG_LIBS_DIRS})
    get_filename_component(folder_name ${entry} NAME)
    if (IS_DIRECTORY ${entry})

        if (IS_DIRECTORY ${entry}/cppSrc)
            include(${entry}/cppSrc/CMakeLists.txt)
            message("${folder_name} 现已加入全源码编译套餐 compile now")
            file(GLOB_RECURSE SO_FILES "${entry}/libs/*.so")
        elseif(EXISTS "${entry}/libs")
                get_filename_component(folder_name ${entry} NAME)
                            message("Folder found: ${folder_name}")
                            message(${entry}/libs/${PLATFORM_NAME})
                            file(GLOB LIBS "${entry}/libs/${CMAKE_OHOS_ARCH_ABI}/*")
                            if (LIBS)
                                message("add libs:${LIBS}")
                                add_library("tcpkg::${folder_name}" INTERFACE IMPORTED)
                                foreach (lib ${LIBS})
                                    get_filename_component(lib_name ${lib} NAME)
                                    if (NOT ${lib_name} STREQUAL "libc++_shared.so")
                                          get_filename_component(lib_name_we ${lib} NAME_WE)
                                          message("add lib: ${PKG_NAME_SPACE}::${folder_name}::${lib_name_we} ")
                                          add_library("tcpkg::${folder_name}::${lib_name_we}" INTERFACE IMPORTED)
                                          target_link_libraries("tcpkg::${folder_name}::${lib_name_we}" INTERFACE ${lib})
                                          target_link_libraries("tcpkg::${folder_name}" INTERFACE ${lib})
                                     endif ()
                                endforeach ()
                                message("${PKG_NAME_SPACE}::${folder_name} 添加成功")
                            endif()

        endif()
        # 处理本地依赖
        if(EXISTS "${entry}/.cxx")
            string(FIND "${PROJECT_BINARY_DIR}" "/.cxx/default/default" position)

            if(NOT position EQUAL -1)
                string(SUBSTRING "${PROJECT_BINARY_DIR}" "${position}" -1 SUB_PATH)

                if(EXISTS "${entry}${SUB_PATH}/${TCPKG_HEADER_TAG}/include")
                    include_directories("${entry}${SUB_PATH}/${TCPKG_HEADER_TAG}/include")
                    message("${entry}${SUB_PATH}/${TCPKG_HEADER_TAG}/include 文件夹添加成功")
                endif()
            endif()
            if(${folder_name}_FOUND
                    AND EXISTS "${${folder_name}_CONFIG}")
                message("${folder_name}_CONFIG is ${${folder_name}_CONFIG}")
                file(STRINGS "${${folder_name}_CONFIG}" CMAKE_FILE_CONTENT)
                set(ENTRY_LIBS)

                foreach(CMAKE_FILE_LINE IN LISTS CMAKE_FILE_CONTENT)
                    if(NOT "${CMAKE_FILE_LINE}" STREQUAL "")
                        string(REGEX MATCH " *add_library\\(([^ ]+) SHARED IMPORTED\\)" MATCH_RESULT ${CMAKE_FILE_LINE})

                        if(NOT "${MATCH_RESULT}" STREQUAL ""
                                AND NOT "${CMAKE_MATCH_1}" STREQUAL "")
                            get_target_property(LOCATION_SO_PATH "${CMAKE_MATCH_1}" IMPORTED_LOCATION)
                            list(APPEND ENTRY_LIBS "${LOCATION_SO_PATH}")
                            message("ENTRY_LIBS 本地依赖 添加 ${LOCATION_SO_PATH}")
                        endif()
                    endif()
                endforeach()

                list(LENGTH ENTRY_LIBS ENTRY_LIBS_LENGTH)

                if(ENTRY_LIBS_LENGTH GREATER 0)
                    if(NOT TARGET ${PKG_NAME_SPACE}::${folder_name})
                        add_library("${PKG_NAME_SPACE}::${folder_name}" INTERFACE IMPORTED GLOBAL)
                    endif()

                    target_link_libraries("${PKG_NAME_SPACE}::${folder_name}" INTERFACE ${ENTRY_LIBS})
                    message("${PKG_NAME_SPACE}::${folder_name} 本地依赖 添加成功")
                else()
                endif()
            endif()
        endif()
        if (EXISTS "${entry}/include")
                    include_directories(${entry}/include)
                    message("${entry}/include 文件夹添加成功")
                endif ()
    endif ()
endforeach ()


foreach (entry ${PKG_LIBS_DIRS})
     if (IS_DIRECTORY ${entry}/cppSrc)
           include(${entry}/cppSrc/link.cmake)
      endif()
endforeach ()

set(CMAKE_VERBOSE_MAKEFILE ON)

# 添加源文件
file(GLOB_RECURSE SOURCES
    "src/main/cpp/*.cpp"
    "src/main/cpp/*.c"
)

# 创建 FIM NAPI 桥接库
add_library(fim_napi SHARED ${SOURCES})

# 设置 FliggyIMSDK 路径
set(FLIGGY_IMSDK_PATH "/Users/<USER>/Documents/development/Fliggy/Native/C/FliggyIMSDK-ding")

# 链接必要的库
target_link_libraries(fim_napi PUBLIC
    libace_napi.z.so
    tcpkg::pie
    # TODO: 添加 FliggyIMSDK 库链接（需要先构建 FliggyIMSDK）
)

# 设置包含目录
target_include_directories(fim_napi PRIVATE
    src/main/cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/oh_modules/@taobao-ohos/pie/include
    # 添加 FliggyIMSDK 包含目录
    ${FLIGGY_IMSDK_PATH}/sdk/public
    ${FLIGGY_IMSDK_PATH}/sdk/src
    ${FLIGGY_IMSDK_PATH}/sdk/src/ffi/message
    ${FLIGGY_IMSDK_PATH}/sdk/src/ffi/conversation
    ${FLIGGY_IMSDK_PATH}/sdk/src/method_invoker
)
