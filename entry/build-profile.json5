{
  apiType: 'stageMode',
  buildOption: {
    arkOptions: {
      runtimeOnly: {
        sources: [
          './oh_modules/@alipay/cashier_harmony_app_taobao/src/main/ets/debug/TestPage.ets',
          './oh_modules/@alipay/verifyidentity/src/main/ets/module/pages/BioListPage.ets',
          './oh_modules/@alipay/verifyidentity/src/main/ets/module/pages/DynamicActivityView.ets',
          './oh_modules/@alipay/verifyidentity/src/main/ets/module/fingerprint/fingerprint/FingerprintCheckPage.ets',
          './oh_modules/@alipay/verifyidentity/src/main/ets/module/pages/PayPwdDialogPage.ets',
          './oh_modules/@alipay/verifyidentity/src/main/ets/module/pages/MenuPage.ets',
          './oh_modules/@alipay/verifyidentity/src/main/ets/module/pages/component/PasswordInputUnifiedPluginPage.ets',
          './oh_modules/@alipay/verifyidentity/src/main/ets/module/dynamic/ui/UniversalDialogPage.ets',
          './oh_modules/@alipay/verifyidentity/src/main/ets/module/password/plugin/PasswordInputUnifiedPluginView.ets',
          './oh_modules/@alipay/verifyidentity/src/main/ets/module/prodManager/ui/BioLoginSettingPage.ets',
          './oh_modules/@cro-ohos/rp_verify/src/main/ets/ui/pages/RPBiometricsPage.ets',
          './oh_modules/@cro-ohos/rp_verify/src/main/ets/ui/pages/RPTakePhotoPage.ets',
          './oh_modules/@fliggy-ohos/unicorn/src/main/ets/themis/FliggyTmsRouter.ets',
          './oh_modules/@taobao-ohos/account_ui/src/main/ets/pages/LoginMultiAccountPage.ets',
          './oh_modules/@taobao-ohos/account_ui/src/main/ets/pages/LoginMultiAccountPage.ets',
          './oh_modules/@taobao-ohos/artc_engine/src/main/ets/components/DemoPage.ets',
          './oh_modules/@taobao-ohos/stdpop/src/main/ets/pages/StdPopPage.ets',
          './oh_modules/@taobao-ohos/taobao_content_playcontrol/src/main/ets/components/TestPlayControlEntry.ets',
          './oh_modules/@taobao-ohos/taobaoavsdk/src/main/ets/pages/DemoPage.ets',
          './oh_modules/@taobao-ohos/taoliveroom/src/main/ets/pages/TaoLiveRoomPage.ets',
          './oh_modules/@taobao-ohos/taoliveroom/src/main/ets/Playground/TaoLiveRoomPlayground.ets',
          './oh_modules/@taobao-ohos/tbdinamicx/src/main/ets/pages/DXPreviewPage.ets',
          './oh_modules/@taobao-ohos/uikit/src/main/ets/components/UIKError.ets',
          './oh_modules/@taobao-ohos/webview/src/main/ets/playground/PlaygroundEntry.ets',
        ],
        packages: [
          '@taobao-ohos/taobao_runtime',
          '@taobao-ohos/account_auth',
          '@taobao-ohos/account_sso',
          '@taobao-ohos/nav',
          '@fliggy-ohos/commonutils',
          '@fliggy-ohos/appcompat',
          '@fliggy-ohos/commonui',
          '@fliggy-ohos/commonbiz',
          '@fliggy-ohos/unicorn',
          '@fliggy-ohos/fcache',
          '@fliggy-ohos/homepage',
          '@fliggy-ohos/router',
          '@fliggy-ohos/dynamicrouter',
          '@fliggy-ohos/player',
          '@fliggy-ohos/scancode',
          '@fliggy-ohos/flutter_container',
          '@fliggy-ohos/jsbridge',
          '@fliggy-ohos/mtop',
          '@taobao-ohos/xnet_next',
          '@taobao-ohos/tnet',
          '@taobao-ohos/boringssl',
          '@taobao-ohos/aus',
          '@taobao-ohos/ezmock',
          '@taobao-ohos/munion_harmony',
          '@fliggy-ohos/titlebar',
          '@fliggy-ohos/iconfont',
          '@fliggy-ohos/logger',
          '@fliggy-ohos/login',
          '@fliggy-ohos/env',
          '@fliggy-ohos/tracker',
          '@fliggy-ohos/fliggykv',
          '@fliggy-ohos/permission',
          '@fliggy-ohos/launcher',
          '@fliggy-ohos/launcherimpl',
          '@fliggy-ohos/pay',
          '@fliggy-ohos/fliggy_im',
          '@taobao-ohos/image_kit',
          '@taobao-ohos/tlog',
          '@taobao-ohos/preloader',
          '@taobao-ohos/account',
          '@ohos/flutter_ohos',
          '@fliggy-ohos/aion_sdk',
          '@fliggy-ohos/sqflite',
          '@fliggy-ohos/flutter_boost',
          '@fliggy-ohos/path_provider_ohos',
          '@fliggy-ohos/permission_handler_ohos',
          '@fliggy-ohos/device_info_plus',
          '@fliggy-ohos/package_info_plus',
          '@fliggy-ohos/shared_preferences_ohos',
          '@fliggy-ohos/url_launcher_ohos',
          '@fliggy-ohos/high_available',
          '@fliggy-ohos/wpk_uploader',
          '@fliggy-ohos/flutter_module',
          '@ohos/multinavigation',
          '@mobilesec-ohos/securityguardsdk',
          '@taobao-ohos/mtop_next',
          '@taobao-ohos/utdid_sdk',
          '@taobao-ohos/megability_idl_interface',
          '@taobao-ohos/megability_interface',
          '@taobao-ohos/ut_analytics_sdk',
          '@taobao-ohos/ut_kernel',
          '@taobao-ohos/riverlogger',
          '@taobao-ohos/zstd',
          '@taobao-ohos/account_mega',
          '@taobao-ohos/dinamicx',
          '@taobao-ohos/tlog_engine',
          '@taobao-ohos/tlog_uploader',
          '@taobao-ohos/megability_hub',
          '@taobao-ohos/protodb',
          '@taobao-ohos/mtop_ssr',
          '@taobao-ohos/taobao_ffmpeg',
          '@taobao-ohos/taobaoavsdk',
          '@taobao-ohos/uikit',
          '@taobao-ohos/account_ui',
          '@taobao-ohos/orange_next',
          '@taobao-ohos/zcache',
          '@taobao-ohos/webview',
          '@taobao-ohos/pie',
          '@taobao-ohos/hmcrash',
          '@taobao-ohos/accs_ohos',
          '@taobao-ohos/tbrest',
          '@taobao-ohos/multi_screen',
          '@taobao-ohos/craft',
          '@taobao-ohos/tbdinamicx',
          '@taobao-ohos/launcher_interface',
          '@taobao-ohos/megability_for_taobao',
          '@taobao-ohos/mega_navigator_ability',
          '@taobao-ohos/ucc',
          '@taobao-ohos/ucc_mega',
          '@ohos/lottie',
          'long',
          '@ohos/protobufjs',
          'pako',
          '@taobao-ohos/data_provider',
          '@taobao-ohos/xlite',
          '@ohos/gpu_transform',
          '@ohos/crypto-js',
          '@ohos/axios',
          '@ohos/disklrucache',
          '@taobao-ohos/themis_inside',
          '@taobao-ohos/themis_ability_basic',
          '@taobao-ohos/themis_kernel',
          '@taobao-ohos/themis_container',
          '@taobao-ohos/themis_web',
          '@taobao-ohos/themis_uniapp',
          '@taobao-ohos/artc_engine',
          '@taobao-ohos/marvel',
          '@taobao-ohos/mega_broadcast_ability',
          '@taobao-ohos/mega_executor_ability',
          '@taobao-ohos/mega_device_ability',
          '@taobao-ohos/mega_accelerometer_ability',
          '@taobao-ohos/mega_app_ability',
          '@taobao-ohos/mega_clipboard_ability',
          '@taobao-ohos/mega_contacts_ability',
          '@taobao-ohos/mega_haptics_engine_ability',
          '@taobao-ohos/mega_kv_storage_ability',
          '@taobao-ohos/mega_log_ability',
          '@taobao-ohos/mega_mem_kv_storage_ability',
          '@taobao-ohos/mega_screen_ability',
          '@taobao-ohos/mega_security_ability',
          '@taobao-ohos/mega_system_ability',
          '@taobao-ohos/mega_tel_ability',
          '@taobao-ohos/mega_toast_ability',
          '@taobao-ohos/mega_uploader_ability',
          '@taobao-ohos/mega_user_kv_storage_ability',
          '@taobao-ohos/mega_utils',
          '@taobao-ohos/mega_file_ability',
          '@taobao-ohos/mega_ali_upload_service_ability',
          '@taobao-ohos/mega_websocket_ability',
          '@taobao-ohos/mega_audio_context_ability',
          '@taobao-ohos/mega_http_request_ability',
          '@taobao-ohos/mega_orientation_ability',
          '@taobao-ohos/mega_mtop_abilities_ohos',
          '@taobao-ohos/mega_orange_abilities_ohos',
          '@taobao-ohos/ut_mega_sdk',
          '@taobao-ohos/abtest',
          '@taobao-ohos/libcxx-shared',
          '@cro-ohos/rp_verify',
          '@protobufjs/utf8',
          '@protobufjs/eventemitter',
          '@protobufjs/path',
          '@protobufjs/float',
          '@protobufjs/pool',
          '@protobufjs/base64',
          '@protobufjs/aspromise',
          '@cainiao-ohos/bifrost-engine',
          'cpp_nexus',
          '@alipay/afservicesdk',
          '@alipay/cashier_core',
          '@alipay/cashier_platform',
          '@alipay/cashier_harmony_app_taobao',
          '@alipay/mpaas_flybird',
          '@alipay/mpaas_flybird_adapter',
          '@alipay/verifyidentity',
          '@alipay/bifrost',
          '@alipay/network',
          '@alipay/mpaas_netsdkextdepend',
          '@alipay/harmony_transport',
          '@alipay/blueshieldsdk',
          '@alipay/blueshieldres',
          '@alipay/antui',
          '@alipay/product_biometric_adapt',
          '@alipay/product_biometric',
          '@alipay/mpaas_spmtracker',
          '@fliggy-ohos/fliggylive',
          '@taobao-ohos/powermsg_ohos',
          '@taobao-ohos/mega_powermsg_abilities',
          '@taobao-ohos/taoliveroom',
          '@taobao-ohos/taobaolive_next_arch',
          '@taobao-ohos/tblive_ability',
          '@taobao-ohos/taolive-kmp',
          '@taobao-ohos/kmp_api_wrapper',
          '@taobao-ohos/tbkmp_wrapper',
          '@taobao-ohos/kmp_mega_wrapper',
          '@taobao-ohos/megability_c_interface',
          '@taobao-ohos/megability_registry',
          '@taobao-ohos/common_utils_wrapper',
          '@taobao-ohos/taobao_content_playcontrol',
          '@taobao-ohos/tlgoods',
          '@taobao-ohos/stdpop',
          '@taobao-ohos/zcache_ability',
        ],
      },
    },
    nativeLib: {
      filter: {
        select: [
          {
            package: 'libcxx-shared',
            include: [
              'libc++_shared.so',
            ],
          },
        ],
        pickLasts: [
          '**/libblueshield.so',
          '**/libAliHALogEngine.so',
          '**/libProtoDB2.so',
          '**/libaccs_ohos.so',
          '**/libaus.so',
          '**/libmtop_next.so',
          '**/libpie_dynamic.so',
          '**/libtlog_uploader_dynamic.so',
          '**/libxnet_next.so',
          '**/libutdid_native.so',
          '**/libfimsdk.so',
        ],
        excludes: [
          '**/x86_64/*.so',
          '**/armeabi-v7a/*.so',
        ],
      },
    },
  },
  buildOptionSet: [
    {
      name: 'release',
      arkOptions: {},
      nativeLib: {
        debugSymbol: {
          strip: true,
        },
      },
    },
    {
      name: 'debug',
      nativeLib: {
        debugSymbol: {
          strip: true,
        },
      },
    },
    {
      name: 'debugWithSymbol',
      nativeLib: {
        debugSymbol: {
          strip: false,
        },
      },
    },
    {
      name: 'releaseWithSymbol',
      debuggable: false,
      nativeLib: {
        debugSymbol: {
          strip: false,
        },
      },
    },
  ],
  targets: [
    {
      name: 'default',
      runtimeOS: 'HarmonyOS',
    },
    {
      name: 'ohosTest',
    },
  ],
}