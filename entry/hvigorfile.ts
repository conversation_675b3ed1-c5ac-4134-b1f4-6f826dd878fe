/*
* Copyright (c) 2023 Hunan OpenValley Digital Industry Development Co., Ltd.
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

// Script for compiling build behavior. It is built in the build plug-in and cannot be modified currently.
import { hapTasks } from '@ohos/hvigor-ohos-plugin';
import { mtl_entry_hap_plugin_list, commandline_args } from '@ali/fliggy-mtl-protocol-parse'
import { mtl_plugin } from '@ali/tcpkg'

function pluginsList() {
  // mtl构建
  console.log('entry_pluginsList')
  if (process.env.MUPP_PROJECT_ID != undefined) {
    return mtl_entry_hap_plugin_list();
  }
  // end mtl构建
  return []
}

export default {
  system: hapTasks,
  plugins: [...pluginsList(),mtl_plugin()],
}