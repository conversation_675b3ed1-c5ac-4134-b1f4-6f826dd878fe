{"string": [{"name": "module_desc", "value": "module description"}, {"name": "EntryAbility_desc", "value": "description"}, {"name": "EntryAbility_label", "value": "EntryAbility"}, {"name": "FMFlutterAbility_desc", "value": "FMFlutterAbility"}, {"name": "FMFlutterAbility_label", "value": "FMFlutterAbility"}, {"name": "PureFlutterAbility_desc", "value": "description"}, {"name": "PureFlutterAbility_label", "value": "PureFlutterAbility"}, {"name": "FishAbility_desc", "value": "description"}, {"name": "FishAbility_label", "value": "FishAbility"}, {"name": "tab_title", "value": "Tab"}, {"name": "tab_title_flutter", "value": "flutter"}, {"name": "main_page_content", "value": "Hello World"}, {"name": "ability_desc", "value": "This ability loads SplashScreenPage."}, {"name": "app_name", "value": "飞猪"}, {"name": "copyright", "value": "Copyright @2021-2027 xx Company"}, {"name": "skip", "value": "跳过广告"}, {"name": "copyright_title", "value": "Video"}, {"name": "uik_nav_home_text", "value": "首页"}, {"name": "uik_nav_destination_text", "value": "社区"}, {"name": "uik_nav_message_text", "value": "消息"}, {"name": "uik_nav_journey_text", "value": "行程"}, {"name": "uik_nav_my_text", "value": "我的"}, {"name": "permission_reason_camera", "value": "帮助您扫码、拍摄，用于登录、识别、直接拍摄上传图片"}, {"name": "permission_reason_location", "value": "向您推荐周边商品服务；帮助您发布内容时添加位置、快速填写地址"}, {"name": "permission_reason_contacts", "value": "可快速添加酒店入住人、机票乘机人等信息"}, {"name": "permission_reason_microphone", "value": "帮助您输入音视频、实现语音通话"}, {"name": "permission_reason_phone_number", "value": "为了正常识别手机设备、本机手机号，进行手机认证，保证账号安全，飞猪需要用到手机/电话号码"}, {"name": "permission_reason_storage", "value": "帮助您实现读取设备存储的图片和文件"}, {"name": "permission_reason_read_calendar", "value": "允许应用读取日历信息，便于添加、移除或更改行程信息"}, {"name": "permission_reason_write_calendar", "value": "允许应用添加、移除或更改日历活动，便于添加、移除或更改行程信息"}, {"name": "permission_reason_read_imagevideo", "value": "允许读取用户公共目录的图片或视频文件，便于操作图片或者视频"}, {"name": "permission_reason_write_imagevideo", "value": "允许修改用户公共目录的图片或视频文件，便于保存修改图片或者视频"}, {"name": "permission_reason_media_location", "value": "允许读取文件的地理位置信息，便于查看文件位置信息"}, {"name": "permission_reason_get_network_info", "value": "允许读取网络信息，用于优化网络连接，联网为您提供购物服务"}, {"name": "permission_reason_read_pasteboard", "value": "允许读取您的剪切板，为了便于信息填充"}, {"name": "request_permission_network_info", "value": "允许获取您的网络以及WIFI信息，用于优化网络连接，联网为您提供购物服务"}, {"name": "request_permission_bio", "value": "允许获取您的指纹/面容信息，以便为您提供便捷的支付能力"}]}