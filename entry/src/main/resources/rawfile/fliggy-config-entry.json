{"name": "entry", "nav": [{"label": "订单列表", "alias": "act_webview", "metaData": {"url": "https://market.m.taobao.com/app/trip/rx-order-list-new/pages/index"}, "intentFilter": [{"scheme": "page", "host": "order_list"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/apps/market/fliggyraxorderlist/index.html"}]}, {"label": "", "alias": "flutter_view", "metaData": {"flutter_path": "/fliggy_search/search_suggest"}, "intentFilter": [{"scheme": "https", "host": "h5.wapa.taobao.com", "path": "/trip/rx-search/native-home/index.html"}, {"scheme": "https", "host": "h5.m.taobao.com", "path": "/trip/rx-search/native-home/index.html"}]}, {"label": "交通首页-机票", "alias": "flutter_view", "intentFilter": [{"scheme": "page", "host": "flight_home"}, {"scheme": "https", "host": "h5.m.taobao.com", "path": "/trip/traffic-search/search/index.html"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/h5-traffic-search/pages/search/index.html"}], "metaData": {"flutter_path": "/ftraffic_home_page", "traffic_biz": "flight"}, "launchMode": "standard"}, {"label": "火车票首页", "alias": "flutter_view", "intentFilter": [{"scheme": "page", "host": "train_home"}, {"scheme": "https", "host": "h5.m.taobao.com", "path": "/trip/train-main/search/index.html"}, {"scheme": "https", "host": "h5.m.taobao.com", "path": "/trip/train/search/index.html"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/h5-train-main/pages/search/index.html"}], "metaData": {"flutter_path": "/ftraffic_home_page", "traffic_biz": "train"}, "launchMode": "standard"}, {"label": "汽车票首页", "alias": "flutter_view", "launchMode": "standard", "intentFilter": [{"scheme": "page", "host": "bus_main"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/h5-traffic-search/pages/bus/index.html"}], "metaData": {"flutter_path": "/ftraffic_home_page", "traffic_biz": "bus"}}, {"label": "租车首页", "alias": "flutter_view", "launchMode": "standard", "intentFilter": [{"scheme": "page", "host": "car_main"}, {"scheme": "https", "host": "h5.m.taobao.com", "path": "/trip/car-react/search/index.html"}, {"scheme": "https", "host": "h5.m.taobao.com", "path": "/trip/car/search/index.html"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/h5-car-react/pages/search/index.html"}, {"scheme": "https", "host": "h5.m.taobao.com", "path": "/trip/vehicle-new/home/<USER>"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/h5-vehicle-new/pages/home/<USER>"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-vehicle/index"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-vehicle-search/pages/home"}], "metaData": {"flutter_path": "/ftraffic_home_page", "traffic_biz": "car"}}, {"label": "交通首页", "alias": "flutter_view", "launchMode": "standard", "intentFilter": [{"scheme": "https", "host": "outfliggys.m.taobao.com", "path": "/app/trip/rx-traffic-search/pages/index"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-traffic-search/pages/index"}], "metaData": {"flutter_path": "/ftraffic_home_page"}}]}