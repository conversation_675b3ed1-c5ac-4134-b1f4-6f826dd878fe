{"name": "entry", "nav": [{"label": "国内机票单程列表", "alias": "flutter_view", "metaData": {"flutter_path": "/fflight/flight_single_list"}, "intentFilter": [{"scheme": "page", "host": "flight_new_list"}, {"scheme": "page", "host": "flight_list"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-flight-eco/pages/listing"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-flight-eco/pages/listing"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/h5-flight-search/pages/searchlist/index.html"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/h5-flight-search/pages/searchlist/index.html"}], "launchMode": "standard"}, {"label": "国内机票往返列表", "alias": "flutter_view", "metaData": {"flutter_path": "/fflight/flight_round_list"}, "intentFilter": [{"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-flight-eco/pages/roundtrip"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-flight-eco/pages/roundtrip"}], "launchMode": "standard"}, {"label": "国内机票ota页", "alias": "flutter_view", "metaData": {"flutter_path": "/fflight/flight_ota"}, "intentFilter": [{"scheme": "page", "host": "flight_ota_list"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-traffic/pages/ota"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-traffic/pages/ota"}], "launchMode": "standard"}, {"label": "国际机票ota页", "alias": "flutter_view", "metaData": {"flutter_path": "/fiflight/iflight_ota"}, "intentFilter": [{"scheme": "page", "host": "iflight_ota_list"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-iflight-eco/pages/ota"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-iflight-eco/pages/ota"}], "launchMode": "standard"}, {"label": "国际机票单程列表", "alias": "flutter_view", "metaData": {"flutter_path": "/fflight/iflight_list"}, "intentFilter": [{"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/wx-iflight/pages/searchlist"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/wx-iflight/pages/searchlist"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-iflight-eco/pages/listing"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-iflight-eco/pages/listing"}], "launchMode": "standard"}, {"label": "国际机票往返列表", "alias": "flutter_view", "metaData": {"flutter_path": "/fflight/iflight_round_list"}, "intentFilter": [{"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-iflight-eco/pages/iflight-roundtrip"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-iflight-eco/pages/iflight-roundtrip"}], "launchMode": "standard"}]}