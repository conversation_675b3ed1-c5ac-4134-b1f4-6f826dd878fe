{"name": "entry", "nav": [{"label": "酒店首页", "alias": "flutter_view", "intentFilter": [{"scheme": "page", "host": "hotel_home"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/h5-hotel/pages/search/index.html"}, {"scheme": "https", "host": "h5.m.taobao.com", "path": "/trip/hotel/search/index.html"}], "metaData": {"flutter_path": "/hotel_home_flutter"}, "launchMode": "standard"}, {"label": "酒店详情", "alias": "flutter_view", "metaData": {"flutter_path": "/hotel_detail_flutter", "_fli_background_transparent": "true"}, "intentFilter": [{"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/h5-hotel-detail/pages/detail/index.html"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/h5-hotel-detail/pages/detail/index.html"}, {"scheme": "https", "host": "h5.m.taobao.com", "path": "/trip/hotel/detail/detail.html"}, {"scheme": "https", "host": "h5.wapa.taobao.com", "path": "/trip/hotel/detail/detail.html"}, {"scheme": "https", "host": "h5.m.taobao.com", "path": "/trip/hotel/detail2/index.html"}, {"scheme": "https", "host": "h5.m.taobao.com", "path": "/trip/hotel/detail3/index.html"}, {"scheme": "https", "host": "outfliggys.m.taobao.com", "path": "/app/trip/rx-hotel-detail/pages/detail"}, {"scheme": "page", "host": "hotel_detail"}], "launchMode": "standard"}, {"label": "酒店列表", "alias": "flutter_view", "metaData": {"flutter_path": "/hotel_list_flutter"}, "intentFilter": [{"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/h5-hotel/pages/searchlist/index.html"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/h5-hotel/pages/searchlist/index.html"}, {"scheme": "https", "host": "outfliggys.m.taobao.com", "path": "/app/trip/rx-hotel-listing/pages/home"}, {"scheme": "page", "host": "hotel_list"}], "launchMode": "standard"}, {"label": "酒店sug页", "alias": "flutter_view", "metaData": {"flutter_path": "/hotel_sug_flutter"}, "intentFilter": [{"scheme": "page", "host": "hotel_search_keyword_page_new"}, {"scheme": "page", "host": "hotel_new_keywords"}], "launchMode": "standard"}, {"label": "酒店订单详情页", "alias": "flutter_view", "metaData": {"flutter_path": "/hotel_order_detail_page"}, "intentFilter": [{"scheme": "page", "host": "hotel_order_detail"}], "launchMode": "standard"}]}