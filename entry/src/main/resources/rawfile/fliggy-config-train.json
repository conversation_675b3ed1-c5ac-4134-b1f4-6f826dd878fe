{"name": "entry", "nav": [{"label": "列表", "alias": "flutter_view", "metaData": {"flutter_path": "/ftrain_page_list"}, "intentFilter": [{"scheme": "page", "host": "train_list"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/h5-train-main/pages/list/index.html"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/h5-train-main/pages/list/index.html"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-train-eco/pages/listing"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-train-eco/pages/listing"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-train-main/pages/listing"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-train-main/pages/listing"}, {"scheme": "https", "host": "outfliggys.m.taobao.com", "path": "/app/trip/rx-train-main/pages/listing"}, {"scheme": "https", "host": "outfliggys.wapa.taobao.com", "path": "/app/trip/rx-train-main/pages/listing"}], "launchMode": "standard"}, {"label": "直达ota", "alias": "flutter_view", "metaData": {"flutter_path": "/ftrain_page_ota"}, "intentFilter": [{"scheme": "page", "host": "train_no_detail"}, {"scheme": "https", "host": "h5.m.taobao.com", "path": "/trip/train/train-detail/index.html"}, {"scheme": "https", "host": "h5.wapa.taobao.com", "path": "/trip/train/train-detail/index.html"}, {"scheme": "https", "host": "h5.m.taobao.com", "path": "/trip/train-main/detail/index.html"}, {"scheme": "https", "host": "h5.wapa.taobao.com", "path": "/trip/train-main/detail/index.html"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/h5-train-main/pages/detail/index.html"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/h5-train-main/pages/detail/index.html"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-train-main/pages/detail"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-train-main/pages/detail"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-train-eco/pages/detail"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-train-eco/pages/detail"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-train-main/pages/train-detail"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-train-main/pages/train-detail"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-train-main/pages/detail"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-train-main/pages/detail"}], "launchMode": "standard"}, {"label": "中转ota", "alias": "flutter_view", "metaData": {"flutter_path": "/ftrain_page_transfer_ota"}, "intentFilter": [{"scheme": "page", "host": "train_recommend_detail"}, {"scheme": "https", "host": "h5.m.taobao.com", "path": "/trip/train-main/transfer-detail/index.html"}, {"scheme": "https", "host": "h5.wapa.taobao.com", "path": "/trip/train-main/transfer-detail/index.html"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/h5-train-main/pages/transfer-detail/index.html"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/h5-train-main/pages/transfer-detail/index.html"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-train-main/pages/transfer-splicing-detail"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-train-main/pages/transfer-splicing-detail"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-train-main/pages/transfer-detail"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-train-main/pages/transfer-detail"}], "launchMode": "standard"}, {"label": "抢票列表", "alias": "flutter_view", "metaData": {"flutter_path": "/ftrain_page_grab_list"}, "intentFilter": [{"scheme": "page", "host": "train_grab_order_list"}, {"scheme": "https", "host": "h5.m.taobao.com", "path": "/trip/train-grab/history/index.html"}, {"scheme": "https", "host": "h5.wapa.taobao.com", "path": "/trip/train-grab/history/index.html"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/h5-train-grab/pages/history/index.html"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/h5-train-grab/pages/history/index.html"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-train-grab/pages/grab-list"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-train-grab/pages/grab-list"}], "launchMode": "standard"}, {"label": "12306登录", "alias": "flutter_view", "metaData": {"flutter_path": "/ftrain/12306_login"}, "intentFilter": [{"scheme": "page", "host": "train_12306_login"}, {"scheme": "page", "host": "train_12306_login_new"}, {"scheme": "https", "host": "h5.m.taobao.com", "path": "/trip/train-12306/normal-login/index.html"}, {"scheme": "https", "host": "h5.wapa.taobao.com", "path": "/trip/train-12306/normal-login/index.html"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/h5-train-12306/pages/normal-login/index.html"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/h5-train-12306/pages/normal-login/index.html"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-train-account/pages/normal-login"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-train-account/pages/normal-login"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-train-grab/pages/grab-waiting"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-train-grab/pages/grab-waiting"}], "launchMode": "standard"}, {"label": "12306账号管理", "alias": "flutter_view", "metaData": {"flutter_path": "/ftrain/12306_user_manage_page"}, "intentFilter": [{"scheme": "page", "host": "train_12306_management"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/h5-train-12306/pages/12306-manager/index.html"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/h5-train-12306/pages/12306-manager/index.html"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-train-12306/pages/12306-manager"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-train-12306/pages/12306-manager"}], "launchMode": "standard"}, {"label": "订单详情", "alias": "flutter_view", "metaData": {"flutter_path": "/ftrain_page_order_detail"}, "intentFilter": [{"scheme": "page", "host": "train_order_detail"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/h5-train-react/pages/order-detail/index.html"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/h5-train-react/pages/order-detail/index.html"}, {"scheme": "https", "host": "outfliggys.m.taobao.com", "path": "/app/trip/h5-train-react/pages/order-detail/index.html"}, {"scheme": "https", "host": "pre-fliggyrax.wapa.taobao.com", "path": "/app/trip/h5-train-react/pages/order-detail/index.html"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-train-eco/pages/order-detail"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-train-eco/pages/order-detail"}, {"scheme": "https", "host": "outfliggys.m.taobao.com", "path": "/app/trip/rx-train-eco/pages/order-detail"}, {"scheme": "https", "host": "pre-fliggyrax.wapa.taobao.com", "path": "/app/trip/rx-train-eco/pages/order-detail"}, {"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-train-main/pages/order-detail"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-train-main/pages/order-detail"}, {"scheme": "https", "host": "outfliggys.m.taobao.com", "path": "/app/trip/rx-train-main/pages/order-detail"}, {"scheme": "https", "host": "pre-fliggyrax.wapa.taobao.com", "path": "/app/trip/rx-train-main/pages/order-detail"}], "launchMode": "standard"}, {"label": "抢票失败后推荐", "alias": "flutter_view", "metaData": {"flutter_path": "/ftrain_page_grab_recommend"}, "intentFilter": [{"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-train-grab/pages/grab-fail-list"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-train-grab/pages/grab-fail-list"}], "launchMode": "standard"}, {"label": "有票神器首页", "alias": "flutter_view", "metaData": {"flutter_path": "/ftrain/train_ticket_artifact"}, "intentFilter": [{"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-train-round/pages/recommend-home"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-train-round/pages/recommend-home"}], "launchMode": "standard"}, {"label": "有票神器列表", "alias": "flutter_view", "metaData": {"flutter_path": "/ftrain/train_ticket_artifact_result"}, "intentFilter": [{"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-train-round/pages/recommend-list"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-train-round/pages/recommend-list"}], "launchMode": "standard"}, {"label": "在线换座首页", "alias": "flutter_view", "metaData": {"flutter_path": "/ftrainbuy/change_seat_home_page"}, "intentFilter": [{"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-train-seat-change/pages/home"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-train-seat-change/pages/home"}], "launchMode": "standard"}, {"label": "在线换座下单", "alias": "flutter_view", "metaData": {"flutter_path": "/ftrainbuy/change_seat_buy"}, "intentFilter": [{"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-train-seat-change/pages/buy"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-train-seat-change/pages/buy"}], "launchMode": "standard"}, {"label": "在线换座详情", "alias": "flutter_view", "metaData": {"flutter_path": "/ftrainbuy/change_seat_order_detail"}, "intentFilter": [{"scheme": "https", "host": "market.m.taobao.com", "path": "/app/trip/rx-train-seat-change/pages/detail"}, {"scheme": "https", "host": "market.wapa.taobao.com", "path": "/app/trip/rx-train-seat-change/pages/detail"}], "launchMode": "standard"}, {"label": "修改抢票单", "alias": "flutter_view", "metaData": {"flutter_path": "/ftrainbuy/ftrain_grab_order_edit"}, "intentFilter": [{"scheme": "page", "host": "train_grab_edit_order"}], "launchMode": "standard"}]}