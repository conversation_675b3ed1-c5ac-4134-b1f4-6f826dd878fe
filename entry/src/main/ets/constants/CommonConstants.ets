/**
 * Common Constants for all features.
 */
export class CommonConstants {
  /**
   * Time default value.
   */
  static readonly TIME_DEFAULT_VALUE: number = 0;

  /**
   * Delay seconds.
   */
  static readonly DELAY_SECONDS: number = 3;

  /**
   * Incremental value.
   */
  static readonly INCREMENT_VALUE: number = 1;

  /**
   * Interval delay.
   */
  static readonly INTERVAL_DELAY: number = 1000;

  /**
   * Interval id default value.
   */
  static readonly INTERVAL_ID_DEFAULT: number = 0;

  /**
   * Splash page stack layout weight.
   */
  static readonly STACK_LAYOUT_WEIGHT: number = 1;

  /**
   * Border width of skip text.
   */
  static readonly SKIP_TEXT_BORDER_WIDTH: number = 1;

  /**
   * Skip button radius.
   */
  static readonly SKIP_BUTTON_RADIUS: number = 14;

  /**
   * Main page url.
   */
  static readonly MAIN_PAGE_URL: string = 'pages/MainPage';

  /**
   * Copyright area column width.
   */
  static readonly COPYRIGHT_AREA_COLUMN_WIDTH: string = '100%';

  /**
   * Main page row width.
   */
  static readonly MAIN_PAGE_ROW_WIDTH: string = '100%';

  /**
   * Main page column width.
   */
  static readonly MAIN_PAGE_COLUMN_WIDTH: string = '100%';

  /**
   * Stack width of splash screen page.
   */
  static readonly STACK_WIDTH: string = '100%';

  /**
   * Column width of splash screen page.
   */
  static readonly COLUMN_WIDTH: string = '100%';

  /**
   * Column height of splash screen page.
   */
  static readonly COLUMN_HEIGHT: string = '100%';

  /**
   * Image width of splash screen page.
   */
  static readonly IMAGE_WIDTH: string = '100%';

  /**
   * Image height of splash screen page.
   */
  static readonly IMAGE_HEIGHT: string = '100%';

  /**
   * The percentage of 100.
   */
  static readonly PERCENTAGE_100 = '100%';

}

/**
 * The font weight of application.
 */
export enum AppFontWeight {
  BOLD = '400',
  BOLDER = '500',
};

/**
 * The font size of application.
 */
export enum AppFontSize {
  SMALLER = 13,
  SMALL = 14,
  MIDDLE = 16,
  LARGE = 20,
  LARGER = 35,
};
