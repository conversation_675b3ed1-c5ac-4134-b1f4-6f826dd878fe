import { Logger } from '@fliggy-ohos/logger';
import { buildSplashDataModel, saveSplashShowCount, splashDataModel } from '@fliggy-ohos/homepage';
import { PrivacyRequestHelper } from '../privacy/PrivacyRequestHelper';
import { CustomPrivacyDialog } from '../privacy/CustomPrivacyDialog';
import { launchTaskManager } from '@fliggy-ohos/launcherimpl';
import bundleManager from '@ohos.bundle.bundleManager';
import process from '@ohos.process';
import { Anim, FliggyNavigator, navigator, PageContext } from '@fliggy-ohos/router';
import { SplitPolicy } from '@ohos/multinavigation';
import { WantParserUtils, RouterPageWork } from '@fliggy-ohos/launcherimpl';
import { tracker } from '@fliggy-ohos/tracker';
import { env } from '@fliggy-ohos/env';
import { isEmpty } from '@fliggy-ohos/commonutils';
// import { InitMessageModule } from '@taobao-ohos/tbmessage_ohos';
import { StartupArgs } from '@taobao-ohos/taobao_runtime';

const TAG = 'LaunchPage';
const logger: Logger = new Logger("launch")

// 预装包数据回收埋点
export function trackPreinstallData(source: string) {
  let bundleInfo = bundleManager.getBundleInfoForSelfSync(bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_APPLICATION);
  let installSource = bundleInfo.appInfo.installSource;
  let sysPropChannelId = env.getHuaweiOTAChannelId();
  if (isEmpty(sysPropChannelId)) {
    sysPropChannelId = "none"
  }

  let map: Map<string, string> = new Map<string, string>();
  map.set("utdid", env.getUtdid());
  map.set("source", source); // 唤端或者冷启动
  map.set("ttid", env.getTtid());
  map.set("channelId", env.getOriginalChanelId()); // 原始渠道号上报
  map.set("sysPropChannelId", sysPropChannelId); // 系统属性的渠道号上报
  map.set("installSource", installSource); // 应用程序的安装来源
  tracker.trackCommitEvent('preinstall', '', map);
}

/*
 * 启动页面，承载启动图的展示
 * */
@Entry
@Component
export struct LaunchPage {
  pageContext?: PageContext;
  isLauncherStart: boolean = false

  aboutToAppear(): void {
    logger.d(TAG, 'onPageAppear called!')
    this.pageContext?.bind(this);
  }

  openFirstUserPage(){
    if(WantParserUtils.msgObj) {
      // 唤端都先打开首页（否则会有动画问题）
      navigator.openPage(this.pageContext, {
        pageUrl: "page://home_main",
        splitPolicy:SplitPolicy.HOME_PAGE,
      });

      // 50ms后启动落地页
      setTimeout(() => {
        const params = WantParserUtils.msgObj
        if(params) {
          const routePageWork = new RouterPageWork(params, 'EntryAbility')
          routePageWork.routeByUrl()
          trackPreinstallData("cold_want");
          WantParserUtils.msgObj = undefined
        }
      }, 50);

    } else {
      trackPreinstallData("cold_start");
      this.obtainResource()
    }
  }

  async obtainResource() {
    // 获取数据源
    let hasAvailableResource = false;
    try{
      await buildSplashDataModel();
      logger.d(TAG, 'obtain called! splashDataModel: ' + splashDataModel);

      let resourceCacheUrl: string = splashDataModel.resourceCacheUrl;
      hasAvailableResource = !!resourceCacheUrl && resourceCacheUrl.length > 0;
      if (hasAvailableResource) {
        await saveSplashShowCount(splashDataModel.mFirstOfflineResourceModel?.strategyId);
      }
      logger.d(TAG, 'obtain resource end, resourceType: ' + splashDataModel.resourceType + ', resourceCacheUrl: ' + resourceCacheUrl);
    } catch (e) {
      logger.e(TAG, e);
    }

    // check 本地类型的资源是否存在，视频&图片类型， splashDataModel
    if (hasAvailableResource) {
      logger.i(TAG, 'obtain resource 存在闪屏资源，进入闪屏页播放!')
      navigator.openPage(this.pageContext, {
        pageUrl: "page://splash",
        anim: Anim.none,
        params: {
          '_fli_popBeforeOpen': true
        },
      });
    } else {
      logger.i(TAG, 'obtain resource 无可用闪屏资源，直接进入首页!')
      navigator.openPage(this.pageContext, {
        pageUrl: "page://home_main",
        splitPolicy:SplitPolicy.HOME_PAGE,
      });
    }
  }

  pageTransition() {
    PageTransitionEnter({ type: RouteType.None, duration: 0 })
    PageTransitionExit({ type: RouteType.None, duration: 0 })
  }

  onPageShow(): void {
    logger.d(TAG, 'onPageShow called!')
    //防止前后台切换多次调用
    if(this.isLauncherStart) {
      return;
    }
    this.isLauncherStart = true;
    // 隐私弹窗
    if(PrivacyRequestHelper.isNeedPrivacyRequest()) {
      CustomPrivacyDialog.show({
        onUserApproved: () => {
          logger.d(TAG, 'onPageShow 隐私授权成功');
          this.startLaunchTask();
        }
      })
    } else {
      this.startLaunchTask();
    }
  }

  // 启动任务
  startLaunchTask() {
    this.loadModulesPage();

    // 启动任务
    try {
      launchTaskManager.start({
        onBlockTaskDone: () => {
          logger.d(TAG, 'startLaunchTask:onBlockTaskDone');
          WantParserUtils.blockTaskDone = true
          this.openFirstUserPage();
        }
      })
    } catch (e) {
      logger.e(TAG, e);
    }

    // 初始化 tbmessage 模块
  //   setTimeout(() => { // 延迟初始化，确保页面上下文完全准备好
  //     try {
  //       let startupArgs = new StartupArgs();
  //       // 添加必要的参数检查和初始化
  //       if (!startupArgs) {
  //         logger.e(TAG, 'StartupArgs initialization failed');
  //         return;
  //       }
  //
  //       // 设置context，避免native层空指针
  //       startupArgs.context = getContext(this);
  //
  //       logger.d(TAG, 'About to call InitMessageModule with context');
  //       InitMessageModule(startupArgs);
  //       logger.d(TAG, 'InitMessageModule called successfully');
  //     } catch (e) {
  //       logger.e(TAG, 'InitMessageModule failed');
  //       // 不让消息模块初始化失败影响整个启动流程
  //     }
  //   }, 4000);
  }

  loadModulesPage() {
    let st = Date.now();
    let bundleInfo = bundleManager.getBundleInfoForSelfSync(bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_HAP_MODULE);
    bundleInfo.hapModulesInfo.forEach((item) => {
      FliggyNavigator.loadPageConfig(getContext(this), item.name);
    });
    logger.d("loadModulesPage", `useTime: ${Date.now() - st}`);
  }


  existApp(){
    new process.ProcessManager().exit(0);
  }

  // 页面构建
  build() {
    Stack({ alignContent: Alignment.Center }) {
      Image($r('app.media.launcher_center')).objectFit(ImageFit.None)
    }.width('100%').height('100%').backgroundColor('#FFBF23')
  }

}