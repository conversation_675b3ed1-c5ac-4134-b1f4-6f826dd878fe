// // Copyright (c) 2025 Fliggy Team. All rights reserved.
// // Created by guyan on 2025-08-06.
// // FIM NAPI Bridge Complete Test Page - 32个方法独立测试
//
// import {
//   // 消息相关方法 (15个)
//   fim_get_conversations,
//   // fim_send_message,
//   fim_get_message,
//   fim_get_local_message,
//   fim_delete_message,
//   fim_delete_local_message,
//   fim_recall_message,
//   fim_resend_message,
//   fim_reply_message,
//   fim_list_previous_local_msgs,
//   fim_list_previous_msgs,
//   fim_list_next_local_msgs,
//   fim_list_next_msgs,
//   fim_list_messages_read_status,
//   fim_update_message_to_read,
//
//   // 会话相关方法 (8个)
//   // fim_create_conversation,
//   // fim_delete_conversation,
//   // fim_update_conversation,
//   // fim_get_conversation_detail,
//   // fim_get_conversation_members,
//   // fim_add_conversation_members,
//   // fim_remove_conversation_members,
//   // fim_set_conversation_admin,
//   //
//   // // 搜索相关方法 (4个)
//   // fim_search_messages,
//   // fim_search_conversations,
//   // fim_search_contacts,
//   // fim_global_search,
//
//   // 用户和联系人相关方法 (6个)
//   // fim_get_user_info,
//   // fim_update_user_info,
//   // fim_get_contacts,
//   // fim_add_contact,
//   // fim_remove_contact,
//   // fim_update_contact
// } from 'cpp_nexus';
//
// // 测试方法接口定义
// interface FIMTestMethod {
//   id: string;
//   name: string;
//   displayName: string;
//   category: string;
//   func: (params: string) => string;
//   testParams: string;
// }
//
// // 测试结果接口定义
// interface TestResult {
//   id: string;
//   methodName: string;
//   params: string;
//   result: string;
//   success: boolean;
//   timestamp: string;
//   duration: number;
//   expanded?: boolean; // 添加展开状态
// }
//
// @Entry
// @Component
// struct FIMMVPTestPageEntry {
//   build() {
//     Column() {
//       FIMMVPTestPage()
//     }
//     .width('100%')
//     .height('100%')
//   }
// }
//
// @Component
// export struct FIMMVPTestPage {
//   @State testResults: TestResult[] = [];
//   @State isRunning: boolean = false;
//   @State selectedCategory: string = '全部';
//   @State searchText: string = '';
//   @State currentTestingMethod: string = '';
//
//   // 32个FIM方法的完整配置
//   private fimMethods: FIMTestMethod[] = [
//     // 消息相关方法 (15个)
//     {
//       id: 'fim_get_conversations',
//       name: 'fim_get_conversations',
//       displayName: '获取会话列表',
//       category: '消息相关',
//       func: fim_get_conversations,
//       testParams: JSON.stringify({
//         limit: 20,
//         offset: 0
//       })
//     },
//     // {
//     //   id: 'fim_send_message',
//     //   name: 'fim_send_message',
//     //   displayName: '发送消息',
//     //   category: '消息相关',
//     //   func: fim_send_message,
//     //   testParams: JSON.stringify({ messageId: "msg_001", content: "测试消息" })
//     // },
//     {
//       id: 'fim_get_message',
//       name: 'fim_get_message',
//       displayName: '获取消息',
//       category: '消息相关',
//       func: fim_get_message,
//       testParams: JSON.stringify({ messageId: "msg_001" })
//     },
//     {
//       id: 'fim_get_local_message',
//       name: 'fim_get_local_message',
//       displayName: '获取本地消息',
//       category: '消息相关',
//       func: fim_get_local_message,
//       testParams: JSON.stringify({ localId: "local_msg_001" })
//     },
//     {
//       id: 'fim_delete_message',
//       name: 'fim_delete_message',
//       displayName: '删除消息',
//       category: '消息相关',
//       func: fim_delete_message,
//       testParams: JSON.stringify({ messageId: "msg_001" })
//     },
//     {
//       id: 'fim_delete_local_message',
//       name: 'fim_delete_local_message',
//       displayName: '删除本地消息',
//       category: '消息相关',
//       func: fim_delete_local_message,
//       testParams: JSON.stringify({ localId: "local_msg_001" })
//     },
//     {
//       id: 'fim_recall_message',
//       name: 'fim_recall_message',
//       displayName: '撤回消息',
//       category: '消息相关',
//       func: fim_recall_message,
//       testParams: JSON.stringify({ messageId: "msg_001" })
//     },
//     {
//       id: 'fim_resend_message',
//       name: 'fim_resend_message',
//       displayName: '重发消息',
//       category: '消息相关',
//       func: fim_resend_message,
//       testParams: JSON.stringify({ messageId: "msg_001" })
//     },
//     {
//       id: 'fim_reply_message',
//       name: 'fim_reply_message',
//       displayName: '回复消息',
//       category: '消息相关',
//       func: fim_reply_message,
//       testParams: JSON.stringify({ messageId: "msg_001", replyContent: "回复内容" })
//     },
//     {
//       id: 'fim_list_previous_local_msgs',
//       name: 'fim_list_previous_local_msgs',
//       displayName: '列出之前本地消息',
//       category: '消息相关',
//       func: fim_list_previous_local_msgs,
//       testParams: JSON.stringify({ conversationId: "conv_001", limit: 10 })
//     },
//     {
//       id: 'fim_list_previous_msgs',
//       name: 'fim_list_previous_msgs',
//       displayName: '列出之前消息',
//       category: '消息相关',
//       func: fim_list_previous_msgs,
//       testParams: JSON.stringify({ conversationId: "conv_001", limit: 10 })
//     },
//     {
//       id: 'fim_list_next_local_msgs',
//       name: 'fim_list_next_local_msgs',
//       displayName: '列出之后本地消息',
//       category: '消息相关',
//       func: fim_list_next_local_msgs,
//       testParams: JSON.stringify({ conversationId: "conv_001", limit: 10 })
//     },
//     {
//       id: 'fim_list_next_msgs',
//       name: 'fim_list_next_msgs',
//       displayName: '列出之后消息',
//       category: '消息相关',
//       func: fim_list_next_msgs,
//       testParams: JSON.stringify({ conversationId: "conv_001", limit: 10 })
//     },
//     {
//       id: 'fim_list_messages_read_status',
//       name: 'fim_list_messages_read_status',
//       displayName: '列出消息读取状态',
//       category: '消息相关',
//       func: fim_list_messages_read_status,
//       testParams: JSON.stringify({ conversationId: "conv_001", messageIds: ["msg_001", "msg_002"] })
//     },
//     {
//       id: 'fim_update_message_to_read',
//       name: 'fim_update_message_to_read',
//       displayName: '更新消息为已读',
//       category: '消息相关',
//       func: fim_update_message_to_read,
//       testParams: JSON.stringify({ conversationId: "conv_001", messageIds: ["msg_001"] })
//     },
//
//     // // 会话相关方法 (8个)
//     // {
//     //   id: 'fim_create_conversation',
//     //   name: 'fim_create_conversation',
//     //   displayName: '创建会话',
//     //   category: '会话相关',
//     //   func: fim_create_conversation,
//     //   testParams: JSON.stringify({ title: "测试会话", members: ["user_001", "user_002"] })
//     // },
//     // {
//     //   id: 'fim_delete_conversation',
//     //   name: 'fim_delete_conversation',
//     //   displayName: '删除会话',
//     //   category: '会话相关',
//     //   func: fim_delete_conversation,
//     //   testParams: JSON.stringify({ conversationId: "conv_001" })
//     // },
//     // {
//     //   id: 'fim_update_conversation',
//     //   name: 'fim_update_conversation',
//     //   displayName: '更新会话',
//     //   category: '会话相关',
//     //   func: fim_update_conversation,
//     //   testParams: JSON.stringify({ conversationId: "conv_001", title: "更新后的会话标题" })
//     // },
//     // {
//     //   id: 'fim_get_conversation_detail',
//     //   name: 'fim_get_conversation_detail',
//     //   displayName: '获取会话详情',
//     //   category: '会话相关',
//     //   func: fim_get_conversation_detail,
//     //   testParams: JSON.stringify({ conversationId: "conv_001" })
//     // },
//     // {
//     //   id: 'fim_get_conversation_members',
//     //   name: 'fim_get_conversation_members',
//     //   displayName: '获取会话成员',
//     //   category: '会话相关',
//     //   func: fim_get_conversation_members,
//     //   testParams: JSON.stringify({ conversationId: "conv_001" })
//     // },
//     // {
//     //   id: 'fim_add_conversation_members',
//     //   name: 'fim_add_conversation_members',
//     //   displayName: '添加会话成员',
//     //   category: '会话相关',
//     //   func: fim_add_conversation_members,
//     //   testParams: JSON.stringify({ conversationId: "conv_001", members: ["user_003"] })
//     // },
//     // {
//     //   id: 'fim_remove_conversation_members',
//     //   name: 'fim_remove_conversation_members',
//     //   displayName: '移除会话成员',
//     //   category: '会话相关',
//     //   func: fim_remove_conversation_members,
//     //   testParams: JSON.stringify({ conversationId: "conv_001", members: ["user_003"] })
//     // },
//     // {
//     //   id: 'fim_set_conversation_admin',
//     //   name: 'fim_set_conversation_admin',
//     //   displayName: '设置会话管理员',
//     //   category: '会话相关',
//     //   func: fim_set_conversation_admin,
//     //   testParams: JSON.stringify({ conversationId: "conv_001", adminId: "user_001" })
//     // },
//     //
//     // // 搜索相关方法 (4个)
//     // {
//     //   id: 'fim_search_messages',
//     //   name: 'fim_search_messages',
//     //   displayName: '搜索消息',
//     //   category: '搜索相关',
//     //   func: fim_search_messages,
//     //   testParams: JSON.stringify({ keyword: "测试", conversationId: "conv_001" })
//     // },
//     // {
//     //   id: 'fim_search_conversations',
//     //   name: 'fim_search_conversations',
//     //   displayName: '搜索会话',
//     //   category: '搜索相关',
//     //   func: fim_search_conversations,
//     //   testParams: JSON.stringify({ keyword: "测试会话" })
//     // },
//     // {
//     //   id: 'fim_search_contacts',
//     //   name: 'fim_search_contacts',
//     //   displayName: '搜索联系人',
//     //   category: '搜索相关',
//     //   func: fim_search_contacts,
//     //   testParams: JSON.stringify({ keyword: "张三" })
//     // },
//     // {
//     //   id: 'fim_global_search',
//     //   name: 'fim_global_search',
//     //   displayName: '全局搜索',
//     //   category: '搜索相关',
//     //   func: fim_global_search,
//     //   testParams: JSON.stringify({ keyword: "测试", scope: "all" })
//     // },
//     //
//     // // 用户和联系人相关方法 (6个)
//     // {
//     //   id: 'fim_get_user_info',
//     //   name: 'fim_get_user_info',
//     //   displayName: '获取用户信息',
//     //   category: '用户联系人',
//     //   func: fim_get_user_info,
//     //   testParams: JSON.stringify({ userId: "user_001" })
//     // },
//     // {
//     //   id: 'fim_update_user_info',
//     //   name: 'fim_update_user_info',
//     //   displayName: '更新用户信息',
//     //   category: '用户联系人',
//     //   func: fim_update_user_info,
//     //   testParams: JSON.stringify({ userId: "user_001", name: "新用户名", avatar: "avatar_url" })
//     // },
//     // {
//     //   id: 'fim_get_contacts',
//     //   name: 'fim_get_contacts',
//     //   displayName: '获取联系人列表',
//     //   category: '用户联系人',
//     //   func: fim_get_contacts,
//     //   testParams: JSON.stringify({ limit: 50, offset: 0 })
//     // },
//     // {
//     //   id: 'fim_add_contact',
//     //   name: 'fim_add_contact',
//     //   displayName: '添加联系人',
//     //   category: '用户联系人',
//     //   func: fim_add_contact,
//     //   testParams: JSON.stringify({ userId: "user_002", remark: "新朋友" })
//     // },
//     // {
//     //   id: 'fim_remove_contact',
//     //   name: 'fim_remove_contact',
//     //   displayName: '删除联系人',
//     //   category: '用户联系人',
//     //   func: fim_remove_contact,
//     //   testParams: JSON.stringify({ userId: "user_002" })
//     // },
//     // {
//     //   id: 'fim_update_contact',
//     //   name: 'fim_update_contact',
//     //   displayName: '更新联系人信息',
//     //   category: '用户联系人',
//     //   func: fim_update_contact,
//     //   testParams: JSON.stringify({ userId: "user_002", remark: "好朋友" })
//     // }
//   ];
//
//   // 分类列表
//   private categories: string[] = ['全部', '消息相关', '会话相关', '搜索相关', '用户联系人'];
//
//   // 获取过滤后的方法列表
//   private getFilteredMethods(): FIMTestMethod[] {
//     let filtered = this.fimMethods;
//
//     // 按分类过滤
//     if (this.selectedCategory !== '全部') {
//       filtered = filtered.filter(method => method.category === this.selectedCategory);
//     }
//
//     // 按搜索文本过滤
//     if (this.searchText.trim() !== '') {
//       const searchLower = this.searchText.toLowerCase();
//       filtered = filtered.filter(method =>
//         method.displayName.toLowerCase().includes(searchLower) ||
//         method.name.toLowerCase().includes(searchLower)
//       );
//     }
//
//     return filtered;
//   }
//
//   build() {
//     Row() {
//       // 左侧：方法列表区域
//       Column() {
//         // 标题和控制区域
//         Column() {
//           // 主标题行 - 改为水平布局
//           Row() {
//             Text('FIM NAPI Bridge 测试')
//               .fontSize(20)
//               .fontWeight(FontWeight.Bold)
//               .layoutWeight(1)
//           }
//           .width('100%')
//           .margin({ bottom: 10 })
//
//           // 分类选择器
//           Row() {
//             Text('分类:')
//               .fontSize(14)
//               .margin({ right: 10 })
//
//             ForEach(this.categories, (category: string) => {
//               Text(category)
//                 .fontSize(12)
//                 .padding({ left: 8, right: 8, top: 4, bottom: 4 })
//                 .backgroundColor(this.selectedCategory === category ? Color.Blue : Color.Gray)
//                 .fontColor(this.selectedCategory === category ? Color.White : Color.Black)
//                 .borderRadius(4)
//                 .margin({ right: 5 })
//                 .onClick(() => {
//                   this.selectedCategory = category;
//                 })
//             })
//           }
//           .margin({ bottom: 10 })
//
//           // 搜索框
//           TextInput({ placeholder: '搜索方法名称...' })
//             .width('100%')
//             .height(35)
//             .fontSize(12)
//             .onChange((value: string) => {
//               this.searchText = value;
//             })
//             .margin({ bottom: 10 })
//
//           // 批量操作按钮
//           Row() {
//             Button('测试全部')
//               .fontSize(12)
//               .height(30)
//               .enabled(!this.isRunning)
//               .onClick(() => {
//                 this.testAllMethods();
//               })
//               .margin({ right: 5 })
//
//             Button('调试测试')
//               .fontSize(12)
//               .height(30)
//               .backgroundColor(Color.Orange)
//               .enabled(!this.isRunning)
//               .onClick(() => {
//                 this.debugTest();
//               })
//               .margin({ right: 5 })
//
//             Button('清空结果')
//               .fontSize(12)
//               .height(30)
//               .backgroundColor(Color.Gray)
//               .onClick(() => {
//                 this.testResults = [];
//               })
//           }
//           .margin({ bottom: 10 })
//         }
//         .padding(10)
//         .backgroundColor(Color.White)
//         .borderRadius(8)
//         .margin({ bottom: 10 })
//
//         // 方法列表
//         List({ space: 5 }) {
//           ForEach(this.getFilteredMethods(), (method: FIMTestMethod) => {
//             ListItem() {
//               this.MethodItemComponent(method)
//             }
//           })
//         }
//         .layoutWeight(1)
//         .padding({ left: 5, right: 5 })
//       }
//       .width('45%')
//       .height('100%')
//       .padding({ left: 10, right: 5, top: 10, bottom: 10 })
//
//       // 分隔线
//       Line()
//         .width(1)
//         .height('100%')
//         .backgroundColor('#E0E0E0')
//
//       // 右侧：测试结果展示区域
//       Column() {
//         // 结果区域标题
//         Row() {
//           Text('测试结果')
//             .fontSize(18)
//             .fontWeight(FontWeight.Bold)
//
//           Blank()
//
//           if (this.isRunning) {
//             Row() {
//               LoadingProgress()
//                 .width(20)
//                 .height(20)
//                 .margin({ right: 5 })
//               Text(`正在测试: ${this.currentTestingMethod}`)
//                 .fontSize(12)
//                 .fontColor(Color.Blue)
//             }
//           }
//         }
//         .width('100%')
//         .margin({ bottom: 10 })
//
//         // 结果列表
//         if (this.testResults.length === 0) {
//           Column() {
//             Text('暂无测试结果')
//               .fontSize(14)
//               .fontColor(Color.Gray)
//               .margin({ top: 50 })
//           }
//           .width('100%')
//           .layoutWeight(1)
//           .justifyContent(FlexAlign.Start)
//         } else {
//           List({ space: 8 }) {
//             ForEach(this.testResults, (result: TestResult) => {
//               ListItem() {
//                 this.ResultItemComponent(result)
//               }
//             })
//           }
//           .layoutWeight(1)
//         }
//       }
//       .width('54%')
//       .height('100%')
//       .padding({ left: 5, right: 10, top: 10, bottom: 10 })
//     }
//     .width('100%')
//     .height('100%')
//     .backgroundColor('#F5F5F5')
//   }
//
//   // 方法项组件
//   @Builder MethodItemComponent(method: FIMTestMethod) {
//     Column() {
//       Row() {
//         Column() {
//           Text(method.displayName)
//             .fontSize(14)
//             .fontWeight(FontWeight.Medium)
//             .maxLines(1)
//             .textOverflow({ overflow: TextOverflow.Ellipsis })
//
//           Text(method.name)
//             .fontSize(11)
//             .fontColor(Color.Gray)
//             .margin({ top: 2 })
//             .maxLines(1)
//             .textOverflow({ overflow: TextOverflow.Ellipsis })
//         }
//         .alignItems(HorizontalAlign.Start)
//         .layoutWeight(1)
//
//         Button('测试')
//           .fontSize(12)
//           .height(30)
//           .width(50)
//           .enabled(!this.isRunning)
//           .onClick(() => {
//             this.testSingleMethod(method);
//           })
//       }
//       .width('100%')
//       .alignItems(VerticalAlign.Center)
//     }
//     .width('100%')
//     .padding(12)
//     .backgroundColor(Color.White)
//     .borderRadius(6)
//     .border({ width: 1, color: '#E0E0E0' })
//   }
//
//   // 结果项组件
//   @Builder ResultItemComponent(result: TestResult) {
//     Column() {
//       // 标题行
//       Row() {
//         Text(result.methodName)
//           .fontSize(14)
//           .fontWeight(FontWeight.Medium)
//           .fontColor(result.success ? Color.Green : Color.Red)
//           .layoutWeight(1)
//
//         Text(`${result.duration}ms`)
//           .fontSize(11)
//           .fontColor(Color.Gray)
//           .margin({ left: 10 })
//
//         Text(result.timestamp)
//           .fontSize(11)
//           .fontColor(Color.Gray)
//           .margin({ left: 10 })
//
//         // 展开/折叠按钮
//         Button(result.expanded ? '折叠' : '展开')
//           .fontSize(10)
//           .height(24)
//           .padding({ left: 8, right: 8 })
//           .backgroundColor(Color.Transparent)
//           .fontColor('#007AFF')
//           .border({ width: 1, color: '#007AFF', radius: 4 })
//           .onClick(() => {
//             this.toggleResultExpansion(result.id);
//           })
//           .margin({ left: 8 })
//       }
//       .width('100%')
//       .margin({ bottom: 8 })
//
//       // 参数展示
//       Column() {
//         Text('参数:')
//           .fontSize(12)
//           .fontColor(Color.Gray)
//           .width('100%')
//           .margin({ bottom: 4 })
//
//         if (result.expanded) {
//           // 展开状态：显示完整内容，支持滚动
//           Scroll() {
//             Text(result.params)
//               .fontSize(10)
//               .fontColor('#666666')
//               .backgroundColor('#F8F8F8')
//               .padding(8)
//               .borderRadius(4)
//               .width('100%')
//               .copyOption(CopyOptions.InApp)
//           }
//           .width('100%')
//           .height(120)
//           .scrollable(ScrollDirection.Vertical)
//           .scrollBar(BarState.Auto)
//         } else {
//           // 折叠状态：显示预览
//           Text(result.params.length > 100 ? result.params.substring(0, 100) + '...' : result.params)
//             .fontSize(11)
//             .fontColor('#666666')
//             .backgroundColor('#F8F8F8')
//             .padding(6)
//             .borderRadius(4)
//             .width('100%')
//             .maxLines(2)
//             .textOverflow({ overflow: TextOverflow.Ellipsis })
//         }
//       }
//       .width('100%')
//       .alignItems(HorizontalAlign.Start)
//       .margin({ bottom: 8 })
//
//       // 结果展示
//       Column() {
//         Text('结果:')
//           .fontSize(12)
//           .fontColor(Color.Gray)
//           .width('100%')
//           .margin({ bottom: 4 })
//
//         if (result.expanded) {
//           // 展开状态：显示完整内容，支持滚动
//           Scroll() {
//             Text(result.result)
//               .fontSize(10)
//               .fontColor(result.success ? '#2E7D32' : '#D32F2F')
//               .backgroundColor(result.success ? '#E8F5E8' : '#FFEBEE')
//               .padding(8)
//               .borderRadius(4)
//               .width('100%')
//               .copyOption(CopyOptions.InApp)
//           }
//           .width('100%')
//           .height(200)
//           .scrollable(ScrollDirection.Vertical)
//           .scrollBar(BarState.Auto)
//         } else {
//           // 折叠状态：显示预览
//           Text(result.result.length > 150 ? result.result.substring(0, 150) + '...' : result.result)
//             .fontSize(11)
//             .fontColor(result.success ? '#2E7D32' : '#D32F2F')
//             .backgroundColor(result.success ? '#E8F5E8' : '#FFEBEE')
//             .padding(6)
//             .borderRadius(4)
//             .width('100%')
//             .maxLines(3)
//             .textOverflow({ overflow: TextOverflow.Ellipsis })
//         }
//       }
//       .width('100%')
//       .alignItems(HorizontalAlign.Start)
//     }
//     .width('100%')
//     .padding(12)
//     .backgroundColor(Color.White)
//     .borderRadius(6)
//     .border({ width: 1, color: result.success ? '#C8E6C9' : '#FFCDD2' })
//   }
//
//   // 测试单个方法
//   private async testSingleMethod(method: FIMTestMethod) {
//     if (this.isRunning) return;
//
//     this.isRunning = true;
//     this.currentTestingMethod = method.displayName;
//
//     const startTime = Date.now();
//
//     try {
//       // 调试信息：打印调用参数
//       console.log(`[DEBUG] 调用方法: ${method.name}`);
//       console.log(`[DEBUG] 参数类型: ${typeof method.testParams}`);
//       console.log(`[DEBUG] 参数内容: ${method.testParams}`);
//
//       // 调用NAPI方法
//       const result: string = method.func(method.testParams);
//       const duration = Date.now() - startTime;
//
//       console.log(`[DEBUG] 返回结果: ${result}`);
//
//       // 解析结果判断成功失败
//       let success: boolean = false;
//       let resultText: string = result;
//
//       try {
//         const parsedResult: Record<string, Object> = JSON.parse(result) as Record<string, Object>;
//         success = (parsedResult.success as boolean) === true ||
//                  ((parsedResult.data as Record<string, Object>) &&
//                   (parsedResult.data as Record<string, Object>).source === 'FliggyIMSDK_Bridge');
//         resultText = JSON.stringify(parsedResult, null, 2);
//       } catch (e) {
//         // 如果不是JSON格式，直接使用原始结果
//         success = !result.includes('error') && !result.includes('Error');
//       }
//
//       // 添加测试结果
//       const testResult: TestResult = {
//         id: `${method.id}_${Date.now()}`,
//         methodName: method.displayName,
//         params: JSON.stringify(JSON.parse(method.testParams) as Record<string, Object>, null, 2),
//         result: resultText,
//         success: success,
//         timestamp: new Date().toLocaleTimeString(),
//         duration: duration,
//         expanded: true
//       };
//
//       this.testResults.unshift(testResult); // 新结果显示在顶部
//
//     } catch (error) {
//       const duration = Date.now() - startTime;
//       const testResult: TestResult = {
//         id: `${method.id}_${Date.now()}`,
//         methodName: method.displayName,
//         params: JSON.stringify(JSON.parse(method.testParams) as Record<string, Object>, null, 2),
//         result: `调用异常: ${error}`,
//         success: false,
//         timestamp: new Date().toLocaleTimeString(),
//         duration: duration,
//         expanded: true
//       };
//
//       this.testResults.unshift(testResult);
//     } finally {
//       this.isRunning = false;
//       this.currentTestingMethod = '';
//     }
//   }
//
//   // 测试所有方法
//   private async testAllMethods() {
//     if (this.isRunning) return;
//
//     const filteredMethods = this.getFilteredMethods();
//     this.isRunning = true;
//
//     // 添加开始测试的记录
//     const startResult: TestResult = {
//       id: `batch_start_${Date.now()}`,
//       methodName: '批量测试开始',
//       params: `共 ${filteredMethods.length} 个方法`,
//       result: '开始执行批量测试...',
//       success: true,
//       timestamp: new Date().toLocaleTimeString(),
//       duration: 0,
//       expanded: true
//     };
//     this.testResults.unshift(startResult);
//
//     let successCount = 0;
//     let failCount = 0;
//
//     // 逐个测试方法
//     for (const method of filteredMethods) {
//       this.currentTestingMethod = method.displayName;
//
//       const startTime = Date.now();
//
//       try {
//         const result: string = method.func(method.testParams);
//         const duration = Date.now() - startTime;
//
//         let success: boolean = false;
//         let resultText: string = result;
//
//         try {
//           const parsedResult: Record<string, Object> = JSON.parse(result) as Record<string, Object>;
//           success = (parsedResult.success as boolean) === true ||
//                    ((parsedResult.data as Record<string, Object>) &&
//                     (parsedResult.data as Record<string, Object>).source === 'FliggyIMSDK_Bridge');
//           resultText = JSON.stringify(parsedResult, null, 2);
//         } catch (e) {
//           success = !result.includes('error') && !result.includes('Error');
//         }
//
//         const testResult: TestResult = {
//           id: `${method.id}_${Date.now()}`,
//           methodName: method.displayName,
//           params: JSON.stringify(JSON.parse(method.testParams) as Record<string, Object>, null, 2),
//           result: resultText,
//           success: success,
//           timestamp: new Date().toLocaleTimeString(),
//           duration: duration,
//           expanded: true
//         };
//
//         this.testResults.unshift(testResult);
//
//         if (success) {
//           successCount++;
//         } else {
//           failCount++;
//         }
//
//       } catch (error) {
//         const duration = Date.now() - startTime;
//         const testResult: TestResult = {
//           id: `${method.id}_${Date.now()}`,
//           methodName: method.displayName,
//           params: JSON.stringify(JSON.parse(method.testParams) as Record<string, Object>, null, 2),
//           result: `调用异常: ${error}`,
//           success: false,
//           timestamp: new Date().toLocaleTimeString(),
//           duration: duration,
//           expanded: true
//         };
//
//         this.testResults.unshift(testResult);
//         failCount++;
//       }
//
//       // 添加小延迟，避免UI卡顿
//       await new Promise<void>((resolve) => setTimeout(resolve, 100));
//     }
//
//     // 添加测试完成的记录
//     const endResult: TestResult = {
//       id: `batch_end_${Date.now()}`,
//       methodName: '批量测试完成',
//       params: `总计: ${filteredMethods.length} 个方法`,
//       result: `成功: ${successCount} 个，失败: ${failCount} 个`,
//       success: failCount === 0,
//       timestamp: new Date().toLocaleTimeString(),
//       duration: 0,
//       expanded: true
//     };
//     this.testResults.unshift(endResult);
//
//     this.isRunning = false;
//     this.currentTestingMethod = '';
//   }
//
//   // 切换结果展开/折叠状态
//   private toggleResultExpansion(resultId: string) {
//     const index = this.testResults.findIndex(result => result.id === resultId);
//     if (index !== -1) {
//       // 创建新的数组来触发UI更新
//       const newResults: TestResult[] = [];
//       for (let i = 0; i < this.testResults.length; i++) {
//         if (i === index) {
//           // 创建新的对象，切换展开状态
//           const updatedResult: TestResult = {
//             id: this.testResults[i].id,
//             methodName: this.testResults[i].methodName,
//             params: this.testResults[i].params,
//             result: this.testResults[i].result,
//             success: this.testResults[i].success,
//             timestamp: this.testResults[i].timestamp,
//             duration: this.testResults[i].duration,
//             expanded: !this.testResults[i].expanded
//           };
//           newResults.push(updatedResult);
//         } else {
//           newResults.push(this.testResults[i]);
//         }
//       }
//       this.testResults = newResults;
//     }
//   }
//
//   // 调试测试方法
//   private async debugTest() {
//     console.log('[DEBUG] 开始调试测试...');
//
//     // 测试最简单的方法
//     const testMethod = this.fimMethods[0];
//
//     console.log(`[DEBUG] 测试方法: ${testMethod.name}`);
//     console.log(`[DEBUG] 测试参数: ${testMethod.testParams}`);
//     console.log(`[DEBUG] 参数类型: ${typeof testMethod.testParams}`);
//
//     try {
//       // 直接调用函数进行测试
//       const result = testMethod.func(testMethod.testParams);
//       console.log(`[DEBUG] 调用成功，结果: ${result}`);
//
//       // 添加调试结果到界面
//       const debugResult: TestResult = {
//         id: `debug_${Date.now()}`,
//         methodName: `[调试] ${testMethod.displayName}`,
//         params: testMethod.testParams,
//         result: result,
//         success: true,
//         timestamp: new Date().toLocaleTimeString(),
//         duration: 0,
//         expanded: true
//       };
//       this.testResults.unshift(debugResult);
//
//     } catch (error) {
//       console.error(`[DEBUG] 调用失败: ${error}`);
//
//       // 添加错误结果到界面
//       const errorResult: TestResult = {
//         id: `debug_error_${Date.now()}`,
//         methodName: `[调试错误] ${testMethod.displayName}`,
//         params: testMethod.testParams,
//         result: `调试错误: ${error}`,
//         success: false,
//         timestamp: new Date().toLocaleTimeString(),
//         duration: 0,
//         expanded: true
//       };
//       this.testResults.unshift(errorResult);
//     }
//   }
// }
