import { PageContext } from '@fliggy-ohos/router';
import { TitleBar } from '@fliggy-ohos/titlebar';
import { FliggyKV } from '@fliggy-ohos/fliggykv';
import { env, EnvConstant } from '@fliggy-ohos/env';
import promptAction from '@ohos.promptAction';
import http from '@ohos.net.http';
import { EnvironUtils } from '@fliggy-ohos/appcompat';
import dataPreferences from '@ohos.data.preferences';
import { TLogConfig } from '@taobao-ohos/tlog/src/main/ets/tlog/TLogConfig';

@Entry
@Component
export struct OnlineToolsDebugPage {
  pageContext?: PageContext;
  @State needShow : boolean =false;
  private env?: string
  private kv?: FliggyKV
  private h5Preferences?: dataPreferences.Preferences;
  @State showDebugIcon: boolean = false;

  aboutToAppear(): void {
    this.pageContext?.bind(this);
    this.kv = FliggyKV.getFliggyKVWithMMapID('env')
    this.env = this.kv.decodeString('env', EnvConstant[EnvConstant.RELEASE])
    this.h5Preferences = dataPreferences.getPreferencesSync(getContext(), {name: "h5inspector"});
    this.showDebugIcon = this.h5Preferences?.getSync("showDebugIcon", false) as boolean;

    if(EnvironUtils.debuggable()) {
      this.needShow = true
    } else {
      this.checkNet();
    }
  }

  async checkNet(){
    try {
      let httpRequest: http.HttpRequest = http.createHttp();
      let httpResponse: http.HttpResponse = await httpRequest.request('https://acs.wapa.taobao.com/gw/mtop.common.gettimestamp/*/?data=%7B%7D', {
        method: http.RequestMethod.GET,
        expectDataType: http.HttpDataType.STRING,
      });
      httpRequest.destroy();
      if (httpResponse.responseCode == 200) {
        const str: string = httpResponse.result as string
        let json: object = JSON.parse(str)
        if('SUCCESS::接口调用成功' === json['ret'][0]) {
          this.needShow = true;
          return;
        }
      }
    } catch (e) {
    }
    promptAction.showToast({ message: '必须在内网访问此页面',});
  }

  onPageShow() {
  }

  onPageHide() {
  }

  build() {

    Flex({
      direction: FlexDirection.Column
    }) {
      TitleBar({
        showLeftItem: true,
        title: "仅供内部调试使用",
      })
      if(this.needShow) {
        List() {
          ListItem() {
            Button(`切环境-(当前${env.getEnvironmentName() === EnvConstant.RELEASE ? '线上' : env.getEnvironmentName() === EnvConstant.SPEC ? '安全生产' : '预发'})`)
              .onClick(async () => {
                let result = await promptAction.showDialog({
                  title: '切换环境',
                  buttons: [{
                    text: '线上',
                    color: '#000000',
                  },
                    {
                      text: '预发',
                      color: '#000000',
                    }, {
                      text: '安全生产',
                      color: '#000000',
                    }],
                });
                let index = result.index
                let e: string | undefined
                if (index == 1) {
                  e = EnvConstant[EnvConstant.PRECAST]
                } else if (index == 2) {
                  e = EnvConstant[EnvConstant.SPEC]
                } else if (index == 0) {
                  e = EnvConstant[EnvConstant.RELEASE]
                }
                if (e) {
                  this.kv!.encodeString('env', e!)
                  promptAction.showToast({ message: '重启app生效', duration: 1000 });
                }

              }).margin({ top: 10 })
          }
          ListItem() {
            Button(`打开atom`)
              .onClick(() => {
                AppStorage.setOrCreate<boolean>('need_show_atom_icon', true)
                promptAction.showToast({ message: '重启app生效', duration: 1000 });
              }).margin({ top: 10 })
          }
          ListItem() {
            Button(`H5诊断助手: ${this.showDebugIcon ? "开" : "关"}`)
              .onClick(() => {
                this.showDebugIcon = !this.showDebugIcon;
                this.h5Preferences?.putSync("showDebugIcon", this.showDebugIcon);
                this.h5Preferences?.flush();
                TLogConfig.isDebug = true;
              }).margin({ top: 10 })
          }
        }.listDirection(Axis.Vertical)
        .alignListItem(ListItemAlign.Center)
        .flexGrow(1)
        .height('100%')
        .width('100%')
      }

    }
  }

}
