import { TabWebViewPage } from '@fliggy-ohos/unicorn';
import { BottomTabBar, TabData } from '../view/components/BottomTabBar';
import { FliggyHomePage } from '@fliggy-ohos/homepage';
// import { MessageCenterPage } from '@taobao-ohos/tbmessage_ohos';
import DataModel from '../model/DataModel';
import { env, EnvConstant } from '@fliggy-ohos/env';
import { TripFlutterPage } from '@fliggy-ohos/flutter_container';
import { Logger } from '@fliggy-ohos/logger';
import {
  FliggyNavigator,
  navigator,
  PageContext,
  RunningPageStack,
  TabContext,
  TripTabsController,
  UILifecycleManager
} from '@fliggy-ohos/router';
import { Nav } from '@taobao-ohos/nav';
import { login } from '@fliggy-ohos/login';
import { display, promptAction, window } from '@kit.ArkUI';
import { setOnNavigationModeChangeCallback } from './LaunchIndexPage';
import { WindowUtils } from '@fliggy-ohos/appcompat';
import process from '@ohos.process';
import { PushNotification } from '@fliggy-ohos/launcherimpl';
import { tracker } from '@fliggy-ohos/tracker';
import { DeviceHelper } from '@ohos/multinavigation/src/main/ets/components/mainpage/DeviceHelper';
import { FBDocumentAssistantHook } from '../hook/FBDocumentAssistantHook';
// FIM NAPI Bridge MVP 测试
// import { FIMMVPTestPage } from './FIMMVPTestPage';
// FIM 测试组件 - 适配版本
import { FIMTestComponent } from '../components/FIMTestComponent';
// FIM Engine 测试页面
// import { TestFIMPage } from '../atom/pages/TestFIMPage';

const HOME: number = 0;

export const logger = new Logger('MainPage');

// 定义聊天参数接口
interface ChatParams {
  targetType: string;
  targetId: string;
  bizType: string;
  ccode: string;
  needByPass: string;
  source: string;
  bizConfigCode: string;
}

// MessageCenter 对比测试包装器
@Component
struct MessageCenterDiagnosticWrapper {
  pageContext?: PageContext;
  @State currentView: 'diagnostic' | 'flutter' | 'native' | 'fim_test' | 'fim_test_adapted' | 'fim_engine_test' = 'diagnostic';
  @State diagnosticInfo: string = '消息中心对比测试';

  aboutToAppear(): void {
    this.pageContext?.bind(this);
  }

  onPageShow(): void {
    logger.d('MessageCenterDiagnosticWrapper', 'onPageShow');
  }

  onPageHide(): void {
    logger.d('MessageCenterDiagnosticWrapper', 'onPageHide');
  }

  // 打开 Flutter 消息页面
  openFlutterMessageCenter() {
    try {
      logger.d('MessageCenterDiagnosticWrapper', 'Opening Flutter message center');
      // 调用 Flutter 消息中心页面
      // 这里需要通过 FlutterBoost 或 Method Channel 调用
      this.callFlutterMessageCenter();
    } catch (e) {
      logger.e('MessageCenterDiagnosticWrapper', 'Failed to open Flutter message center: ' + JSON.stringify(e));
    }
  }

  // 调用 Flutter 消息中心的方法
  private callFlutterMessageCenter() {
    // 使用原来的 TripFlutterPage 方式显示 Flutter 消息中心
    this.currentView = 'flutter';
  }

  // 从 conversationCode 中解析 targetUserId
  private parseTargetIdFromConversationCode(conversationCode: string): string {
    // conversationCode 格式: "795938765.1-2219471085823.1#11001@cntaobao"
    // 提取模式: "当前用户ID.1-目标用户ID.1#bizType@cntaobao"
    const match = conversationCode.match(/^\d+\.1-(\d+)\.1#/);
    return match ? match[1] : "";
  }

  // 从 conversationCode 中解析 bizType
  private parseBizTypeFromConversationCode(conversationCode: string): string {
    // 提取 #后面的数字
    const match = conversationCode.match(/#(\d+)@/);
    return match ? match[1] : "11001";
  }

  // 动态构造聊天参数
  private buildChatParams(currentUserId: string, targetUserId: string, bizType: string = "11001"): ChatParams {
    // 构造 ccode：当前用户ID.1-目标用户ID.1#bizType@cntaobao
    const ccode = `${currentUserId}.1-${targetUserId}.1#${bizType}@cntaobao`;

    const params: ChatParams = {
      targetType: "3",
      targetId: targetUserId,
      bizType: bizType,
      ccode: ccode,
      needByPass: "false",
      source: "MSGBOX",
      bizConfigCode: "messageSingleBC.json"
    };

    return params;
  }

  // 标准 ChatPage 跳转测试（使用动态参数构造）
  testChatPageJump() {
    try {
      logger.d('MessageCenterDiagnosticWrapper', '=== 标准 ChatPage 跳转测试 ===');

      // 参数说明：
      const currentUserId = "795938765";    // 当前登录用户ID
      const targetUserId = "2219471085823"; // 要聊天的目标用户ID
      const bizType = "11001";              // 业务类型：11001=B2C, 10001=C2C

      // 动态构造参数
      const chatParams = this.buildChatParams(currentUserId, targetUserId, bizType);

      logger.d('MessageCenterDiagnosticWrapper', '参数构造分析：');
      logger.d('MessageCenterDiagnosticWrapper', `当前用户ID: ${currentUserId}`);
      logger.d('MessageCenterDiagnosticWrapper', `目标用户ID: ${targetUserId}`);
      logger.d('MessageCenterDiagnosticWrapper', `业务类型: ${bizType}`);
      logger.d('MessageCenterDiagnosticWrapper', `构造的ccode: ${chatParams.ccode}`);

      // 构建查询字符串
      const params = Object.entries(chatParams)
        .map((entry: [string, string]) => {
          return `${entry[0]}=${encodeURIComponent(entry[1])}`;
        })
        .join('&');

      const uri = `//tb.cn/n/im/dynamic/chat.html?${params}`;

      logger.d('MessageCenterDiagnosticWrapper', `跳转 URI: ${uri}`);

      Nav.from(null).toUri(uri)
        .then((result: boolean) => {
          logger.d('MessageCenterDiagnosticWrapper', `ChatPage 跳转结果: ${result}`);
          if (!result) {
            logger.e('MessageCenterDiagnosticWrapper', 'ChatPage 跳转失败');
          }
        })
        .catch((error: Error) => {
          logger.e('MessageCenterDiagnosticWrapper', `Nav.toUri 失败: ${error.message}`);
        });

    } catch (e) {
      logger.e('MessageCenterDiagnosticWrapper', 'Error testing ChatPage jump: ' + JSON.stringify(e));
    }
  }


  // 使用自定义参数测试跳转（用于调试不同参数组合）
  testWithCustomParams(currentUserId: string, targetUserId: string, bizType: string = "11001") {
    try {
      logger.d('MessageCenterDiagnosticWrapper', `=== 自定义参数测试 ===`);
      logger.d('MessageCenterDiagnosticWrapper', `当前用户: ${currentUserId}, 目标用户: ${targetUserId}, 业务类型: ${bizType}`);

      // 使用动态构造方法
      const chatParams = this.buildChatParams(currentUserId, targetUserId, bizType);

      logger.d('MessageCenterDiagnosticWrapper', `动态构造的ccode: ${chatParams.ccode}`);

      // 构建查询字符串
      const params = Object.entries(chatParams)
        .map((entry: [string, string]) => {
          return `${entry[0]}=${encodeURIComponent(entry[1])}`;
        })
        .join('&');

      const uri = `//tb.cn/n/im/dynamic/chat.html?${params}`;

      logger.d('MessageCenterDiagnosticWrapper', `自定义参数 URI: ${uri}`);

      Nav.from(null).toUri(uri)
        .then((result: boolean) => {
          logger.d('MessageCenterDiagnosticWrapper', `自定义参数跳转结果: ${result}`);
          if (!result) {
            logger.e('MessageCenterDiagnosticWrapper', '自定义参数跳转失败');
          }
        })
        .catch((error: Error) => {
          logger.e('MessageCenterDiagnosticWrapper', `自定义参数 Nav.toUri 失败: ${error.message}`);
        });

    } catch (e) {
      logger.e('MessageCenterDiagnosticWrapper', 'Error testing with custom params: ' + JSON.stringify(e));
    }
  }

  // 基于 conversationCode 的测试（模拟真实场景）
  testWithConversationCode(conversationCode: string) {
    try {
      logger.d('MessageCenterDiagnosticWrapper', `=== 基于 conversationCode 的测试 ===`);
      logger.d('MessageCenterDiagnosticWrapper', `conversationCode: ${conversationCode}`);

      // 解析参数
      const targetUserId = this.parseTargetIdFromConversationCode(conversationCode);
      const bizType = this.parseBizTypeFromConversationCode(conversationCode);
      const currentUserId = "795938765"; // 当前用户ID（实际应该从登录状态获取）

      logger.d('MessageCenterDiagnosticWrapper', `解析结果:`);
      logger.d('MessageCenterDiagnosticWrapper', `- 当前用户ID: ${currentUserId}`);
      logger.d('MessageCenterDiagnosticWrapper', `- 目标用户ID: ${targetUserId}`);
      logger.d('MessageCenterDiagnosticWrapper', `- 业务类型: ${bizType}`);

      if (!targetUserId) {
        logger.e('MessageCenterDiagnosticWrapper', '无法从 conversationCode 中解析出 targetUserId');
        return;
      }

      // 使用解析出的参数构造聊天参数
      const chatParams = this.buildChatParams(currentUserId, targetUserId, bizType);

      // 构建查询字符串
      const params = Object.entries(chatParams)
        .map((entry: [string, string]) => {
          return `${entry[0]}=${encodeURIComponent(entry[1])}`;
        })
        .join('&');

      const uri = `//tb.cn/n/im/dynamic/chat.html?${params}`;

      logger.d('MessageCenterDiagnosticWrapper', `conversationCode 测试 URI: ${uri}`);

      Nav.from(null).toUri(uri)
        .then((result: boolean) => {
          logger.d('MessageCenterDiagnosticWrapper', `conversationCode 测试跳转结果: ${result}`);
          if (!result) {
            logger.e('MessageCenterDiagnosticWrapper', 'conversationCode 测试跳转失败');
          }
        })
        .catch((error: Error) => {
          logger.e('MessageCenterDiagnosticWrapper', `conversationCode 测试 Nav.toUri 失败: ${error.message}`);
        });

    } catch (e) {
      logger.e('MessageCenterDiagnosticWrapper', 'Error testing with conversationCode: ' + JSON.stringify(e));
    }
  }

  // 辅助方法：使用 Nav 测试跳转
  private testChatPageWithParams(testName: string, params: string) {
    const uri = `//tb.cn/n/im/dynamic/chat.html?${params}`;

    logger.d('MessageCenterDiagnosticWrapper', `${testName}: ${uri}`);

    Nav.from(null).toUri(uri)
      .then((result: boolean) => {
        logger.d('MessageCenterDiagnosticWrapper', `${testName} 跳转结果: ${result}`);
        if (!result) {
          logger.e('MessageCenterDiagnosticWrapper', `${testName} 跳转失败`);
        }
      })
      .catch((error: Error) => {
        logger.e('MessageCenterDiagnosticWrapper', `${testName} Nav.toUri 失败: ${error.message}`);
        });
  }



  build() {
    Column() {
      if (this.currentView === 'flutter') {
        // Flutter 模式：让 TripFlutterPage 占据全部空间
        TripFlutterPage({
          pageContext: this.pageContext,
          flutterParams: {
            "uri": "/fliggy_message_center/home",
            "params": {
              "__flutter_arguments__": "{\"un_flutter\":\"true\",\"isTab\":\"true\"}"
            }
          }
        })
        .width('100%')
        .height('100%')

      } else if (this.currentView === 'diagnostic') {
        // 诊断和选择界面 - 重新组织为滚动列表
        Column() {
          Text('Fliggy IM 测试中心')
            .fontSize(20)
            .fontWeight(FontWeight.Bold)
            .margin({ bottom: 10 })

          Text('选择要测试的功能模块：')
            .fontSize(14)
            .fontColor(Color.Gray)
            .margin({ bottom: 20 })

          // 使用Scroll组件创建可滚动列表
          Scroll() {
            Column() {
              // === 1. FIM Engine 测试区域 ===
              Text('🚀 FIM Engine 测试')
                .fontSize(16)
                .fontWeight(FontWeight.Medium)
                .fontColor('#1f2937')
                .width('100%')
                .textAlign(TextAlign.Start)
                .margin({ bottom: 15 })

              Button('FIM Engine 启动测试')
                .onClick(() => {
                  this.currentView = 'fim_engine_test';
                })
                .backgroundColor('#10b981')
                .fontColor(Color.White)
                .borderRadius(8)
                .padding({ left: 20, right: 20, top: 12, bottom: 12 })
                .margin({ bottom: 20 })
                .width('100%')

              // 分隔线
              Divider()
                .color('#E0E0E0')
                .strokeWidth(1)
                .margin({ top: 10, bottom: 20 })

              // === 2. FIM NAPI Bridge 测试区域 ===
              Text('🔗 FIM NAPI Bridge 测试')
                .fontSize(16)
                .fontWeight(FontWeight.Medium)
                .fontColor('#1f2937')
                .width('100%')
                .textAlign(TextAlign.Start)
                .margin({ bottom: 15 })

          // FIM NAPI Bridge 测试按钮
          Button('🚀 FIM NAPI Bridge 测试 (原版)')
            .onClick(() => {
              this.currentView = 'fim_test';
            })
            .backgroundColor('#8E44AD')
            .fontColor(Color.White)
            .borderRadius(8)
            .padding({ left: 20, right: 20, top: 12, bottom: 12 })
            .margin({ bottom: 15 })
            .width('100%')

          // FIM 适配版测试按钮
          Button('🔗 FIM 测试 (fliggy_im.har 适配版)')
            .onClick(() => {
              this.currentView = 'fim_test_adapted';
            })
            .backgroundColor('#6366f1')
            .fontColor(Color.White)
            .borderRadius(8)
            .padding({ left: 20, right: 20, top: 12, bottom: 12 })
            .margin({ bottom: 20 })
            .width('100%')

          // 分隔线
          Divider()
            .color('#E0E0E0')
            .strokeWidth(1)
            .margin({ top: 10, bottom: 20 })

          // === 3. 消息中心测试区域 ===
          Text('💬 消息中心测试')
            .fontSize(16)
            .fontWeight(FontWeight.Medium)
            .fontColor('#1f2937')
            .width('100%')
            .textAlign(TextAlign.Start)
            .margin({ bottom: 15 })

          // Flutter 版本按钮
          Button('Flutter 消息中心')
            .onClick(() => {
              this.openFlutterMessageCenter();
            })
            .backgroundColor('#FF6B35')
            .fontColor(Color.White)
            .borderRadius(8)
            .padding({ left: 20, right: 20, top: 12, bottom: 12 })
            .margin({ bottom: 15 })
            .width('100%')

          // 原生版本按钮
          Button('原生 tbmessage 页面')
            .onClick(() => {
              this.currentView = 'native';
            })
            .backgroundColor('#007AFF')
            .fontColor(Color.White)
            .borderRadius(8)
            .padding({ left: 20, right: 20, top: 12, bottom: 12 })
            .margin({ bottom: 15 })
            .width('100%')

          // 标准 ChatPage 跳转测试按钮
          Button('✅ 标准 ChatPage 跳转')
            .onClick(() => {
              this.testChatPageJump();
            })
            .backgroundColor('#34C759')
            .fontColor(Color.White)
            .borderRadius(8)
            .padding({ left: 20, right: 20, top: 12, bottom: 12 })
            .margin({ bottom: 15 })
            .width('100%')

          // 自定义参数测试按钮
          Button('🔧 自定义参数测试')
            .onClick(() => {
              // 示例：测试不同的用户组合
              this.testWithCustomParams("795938765", "2219471085823", "11001");
            })
            .backgroundColor('#FF9500')
            .fontColor(Color.White)
            .borderRadius(8)
            .padding({ left: 20, right: 20, top: 12, bottom: 12 })
            .margin({ bottom: 15 })
            .width('100%')

          // conversationCode 解析测试按钮
          Button('📋 conversationCode 解析测试')
            .onClick(() => {
              // 使用真实的 conversationCode 进行测试
              this.testWithConversationCode("795938765.1-2219471085823.1#11001@cntaobao");
            })
            .backgroundColor('#5856D6')
            .fontColor(Color.White)
            .borderRadius(8)
            .padding({ left: 20, right: 20, top: 12, bottom: 12 })
            .margin({ bottom: 30 })
            .width('100%')

            }
            .width('100%')
            .padding({ left: 20, right: 20 })
          }
          .layoutWeight(1)
          .width('100%')
        }
        .width('100%')
        .height('100%')
        .backgroundColor(Color.White)
        .padding({ top: 20, bottom: 20 })

      } else if (this.currentView === 'native') {
        // 原生 tbmessage 页面
        Column() {
          Row() {
            Button('← 返回')
              .onClick(() => {
                this.currentView = 'diagnostic';
              })
              .backgroundColor(Color.Transparent)
              .fontColor('#007AFF')
              .fontSize(16)
          }
          .width('100%')
          .justifyContent(FlexAlign.Start)
          .padding({ left: 15, top: 10 })

          // // 实际的 MessageCenterPage
          // MessageCenterPage()
          //   .layoutWeight(1)
        }
        .width('100%')
        .height('100%')

      } else if (this.currentView === 'fim_test') {
        // FIM NAPI Bridge 完整测试界面 - 使用重构的测试页面
        Column() {
          // 顶部导航栏
          Row() {
            Button('← 返回')
              .onClick(() => {
                this.currentView = 'diagnostic';
              })
              .backgroundColor(Color.Transparent)
              .fontColor('#007AFF')
              .fontSize(16)

            Text('FIM NAPI Bridge 完整测试')
              .fontSize(18)
              .fontWeight(FontWeight.Bold)
              .layoutWeight(1)
              .textAlign(TextAlign.Center)
          }
          .width('100%')
          .justifyContent(FlexAlign.SpaceBetween)
          .padding({ left: 15, right: 15, top: 10, bottom: 10 })
          .backgroundColor('#FFFFFF')
          .border({ width: { bottom: 1 }, color: '#E0E0E0' })

          // 嵌入重构的测试页面组件
          Column() {
            // FIMMVPTestPage()
          }
          .layoutWeight(1)
          .width('100%')
        }
        .width('100%')
        .height('100%')
        .backgroundColor('#F5F5F5')

      } else if (this.currentView === 'fim_test_adapted') {
        // FIM 适配版测试界面 - 使用 fliggy_im.har 适配的测试组件
        Column() {
          FIMTestComponent({
            onBack: () => {
              this.currentView = 'diagnostic';
            }
          })
        }
        .width('100%')
        .height('100%')
        .backgroundColor('#F5F5F5')

      } else if (this.currentView === 'fim_engine_test') {
        // FIM Engine 测试界面 - 使用 TestFIMPage
        Column() {
          // 顶部导航栏
          Row() {
            Button('← 返回')
              .onClick(() => {
                this.currentView = 'diagnostic';
              })
              .backgroundColor(Color.Transparent)
              .fontColor('#007AFF')
              .fontSize(16)

            Text('FIM Engine 启动测试')
              .fontSize(18)
              .fontWeight(FontWeight.Bold)
              .layoutWeight(1)
              .textAlign(TextAlign.Center)
          }
          .width('100%')
          .justifyContent(FlexAlign.SpaceBetween)
          .padding({ left: 15, right: 15, top: 10, bottom: 10 })
          .backgroundColor('#FFFFFF')
          .border({ width: { bottom: 1 }, color: '#E0E0E0' })

          // 嵌入 TestFIMPage 组件
          // TestFIMPage()
          //   .layoutWeight(1)
        }
        .width('100%')
        .height('100%')
        .backgroundColor('#F5F5F5')
      }
    }
    .width('100%')
    .height('100%')
  }



  // 打开会话详情页面的方法
  private openConversationDetail(conversationId: string) {
    try {
      logger.d('MessageCenterDiagnosticWrapper', `Opening conversation detail: ${conversationId}`);
      // TODO: 这里需要调用 tbmessage 的会话详情页面
      // 需要研究 tbmessage 的 API 调用方式
    } catch (e) {
      logger.e('MessageCenterDiagnosticWrapper', 'Failed to open conversation detail: ' + JSON.stringify(e));
    }
  }
}

// MessageCenterPage 占位符组件，用于通过 navigator.openPage 打开消息中心
@Component
struct MessageCenterPlaceholder {
  pageContext?: PageContext;
  @State hasNavigated: boolean = false;

  aboutToAppear(): void {
    this.pageContext?.bind(this);
  }

  onPageShow(): void {
    logger.d('MessageCenterPlaceholder', 'onPageShow');
    // 当 tab 显示时，自动打开 MessageCenterPage
    if (!this.hasNavigated) {
      this.hasNavigated = true;
      setTimeout(() => {
        navigator.openPage(this.pageContext, {
          pageUrl: "page://message_center_home",
        });
      }, 100); // 延迟一点确保页面完全加载
    }
  }

  onPageHide(): void {
    logger.d('MessageCenterPlaceholder', 'onPageHide');
  }

  build() {
    Column() {
      Text('正在打开消息中心...')
        .fontSize(16)
        .fontColor(Color.Gray)
        .textAlign(TextAlign.Center)
        .width('100%')
        .height('100%')
        .backgroundColor(Color.White)

      Button('手动打开消息中心')
        .onClick(() => {
          navigator.openPage(this.pageContext, {
            pageUrl: "page://message_center_home",
          });
        })
        .margin({ top: 20 })
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
  }
}




export class MainTabsController extends TripTabsController {
  onTabSelected(index: number) {
    let tabContext = this.listeners.get(index);
    if (tabContext && tabContext.pageObject && tabContext.pageObject['onTabSelected']) {
      tabContext.pageObject['onTabSelected']();
    }
  }

  onTabUnselected(index: number) {
    let tabContext = this.listeners.get(index);
    if (tabContext && tabContext.pageObject && tabContext.pageObject['onTabUnselected']) {
      tabContext.pageObject['onTabUnselected']();
    }
  }

  onTabReselected(index: number) {
    let tabContext = this.listeners.get(index);
    if (tabContext && tabContext.pageObject && tabContext.pageObject['onTabReselected']) {
      tabContext.pageObject['onTabReselected']();
    }
  }
}

let storage = LocalStorage.getShared();

@Entry(storage)
@Component
export struct MainPage {
  pageContext?: PageContext;
  @StorageLink("bottomRectHeight") bottomRectHeight: number = 0

  private tabBarConfigs: TabData[] = DataModel.NAVIGATION_TABS; // bottom navigation data.
  @State currentIndex: number = 0;
  private lastClickIndex: number = 0;
  // private controller: TabsController = new TabsController();
  tabsController: MainTabsController = new MainTabsController();
  private deviceEnv: EnvConstant = env.getEnvironmentName()
  @State loginInterceptIndex: number = -1;
  @StorageLink('navigation_mode') navigationMode: NavigationMode = NavigationMode.Stack;
  @State foldStatus: display.FoldStatus = display.FoldStatus.FOLD_STATUS_UNKNOWN;
  private foldStatusCallBack :Callback<display.FoldStatus> = (data: display.FoldStatus) => {}
  private lastBackTime: number = 0
  private firstShowIndex: number = 0;

  build() {
    Flex({
      direction: FlexDirection.Column,
    }) {
      Tabs({ index: 0, controller: this.tabsController }) {
        ForEach(this.tabBarConfigs, (item: TabData, index?: number) => {
          TabContent() {
            if (index === HOME) {
              FliggyHomePage({
                pageContext: new TabContext("page://home", new Object(), this.tabsController, index),
              })
            } else if (index == 1) {
              TripFlutterPage({
                pageContext: new TabContext("page://destination_home", new Object(), this.tabsController, index),
                flutterParams: {
                  "uri": '/fliggy_flutter_community/home',
                  "params":{
                    "__flutter_arguments__":"{\"un_flutter\":\"true\",\"isTab\":\"true\"}",
                  }
                }
              })
            } else if (index == 2) {
              // 1. 添加诊断包装器来检查 MessageCenterPage 的状态
              MessageCenterDiagnosticWrapper({
                pageContext: new TabContext("page://message_center_home", new Object(), this.tabsController, index),
              })

              //  2. 原实现
              // TripFlutterPage({
              //   pageContext: new TabContext("page://message_center_home", new Object(), this.tabsController, index),
              //   flutterParams: {
              //     "uri": "/fliggy_message_center/home",
              //     "params": {
              //       "__flutter_arguments__": "{\"un_flutter\":\"true\",\"isTab\":\"true\"}"
              //     }
              //   }
              // })

            } else if (index == 3) {
              TabWebViewPage({
                pageContext: new TabContext("page://journey_home", new Object(), this.tabsController, index),
                url: (this.deviceEnv === EnvConstant.RELEASE) ?
                  "https://outfliggys.m.taobao.com/app/trip/rx-journey-ssr/pages/home?titleBarHidden=2&disableNav=YES&noBack=true&webViewBackgroundColor=f2f3f5" :
                  "https://outfliggys.wapa.taobao.com/app/trip/rx-journey-ssr/pages/home?titleBarHidden=2&disableNav=YES&noBack=true&webViewBackgroundColor=f2f3f5",
              })
            } else if (index == 4) {
              TripFlutterPage({
                pageContext: new TabContext("page://usercenter_home", new Object(), this.tabsController, index),
                flutterParams: {
                  "uri": '/fliggy_user_center/user_center',
                  "params":{
                    "__flutter_arguments__":"{}"
                  }
                }
              })
            }
          }
        }, (item: TabData) => JSON.stringify(item))
      }
      .barMode(BarMode.Fixed)
      .animationDuration(0)
      .layoutWeight(1)
      .barHeight(0)
      .scrollable(false)
      .onChange((index: number) => {
        logger.d('onChange', "index:" + index);
        if (index != this.currentIndex) {
          const from = this.tabsController.listeners.get(this.currentIndex);
          const to = this.tabsController.listeners.get(index);

          // 添加空值检查，避免 trackFragmentChange 错误
          if (from && to) {
            tracker.trackFragmentChange(from, to);
          } else {
            logger.w('onChange', `trackFragmentChange skipped: from=${from}, to=${to}, currentIndex=${this.currentIndex}, targetIndex=${index}`);
          }
        }
        this.tabsController.onPageHide(this.currentIndex);
        this.tabsController.onPageShow(index);
        this.tabsController.onTabSelected(index);
        this.tabsController.onTabUnselected(this.currentIndex);
        this.currentIndex = index;
      })

      BottomTabBar({
        onTabClick: (index: number) => {
          logger.d('onTabClick', "index:" + index);
          if (!login.hasLogin() && this.isNeedLoginIndex(index)) {
            logger.d('onclick', "to login page:" + index);
            this.loginInterceptIndex = index;
            login.login(true);
            return;
          }
          if (index == this.currentIndex) {
            this.tabsController.onTabReselected(index)
          }
          this.changeIndex(index);
        },
        tabBarConfigs: this.tabBarConfigs,
        navCurrentPosition: this.currentIndex,
      })
    }
    .padding({bottom: px2vp(this.bottomRectHeight)})
  }

  changeIndex(index:number, navigationMode: NavigationMode = this.navigationMode) {
    this.lastClickIndex = index;
    if(navigationMode === NavigationMode.Split && WindowUtils.isTwoColumn()) {
      if(index === 1) {
        navigator.openPage(this.pageContext, {
          pageUrl: "page://flutter_view/fliggy_flutter_community/home?_fli_unify=false",
        });
      } else if (index == 2) {
        // 修改为与普通模式保持一致，使用 MessageCenterPage
        // 如果 MessageCenterPage 不支持分屏模式，可以考虑其他方案
        this.tabsController.changeIndex(index);
      } else if(index === 3) {
        navigator.openPage(this.pageContext, {
          pageUrl: (this.deviceEnv === EnvConstant.RELEASE) ?
            "https://outfliggys.m.taobao.com/app/trip/rx-journey-ssr/pages/home?titleBarHidden=2&disableNav=YES&noBack=true&webViewBackgroundColor=f2f3f5&_fli_unify=false" :
            "https://outfliggys.wapa.taobao.com/app/trip/rx-journey-ssr/pages/home?titleBarHidden=2&disableNav=YES&noBack=true&webViewBackgroundColor=f2f3f5&_fli_unify=false",
        });
      } else if(index === 4) {
        navigator.openPage(this.pageContext, {
          pageUrl: "page://flutter_view/fliggy_user_center/user_center?_fli_unify=false",
        });
      } else {
        this.tabsController.changeIndex(index);
      }
    } else {
      this.tabsController.changeIndex(index);
    }
  }

  foldOrNavModeStatusCallback(status : display.FoldStatus = this.foldStatus, navigationMode: NavigationMode = this.navigationMode){
    logger.d('foldOrNavModeStatusCallback', `status: ${status} navigationMode: ${navigationMode} ScreenWidth: ${FBDocumentAssistantHook.getScreenWidth()}`);
    // 折叠打开情况，判是分屏的模式
    if (WindowUtils.isTwoColumn() && navigationMode === NavigationMode.Split) {
      let lastIndex : number = this.currentIndex
      logger.d('foldOrNavModeStatusCallback', `this typeof: ${typeof this}  index: ${lastIndex}`);
      WindowUtils.getWindowStage()?.getMainWindowSync()?.setPreferredOrientation(window.Orientation.AUTO_ROTATION_UNSPECIFIED);
      if(lastIndex != 0) {
        this.changeIndex(0, navigationMode)
        const topIsHome = RunningPageStack.getTopPageContext()?.pageUrl?.getHost() === "home_main"
        this.changeIndex(lastIndex, navigationMode)
        // 处理打开多个页面后，折叠屏展开页面栈不对的情况
        if(!topIsHome && lastIndex !== 0) {
          let mainIndex = navigator.findPageIndex('page://home_main')
          const size = FliggyNavigator.getDefaultRouter().size();
          logger.d('foldOrNavModeStatusCallback', "topIsHome:" + topIsHome + " index:" + lastIndex + " mainIndex:" + mainIndex + " size:" + size);
          if (mainIndex !== undefined && mainIndex < size - 2) {
            logger.d('foldOrNavModeStatusCallback', "top index params:" + this.paramsToString(size - 1));
            for (let index = mainIndex + 1; index < size - 1; index++) {
              if((FliggyNavigator.getDefaultRouter().getParamByIndex(mainIndex + 1) as object)?.['_fli_fold_right_first_page'] !== true){
                logger.d('foldOrNavModeStatusCallback', "moveIndexToTop index params:" + this.paramsToString(mainIndex + 1));
                FliggyNavigator.getDefaultRouter().moveIndexToTop(mainIndex + 1, false);
              } else {
                logger.d('foldOrNavModeStatusCallback', "skip moveIndexToTop index params:" + this.paramsToString(mainIndex + 1));
                mainIndex ++;
              }
            }
          }
        }
      }
    } else if (navigationMode === NavigationMode.Stack && DeviceHelper.isFold() && display.getFoldStatus() === display.FoldStatus.FOLD_STATUS_FOLDED) {
        // 折叠屏折叠起来的情况，处理从折叠屏展开到收起后底部tab展示为页面的情况
      const topIsHome = RunningPageStack.getTopPageContext()?.pageUrl?.getHost() === "home_main"
      if(!topIsHome) {
        logger.d('foldOrNavModeStatusCallback', ` fold lastClickIndex: ${this.lastClickIndex}`);
        const mainIndex = navigator.findPageIndex('page://home_main')
        const size = FliggyNavigator.getDefaultRouter().size();
        if (mainIndex !== undefined && mainIndex + 1 < size && this.lastClickIndex > 0) {
          logger.d('foldOrNavModeStatusCallback', "top index params:" + this.paramsToString(size - 1));
          if((FliggyNavigator.getDefaultRouter().getParamByIndex(mainIndex + 1) as object)?.['_fli_fold_right_first_page'] !== true){
            logger.d('foldOrNavModeStatusCallback', ` removeByIndexes index params: ${this.paramsToString(mainIndex + 1)}`);
            // 存在首页的tab页，则删除, 不存在说明用户已经点击过左边首页的其他页面，lastClickIndex修改为0
            if(this.isHomeTabPage(FliggyNavigator.getDefaultRouter().getParamByIndex(mainIndex + 1) as object)) {
              FliggyNavigator.getDefaultRouter().removeByIndexes([mainIndex + 1]);
            } else {
              this.lastClickIndex = 0;
            }
            // 调用后如果tab是flutter的并且最上面的页面也是flutter的，那么会抢占flutter的engin，导致上面的flutter页面不可交互
            this.changeIndex(this.lastClickIndex, navigationMode)
            // 补偿逻辑，在调用一下onPageShow
            const topPageContext : PageContext | undefined = RunningPageStack.getTopPageContext();
            if(topPageContext?.pageUrl?.getHost() !== "home_main" && topPageContext.pageUrl.getHost() === 'flutter_view') {
              setTimeout(() => {
                const topPageContext : PageContext | undefined = RunningPageStack.getTopPageContext();
                if(topPageContext?.pageUrl?.getHost() !== "home_main" && topPageContext.pageUrl.getHost() === 'flutter_view') {
                  topPageContext?.onPageShow()
                  // UILifecycleManager.getInstance().onPageShow(topPageContext);
                }
              }, 200)
            }
          }
        }
      }
    } else {
      WindowUtils.getWindowStage()?.getMainWindowSync()?.setPreferredOrientation(window.Orientation.PORTRAIT);
    }
  }

  private isHomeTabPage(params?: object) : boolean {
    let url = params?.['url'] as string
    if(url && (url.startsWith('page://flutter_view/fliggy_destination_new/destination_tab_page')
    || url.startsWith('page://flutter_view/fliggy_message_center/home')
    || url.startsWith('https://outfliggys.m.taobao.com/app/trip/rx-journey-ssr/pages/home')
    || url.startsWith('https://outfliggys.wapa.taobao.com/app/trip/rx-journey-ssr/pages/home')
    || url.startsWith('page://flutter_view/fliggy_user_center/user_center'))) {
      return true
    }
    return false
  }

  private paramsToString(index: number) : string {
    try {
      let paramStr = JSON.stringify(FliggyNavigator.getDefaultRouter().getParamByIndex(index));
      return paramStr
    } catch (e) {
      logger.d('foldOrNavModeStatusCallback', 'Failed to stringify. Code: '+JSON.stringify(e));
    }
    return ''
  }

  // 组件生命周期
  aboutToAppear() {
    this.pageContext?.bind(this);

    // 为 MessageCenterPage (index=2) 手动注册 TabContext
    // 因为 MessageCenterPage 不接受 pageContext 参数，需要手动注册以避免 trackFragmentChange 中的 undefined 错误
    const messageCenterTabContext = new TabContext("page://message_center_home", new Object(), this.tabsController, 2);
    this.tabsController.listeners.set(2, messageCenterTabContext);

    // 处理第一次需要展示的tab
    let tabTag = this.pageContext?.params["tab_page"] as string;
    if (tabTag) {
      this.tabBarConfigs.forEach((item: TabData) => {
        if (item.tag_name === tabTag) {
          this.currentIndex = item.num;
        }
      })
    }
    this.firstShowIndex = this.currentIndex
    logger.d('aboutToAppear', "index:" + this.currentIndex);
    logger.d('aboutToAppear', `setPreferredOrientation: ${WindowUtils.isTwoColumn()} ${this.navigationMode}`);
    if (WindowUtils.isTwoColumn() && this.navigationMode === NavigationMode.Split) {
      WindowUtils.getWindowStage()?.getMainWindowSync()?.setPreferredOrientation(window.Orientation.AUTO_ROTATION_UNSPECIFIED);
    }
    try {
      this.foldStatus = display.getFoldStatus()
      this.foldStatusCallBack = (status: display.FoldStatus) => {
        this.foldStatus = status
        this.foldOrNavModeStatusCallback(status)
      };
      display.on('foldStatusChange', this.foldStatusCallBack);
    } catch (exception) {
      logger.e('aboutToAppear', 'Failed to register callback. Code: '+JSON.stringify(exception));
    }
    setOnNavigationModeChangeCallback((navigationMode: NavigationMode) => {
      this.navigationMode = navigationMode;
      this.foldOrNavModeStatusCallback(this.foldStatus, navigationMode);
    });

    //进入首页后，删掉首页之外的页面
    if(RunningPageStack.getPageStack().length > 1) {
      try {
        let prePageContext = RunningPageStack.getPageStack().get(0);
        if(prePageContext) {
          if(prePageContext.isShown) {
            logger.e('aboutToAppear', 'hide pre page.: '+prePageContext?.pageUrl);
            prePageContext.onPageHide()
            UILifecycleManager.getInstance().onPageHide(prePageContext);
          }
          logger.e('aboutToAppear', 'remove pre page.: '+prePageContext?.pageUrl);
          FliggyNavigator.getDefaultRouter().removeByIndexes([navigator.findPageIndex(prePageContext)])
        }
      } catch (exception) {
        logger.e('aboutToAppear', exception);
      }
    }
  }

  // 组件生命周期
  aboutToDisappear() {
    try {
      display.off('foldStatusChange', this.foldStatusCallBack);
    } catch (exception) {
      logger.e('aboutToDisappear', 'Failed to unregister callback. Code: '+JSON.stringify(exception));
    }
    logger.d('aboutToDisappear', "index:" + this.currentIndex);
  }

  // 只有被@Entry装饰的组件才可以调用页面的生命周期
  onPageShow() {
    PushNotification.resetBadge();
    let hasLogin = login.hasLogin();
    if (!hasLogin && this.isNeedLoginIndex(this.currentIndex)) {
      this.loginInterceptIndex = -1;
      this.changeIndex(0);
      return;
    }
    if (hasLogin && this.loginInterceptIndex >= 0) {
      this.changeIndex(this.loginInterceptIndex);
      this.loginInterceptIndex = -1;
      logger.d('onPageShow', "login redirect index:" + this.loginInterceptIndex);
      return;
    }
    this.loginInterceptIndex = -1;
    logger.d('onPageShow', "index:" + this.currentIndex);
    // 处理第一次进来首页的时候的页面埋点
    if (this.firstShowIndex >= 0) {
      const firstPage = this.tabsController.listeners.get(this.firstShowIndex);
      const mainPage = this.pageContext;
      if(firstPage && mainPage){
        const mainPageUri = mainPage.pageUrl;
        let firstUri = firstPage.pageUrl.buildUpon();
        mainPageUri.getQueryParameterNames().forEach((key) => {
          firstUri.appendQueryParameter(key, mainPageUri.getQueryParameter(key));
        })
        firstPage.pageUrl = firstUri.build();
        firstPage.params = mainPage.params
      }
    }
    this.firstShowIndex = -1;
    this.tabsController.onPageShow(this.currentIndex);
    this.tabsController.onTabSelected(this.currentIndex);
  }

  // 只有被@Entry装饰的组件才可以调用页面的生命周期
  onPageHide() {
    logger.d('onPageHide', "index:" + this.currentIndex);
    this.tabsController.onPageHide(this.currentIndex);
  }

  onGotoDataReceived(data: object) {
    let tabTag = data["tab_page"] as string;
    if (tabTag) {
      this.tabBarConfigs.forEach((item: TabData) => {
        if (item.tag_name === tabTag) {
          this.changeIndex(item.num);
        }
      })
    }
  }

  onBackPress(): boolean {
    if(this.currentIndex != 0) {
      this.changeIndex(0);
      return true;
    }
    const currentTime = Date.now();
    if (currentTime- this.lastBackTime < 1500) {
      const windowStage = AppStorage.get<window.WindowStage>('entryWindowStage');
      if (windowStage !== undefined) {
        try {
          logger.d('onBackPress', 'MainPage hideWithAnimation');
          windowStage.getMainWindowSync().minimize();
        } catch (e) {
          logger.e('onBackPress', e);
          this.lastBackTime = currentTime;
          this.existApp();
          logger.e('onBackPress', 'MainPage existApp');
          return false;
        }
        this.lastBackTime = 0;
        return true;
      }
    } else {
      this.lastBackTime = currentTime;
      promptAction.showToast({message: '再按一次退出'});
      return true;
    }
    return true;
  }

  existApp(){
    new process.ProcessManager().exit(0);
  }

  isNeedLoginIndex(index:number):boolean{
    return index >= 2;
  }

  skipTrack(): boolean {
    return true;
  }
}
