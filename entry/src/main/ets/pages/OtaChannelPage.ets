import { PageContext } from '@fliggy-ohos/router';
import { TitleBar } from '@fliggy-ohos/titlebar';
import { env } from '@fliggy-ohos/env';
import { Logger } from '@fliggy-ohos/logger';
import { isEmpty } from '@fliggy-ohos/commonutils';
import { bundleManager } from '@kit.AbilityKit';

const logger = new Logger('OtaChannelPage');

@Entry
@Component
export struct OtaChannelPage {
  pageContext?: PageContext;

  private realChannelId = "";
  private originalChanelId = "";
  private systemPropertyChanelId = "";
  private installSource = "";

  aboutToAppear(): void {
    this.pageContext?.bind(this);
    let ttid = env.getTtid();
    if (ttid && ttid.includes('@')) {
      let parts = ttid.split('@');
      if (parts && parts.length > 0) {
        ttid = parts[0];
      }
    }
    this.realChannelId = ttid;
    this.originalChanelId = env.getOriginalChanelId();
    this.systemPropertyChanelId = env.getHuaweiOTAChannelId();
    if (isEmpty(this.systemPropertyChanelId)) {
      this.systemPropertyChanelId = "无系统属性配置"
    }

    let bundleInfo = bundleManager.getBundleInfoForSelfSync(bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_APPLICATION);
    this.installSource = bundleInfo.appInfo.installSource;
  }

  onPageShow() {
    logger.d('OtaChannelPage', 'onPageShow');
  }

  // 只有被@Entry装饰的组件才可以调用页面的生命周期
  onPageHide() {
    logger.d('OtaChannelPage', 'onPageHide');
  }

  build() {
    Flex({
      direction: FlexDirection.Column
    }) {
      TitleBar({
        showLeftItem: false,
        title: "渠道号防覆盖信息",
      })
      Column() {
        Text("防覆盖后的渠道号: " + this.realChannelId).margin({ top: 10, left: 10 })
        Text("安装包原始渠道号: " + this.originalChanelId).margin({ top: 10, left: 10 })
        Text("系统属性的渠道号: " + this.systemPropertyChanelId).margin({ top: 10, left: 10 })
        Text("安装来源: " + this.installSource).margin({ top: 10, left: 10 })
      }.alignItems(HorizontalAlign.Start)
    }
  }
}
