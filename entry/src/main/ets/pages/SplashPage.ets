import { f_isNotEmpty, splashDataModel, SplashResourceType } from '@fliggy-ohos/homepage';
import media from '@ohos.multimedia.media';
import { <PERSON><PERSON>, navigator, PageContext } from '@fliggy-ohos/router';
import { CommonConstants } from '../constants/CommonConstants';
import { BusinessError } from '@ohos.base';
import { Logger } from '@fliggy-ohos/logger';
import { Fplayer, FPlayerController, VideoUrlType } from '@fliggy-ohos/player';
import { display } from '@kit.ArkUI';
import { Globals } from '@taobao-ohos/taobao_runtime';
import { f_isEmpty } from '@fliggy-ohos/homepage/src/main/ets/splash/utils/SafeTypeUtil';
import { SplitPolicy } from '@ohos/multinavigation';
import { ExposeData } from '@fliggy-ohos/homepage';
import { AlimamaAdvertising } from '@taobao-ohos/munion_harmony';

const TAG = 'SplashPage';
const logger: Logger = new Logger("[Splash]")

@Entry
@Component
export struct SplashPage {
  pageContext?: PageContext;
  private controller: FPlayerController | null = null;
  private timeOut: number = splashDataModel?.mFirstOfflineResourceModel?.creative?.showTime?.value ?? 0;
  private intervalID: number = -1;
  private hasAction = false;

  @State leftTimeSecond: number = this.timeOut === 0 ? CommonConstants.DELAY_SECONDS : this.timeOut;

  aboutToAppear(): void {
    this.pageContext?.bind(this);
  }

  getPageSpmCnt(): string {
    return '181.11187363.0.0'
  }

  onPageShow(): void {
    logger.d(TAG, 'onPageShow called!' + JSON.stringify(splashDataModel.mFirstOfflineResourceModel))

    // 倒计时
    this.intervalID = setInterval(() => {
      if (this.leftTimeSecond > 0) {
        this.leftTimeSecond -= 1;
      }
      // 倒计时结束
      if (this.leftTimeSecond == 0 && !this.hasAction) {
        this.hasAction = true;
        clearInterval(this.intervalID);
        gotoHomePage(this.pageContext!);
      }
    }, CommonConstants.INTERVAL_DELAY);
  }

  onPageHide() {
    logger.d(TAG, 'onPageHide setTimeout!')
    clearInterval(this.intervalID);
  }

  onBackPress(): boolean {
    logger.d(TAG, 'onBackPressed called, not enable in splash!');
    return true;
  }

  pageTransition() {
    PageTransitionEnter({ type: RouteType.None, duration: 0 })
    PageTransitionExit({ type: RouteType.None, duration: 0 })
  }

  // 页面构建
  build() {
    Stack({ alignContent: Alignment.TopStart }) {
      // 图片和背景
      this._buildBackgroundView()
      // 内容
      Column() {

        Row() {
          // 左上角logo
          Image($r('app.media.ic_fliggy_splash_logo_left_top_corner'))
            .width(80)
            .height(22)
            .objectFit(ImageFit.Contain)
            .margin({ top: 3,
              left: 12,
            })
          .visibility(splashDataModel.hideLeftTopLogo ?  Visibility.None : Visibility.Visible)

          // 上部分: 倒计时按钮
          Flex({
            direction: FlexDirection.Row,
            justifyContent: FlexAlign.End
          }) {
            CountDownButton({ pageContext: this.pageContext, secondsCount: this.leftTimeSecond })
              .onClick(() => {
                if(this.pageContext) {
                  this.hasAction = true;
                  gotoHomePage(this.pageContext);
                }
              })
          }
          .margin({
            right:0,
          })
          .layoutWeight(1)
        }
        .padding({
          top: (Globals.getStatusBarHeightNumber() + 32)+ "px",
        })
        .alignItems(VerticalAlign.Center)

        // 下部分:闪屏跳转按钮
        if (f_isNotEmpty(splashDataModel.mFirstOfflineResourceModel?.splashResourceJumpUrl)) {
          SplashJumpButton({
            pageContext: this.pageContext,
            text: splashDataModel.mFirstOfflineResourceModel?.splashButtonName,
            jumpUrl: splashDataModel.mFirstOfflineResourceModel?.splashResourceJumpUrl,
          })
            .onClick(() => {
              if(this.pageContext) {
                this.hasAction = true;
                _gotoPage(this.pageContext, splashDataModel.mFirstOfflineResourceModel?.splashResourceJumpUrl); // 跳转详情
              }
            })
            .margin({
              bottom: 139.5,
            })
        }
      }
      .justifyContent(FlexAlign.SpaceBetween)
      .onVisibleAreaChange([0, 0.1, 0.6, 1.0], (isVisible: boolean, currentRatio: number) => {
        if (isVisible && currentRatio >= 0.6) {
          let exposeData: ExposeData | undefined = splashDataModel?.mFirstOfflineResourceModel?.exposeData;
          if (exposeData && exposeData.ifs) {
            let ifs = exposeData.ifs;
            try {
              AlimamaAdvertising.getInstance()
                .buildIfsExposure(getContext(undefined).getApplicationContext(), ifs)
                .withArgPid(exposeData.pid ?? "")
                .withArgNamespace(exposeData.nameSpace ?? "")
                .commit();
              logger.d(TAG, `banner mamaExposeData，index：${splashDataModel.mFirstOfflineResourceModel?.creativeId}`);
            } catch (e) {
              logger.e(TAG, `banner mamaExposeData error`);
            }
          }
        }
      })
      .width('100%')
      .height('100%')
    }
    .width('100%')
    .height('100%')
  }

  @Builder
  _buildBackgroundView() {
    // 图片和背景
    if (f_isNotEmpty(splashDataModel.imageCachePath)) {
      Stack({ alignContent: Alignment.TopStart }) {
        // 闪屏-图片
        Image(splashDataModel.imageCachePath)
          .width('100%')
          .height('100%')
        .gesture(
          TapGesture({
            count: 1,
          }).onAction((event) => {
            this.onSplashResourceClicked(SplashResourceType.cacheImage);
          })
        );
        // 图片-底部白色 logo bar
        Column() {
          Image($r('app.media.ic_fliggy_splash_logo'))
            .width(274/2)
            .height(72/2)
            .objectFit(ImageFit.Contain);
          }
          .backgroundColor('white')
          .width('100%')
          .height(112)
          .justifyContent(FlexAlign.Center)
          .alignItems(HorizontalAlign.Center)
          .margin({
            top: px2vp(display.getDefaultDisplaySync().height) - 112,
          })
          .visibility(splashDataModel.hideFliggyLogo ?  Visibility.None : Visibility.Visible)
        }
        .width('100%')
        .height('100%')
    }

    // 闪屏-视屏
    if (f_isNotEmpty(splashDataModel.videoCachePath)) {
      Fplayer({
        urlType: VideoUrlType.LocalVideoUrl,
        videoUrl: splashDataModel.videoCachePath,
        controller: new FPlayerController(),
        autoPlay: true,
        loop: false,
        videoScaleType: media.VideoScaleType.VIDEO_SCALE_TYPE_FIT_CROP,
        showEndImg: false,
        onPrepared: (controller) => {
          this.controller = controller;
        },
        onStart() {
          logger.d(TAG, 'play onStart:');
        },
        onPlay() {
          logger.d(TAG, 'play onPlay:');
        },
        onPause() {
          logger.d(TAG, 'play onPause:');
        },
        onComplete() {
          logger.d(TAG, 'play onComplete:');
        },
        onError(error: BusinessError) {
          logger.e(TAG, 'play error: ' + error);
        },
        onDurationUpdate(currentTime: Number, allTime: Number) {
          // logger.d(TAG, 'play currentTime: ' + currentTime + 'allTime' + allTime);
        },
        onStartRenderFrame() {
        }
      })
        .width ('100%')
        .height('100%')
        .gesture(
          TapGesture({
            count: 1,
          }).onAction((event) => {
            this.onSplashResourceClicked(SplashResourceType.cacheVideo);
          })
      );
    }
  }

  // 点击背景
  private onSplashResourceClicked(type: SplashResourceType) {
    if (f_isNotEmpty(splashDataModel.mFirstOfflineResourceModel?.splashResourceJumpUrl)) {
      return;
    }

    let jumpUrl = splashDataModel.mFirstOfflineResourceModel?.splashResourceJumpUrl;
    if (f_isNotEmpty(jumpUrl)) {
      navigator.openPage(this.pageContext, {
        pageUrl: jumpUrl,
        anim: Anim.push
      });
    }
    logger.d(TAG, 'click resource type: ' + type + ', resourceUrl: ' + splashDataModel.mFirstOfflineResourceModel?.splashResourceJumpUrl);
  }
}

// 倒计时按钮
@Component
struct CountDownButton {
  pageContext?: PageContext;
  @Prop secondsCount: number = 0;

  build() {
      Row() {
        // count
        Text(this.secondsCount.toString())
          .fontColor('rgb(255, 195, 48)')
          .fontSize(14)
          .margin({
            left: 14,
          })
          .textAlign(TextAlign.Start)
        // 跳转文案
        Text($r('app.string.skip'))
          .fontColor(Color.White)
          .fontSize(13)
          .textAlign(TextAlign.Start)
          .margin({
            left: 8,
            right: 2,
          })
      }
      .width($r('app.float.skip_text_width'))
      .height($r('app.float.skip_text_height'))
      .borderRadius(CommonConstants.SKIP_BUTTON_RADIUS)
      .backgroundColor('rgba(0, 0, 0, 0.5)')
      .margin({
        right: $r('app.float.skip_text_margin_right'),
        top: $r('app.float.skip_text_margin_top')
      })
  }
}

// 闪屏跳转按钮
@Component
struct SplashJumpButton {
  pageContext?: PageContext;
  @Prop jumpUrl: string = CommonConstants.MAIN_PAGE_URL;
  @Prop text: string = '点击跳转详情页面';
  @Prop screenWidth: number = px2vp(display.getDefaultDisplaySync().width);
  @Prop screenHeight: number = px2vp(display.getDefaultDisplaySync().height);
  @Prop buttonWidth: number = this.screenWidth * 0.8;
  @Prop buttonHeight: number = this.buttonWidth / 6;

  build() {
    Row() {
      Text(this.text)
        .fontColor(Color.White)
        .fontSize(17)
        .margin({
          left: 24,
        })
        .textAlign(TextAlign.Start)
      Image($r('app.media.ic_arrow_right'))
        .objectFit(ImageFit.Contain)
        .width(8).height(15)
        .margin({
          left: 10,
        })
    }
    .justifyContent(FlexAlign.Center)
    .alignItems(VerticalAlign.Center)
    .width(this.buttonWidth)
    .height(this.buttonHeight)
    .borderRadius(this.buttonHeight / 2)
    .borderWidth(2)
    .borderColor('rgba(255, 255, 255, 0.6)')
    .backgroundColor('rgba(0, 0, 0, 0.6)')
  }
}

function gotoHomePage(pageContext: PageContext) {
  navigator.openPage(pageContext, {
    pageUrl: 'page://home_main',
    splitPolicy:SplitPolicy.HOME_PAGE,
  });
}

/// 调闪屏详情页面
function _gotoPage(pageContext: PageContext, pageUrl?: string) {
  if (f_isEmpty(pageUrl)) {
    logger.e(TAG, 'gotoPage pageUrl is empty!');
    return;
  }
  navigator.openPage(pageContext, {
    pageUrl: pageUrl,
    params: {
      '_fli_popBeforeOpen': true
    },
  });
}