import { FliggyNavigator, PageContext, ShellPage, TaoBaoShellPage } from '@fliggy-ohos/router';

import { SplashPage } from './SplashPage';
import { MainPage } from './MainPage';
import { ScanCodeIndex } from '@fliggy-ohos/scancode';
import { MultiNavigation, MultiNavPathStack, SplitPolicy } from '@ohos/multinavigation';
import { LaunchPage } from './LaunchPage';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { Callback } from '@ohos.base';
import { OnlineToolsDebugPage } from './OnlineToolsDebugPage';
import '../atom/InitAtom';
import { ezLongTake } from '@fliggy-ohos/router';
import { OCRPage } from '@fliggy-ohos/commonbiz';
import { GlobalWindowBuilder } from '@taobao-ohos/nav/src/main/ets/windows/GlobalWindows';
import { OtaChannelPage } from './OtaChannelPage';
import { MultiNavigationHook } from '../hook/MultiNavigationHook';
import { FliggyLivePage } from '@fliggy-ohos/fliggylive';

@Builder
function buildLauncherPage(pageContext: PageContext) {
  LaunchPage({ pageContext: pageContext });
}

@Builder
function buildSplashPage(pageContext: PageContext) {
  SplashPage({ pageContext: pageContext });
}

@Builder
function buildMainPage(pageContext: PageContext) {
  MainPage({ pageContext: pageContext });
}

@Builder
function buildScanPage(pageContext: PageContext) {
  ScanCodeIndex({ pageContext: pageContext });
}

@Builder
function buildOCRPage(pageContext: PageContext) {
  OCRPage({ pageContext: pageContext });
}

@Builder
function buildOnlineToolsPage(pageContext: PageContext) {
  OnlineToolsDebugPage({ pageContext: pageContext });
}

@Builder
function buildOtaChannelPage(pageContext: PageContext) {
  OtaChannelPage({ pageContext: pageContext });
}

@Builder
function buildFliggyLivePage(pageContext: PageContext) {
  FliggyLivePage({ pageContext: pageContext });
}

export let onNavigationModeChangeCallback: Callback<NavigationMode> | undefined = undefined
export function setOnNavigationModeChangeCallback(callback: Callback<NavigationMode>) {
  onNavigationModeChangeCallback = callback;
}

/*
 * 启动页面，承载启动图的展示
 * */
@Entry
@Component
export struct LaunchIndexPage {
  pageContext = new PageContext("page://launcher_index", new Object());
  @State defaultRouter: NavPathStack = new NavPathStack();
  @State isEnabled: boolean = true;
  @StorageProp("globalWindowBuilderMap") globalWindowBuilderSet: GlobalWindowBuilder[] = [];

  aboutToAppear(): void {

    FliggyNavigator.registerBuilder("launcher", wrapBuilder(buildLauncherPage));
    FliggyNavigator.registerBuilder("splash", wrapBuilder(buildSplashPage));
    FliggyNavigator.registerBuilder("home_main", wrapBuilder(buildMainPage));
    FliggyNavigator.registerBuilder("scan", wrapBuilder(buildScanPage));
    FliggyNavigator.registerBuilder("commbiz_ocr_scan", wrapBuilder(buildOCRPage));
    FliggyNavigator.registerBuilder("onlinetools_debug", wrapBuilder(buildOnlineToolsPage));
    FliggyNavigator.registerBuilder("ota_channel", wrapBuilder(buildOtaChannelPage));
    FliggyNavigator.registerBuilder("fliggy_live", wrapBuilder(buildFliggyLivePage));
    if(FliggyNavigator.isStraightProduct) {
      this.defaultRouter = new NavPathStack()
      FliggyNavigator.setDefaultRouter(this.defaultRouter);
      FliggyNavigator.getDefaultRouter().pushPath({name:"page://launcher",param: new Object()}, false);
    } else {
      this.defaultRouter = new MultiNavigationHook()
      FliggyNavigator.setDefaultRouter(this.defaultRouter);
      (FliggyNavigator.getDefaultRouter() as MultiNavPathStack).pushPath({name:"page://launcher",param: new Object()}, false, SplitPolicy.FULL_PAGE);
    }
  }

  // 打开淘宝的页面只能使用Nav打开，不能使用飞猪方式。
  @Builder
  routerMap(name: string, param?: object) {
    if (name == 'nav') {
      TaoBaoShellPage(param);
    } else {
      ShellPage({ pageContext: new PageContext(name, param ?? new Object()),});
    }
  };


  // 页面构建
  build() {
    Stack({ alignContent: Alignment.TopStart }) {
      if(FliggyNavigator.isStraightProduct) {
        Navigation(this.defaultRouter).navDestination(this.routerMap)
          .customNavContentTransition((from: NavContentInfo, to: NavContentInfo, operation: NavigationOperation) => {
            return ezLongTake.customNavContentTransition(from, to, operation, {
              onTransitionStart: () => { this.isEnabled = false; },
              onTransitionEnd: () => { this.isEnabled = true; },
            });
          })
          .hideNavBar(true)
          .enabled(this.isEnabled)
          .width("100%").height("100%");
      } else {
        MultiNavigation({
          navDestination: this.routerMap,
          multiStack: this.defaultRouter as MultiNavPathStack,
          onHomeShowInTop: (name) => {
            hilog.info(0x0000, 'LaunchIndexPage', 'index onHomeShowInTop name=' + name);
          },
          onNavigationModeChangeCallback: (mode) => {
            hilog.info(0x0000, 'LaunchIndexPage', 'index onNavigationModeChangeCallback mode=' + mode);
            AppStorage.setOrCreate("navigation_mode", mode);
            if (onNavigationModeChangeCallback) {
              onNavigationModeChangeCallback!(mode);
            }
          },
          isPortrait: true
        }).width("100%").height("100%");
      }
      ForEach(this.globalWindowBuilderSet, (item: GlobalWindowBuilder) => {
        // 全局浮层支持
        if (item.args) {
          item.builder?.builder(...item.args);
        } else {
          item.builder?.builder();
        }
      })
    }
  }

}
