import { Nav } from '@taobao-ohos/nav'
import uri from '@ohos.uri';
import { TestFIMPage } from './TestFIMPage';

//
// test();

@Entry
@Component
struct Index {
  @State message: string = '点击跳转你的页面';

  build() {
    Row() {
      Column() {
        Button(this.message)
          .fontSize(20)
          .fontWeight(FontWeight.Bold)
          .onClick(() => {
            Nav
              .from(this)
              .withExtras({ "bizData": this.message })
              .toPage(TestFIMPage)
          })
      }
      .width('100%')
    }
    .height('100%')
  }
}