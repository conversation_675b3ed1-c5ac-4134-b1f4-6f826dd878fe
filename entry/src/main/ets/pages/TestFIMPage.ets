// Copyright (c) 2025 Fliggy Team. All rights reserved.
// Created by guyan on 2025-08-21.
// FIM Engine Start Test Page - Engine启动配置测试页面 (迁移自portal项目)

import { fimsdk_Napi } from '@fliggy-ohos/fliggy_im';
import { fileIo } from '@kit.CoreFileKit';
import { hilog } from '@kit.PerformanceAnalysisKit';

const DOMAIN = 0x0000;

// 配置项接口定义
interface FIMEngineConfig {
  appId: string;
  appKey: string;
  appName: string;
  appVersion: string;
  deviceId: string;
  deviceName: string;
  deviceType: string;
  osName: string;
  osVersion: string;
  dataPath: string;
  timeZone: string;
  envType: string;
}

// 测试结果接口定义
interface TestResult {
  id: string;
  type: 'config' | 'start' | 'message' | 'auth' | 'event' | 'conversation' | 'search' | 'batch' | 'query' | 'operation';
  title: string;
  content: string;
  success: boolean;
  timestamp: string;
  expanded?: boolean;
}

// 测试项接口定义
interface TestItem {
  id: string;
  name: string;
  description: string;
  category: string;
  color: string;
  enabled: boolean;
  method: () => Promise<void>;
}


// 测试参数接口定义
interface CustomMessageData {
  message: string;
  timestamp: number;
}

interface CustomContent {
  type: string;
  data: CustomMessageData;
}

interface CustomMessageContent {
  content_type: number;
  custom_content: CustomContent;
}

interface CustomMessageParams {
  conversationId: string;
  content: CustomMessageContent;
}

interface StructMessageData {
  title: string;
  description: string;
  url: string;
  imageUrl: string;
}


@Entry
@Component
export struct TestFIMPage {
  @State testResults: TestResult[] = [];
  @State isRunning: boolean = false;
  @State currentOperation: string = '';
  @State engineStarted: boolean = false;
  @State autoStartCompleted: boolean = false;
  @State selectedCategory: string = 'all';
  @State runningBatchTest: boolean = false;
  @State batchTestProgress: string = '';

  // 测试项列表
  private testItems: TestItem[] = [];

  // 配置状态
  @State config: FIMEngineConfig = {
    appId: 'fliggy',
    appKey: 'f5337bdb5dea4868b6c310576aab039a', // 预发环境可用
    appName: 'fliggy',
    appVersion: '1.0.0',
    deviceId: 'test_utdid_67890',
    deviceName: 'test_device_12345',
    deviceType: 'harmony',
    osName: 'harmony',
    osVersion: '12.0.0',
    dataPath: '/data/storage/el2/base/haps/entry/files/fim',
    timeZone: 'Asia/Shanghai',
    envType: 'daily'
  };

  // 初始化测试项列表
  private initTestItems(): void {
    this.testItems = [
      // 认证相关
      { id: 'login', name: '登录认证', description: '测试用户登录认证功能', category: 'auth', color: '#FF6B6B', enabled: true, method: (): Promise<void> => this.testLogin() },

      // 事件监听相关
      { id: 'onMessageEvent', name: '消息事件监听', description: '测试消息变动事件监听', category: 'event', color: '#4ECDC4', enabled: true, method: (): Promise<void> => this.testOnMessageEvent() },
      { id: 'onConversationEvent', name: '会话事件监听', description: '测试会话变动事件监听', category: 'event', color: '#4ECDC4', enabled: true, method: (): Promise<void> => this.testOnConversationEvent() },
      { id: 'onGroupEvent', name: '群组事件监听', description: '测试群组事件变动监听', category: 'event', color: '#4ECDC4', enabled: true, method: (): Promise<void> => this.testOnGroupEvent() },

      // 消息发送相关
      { id: 'sendMessageText', name: '发送文本消息', description: '测试发送文本消息功能', category: 'message', color: '#45B7D1', enabled: true, method: (): Promise<void> => this.testSendTextMessage() },
      { id: 'sendMessageCustom', name: '发送自定义消息', description: '测试发送自定义消息功能', category: 'message', color: '#45B7D1', enabled: true, method: (): Promise<void> => this.testSendMessageCustom() },
      { id: 'sendMessageImage', name: '发送图片消息', description: '测试发送图片消息功能', category: 'message', color: '#45B7D1', enabled: true, method: (): Promise<void> => this.testSendMessageImage() },
      { id: 'sendMessageAt', name: '发送@消息', description: '测试发送@消息功能', category: 'message', color: '#45B7D1', enabled: true, method: (): Promise<void> => this.testSendMessageAt() },
      { id: 'sendMessageStruct', name: '发送结构化消息', description: '测试发送结构化消息功能', category: 'message', color: '#45B7D1', enabled: true, method: (): Promise<void> => this.testSendMessageStruct() },
      { id: 'sendMessageVideo', name: '发送视频消息', description: '测试发送视频消息功能', category: 'message', color: '#45B7D1', enabled: true, method: (): Promise<void> => this.testSendMessageVideo() },
      { id: 'replyMessage', name: '回复消息', description: '测试回复消息功能', category: 'message', color: '#45B7D1', enabled: true, method: (): Promise<void> => this.testReplyMessage() },
      { id: 'resendMessage', name: '重发消息', description: '测试重发消息功能', category: 'message', color: '#45B7D1', enabled: true, method: (): Promise<void> => this.testResendMessage() },

      // 消息查询相关
      { id: 'getMessage', name: '获取消息', description: '测试获取单条消息功能', category: 'query', color: '#96CEB4', enabled: true, method: (): Promise<void> => this.testGetMessage() },
      { id: 'listMessages', name: '消息列表', description: '测试获取消息列表功能', category: 'query', color: '#96CEB4', enabled: true, method: (): Promise<void> => this.testListMessages() },
      { id: 'getLocalMessage', name: '获取本地消息', description: '测试获取本地消息功能', category: 'query', color: '#96CEB4', enabled: true, method: (): Promise<void> => this.testGetLocalMessage() },
      { id: 'listMessagesReadStatus', name: '消息已读状态', description: '测试获取消息已读状态', category: 'query', color: '#96CEB4', enabled: true, method: (): Promise<void> => this.testListMessagesReadStatus() },

      // 消息操作相关
      { id: 'deleteMessage', name: '删除消息', description: '测试删除消息功能', category: 'operation', color: '#FECA57', enabled: true, method: (): Promise<void> => this.testDeleteMessage() },
      { id: 'recallMessage', name: '撤回消息', description: '测试撤回消息功能', category: 'operation', color: '#FECA57', enabled: true, method: (): Promise<void> => this.testRecallMessage() },
      { id: 'deleteLocalMessage', name: '删除本地消息', description: '测试删除本地消息功能', category: 'operation', color: '#FECA57', enabled: true, method: (): Promise<void> => this.testDeleteLocalMessage() },
      { id: 'clearMessages', name: '清空消息', description: '测试清空聊天记录功能', category: 'operation', color: '#FECA57', enabled: true, method: (): Promise<void> => this.testClearMessages() },
      { id: 'updateMessageToRead', name: '设置已读', description: '测试设置消息已读功能', category: 'operation', color: '#FECA57', enabled: true, method: (): Promise<void> => this.testUpdateMessageToRead() },

      // 会话管理相关
      { id: 'getConversations', name: '会话列表', description: '测试获取会话列表功能', category: 'conversation', color: '#A8E6CF', enabled: true, method: (): Promise<void> => this.testGetConversations() },
      { id: 'activeConversation', name: '激活会话', description: '测试激活会话功能', category: 'conversation', color: '#A8E6CF', enabled: true, method: (): Promise<void> => this.testActiveConversation() },
      { id: 'getMuteStatus', name: '获取静音状态', description: '测试获取静音状态功能', category: 'conversation', color: '#A8E6CF', enabled: true, method: (): Promise<void> => this.testGetMuteStatus() },
      { id: 'createCustomSingleConversation', name: '创建自定义会话', description: '测试创建自定义单人会话', category: 'conversation', color: '#A8E6CF', enabled: true, method: (): Promise<void> => this.testCreateCustomSingleConversation() },
      { id: 'leaveGroup', name: '退群', description: '测试退群功能', category: 'conversation', color: '#A8E6CF', enabled: true, method: (): Promise<void> => this.testLeaveGroup() },

      // 搜索工具相关
      { id: 'getMyFuid', name: '获取FUID', description: '测试获取用户FUID功能', category: 'search', color: '#DDA0DD', enabled: true, method: (): Promise<void> => this.testGetMyFuid() },
      { id: 'searchChatContent', name: '搜索聊天内容', description: '测试搜索聊天内容功能', category: 'search', color: '#DDA0DD', enabled: true, method: (): Promise<void> => this.testSearchChatContent() },
      { id: 'getMediaLocalPath', name: '获取媒体路径', description: '测试获取媒体本地路径功能', category: 'search', color: '#DDA0DD', enabled: true, method: (): Promise<void> => this.testGetMediaLocalPath() }
    ];
  }

  // 页面生命周期 - 自动启动Engine
  aboutToAppear() {
    hilog.info(DOMAIN, 'TestFIMPage', 'Page aboutToAppear - starting auto initialization');
    // 初始化测试项
    this.initTestItems();
    // 延迟一点时间确保页面完全加载
    setTimeout(() => {
      this.autoStartEngine();
    }, 1000);
  }

  // 自动启动Engine（页面加载时）
  private async autoStartEngine() {
    if (this.autoStartCompleted || this.engineStarted || this.isRunning) return;

    hilog.info(DOMAIN, 'TestFIMPage', 'Auto starting FIM Engine...');
    this.autoStartCompleted = true;

    // 先自动配置
    await this.autoConfigureEngine();

    // 然后启动Engine
    await this.startFIMEngine();
  }



  onPageShow() {
    hilog.info(DOMAIN, 'TestFIMPage', 'onPageShow');
  }

  // 只有被@Entry装饰的组件才可以调用页面的生命周期
  onPageHide() {
    hilog.info(DOMAIN, 'TestFIMPage', 'onPageHide');
  }

  // 添加测试结果
  private addResult(type: 'config' | 'start' | 'message' | 'auth' | 'event' | 'conversation' | 'search' | 'batch' | 'query' | 'operation', title: string, content: string, success: boolean): void {
    const result: TestResult = {
      id: `${type}_${Date.now()}`,
      type: type,
      title: title,
      content: content,
      success: success,
      timestamp: new Date().toLocaleTimeString(),
      expanded: true // 默认展开
    };
    this.testResults.unshift(result);
  }

  // 切换结果展开状态
  private toggleResultExpanded(resultId: string) {
    const index = this.testResults.findIndex(result => result.id === resultId);
    if (index !== -1) {
      this.testResults[index].expanded = !this.testResults[index].expanded;
      // 触发状态更新
      this.testResults = [...this.testResults];
    }
  }

  // 自动配置Engine参数（页面加载时调用）
  private async autoConfigureEngine() {
    try {
      hilog.info(DOMAIN, 'TestFIMPage', 'Auto-configuring Engine parameters...');

      const fim = fimsdk_Napi;

      // 依次调用各个配置方法 (使用fimsdk_Napi的驼峰命名方法)
      await fim.setAppId(this.config.appId);
      fim.setAppKey(this.config.appKey);
      fim.setAppName(this.config.appName);
      fim.setAppVersion(this.config.appVersion);
      fim.setDeviceId(this.config.deviceId);
      fim.setDeviceName(this.config.deviceName);
      fim.setDeviceType(this.config.deviceType);
      fim.setOsName(this.config.osName);
      fim.setOsVersion(this.config.osVersion);

      // 创建数据目录
      try {
        if (!fileIo.accessSync(this.config.dataPath)) {
          fileIo.mkdirSync(this.config.dataPath, true);
          hilog.info(DOMAIN, 'TestFIMPage', `数据目录创建成功: ${this.config.dataPath}`);
        } else {
          hilog.info(DOMAIN, 'TestFIMPage', `数据目录已存在: ${this.config.dataPath}`);
        }
      } catch (error) {
        hilog.warn(DOMAIN, 'TestFIMPage', `数据目录创建失败: ${error}, 继续使用默认路径`);
      }

      fim.setDataPath(this.config.dataPath);

      this.addResult('config', '自动配置完成',
        `Engine参数已自动配置完成:\n` +
        `AppID: ${this.config.appId}\n` +
        `AppKey: ${this.config.appKey}\n` +
        `DeviceId: ${this.config.deviceId}\n` +
        `DataPath: ${this.config.dataPath}\n` +
        `EnvType: ${this.config.envType}`,
        true);

      hilog.info(DOMAIN, 'TestFIMPage', 'Auto-configuration completed successfully');

    } catch (error) {
      const errorMsg = `自动配置失败: ${error}`;
      hilog.error(DOMAIN, 'TestFIMPage', errorMsg);
      this.addResult('config', '自动配置失败', errorMsg, false);
    }
  }

  // 启动FIM Engine
  private async startFIMEngine() {
    if (this.isRunning) return;

    this.isRunning = true;
    this.currentOperation = '启动FIM Engine';

    try {
      hilog.info(DOMAIN, 'TestFIMPage', 'Starting FIM Engine...');

      this.addResult('start', 'Engine启动中',
        '正在启动FIM Engine...',
        true);

      let result: string;

      // 使用fliggy_im项目的SDK启动Engine
      const fim: ESObject = fimsdk_Napi;
      if (fim.fimEngineStart) {
        result = await fim.fimEngineStart() as string;
      } else {
        throw new Error('fimEngineStart 方法不可用，请检查SDK配置');
      }

      this.addResult('start', 'Engine启动成功',
        `FIM Engine已成功启动\n结果: ${result}`,
        true);

      this.engineStarted = true;
      hilog.info(DOMAIN, 'TestFIMPage', 'FIM Engine started successfully');

    } catch (error) {
      const errorMsg = `Engine启动失败: ${error}`;
      hilog.error(DOMAIN, 'TestFIMPage', errorMsg);
      this.addResult('start', 'Engine启动失败', errorMsg, false);
    } finally {
      this.isRunning = false;
      this.currentOperation = '';
    }
  }

  // 测试发送文本消息
  private async testSendTextMessage() {
    if (this.isRunning || !this.engineStarted) return;

    this.isRunning = true;
    this.currentOperation = '测试发送文本消息';

    try {
      hilog.info(DOMAIN, 'TestFIMPage', 'Testing send text message...');

      const testParams = JSON.stringify({
        conversationId: "test_conv_001",
        text: "测试消息内容",
        receivers: ["test_user_001"]
      });

      this.addResult('message', '发送文本消息测试中',
        `正在测试发送文本消息...\n参数: ${testParams}`,
        true);

      const result: string = await fimsdk_Napi.fimSendMessageText(testParams);

      this.addResult('message', '发送文本消息成功',
        `文本消息发送测试完成\n参数: ${testParams}\n结果: ${result}`,
        true);

      hilog.info(DOMAIN, 'TestFIMPage', 'Send text message test completed successfully');

    } catch (error) {
      const errorMsg = `发送文本消息测试失败: ${error}`;
      hilog.error(DOMAIN, 'TestFIMPage', errorMsg);
      this.addResult('message', '发送文本消息失败', errorMsg, false);
    } finally {
      this.isRunning = false;
      this.currentOperation = '';
    }
  }

  // 测试重发消息
  private async testResendMessage() {
    if (this.isRunning || !this.engineStarted) return;

    this.isRunning = true;
    this.currentOperation = '测试重发消息';

    try {
      hilog.info(DOMAIN, 'TestFIMPage', 'Testing resend message...');

      const testParams = JSON.stringify({
        messageId: "test_msg_001",
        conversationId: "test_conv_001"
      });

      this.addResult('message', '重发消息测试中',
        `正在测试重发消息...\n参数: ${testParams}`,
        true);

      const result = await fimsdk_Napi.fimResendMessage(testParams);

      this.addResult('message', '重发消息成功',
        `重发消息测试完成\n参数: ${testParams}\n结果: ${result}`,
        true);

      hilog.info(DOMAIN, 'TestFIMPage', 'Resend message test completed successfully');

    } catch (error) {
      const errorMsg = `重发消息测试失败: ${error}`;
      hilog.error(DOMAIN, 'TestFIMPage', errorMsg);
      this.addResult('message', '重发消息测试失败', errorMsg, false);
    } finally {
      this.isRunning = false;
      this.currentOperation = '';
    }
  }

  // 测试回复消息
  private async testReplyMessage() {
    if (this.isRunning || !this.engineStarted) return;

    this.isRunning = true;
    this.currentOperation = '测试回复消息';

    try {
      hilog.info(DOMAIN, 'TestFIMPage', 'Testing reply message...');

      const testParams = JSON.stringify({
        originalMessageId: "test_msg_001",
        conversationId: "test_conv_001",
        text: "这是回复内容"
      });

      this.addResult('message', '回复消息测试中',
        `正在测试回复消息...\n参数: ${testParams}`,
        true);

      const result = await fimsdk_Napi.fimReplyMessage(testParams);

      this.addResult('message', '回复消息成功',
        `回复消息测试完成\n参数: ${testParams}\n结果: ${result}`,
        true);

      hilog.info(DOMAIN, 'TestFIMPage', 'Reply message test completed successfully');

    } catch (error) {
      const errorMsg = `回复消息测试失败: ${error}`;
      hilog.error(DOMAIN, 'TestFIMPage', errorMsg);
      this.addResult('message', '回复消息测试失败', errorMsg, false);
    } finally {
      this.isRunning = false;
      this.currentOperation = '';
    }
  }

  // 测试获取消息
  private async testGetMessage() {
    if (this.isRunning || !this.engineStarted) return;

    this.isRunning = true;
    this.currentOperation = '测试获取消息';

    try {
      hilog.info(DOMAIN, 'TestFIMPage', 'Testing get message...');

      const testParams = JSON.stringify({
        conversationId: "test_conversation_001",
        messageId: "test_message_001"
      });

      this.addResult('message', '获取消息测试中',
        `正在测试获取消息...\n参数: ${testParams}`,
        true);

      const result: string = await fimsdk_Napi.fimGetMessage(testParams);

      this.addResult('message', '获取消息成功',
        `获取消息测试完成\n参数: ${testParams}\n结果: ${result}`,
        true);

      hilog.info(DOMAIN, 'TestFIMPage', 'Get message test completed successfully');

    } catch (error) {
      const errorMsg = `获取消息测试失败: ${error}`;
      hilog.error(DOMAIN, 'TestFIMPage', errorMsg);
      this.addResult('message', '获取消息测试失败', errorMsg, false);
    } finally {
      this.isRunning = false;
      this.currentOperation = '';
    }
  }

  // 测试删除消息
  private async testDeleteMessage() {
    if (this.isRunning || !this.engineStarted) return;

    this.isRunning = true;
    this.currentOperation = '测试删除消息';

    try {
      hilog.info(DOMAIN, 'TestFIMPage', 'Testing delete message...');

      const testParams = JSON.stringify({
        conversationId: "test_conversation_001",
        messageIds: ["test_message_001", "test_message_002"]
      });

      this.addResult('message', '删除消息测试中',
        `正在测试删除消息...\n参数: ${testParams}`,
        true);

      const result = await fimsdk_Napi.fimDeleteMessage(testParams);

      this.addResult('message', '删除消息成功',
        `删除消息测试完成\n参数: ${testParams}\n结果: ${result}`,
        true);

      hilog.info(DOMAIN, 'TestFIMPage', 'Delete message test completed successfully');

    } catch (error) {
      const errorMsg = `删除消息测试失败: ${error}`;
      hilog.error(DOMAIN, 'TestFIMPage', errorMsg);
      this.addResult('message', '删除消息测试失败', errorMsg, false);
    } finally {
      this.isRunning = false;
      this.currentOperation = '';
    }
  }

  // 测试撤回消息
  private async testRecallMessage() {
    if (this.isRunning || !this.engineStarted) return;

    this.isRunning = true;
    this.currentOperation = '测试撤回消息';

    try {
      hilog.info(DOMAIN, 'TestFIMPage', 'Testing recall message...');

      const testParams = JSON.stringify({
        conversationId: "test_conversation_001",
        messageId: "test_message_001"
      });

      this.addResult('message', '撤回消息测试中',
        `正在测试撤回消息...\n参数: ${testParams}`,
        true);

      const result = await fimsdk_Napi.fimRecallMessage(testParams);

      this.addResult('message', '撤回消息成功',
        `撤回消息测试完成\n参数: ${testParams}\n结果: ${result}`,
        true);

      hilog.info(DOMAIN, 'TestFIMPage', 'Recall message test completed successfully');

    } catch (error) {
      const errorMsg = `撤回消息测试失败: ${error}`;
      hilog.error(DOMAIN, 'TestFIMPage', errorMsg);
      this.addResult('message', '撤回消息测试失败', errorMsg, false);
    } finally {
      this.isRunning = false;
      this.currentOperation = '';
    }
  }

  // 测试消息列表
  private async testListMessages() {
    if (this.isRunning || !this.engineStarted) return;

    this.isRunning = true;
    this.currentOperation = '测试消息列表';

    try {
      hilog.info(DOMAIN, 'TestFIMPage', 'Testing list messages...');

      const testParams = JSON.stringify({
        conversationId: "test_conversation_001",
        cursor: "0",
        count: "20"
      });

      this.addResult('message', '消息列表测试中',
        `正在测试获取消息列表...\n参数: ${testParams}`,
        true);

      const result = await fimsdk_Napi.fimListPreviousLocalMsgs(testParams);

      this.addResult('message', '消息列表成功',
        `消息列表测试完成\n参数: ${testParams}\n结果: ${result}`,
        true);

      hilog.info(DOMAIN, 'TestFIMPage', 'List messages test completed successfully');

    } catch (error) {
      const errorMsg = `消息列表测试失败: ${error}`;
      hilog.error(DOMAIN, 'TestFIMPage', errorMsg);
      this.addResult('message', '消息列表测试失败', errorMsg, false);
    } finally {
      this.isRunning = false;
      this.currentOperation = '';
    }
  }

  // 测试会话列表
  private async testGetConversations() {
    if (this.isRunning || !this.engineStarted) return;

    this.isRunning = true;
    this.currentOperation = '测试会话列表';

    try {
      hilog.info(DOMAIN, 'TestFIMPage', 'Testing get conversations...');

      const testParams = JSON.stringify({
        conversationIds: ["test_conversation_001", "test_conversation_002"]
      });

      this.addResult('message', '会话列表测试中',
        `正在测试获取会话列表...\n参数: ${testParams}`,
        true);

      const result = await fimsdk_Napi.fimGetConversations(testParams);

      this.addResult('message', '会话列表成功',
        `会话列表测试完成\n参数: ${testParams}\n结果: ${result}`,
        true);

      hilog.info(DOMAIN, 'TestFIMPage', 'Get conversations test completed successfully');

    } catch (error) {
      const errorMsg = `会话列表测试失败: ${error}`;
      hilog.error(DOMAIN, 'TestFIMPage', errorMsg);
      this.addResult('message', '会话列表测试失败', errorMsg, false);
    } finally {
      this.isRunning = false;
      this.currentOperation = '';
    }
  }

  // ============================================================================
  // 新增API测试方法
  // ============================================================================

  // 测试登录认证
  private async testLogin() {
    if (this.isRunning || !this.engineStarted) return;

    this.isRunning = true;
    this.currentOperation = '测试登录认证';

    try {
      hilog.info(DOMAIN, 'TestFIMPage', 'Testing login...');

      const testParams = JSON.stringify({
        userId: "test_user_123",
        token: "test_token_abc123"
      });

      this.addResult('auth', '登录认证测试中',
        `正在测试登录认证...\n参数: ${testParams}`,
        true);

      const result = await fimsdk_Napi.fimLogin(testParams);

      this.addResult('auth', '登录认证成功',
        `登录认证测试完成\n参数: ${testParams}\n结果: ${result}`,
        true);

      hilog.info(DOMAIN, 'TestFIMPage', 'Login test completed successfully');

    } catch (error) {
      const errorMsg = `登录认证测试失败: ${error}`;
      hilog.error(DOMAIN, 'TestFIMPage', errorMsg);
      this.addResult('auth', '登录认证失败', errorMsg, false);
    } finally {
      this.isRunning = false;
      this.currentOperation = '';
    }
  }

  // 测试消息事件监听
  private async testOnMessageEvent() {
    if (this.isRunning || !this.engineStarted) return;

    this.isRunning = true;
    this.currentOperation = '测试消息事件监听';

    try {
      hilog.info(DOMAIN, 'TestFIMPage', 'Testing message event...');

      const testParams = JSON.stringify({
        eventType: "all"
      });

      this.addResult('event', '消息事件监听测试中',
        `正在测试消息事件监听...\n参数: ${testParams}`,
        true);

      const result = await fimsdk_Napi.fimOnMessageEvent(testParams);

      this.addResult('event', '消息事件监听成功',
        `消息事件监听测试完成\n参数: ${testParams}\n结果: ${result}`,
        true);

      hilog.info(DOMAIN, 'TestFIMPage', 'Message event test completed successfully');

    } catch (error) {
      const errorMsg = `消息事件监听测试失败: ${error}`;
      hilog.error(DOMAIN, 'TestFIMPage', errorMsg);
      this.addResult('event', '消息事件监听失败', errorMsg, false);
    } finally {
      this.isRunning = false;
      this.currentOperation = '';
    }
  }

  // 测试会话事件监听
  private async testOnConversationEvent() {
    if (this.isRunning || !this.engineStarted) return;

    this.isRunning = true;
    this.currentOperation = '测试会话事件监听';

    try {
      hilog.info(DOMAIN, 'TestFIMPage', 'Testing conversation event...');

      const testParams = JSON.stringify({
        eventType: "all"
      });

      this.addResult('event', '会话事件监听测试中',
        `正在测试会话事件监听...\n参数: ${testParams}`,
        true);

      const result = await fimsdk_Napi.fimOnConversationEvent(testParams);

      this.addResult('event', '会话事件监听成功',
        `会话事件监听测试完成\n参数: ${testParams}\n结果: ${result}`,
        true);

      hilog.info(DOMAIN, 'TestFIMPage', 'Conversation event test completed successfully');

    } catch (error) {
      const errorMsg = `会话事件监听测试失败: ${error}`;
      hilog.error(DOMAIN, 'TestFIMPage', errorMsg);
      this.addResult('event', '会话事件监听失败', errorMsg, false);
    } finally {
      this.isRunning = false;
      this.currentOperation = '';
    }
  }

  // 测试群组事件监听
  private async testOnGroupEvent() {
    if (this.isRunning || !this.engineStarted) return;

    this.isRunning = true;
    this.currentOperation = '测试群组事件监听';

    try {
      hilog.info(DOMAIN, 'TestFIMPage', 'Testing group event...');

      const testParams = JSON.stringify({
        eventType: "all"
      });

      this.addResult('event', '群组事件监听测试中',
        `正在测试群组事件监听...\n参数: ${testParams}`,
        true);

      const result = await fimsdk_Napi.fimOnGroupEvent(testParams);

      this.addResult('event', '群组事件监听成功',
        `群组事件监听测试完成\n参数: ${testParams}\n结果: ${result}`,
        true);

      hilog.info(DOMAIN, 'TestFIMPage', 'Group event test completed successfully');

    } catch (error) {
      const errorMsg = `群组事件监听测试失败: ${error}`;
      hilog.error(DOMAIN, 'TestFIMPage', errorMsg);
      this.addResult('event', '群组事件监听失败', errorMsg, false);
    } finally {
      this.isRunning = false;
      this.currentOperation = '';
    }
  }

  // 测试发送自定义消息
  private async testSendMessageCustom() {
    if (this.isRunning || !this.engineStarted) return;

    this.isRunning = true;
    this.currentOperation = '测试发送自定义消息';

    try {
      hilog.info(DOMAIN, 'TestFIMPage', 'Testing send custom message...');

      const customData: CustomMessageData = { message: "这是一条自定义消息", timestamp: Date.now() };
      const customContent: CustomContent = { type: "test_custom", data: customData };
      const messageContent: CustomMessageContent = { content_type: 10, custom_content: customContent };
      const params: CustomMessageParams = { conversationId: "test_conversation_123", content: messageContent };
      const testParams = JSON.stringify(params);

      this.addResult('message', '发送自定义消息测试中',
        `正在测试发送自定义消息...\n参数: ${testParams}`,
        true);

      const result = await fimsdk_Napi.fimSendMessageCustom(testParams);

      this.addResult('message', '发送自定义消息成功',
        `发送自定义消息测试完成\n参数: ${testParams}\n结果: ${result}`,
        true);

      hilog.info(DOMAIN, 'TestFIMPage', 'Send custom message test completed successfully');

    } catch (error) {
      const errorMsg = `发送自定义消息测试失败: ${error}`;
      hilog.error(DOMAIN, 'TestFIMPage', errorMsg);
      this.addResult('message', '发送自定义消息失败', errorMsg, false);
    } finally {
      this.isRunning = false;
      this.currentOperation = '';
    }
  }

  // 测试发送图片消息
  private async testSendMessageImage() {
    if (this.isRunning || !this.engineStarted) return;

    this.isRunning = true;
    this.currentOperation = '测试发送图片消息';

    try {
      hilog.info(DOMAIN, 'TestFIMPage', 'Testing send image message...');

      const testParams = JSON.stringify({
        conversationId: "test_conversation_123",
        imagePath: "/data/storage/el2/base/haps/entry/files/test_image.jpg"
      });

      this.addResult('message', '发送图片消息测试中',
        `正在测试发送图片消息...\n参数: ${testParams}`,
        true);

      const result = await fimsdk_Napi.fimSendMessageImage(testParams);

      this.addResult('message', '发送图片消息成功',
        `发送图片消息测试完成\n参数: ${testParams}\n结果: ${result}`,
        true);

      hilog.info(DOMAIN, 'TestFIMPage', 'Send image message test completed successfully');

    } catch (error) {
      const errorMsg = `发送图片消息测试失败: ${error}`;
      hilog.error(DOMAIN, 'TestFIMPage', errorMsg);
      this.addResult('message', '发送图片消息失败', errorMsg, false);
    } finally {
      this.isRunning = false;
      this.currentOperation = '';
    }
  }

  // 测试发送@消息
  private async testSendMessageAt() {
    if (this.isRunning || !this.engineStarted) return;

    this.isRunning = true;
    this.currentOperation = '测试发送@消息';

    try {
      hilog.info(DOMAIN, 'TestFIMPage', 'Testing send at message...');

      const testParams = JSON.stringify({
        conversationId: "test_conversation_123",
        text: "这是一条@消息 @张三 @李四",
        atUserIds: ["user_123", "user_456"]
      });

      this.addResult('message', '发送@消息测试中',
        `正在测试发送@消息...\n参数: ${testParams}`,
        true);

      const result = await fimsdk_Napi.fimSendMessageAt(testParams);

      this.addResult('message', '发送@消息成功',
        `发送@消息测试完成\n参数: ${testParams}\n结果: ${result}`,
        true);

      hilog.info(DOMAIN, 'TestFIMPage', 'Send at message test completed successfully');

    } catch (error) {
      const errorMsg = `发送@消息测试失败: ${error}`;
      hilog.error(DOMAIN, 'TestFIMPage', errorMsg);
      this.addResult('message', '发送@消息失败', errorMsg, false);
    } finally {
      this.isRunning = false;
      this.currentOperation = '';
    }
  }

  // 测试发送结构化消息
  private async testSendMessageStruct() {
    if (this.isRunning || !this.engineStarted) return;

    this.isRunning = true;
    this.currentOperation = '测试发送结构化消息';

    try {
      hilog.info(DOMAIN, 'TestFIMPage', 'Testing send struct message...');

      const structData: StructMessageData = {
          title: "结构化消息标题",
          description: "这是一条结构化消息的描述",
          url: "https://example.com",
          imageUrl: "https://example.com/image.jpg"
      };
      const testParams = JSON.stringify({
        conversationId: "test_conversation_123",
        structData: structData
      });

      this.addResult('message', '发送结构化消息测试中',
        `正在测试发送结构化消息...\n参数: ${testParams}`,
        true);

      const result = await fimsdk_Napi.fimSendMessageStruct(testParams);

      this.addResult('message', '发送结构化消息成功',
        `发送结构化消息测试完成\n参数: ${testParams}\n结果: ${result}`,
        true);

      hilog.info(DOMAIN, 'TestFIMPage', 'Send struct message test completed successfully');

    } catch (error) {
      const errorMsg = `发送结构化消息测试失败: ${error}`;
      hilog.error(DOMAIN, 'TestFIMPage', errorMsg);
      this.addResult('message', '发送结构化消息失败', errorMsg, false);
    } finally {
      this.isRunning = false;
      this.currentOperation = '';
    }
  }

  // 测试发送视频消息
  private async testSendMessageVideo() {
    if (this.isRunning || !this.engineStarted) return;

    this.isRunning = true;
    this.currentOperation = '测试发送视频消息';

    try {
      hilog.info(DOMAIN, 'TestFIMPage', 'Testing send video message...');

      const testParams = JSON.stringify({
        conversationId: "test_conversation_123",
        videoPath: "/data/storage/el2/base/haps/entry/files/test_video.mp4"
      });

      this.addResult('message', '发送视频消息测试中',
        `正在测试发送视频消息...\n参数: ${testParams}`,
        true);

      const result = await fimsdk_Napi.fimSendMessageVideo(testParams);

      this.addResult('message', '发送视频消息成功',
        `发送视频消息测试完成\n参数: ${testParams}\n结果: ${result}`,
        true);

      hilog.info(DOMAIN, 'TestFIMPage', 'Send video message test completed successfully');

    } catch (error) {
      const errorMsg = `发送视频消息测试失败: ${error}`;
      hilog.error(DOMAIN, 'TestFIMPage', errorMsg);
      this.addResult('message', '发送视频消息失败', errorMsg, false);
    } finally {
      this.isRunning = false;
      this.currentOperation = '';
    }
  }

  // 批量测试所有API
  private async testAllAPIs() {
    if (this.runningBatchTest) return;

    this.runningBatchTest = true;
    this.batchTestProgress = '开始批量测试...';

    try {
      this.addResult('batch', '批量测试开始', '开始执行所有API的批量测试', true);

      const enabledTests = this.testItems.filter(item => item.enabled);
      const totalTests = enabledTests.length;

      for (let i = 0; i < enabledTests.length; i++) {
        const testItem = enabledTests[i];
        this.batchTestProgress = `正在执行 ${i + 1}/${totalTests}: ${testItem.name}`;

        try {
          await testItem.method();
          // 等待一秒再执行下一个测试
          // await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (error) {
          hilog.error(DOMAIN, 'TestFIMPage', `Batch test failed for ${testItem.name}: ${error}`);
        }
      }

      this.addResult('batch', '批量测试完成', `所有 ${totalTests} 个API测试已完成`, true);
      this.batchTestProgress = '';

    } catch (error) {
      const errorMsg = `批量测试失败: ${error}`;
      hilog.error(DOMAIN, 'TestFIMPage', errorMsg);
      this.addResult('batch', '批量测试失败', errorMsg, false);
    } finally {
      this.runningBatchTest = false;
      this.batchTestProgress = '';
    }
  }

  // 添加剩余的测试方法占位符（这些方法需要根据实际SDK API实现）
  private async testGetLocalMessage() {
    // TODO: 实现获取本地消息测试
    this.addResult('query', '获取本地消息', '功能待实现', true);
  }

  private async testListMessagesReadStatus() {
    // TODO: 实现获取消息已读状态测试
    this.addResult('query', '消息已读状态', '功能待实现', true);
  }

  private async testDeleteLocalMessage() {
    // TODO: 实现删除本地消息测试
    this.addResult('operation', '删除本地消息', '功能待实现', true);
  }

  private async testClearMessages() {
    // TODO: 实现清空消息测试
    this.addResult('operation', '清空消息', '功能待实现', true);
  }

  private async testUpdateMessageToRead() {
    // TODO: 实现设置消息已读测试
    this.addResult('operation', '设置已读', '功能待实现', true);
  }

  private async testActiveConversation() {
    // TODO: 实现激活会话测试
    this.addResult('conversation', '激活会话', '功能待实现', true);
  }

  private async testGetMuteStatus() {
    // TODO: 实现获取静音状态测试
    this.addResult('conversation', '获取静音状态', '功能待实现', true);
  }

  private async testCreateCustomSingleConversation() {
    // TODO: 实现创建自定义会话测试
    this.addResult('conversation', '创建自定义会话', '功能待实现', true);
  }

  private async testLeaveGroup() {
    // TODO: 实现退群测试
    this.addResult('conversation', '退群', '功能待实现', true);
  }

  private async testGetMyFuid() {
    // TODO: 实现获取FUID测试
    this.addResult('search', '获取FUID', '功能待实现', true);
  }

  private async testSearchChatContent() {
    // TODO: 实现搜索聊天内容测试
    this.addResult('search', '搜索聊天内容', '功能待实现', true);
  }

  private async testGetMediaLocalPath() {
    // TODO: 实现获取媒体路径测试
    this.addResult('search', '获取媒体路径', '功能待实现', true);
  }

  build() {
    Column() {
      // 标题区域
      Text('FIM API 测试中心')
        .fontSize(22)
        .fontWeight(FontWeight.Bold)
        .fontColor('#1f2937')
        .margin({ top: 20, bottom: 15 })

      // 控制区域
      Column() {
        // Engine启动按钮
        Button('启动 FIM Engine')
          .width('90%')
          .height(50)
          .fontSize(16)
          .backgroundColor(this.engineStarted ? '#10b981' : '#3b82f6')
          .fontColor(Color.White)
          .enabled(!this.isRunning && !this.runningBatchTest)
          .margin({ bottom: 10 })
          .onClick(() => {
            this.startFIMEngine();
          })

        // 批量测试按钮
        Button(this.runningBatchTest ? `批量测试中... ${this.batchTestProgress}` : '🚀 批量测试所有API')
          .width('90%')
          .height(45)
          .fontSize(14)
          .backgroundColor(this.runningBatchTest ? '#f59e0b' : '#8b5cf6')
          .fontColor(Color.White)
          .enabled(!this.isRunning && !this.runningBatchTest && this.engineStarted)
          .margin({ bottom: 15 })
          .onClick(() => {
            this.testAllAPIs();
          })

        // 分类筛选器
        Row() {
          Text('分类筛选:')
            .fontSize(14)
            .fontColor('#6b7280')
            .margin({ right: 10 })

          Button(this.selectedCategory === 'all' ? '全部 ✓' : '全部')
            .fontSize(12)
            .height(30)
            .backgroundColor(this.selectedCategory === 'all' ? '#3b82f6' : '#e5e7eb')
            .fontColor(this.selectedCategory === 'all' ? Color.White : '#374151')
            .margin({ right: 5 })
            .onClick(() => { this.selectedCategory = 'all'; })

          Button(this.selectedCategory === 'auth' ? '认证 ✓' : '认证')
            .fontSize(12)
            .height(30)
            .backgroundColor(this.selectedCategory === 'auth' ? '#FF6B6B' : '#e5e7eb')
            .fontColor(this.selectedCategory === 'auth' ? Color.White : '#374151')
            .margin({ right: 5 })
            .onClick(() => { this.selectedCategory = 'auth'; })

          Button(this.selectedCategory === 'message' ? '消息 ✓' : '消息')
            .fontSize(12)
            .height(30)
            .backgroundColor(this.selectedCategory === 'message' ? '#45B7D1' : '#e5e7eb')
            .fontColor(this.selectedCategory === 'message' ? Color.White : '#374151')
            .margin({ right: 5 })
            .onClick(() => { this.selectedCategory = 'message'; })
        }
        .width('90%')
        .margin({ bottom: 10 })

        // 当前操作状态
        if (this.isRunning || this.runningBatchTest) {
          Text(this.runningBatchTest ? this.batchTestProgress : `正在执行: ${this.currentOperation}`)
            .fontSize(14)
            .fontColor('#f59e0b')
            .fontWeight(FontWeight.Medium)
            .margin({ bottom: 10 })
        }
      }
      .width('100%')
      .padding({ left: 20, right: 20 })

      // 测试按钮区域 - 可滚动列表
      Text('API 测试列表')
        .fontSize(16)
        .fontWeight(FontWeight.Medium)
        .fontColor('#374151')
        .margin({ left: 20, bottom: 10 })
        .alignSelf(ItemAlign.Start)

      List() {
        ForEach(this.getFilteredTestItems(), (testItem: TestItem) => {
          ListItem() {
            this.buildTestButton(testItem)
          }
          .margin({ bottom: 8 })
        })
      }
      .width('100%')
      .height(200)
      .padding({ left: 20, right: 20 })
      .scrollBar(BarState.Auto)

      // 测试结果区域
      Text('测试结果')
        .fontSize(16)
        .fontWeight(FontWeight.Medium)
        .fontColor('#374151')
        .margin({ left: 20, top: 15, bottom: 10 })
        .alignSelf(ItemAlign.Start)

      List() {
        ForEach(this.testResults, (result: TestResult) => {
          ListItem() {
            this.buildResultItem(result)
          }
          .margin({ bottom: 8 })
        })
      }
      .width('100%')
      .layoutWeight(1)
      .padding({ left: 20, right: 20, bottom: 20 })
      .backgroundColor('#f8fafc')
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#f8fafc')
  }

  // 获取筛选后的测试项
  private getFilteredTestItems(): TestItem[] {
    if (this.selectedCategory === 'all') {
      return this.testItems;
    }
    return this.testItems.filter(item => item.category === this.selectedCategory);
  }

  // 构建测试按钮
  @Builder
  buildTestButton(testItem: TestItem) {
    Row() {
      // 状态指示器
      Circle({ width: 8, height: 8 })
        .fill(testItem.enabled ? testItem.color : '#d1d5db')
        .margin({ right: 12 })

      // 测试信息
      Column() {
        Text(testItem.name)
          .fontSize(14)
          .fontWeight(FontWeight.Medium)
          .fontColor('#1f2937')
          .textAlign(TextAlign.Start)
          .width('100%')

        Text(testItem.description)
          .fontSize(12)
          .fontColor('#6b7280')
          .textAlign(TextAlign.Start)
          .width('100%')
          .margin({ top: 2 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)

      // 分类标签
      Text(testItem.category.toUpperCase())
        .fontSize(10)
        .fontColor(Color.White)
        .backgroundColor(testItem.color)
        .padding({ left: 6, right: 6, top: 2, bottom: 2 })
        .borderRadius(4)
        .margin({ right: 8 })
    }
    .width('100%')
    .height(60)
    .padding({ left: 15, right: 15, top: 8, bottom: 8 })
    .backgroundColor(Color.White)
    .borderRadius(8)
    .border({ width: 1, color: '#e5e7eb' })
    .enabled(testItem.enabled && !this.isRunning && !this.runningBatchTest && this.engineStarted)
    .onClick(() => {
      testItem.method();
    })
  }

  // 构建测试结果项
  @Builder
  buildResultItem(result: TestResult) {
    Column() {
      // 标题行
      Row() {
        // 状态图标
        Text(result.success ? '✅' : '❌')
          .fontSize(16)
          .margin({ right: 8 })

        // 标题和类型
        Column() {
          Text(result.title)
            .fontSize(16)
            .fontWeight(FontWeight.Medium)
            .fontColor(result.success ? '#10b981' : '#ef4444')
            .textAlign(TextAlign.Start)
            .width('100%')

          Text(`[${result.type.toUpperCase()}] ${result.timestamp}`)
            .fontSize(12)
            .fontColor('#6b7280')
            .textAlign(TextAlign.Start)
            .width('100%')
            .margin({ top: 2 })
        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.Start)

        // 展开/收起按钮
        Text(result.expanded ? '▼' : '▶')
          .fontSize(12)
          .fontColor('#9ca3af')
          .onClick(() => {
            this.toggleResultExpanded(result.id);
          })
      }
      .width('100%')
      .padding({ top: 12, bottom: 8, left: 15, right: 15 })

      // 详细内容（可展开）
      if (result.expanded) {
        Text(result.content)
          .fontSize(13)
          .fontColor('#374151')
          .textAlign(TextAlign.Start)
          .width('100%')
          .padding({ left: 15, right: 15, bottom: 12 })
          .backgroundColor('#f9fafb')
          .borderRadius({ bottomLeft: 6, bottomRight: 6 })
      }
    }
    .width('100%')
    .backgroundColor(Color.White)
    .borderRadius(8)
    .border({ width: 1, color: result.success ? '#d1fae5' : '#fecaca' })
  }
}
