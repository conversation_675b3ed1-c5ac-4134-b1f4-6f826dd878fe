import hilog from '@ohos.hilog';

export class PrivacyRequestHelper{

  static isNeedPrivacyRequest() : boolean {
    PersistentStorage.persistProp('privacy_request', true);
    return AppStorage.get<boolean>('privacy_request') ?? true;
  }

  static setNoMoreNeedPrivacyRequest() {
    PersistentStorage.persistProp('privacy_request', false);
    AppStorage.setOrCreate<boolean>('privacy_request', false);
  }

}