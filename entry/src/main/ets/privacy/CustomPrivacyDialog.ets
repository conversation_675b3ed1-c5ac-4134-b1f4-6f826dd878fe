import { common, Want } from '@kit.AbilityKit';
import process from '@ohos.process';
import { PrivacyRequestHelper } from './PrivacyRequestHelper';

const title = '温馨提示'
const dialog_text = `亲，感谢您对飞猪一直以来的信任！
我们依据最新的监管要求更新了`
const dialog_text2 = `（点击了解更新后的详细内容），特向您说明如下：
1、为向您提供交易相关基本功能，我们会收集、使用必要的信息；
2、基于您的授权，我们可能会获取您的位置等信息，您有权拒绝或取消授权；
3、我们会采取业界先进的安全措施保护您的信息安全；
4、未经您同意，我们不会从第三方处获取、共享或向其提供您的信息；
5、您可以查询、更正、删除您的个人信息，我们也提供账户注销的渠道。`

export interface OnUserActionCallback {
  onUserApproved: () => void;
  onUserDenied: () => void;
}

export interface OnUserApprovedCallback {
  onUserApproved: () => void;
}


function existApp() {
  new process.ProcessManager().exit(0);
}

let outCallback: OnUserApprovedCallback | undefined

@Component
struct DialogWrapper {
  _open(callback: OnUserActionCallback): CustomDialogController {
    let dialogController = new CustomDialogController({
      builder: CustomPrivacyDialog({
        callback: callback
      }),
      cancel: existApp,
      autoCancel: false,
      customStyle: true,
      alignment: DialogAlignment.Center
    })
    dialogController.open()
    return dialogController
  }

  build() {
  }

  openDialog(callback: OnUserActionCallback): CustomDialogController {
    return this._open(callback)
  }
}

@CustomDialog
export struct CustomPrivacyDialog {
  controller?: CustomDialogController;
  callback?: OnUserActionCallback;

  static show(approvedCallback?: OnUserApprovedCallback) {
    if (approvedCallback) {
      outCallback = approvedCallback
    }
    let controller = new DialogWrapper().openDialog({
      onUserApproved: () => {
        controller.close()
        PrivacyRequestHelper.setNoMoreNeedPrivacyRequest()
        outCallback?.onUserApproved()
      },
      onUserDenied: () => {
        controller.close()
        CustomPrivacyDialog1.show()
      }
    })
  }

  build() {
    Column() {
      //主标题
      Text(title)
        .fontColor('#ff0F131A')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .alignSelf(ItemAlign.Center)

      //文本内容，可滚动
      Scroll() {
        // 定义Text作为滚动条的样式
        Column({}) {
          Text() {
            Span(dialog_text)
            Span('《飞猪隐私政策》').fontColor('#FF7300').onClick(() => {
              let context = getContext(this) as common.UIAbilityContext;
              let wantInfo: Want = {
                action: 'ohos.want.action.viewData',
                entities: ['entity.system.browsable'],
                uri: 'http://terms.alicdn.com/legal-agreement/terms/Flyingpig/Flyingpig201802061043_85261.html'
              }
              context.startAbility(wantInfo)
            })
            Span(dialog_text2)
          }
          .fontColor('#030303')
          .fontSize(12)
          .textAlign(TextAlign.Start)
          .alignSelf(ItemAlign.Start)
        }
      }.scrollBar(BarState.Off)
      .constraintSize({ maxHeight: '40%' })
      .margin({
        top: 12,
      })

      Button('同意')
        .onClick(() => {
          this.callback?.onUserApproved()
        })
        .backgroundColor('#FFDD00')
        .fontColor('#ff030303')
        .width('100%')
        .height(42)
        .fontSize(12)
        .margin({ top: 18 })


      Button('不同意')
        .onClick(() => {
          this.callback?.onUserDenied()
        })
        .backgroundColor('#00000000')
        .fontColor('#ff030303')
        .width('100%')
        .height(42)
        .fontSize(12)
        .margin({ top: 10 })

    }
    .width('72%')
    .backgroundColor(Color.White)
    .borderRadius(12)
    .padding(18)
  }
}

@Component
struct DialogWrapper1 {
  _open(callback: OnUserActionCallback): CustomDialogController {
    let dialogController = new CustomDialogController({
      builder: CustomPrivacyDialog1({
        callback: callback
      }),
      cancel: existApp,
      autoCancel: false,
      customStyle: true,
      alignment: DialogAlignment.Center
    })
    dialogController.open()
    return dialogController
  }

  build() {
  }

  openDialog(callback: OnUserActionCallback): CustomDialogController {
    return this._open(callback)
  }
}

@CustomDialog
export struct CustomPrivacyDialog1 {
  controller?: CustomDialogController;
  callback?: OnUserActionCallback;

  static show() {
    let controller = new DialogWrapper1().openDialog({
      onUserApproved: () => {
        controller.close()
        CustomPrivacyDialog.show()
      },
      onUserDenied: () => {
        controller.close()
        CustomPrivacyDialog2.show()
      }
    })
  }

  build() {
    Column() {
      //主标题
      Text(`您需要同意本隐私政策
才能继续使用飞猪`)
        .fontColor('#ff0F131A')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .alignSelf(ItemAlign.Center)

      Text(`若您不同意本隐私权政策，很遗憾我们将无法为您服务。`)
        .fontColor('#ff0F131A')
        .fontSize(12)
        .alignSelf(ItemAlign.Center)
        .margin({
          top: 12,
        })

      Row() {
        Button('仍不同意')
          .onClick(() => {
            this.callback?.onUserDenied()
          })
          .backgroundColor('#F7F8FA')
          .fontColor('#ff5C5F66')
          .height(42)
          .layoutWeight(1)
          .fontSize(16)
          .margin({ top: 18, right: 6 })


        Button('查看协议')
          .onClick(() => {
            this.callback?.onUserApproved()
          })
          .backgroundColor('#FFDD00')
          .fontColor('#5C5F66')
          .layoutWeight(1)
          .height(42)
          .fontSize(16)
          .margin({ top: 18, left: 6 })
      }

    }
    .width('72%')
    .backgroundColor(Color.White)
    .borderRadius(12)
    .padding(18)
  }
}


@Component
struct DialogWrapper2 {
  _open(callback: OnUserActionCallback): CustomDialogController {
    let dialogController = new CustomDialogController({
      builder: CustomPrivacyDialog2({
        callback: callback
      }),
      cancel: existApp,
      autoCancel: false,
      customStyle: true,
      alignment: DialogAlignment.Center
    })
    dialogController.open()
    return dialogController
  }

  build() {
  }

  openDialog(callback: OnUserActionCallback): CustomDialogController {
    return this._open(callback)
  }
}

@CustomDialog
export struct CustomPrivacyDialog2 {
  controller?: CustomDialogController;
  callback?: OnUserActionCallback;

  static show() {
    let controller = new DialogWrapper2().openDialog({
      onUserApproved: () => {
        controller.close()
        CustomPrivacyDialog.show()
      },
      onUserDenied: () => {
        controller.close()
        existApp()
      }
    })
  }

  build() {
    Column() {
      //主标题
      Text(`亲，要不再想想`)
        .fontColor('#ff0F131A')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .alignSelf(ItemAlign.Center)


      Row() {
        Button('退出应用')
          .onClick(() => {
            this.callback?.onUserDenied()
          })
          .backgroundColor('#F7F8FA')
          .fontColor('#ff5C5F66')
          .height(42)
          .layoutWeight(1)
          .fontSize(16)
          .margin({ top: 18, right: 6 })


        Button('查看协议')
          .onClick(() => {
            this.callback?.onUserApproved()
          })
          .backgroundColor('#FFDD00')
          .fontColor('#5C5F66')
          .layoutWeight(1)
          .height(42)
          .fontSize(16)
          .margin({ top: 18, left: 6 })
      }

    }
    .width('72%')
    .backgroundColor(Color.White)
    .borderRadius(12)
    .padding(18)
  }
}