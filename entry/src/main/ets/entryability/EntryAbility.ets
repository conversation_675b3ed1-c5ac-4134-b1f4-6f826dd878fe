/*
 * The MIT License (MIT)
 *
 * Copyright (c) 2019 Alibaba Group
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
import UIAbility from '@ohos.app.ability.UIAbility';
import window from '@ohos.window';
import Want from '@ohos.app.ability.Want';
import AbilityConstant from '@ohos.app.ability.AbilityConstant';

import { logger } from '../FliggyEntryAbilityStage';
import { IconFontManager } from '@fliggy-ohos/iconfont';
import { FlutterManager } from '@ohos/flutter_ohos';
import FlutterBoostPlugin, { FlutterBoost, FlutterBoostDelegate, FlutterBoostRouteOptions } from '@fliggy-ohos/flutter_boost';
import PathProviderPlugin from '@fliggy-ohos/path_provider_ohos';
import DeviceInfoPlusOhosPlugin from '@fliggy-ohos/device_info_plus';
import PermissionHandlerPlugin from '@fliggy-ohos/permission_handler_ohos';
import SharedPreferencesOhosPlugin from '@fliggy-ohos/shared_preferences_ohos';
import SqflitePlugin from '@fliggy-ohos/sqflite';
import PackageInfoPlugin from '@fliggy-ohos/package_info_plus';
import UrlLauncherPlugin from '@fliggy-ohos/url_launcher_ohos';
import HighAvailablePlugin from '@fliggy-ohos/high_available';
import WpkUploaderPlugin from '@fliggy-ohos/wpk_uploader';
import AionSdkPlugin from '@fliggy-ohos/aion_sdk';
import { StaticContext, WindowUtils } from '@fliggy-ohos/appcompat';
import { addPlugin } from '@fliggy-ohos/flutter_container';
import { navigator, PageContext, RunningPageStack, UILifecycleManager } from '@fliggy-ohos/router';
import { WantParserUtils, RouterPageWork } from '@fliggy-ohos/launcherimpl';
import { LoginOAuthService } from '@taobao-ohos/account_auth';
import { ezLongTake } from '@fliggy-ohos/router';
import { ConfigurationConstant } from '@kit.AbilityKit';
import { trackPreinstallData } from '../pages/LaunchPage';
import { FlutterEventHook } from '../hook/FlutterEventHook';

export default class EntryAbility extends UIAbility {

  private mainWindow?: window.Window | null;

  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void{
    logger.d('onCreate', `Ability onCreate want: ${JSON.stringify(want)} launchParam: ${JSON.stringify(launchParam)}`);
    this.context.getApplicationContext().setColorMode(ConfigurationConstant.ColorMode.COLOR_MODE_LIGHT);
    FlutterManager.getInstance().pushUIAbility(this)
    // 处理smartbanner数据
    WantParserUtils.parseEntryWant(want)
  }

  onNewWant(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    logger.d('onNewWant', `Ability onNewWant want: ${JSON.stringify(want)} launchParam: ${JSON.stringify(launchParam)}`);
    // 处理smartbanner数据
    WantParserUtils.parseEntryWant(want)
    if (WantParserUtils.msgObj && navigator.findPage('page://home_main')) {
      const routerPageWork = new RouterPageWork(WantParserUtils.msgObj, 'EntryAbility')
      const jumped = routerPageWork.routeByUrl()
      trackPreinstallData("warm_want");
      WantParserUtils.msgObj = undefined
    }
    LoginOAuthService.getInstance().handleResponse(want);
    // AFServiceCenter.handleResponse(want);
  }

  onDestroy(): void {
    logger.d('onDestroy', 'Ability onDestroy');
    FlutterManager.getInstance().popUIAbility(this)
  }

  onWindowStageCreate(windowStage: window.WindowStage): void {
    // Main window is created, set main page for this ability
    logger.d('onWindowStageCreate', 'Ability onWindowStageCreate');
    AppStorage.setOrCreate('entryWindowStage', windowStage);
    FlutterManager.getInstance().pushWindowStage(this, windowStage);
    FlutterEventHook.getInstance().applyHook(windowStage);
    ezLongTake.init(windowStage);
    this.initFlutterPlugin();
    // 启动页
    // UI注册必须放到loadContent里！！！
    windowStage.loadContent('pages/LaunchIndexPage', (err, data) => {
      if (err.code) {
        logger.e('onWindowStageCreate', JSON.stringify(err) ?? '');
        return;
      }

      let windowClass: window.Window = windowStage.getMainWindowSync(); // 获取应用主窗口

      // 2. 获取布局避让遮挡的区域
      // 以导航条避让为例
      let type = window.AvoidAreaType.TYPE_SYSTEM;
      let avoidArea = windowClass.getWindowAvoidArea(type);

      // 初始化Window工具类
      WindowUtils.init(this.context.getApplicationContext());
      WindowUtils.setWindowClass(windowClass);
      WindowUtils.setWindowStage(windowStage);
      WindowUtils.setAvoidArea(avoidArea);

      // 获取状态栏区域高度
      AppStorage.setOrCreate('topRectHeight', avoidArea.topRect.height);
      AppStorage.setOrCreate('topRectHeightVp', px2vp(avoidArea.topRect.height));
      // 获取到导航条区域的高度
      type = window.AvoidAreaType.TYPE_NAVIGATION_INDICATOR;
      avoidArea = windowClass.getWindowAvoidArea(type);
      AppStorage.setOrCreate('bottomRectHeight', avoidArea.bottomRect.height);
      AppStorage.setOrCreate('bottomRectHeightVp', px2vp(avoidArea.bottomRect.height));

      // 3. 注册iconfont
      IconFontManager.registerFont();

      this.mainWindow = windowStage.getMainWindowSync();
      this.mainWindow?.setWindowLayoutFullScreen(true);

      const width = windowClass.getWindowProperties().windowRect.width;
      const height = windowClass.getWindowProperties().windowRect.height;

      AppStorage.setOrCreate('atomLeft', px2vp(width) - 45);
      AppStorage.setOrCreate('atomTop', px2vp(height) / 2);

      logger.d('onWindowStageCreate', JSON.stringify(data) ?? '');
    });

    logger.d('onWindowStageCreate', 'Ability onCreate');

  }



  onWindowStageDestroy(): void {
    // Main window is destroyed, release UI related resources
    logger.d('onWindowStageDestroy', 'Ability onWindowStageDestroy');
    AppStorage.delete('entryWindowStage');
    FlutterManager.getInstance().popWindowStage(this)
  }

  onForeground(): void {
    // Ability has brought to foreground
    logger.d('onForeground', 'Ability onForeground');
    AppStorage.setOrCreate('entryOnForeground', true);
    AppStorage.setOrCreate('entryOnBackground', false);
    FlutterBoost.getInstance().onForeground();
    const topPageContext : PageContext | undefined = RunningPageStack.getTopPageContext();
    if(topPageContext?.pageUrl?.getHost() === "home_main") {
      topPageContext.onPageShow();
      UILifecycleManager.getInstance().onPageShow(topPageContext);
    }
  }

  onBackground(): void {
    // Ability has back to background
    logger.d('onBackground', 'Ability onBackground');
    AppStorage.setOrCreate('entryOnForeground', false);
    AppStorage.setOrCreate('entryOnBackground', true);
    FlutterBoost.getInstance().onBackground();
    const topPageContext : PageContext | undefined = RunningPageStack.getTopPageContext();
    if(topPageContext?.pageUrl?.getHost() === "home_main") {
      topPageContext.onPageHide();
      UILifecycleManager.getInstance().onPageHide(topPageContext);
    }
  }

  initFlutterPlugin() {
    logger.d('initFlutterPlugin', '添加插件');
    addPlugin(new PathProviderPlugin(this.context));
    addPlugin(new DeviceInfoPlusOhosPlugin());
    addPlugin(new PackageInfoPlugin());
    addPlugin(new PermissionHandlerPlugin());
    addPlugin(new SharedPreferencesOhosPlugin());
    addPlugin(new SqflitePlugin(this.context));
    addPlugin(new UrlLauncherPlugin());
    addPlugin(new FlutterBoostPlugin());
    addPlugin(new HighAvailablePlugin());
    addPlugin(new WpkUploaderPlugin());
    addPlugin(new AionSdkPlugin());
  }

}