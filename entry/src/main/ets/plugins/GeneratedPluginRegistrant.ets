// import { FlutterEngine, Log } from '@ohos/flutter_ohos';
// import ConnectivityPlugin from 'connectivity_plus';
// import DeviceInfoPlusOhosPlugin from 'device_info_plus';
// import FlutterBoostPlugin from 'flutter_boost';
// import HighAvailablePlugin from 'high_available';
// import PackageInfoPlugin from 'package_info_plus';
// import PathProviderPlugin from 'path_provider_ohos';
// import PermissionHandlerPlugin from 'permission_handler_ohos';
// import SharedPreferencesPlugin from 'shared_preferences_ohos';
// import SqflitePlugin from 'sqflite';
// import UrlLauncherPlugin from 'url_launcher_ohos';
//
// /**
//  * Generated file. Do not edit.
//  * This file is generated by the Flutter tool based on the
//  * plugins that support the Ohos platform.
//  */
//
// const TAG = "GeneratedPluginRegistrant";
//
// export class GeneratedPluginRegistrant {
//
//   static registerWith(flutterEngine: FlutterEngine) {
//     try {
//       flutterEngine.getPlugins()?.add(new ConnectivityPlugin());
//       flutterEngine.getPlugins()?.add(new DeviceInfoPlusOhosPlugin());
//       flutterEngine.getPlugins()?.add(new FlutterBoostPlugin());
//       flutterEngine.getPlugins()?.add(new HighAvailablePlugin());
//       flutterEngine.getPlugins()?.add(new PackageInfoPlugin());
//       flutterEngine.getPlugins()?.add(new PathProviderPlugin());
//       flutterEngine.getPlugins()?.add(new PermissionHandlerPlugin());
//       flutterEngine.getPlugins()?.add(new SharedPreferencesPlugin());
//       flutterEngine.getPlugins()?.add(new SqflitePlugin());
//       flutterEngine.getPlugins()?.add(new UrlLauncherPlugin());
//     } catch (e) {
//       Log.e(
//         TAG,
//         "Tried to register plugins with FlutterEngine ("
//           + flutterEngine
//           + ") failed.");
//       Log.e(TAG, "Received exception while registering", e);
//     }
//   }
// }
