
export interface TabData {
  num: number;
  tag_name: string;
  text: Resource;
  icon_after: Resource;
  badge?: number;
  icon: Resource;
}

@Component
export struct BottomTabBar {
  @Prop navCurrentPosition: number = 0;
  tabBarConfigs: TabData[] = [];
  onTabClick: (index: number) => void = () => {
  };

  build() {
    Column() {
      Divider()
        .height(1)
      Row() {
        ForEach(this.tabBarConfigs, (item: TabData, index: number) => {
          Stack() {
            // https://developer.harmonyos.com/cn/docs/documentation/doc-references/ts-container-badge-0000001333800569
            Badge({
              count: item?.badge ?? 0,
              maxCount: 99,
              position: BadgePosition.RightTop,
              style: { color: 0xFFFFFF, fontSize: 9, badgeSize: 12, badgeColor: Color.Red }
            }) {
              Row()
                .width(40)
                .height(40)
            }

            Image(this.navCurrentPosition === index ? item?.icon_after : item?.icon)
              .height(55)
              .width(55)
              .objectFit(ImageFit.Fill)

            if (!(this.navCurrentPosition === index && index == 0)) {
              Text(item?.text)
                .fontWeight(this.navCurrentPosition === index ? FontWeight.Bold : FontWeight.Normal)
                .fontSize(11)
                .fontColor(this.navCurrentPosition === index ? "#6666ff": Color.Black)
                .margin({ bottom: 1})
            }
          }
          .width('20%')
          .alignContent(Alignment.Bottom)
          .onClick(() => {
            // this.navCurrentPosition = item.num;
            // if (index) {
              this.onTabClick(index);
            // }
          })
        }, (item: TabData) => JSON.stringify(item))
      }
    }
    .height(50)
    .margin({bottom: 0})
  }
}

