import { TabData } from '../view/components/BottomTabBar';

/*
 * The DataModel file instead of network data.
 */
export default class DataModel {

  public static readonly NAVIGATION_TABS: TabData[] = [
    {
      num: 0,
      tag_name: "home",
      text: $r('app.string.uik_nav_home_text'),
      icon: $rawfile('main/ic_element_tabbar_home_normal_v1.png'),
      icon_after: $rawfile('main/ic_element_tabbar_home_pressed_v1.png'),
    },
    {
      num: 1,
      tag_name: "destination_tab_home",
      text: $r('app.string.uik_nav_destination_text'),
      icon: $rawfile('main/ic_element_tabbar_destination_normal_v1.png'),
      icon_after: $rawfile('main/ic_element_tabbar_destination_pressed_v1.png'),
    },
    {
      num: 2,
      tag_name: "message_center_home",
      text: $r('app.string.uik_nav_message_text'),
      icon: $rawfile('main/ic_element_tabbar_message_normal_v1.png'),
      icon_after: $rawfile('main/ic_element_tabbar_message_pressed_v1.png'),
    },
    {
      num: 3,
      tag_name: "journey_home",
      text: $r('app.string.uik_nav_journey_text'),
      icon: $rawfile('main/ic_element_tabbar_schedule_normal_v1.png'),
      icon_after: $rawfile('main/ic_element_tabbar_schedule_pressed_v1.png'),
    },
    {
      num: 4,
      tag_name: "usercenter_home",
      text: $r('app.string.uik_nav_my_text'),
      icon: $rawfile('main/ic_element_tabbar_user_normal_v1.png'),
      icon_after: $rawfile('main/ic_element_tabbar_user_pressed_v1.png'),
    }
  ];
}

