import { BackupExtensionAbility, BundleVersion } from '@kit.CoreFileKit';
import { LoginRestore } from '@taobao-ohos/account';
import { Logger } from '@fliggy-ohos/logger';

const TAG = "AndroidToHarmony"
export const logger = new Logger("FliggyBackup");

/*
 * Android to Harmony Next
 * 技术文档：https://aliyuque.antfin.com/up/ornhpt/ofv7kmgfblmc4e4r
 * */
export default class FliggyBackupAbility extends BackupExtensionAbility {
  async onBackup() {
    logger.i(TAG, 'onBackup ok');
  }

  /**
   * 数据恢复处理接口。接口是同步接口，其内部所有的异步操作请进行同步等待。
   *
   * @param bundleVersion 版本信息
   */
  async onRestore(bundleVersion: BundleVersion) {
    logger.i(TAG, `onRestore bundleVersion:${bundleVersion.name} onRestore ok ${JSON.stringify(bundleVersion)}`);
    if (bundleVersion.name.startsWith("0.0.0.0")) {
      // 在此处实现终端设备从HarmonyOS 4.0升级到HarmonyOS NEXT后，应用数据的转换和迁移
      // 涉及异步操作请进行同步等待
      logger.w(TAG, `Android to HarmonyOS NEXT scenario`)
      // 登录数据恢复逻辑
      let loginDataDir = this.context.backupDir + "restore/com.taobao.trip/ce/files/login/"
      LoginRestore.restoreLoginDataInBackupProcessSync(this.context, loginDataDir)

    } else {
      // 在此处实现从HarmonyOS NEXT设备迁移到HarmonyOS NEXT设备后，应用数据的处理。无特殊要求，可以空实现
      // 涉及异步操作请进行同步等待
      logger.w(TAG, `Other scenario`)
    }
  }
}