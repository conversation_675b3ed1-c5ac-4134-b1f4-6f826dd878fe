import { navigator, PageContext, RunningPageStack } from '@fliggy-ohos/router';
import { insightIntent, InsightIntentExecutor } from '@kit.AbilityKit';
import { window } from '@kit.ArkUI';
import { BusinessError } from '@kit.BasicServicesKit';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { Log } from '@ohos/flutter_ohos';
import { WantParserUtils } from '@fliggy-ohos/launcherimpl';

/**
 * 意图调用样例
 */
export default class FliggyInsightIntentExecutor extends InsightIntentExecutor {
  private static readonly VIEW_TRAVEL_DETAILS = 'ViewTravelGuides';
  /**
   * override 执行前台UIAbility意图
   *
   * @param name 意图名称
   * @param param 意图参数
   * @param pageLoader 窗口
   * @returns 意图调用结果
   */
  onExecuteInUIAbilityForegroundMode(name: string, param: Record<string, Object>, pageLoader: window.WindowStage):
    Promise<insightIntent.ExecuteResult> {

    if(WantParserUtils.blockTaskDone) {
      // 热启动 直接跳转到意图框架落地页
      const clickUrl: string = param['clickUrl'] as string ?? ''
      if(clickUrl) {
        const topPageContext: PageContext = RunningPageStack.getTopPageContext()
        navigator.openPage(topPageContext, {
          pageUrl: clickUrl,
        }).catch(() => {})
      }
    } else {
      // 冷启动 记录跳转参数在RouterPageWork里跳转
      let insightIntent: Record<string, Object> = {
        "intentName": name,
        "intentData": param
      };

      WantParserUtils.msgObj = insightIntent
    }

    return Promise.resolve({
      code: 0,
      result: {
        message: 'Intent execute succeed'
      }
    } as insightIntent.ExecuteResult );
  }

}