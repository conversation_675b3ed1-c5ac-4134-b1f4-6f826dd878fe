// @ts-ignore
import { FBDocumentAssistant } from '@alipay/mpaas_flybird/src/main/ets/core/FBDocumentAssistant';
// @ts-ignore
import { WindowUtils } from '@fliggy-ohos/appcompat';
import { display, Size } from '@kit.ArkUI';
import hilog from '@ohos.hilog';

// 支付宝支付的弹窗页面获取的是display的宽度，在折叠屏展开的时候会有问题，所以需要hook
// 还存在的问题，在折叠情况下打开支付宝然后再展开还是会展示不全，因为navigation_mode的回调很慢。
export class FBDocumentAssistantHook extends FBDocumentAssistant {
  private static tempFoldExpandScreenWidth: number = 0;

  public static addSizeChangeListener() {
    WindowUtils.addWindowSizeChangeListener({
      onSizeChange(data: Size) {
        FBDocumentAssistantHook.tempFoldExpandScreenWidth = data.width;
        hilog.info(0x001, 'hook', `FBDocumentAssistantHook getScreenWidth WindowUtils.isTwoColumn():${WindowUtils.isTwoColumn()} "navigation_mode"):${AppStorage.get("navigation_mode")}`);
        hilog.info(0x001, 'hook',
          "FBDocumentAssistantHook onSizeChange getScreenWidth:" + FBDocumentAssistantHook.tempFoldExpandScreenWidth);
      }
    })
  }

  public static getScreenWidth(): number {
    hilog.info(0x001, 'hook', `FBDocumentAssistantHook getScreenWidth WindowUtils.isTwoColumn():${WindowUtils.isTwoColumn()} "navigation_mode"):${AppStorage.get("navigation_mode")}`);
    // @ts-ignore
    if (WindowUtils.isTwoColumn() && AppStorage.get("navigation_mode") === NavigationMode.Split) {
      if (FBDocumentAssistantHook.tempFoldExpandScreenWidth === 0) {
        const disp: display.Display = display.getDefaultDisplaySync();
        FBDocumentAssistantHook.tempFoldExpandScreenWidth = disp.width;
        FBDocumentAssistantHook.addSizeChangeListener();
      }
      let screenWidth = FBDocumentAssistantHook.tempFoldExpandScreenWidth / 2
      hilog.info(0x001, 'hook',
        "FBDocumentAssistantHook getScreenWidth/2:" + screenWidth);
      return screenWidth;
    }
    // @ts-ignore
    let screenWidth = FBDocumentAssistant.screenWidth
    hilog.info(0x001, 'hook', "FBDocumentAssistantHook getScreenWidth:" + screenWidth);
    return screenWidth
  }
}

hilog.info(0x001, 'hook', "FBDocumentAssistantHook load start");
FBDocumentAssistant.getScreenWidth = FBDocumentAssistantHook.getScreenWidth
hilog.info(0x001, 'hook', "FBDocumentAssistantHook load end");
