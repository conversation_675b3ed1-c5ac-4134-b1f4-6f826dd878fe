
// @ts-ignore
import  {MultiNavPathStack}  from "@ohos/multinavigation/src/main/ets/components/mainpage/MultiNavigation";
import { hilog } from "@kit.PerformanceAnalysisKit";


// @ts-ignore
export class MultiNavigationHook extends MultiNavPathStack {
  
  needShowPlaceHolder(){
    hilog.info(0x001, 'MultiNavigationHook',
      `needShowPlaceHolder displayMode: ${this.needRenderDisplayMode.displayMode}`);
    //11 三折叠全部展开
    // https://developer.huawei.com/consumer/cn/doc/best-practices/bpta-matext-guide
    if(this.needRenderDisplayMode.displayMode == 11) {
      return false;
    }
    // @ts-ignore
    return super.needShowPlaceHolder();
  }
  
}