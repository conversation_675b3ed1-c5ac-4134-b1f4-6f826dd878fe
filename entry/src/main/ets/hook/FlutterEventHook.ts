
// export default class 这种导出的必须使用下面这种导入，不能带{}
// @ts-ignore
import TouchEventProcessor from '@ohos/flutter_ohos/src/main/ets/embedding/ohos/TouchEventProcessor';
// @ts-ignore
import { CustomTouchEvent } from '@ohos/flutter_ohos/src/main/ets/plugin/platform/CustomTouchEvent';
import { display, window } from '@kit.ArkUI';
import { hilog } from '@kit.PerformanceAnalysisKit';

export class FlutterEventHook extends TouchEventProcessor {
  private densityPixels: number = display.getDefaultDisplaySync().densityPixels

  private static instance2: FlutterEventHook;

  static getInstance(): FlutterEventHook {
    if (FlutterEventHook.instance2 == null) {
      FlutterEventHook.instance2 = new FlutterEventHook();
    }
    return FlutterEventHook.instance2;
  }

  applyHook(windowStage: window.WindowStage) {
    try {
      let densityPixels: number = display.getDefaultDisplaySync().densityPixels
      let mainWindow = windowStage.getMainWindowSync();
      const width = mainWindow.getWindowProperties().windowRect.width;
      const height = mainWindow.getWindowProperties().windowRect.height;
      hilog.info(0x001, 'FlutterEventHook',
        `setWindowStage width: ${width} height: ${height} densityPixels: ${densityPixels}`);
      this.densityPixels = width / 375;
      hilog.info(0x001, 'FlutterEventHook', `setWindowStage new densityPixels: ${this.densityPixels}`);

      hilog.info(0x001, 'FlutterEventHook', "load start");
      TouchEventProcessor.getInstance = FlutterEventHook.getInstance;
      hilog.info(0x001, 'FlutterEventHook', "load end");
    } catch (e) {
      hilog.error(0x001, 'FlutterEventHook', `load error ${JSON.stringify(e)}`);
    }
  }


  /** Construct the CustomTouchEvent and return. */
  public constureCustomTouchEvent(strings: Array<string>, top: number, left: number): CustomTouchEvent {
    hilog.info(0x001, 'FlutterEventHook', "constureCustomTouchEvent densityPixels: " + this.densityPixels);
    // @ts-ignore
    let touchPacket: TouchPacket = super.decodeTouchPacket(strings, this.densityPixels, top, left);
    // @ts-ignore
    let customTouchEvent: CustomTouchEvent = super.constureCustomTouchEventImpl(touchPacket);
    return customTouchEvent;
  }
}
