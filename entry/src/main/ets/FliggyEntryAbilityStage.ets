import { AbilityStage } from '@kit.AbilityKit';
import { EnvironUtils, StaticContext } from '@fliggy-ohos/appcompat';
import { launchTaskManager } from '@fliggy-ohos/launcherimpl';
import { Logger } from '@fliggy-ohos/logger';
import { TLogConfig } from '@taobao-ohos/tlog/src/main/ets/tlog/TLogConfig';

export const logger = new Logger("entry");

export default class FliggyEntryAbilityStage extends AbilityStage {
  onCreate(): void {
    TLogConfig.isAppDebug = EnvironUtils.debuggable()
    TLogConfig.isDebug = TLogConfig.isAppDebug;
    logger.d('FliggyEntryAbilityStage', 'onCreate');

    let context = this.context.getApplicationContext()
    StaticContext.attachContext(context);
    launchTaskManager.attachContext(this.context);
    logger.d('FliggyEntryAbilityStage', 'onCreate context is ' + context);
  }
}