/*
 * Copyright (c) 2025 Fliggy Team. All rights reserved.
 * FIM 测试组件 - 适配版本
 */

import { hilog } from '@kit.PerformanceAnalysisKit';
import { FIMAPIAdapter } from '../utils/FIMAPIAdapter';

const DOMAIN = 0x0000;

// 测试方法接口定义
interface FIMTestMethod {
  id: string;
  name: string;
  displayName: string;
  category: string;
  func: (params: string) => Promise<string>;
  testParams: string;
}

// 测试结果接口定义
interface TestResult {
  id: string;
  methodName: string;
  params: string;
  result: string;
  success: boolean;
  timestamp: string;
  duration: number;
  expanded?: boolean;
}

@Component
export struct FIMTestComponent {
  // 添加回调函数，用于返回主页面
  onBack?: () => void;
  @State testResults: TestResult[] = [];
  @State isRunning: boolean = false;
  @State selectedCategory: string = '全部';
  @State searchText: string = '';
  @State currentTestingMethod: string = '';
  @State currentSDKMode: string = 'real';
  private scroller: Scroller = new Scroller();
  private apiAdapter: FIMAPIAdapter = FIMAPIAdapter.getInstance();

  // 分类列表
  private categories: string[] = ['全部', '消息相关', '会话相关', '搜索相关', '用户联系人'];

  // 页面初始化
  aboutToAppear() {
    this.getCurrentSDKMode();
  }

  // FIM方法的完整配置（适配版本）
  private fimMethods: FIMTestMethod[] = [
    // 消息相关方法
    {
      id: 'fim_send_message',
      name: 'fim_send_message',
      displayName: '发送消息',
      category: '消息相关',
      func: (params: string) => this.apiAdapter.fim_send_message(params),
      testParams: '{"conversationId":"conv_123","messageType":"text","content":"Test message"}'
    },
    {
      id: 'fim_get_message',
      name: 'fim_get_message',
      displayName: '获取消息',
      category: '消息相关',
      func: (params: string) => this.apiAdapter.fim_get_message(params),
      testParams: '{"messageId":"msg_123"}'
    },

    // 会话相关方法
    {
      id: 'fim_get_conversations',
      name: 'fim_get_conversations',
      displayName: '获取会话列表',
      category: '会话相关',
      func: (params: string) => this.apiAdapter.fim_get_conversations(params),
      testParams: '{"conversationIds":["conv1","conv2"]}'
    },
    {
      id: 'fim_create_conversation',
      name: 'fim_create_conversation',
      displayName: '创建会话',
      category: '会话相关',
      func: (params: string) => this.apiAdapter.fim_create_conversation(params),
      testParams: '{"name":"Test Conversation","members":["user1","user2"]}'
    },
    {
      id: 'fim_get_conversation_detail',
      name: 'fim_get_conversation_detail',
      displayName: '获取会话详情',
      category: '会话相关',
      func: (params: string) => this.apiAdapter.fim_get_conversation_detail(params),
      testParams: '{"conversationId":"conv_123"}'
    },

    // 搜索相关方法
    {
      id: 'fim_search_messages',
      name: 'fim_search_messages',
      displayName: '搜索消息',
      category: '搜索相关',
      func: (params: string) => this.apiAdapter.fim_search_messages(params),
      testParams: '{"keyword":"test","conversationId":"conv_123"}'
    },
    {
      id: 'fim_global_search',
      name: 'fim_global_search',
      displayName: '全局搜索',
      category: '搜索相关',
      func: (params: string) => this.apiAdapter.fim_global_search(params),
      testParams: '{"keyword":"test","types":["message","conversation","contact"]}'
    },

    // 更多消息相关方法
    {
      id: 'fim_delete_message',
      name: 'fim_delete_message',
      displayName: '删除消息',
      category: '消息相关',
      func: (params: string) => this.apiAdapter.fim_delete_message(params),
      testParams: '{"messageId":"msg_123"}'
    },
    {
      id: 'fim_recall_message',
      name: 'fim_recall_message',
      displayName: '撤回消息',
      category: '消息相关',
      func: (params: string) => this.apiAdapter.fim_recall_message(params),
      testParams: '{"messageId":"msg_123"}'
    },

    // 更多会话相关方法
    {
      id: 'fim_delete_conversation',
      name: 'fim_delete_conversation',
      displayName: '删除会话',
      category: '会话相关',
      func: (params: string) => this.apiAdapter.fim_delete_conversation(params),
      testParams: '{"conversationId":"conv_123"}'
    },
    {
      id: 'fim_get_conversation_members',
      name: 'fim_get_conversation_members',
      displayName: '获取会话成员',
      category: '会话相关',
      func: (params: string) => this.apiAdapter.fim_get_conversation_members(params),
      testParams: '{"conversationId":"conv_123"}'
    },

    // 更多搜索相关方法
    {
      id: 'fim_search_conversations',
      name: 'fim_search_conversations',
      displayName: '搜索会话',
      category: '搜索相关',
      func: (params: string) => this.apiAdapter.fim_search_conversations(params),
      testParams: '{"keyword":"test"}'
    },
    {
      id: 'fim_search_contacts',
      name: 'fim_search_contacts',
      displayName: '搜索联系人',
      category: '搜索相关',
      func: (params: string) => this.apiAdapter.fim_search_contacts(params),
      testParams: '{"keyword":"test"}'
    },

    // 用户和联系人相关方法
    {
      id: 'fim_get_user_info',
      name: 'fim_get_user_info',
      displayName: '获取用户信息',
      category: '用户联系人',
      func: (params: string) => this.apiAdapter.fim_get_user_info(params),
      testParams: '{"userId":"user_123"}'
    },
    {
      id: 'fim_get_contacts',
      name: 'fim_get_contacts',
      displayName: '获取联系人列表',
      category: '用户联系人',
      func: (params: string) => this.apiAdapter.fim_get_contacts(params),
      testParams: '{"limit":50,"offset":0}'
    },
    {
      id: 'fim_add_contact',
      name: 'fim_add_contact',
      displayName: '添加联系人',
      category: '用户联系人',
      func: (params: string) => this.apiAdapter.fim_add_contact(params),
      testParams: '{"userId":"user_456","remark":"Test Contact"}'
    }
  ];

  // 获取过滤后的方法列表
  private getFilteredMethods(): FIMTestMethod[] {
    let filtered = this.fimMethods;

    // 按分类过滤
    if (this.selectedCategory !== '全部') {
      filtered = filtered.filter(method => method.category === this.selectedCategory);
    }

    // 按搜索文本过滤
    if (this.searchText.trim() !== '') {
      const searchLower = this.searchText.toLowerCase();
      filtered = filtered.filter(method =>
        method.displayName.toLowerCase().includes(searchLower) ||
        method.name.toLowerCase().includes(searchLower)
      );
    }

    return filtered;
  }

  // 测试单个方法
  private async testMethod(method: FIMTestMethod) {
    if (this.isRunning) return;

    this.isRunning = true;
    this.currentTestingMethod = method.name;
    const startTime = Date.now();

    try {
      hilog.info(DOMAIN, 'FIMTest', `Testing method: ${method.name} with params: ${method.testParams}`);

      const result: string = await method.func(method.testParams);
      const duration = Date.now() - startTime;

      hilog.info(DOMAIN, 'FIMTest', `${method.name} returned: ${result}`);

      // 添加成功结果
      const testResult: TestResult = {
        id: `${method.id}_${Date.now()}`,
        methodName: method.displayName,
        params: method.testParams,
        result: typeof result === 'string' ? result : JSON.stringify(result),
        success: true,
        timestamp: new Date().toLocaleTimeString(),
        duration: duration,
        expanded: true
      };

      this.testResults.unshift(testResult);
      hilog.info(DOMAIN, 'FIMTest', `✅ ${method.name} succeeded in ${duration}ms`);

    } catch (error) {
      const duration = Date.now() - startTime;
      
      const testResult: TestResult = {
        id: `${method.id}_${Date.now()}`,
        methodName: method.displayName,
        params: method.testParams,
        result: `Error: ${error}`,
        success: false,
        timestamp: new Date().toLocaleTimeString(),
        duration: duration,
        expanded: true
      };

      this.testResults.unshift(testResult);
      hilog.error(DOMAIN, 'FIMTest', `❌ ${method.name} failed: ${error}`);
    } finally {
      this.isRunning = false;
      this.currentTestingMethod = '';
    }
  }

  // 测试所有方法
  private async testAllMethods() {
    if (this.isRunning) return;

    const filteredMethods = this.getFilteredMethods();
    hilog.info(DOMAIN, 'FIMTest', `Starting batch test of ${filteredMethods.length} methods`);

    for (const method of filteredMethods) {
      await this.testMethod(method);
      await new Promise<void>(resolve => setTimeout(resolve, 100));
    }

    hilog.info(DOMAIN, 'FIMTest', 'Batch test completed');
  }

  // 切换SDK模式
  private async switchSDKMode() {
    if (this.isRunning) return;

    this.isRunning = true;
    const newMode = this.currentSDKMode === 'mock' ? 'real' : 'mock';

    try {
      const params = JSON.stringify({ useRealSDK: newMode === 'real' });
      const result = await this.apiAdapter.fim_set_sdk_mode(params);

      const resultObj: Record<string, ESObject> = JSON.parse(result) as Record<string, ESObject>;
      if (resultObj.success) {
        this.currentSDKMode = newMode;

        const switchResult: TestResult = {
          id: `switch_${Date.now()}`,
          methodName: `SDK模式切换`,
          params: params,
          result: `已切换到: ${newMode === 'real' ? '真实SDK' : '模拟SDK'}`,
          success: true,
          timestamp: new Date().toLocaleTimeString(),
          duration: 0,
          expanded: true
        };

        this.testResults.unshift(switchResult);
        hilog.info(DOMAIN, 'FIMTest', `SDK mode switched to: ${newMode}`);
      }
    } catch (error) {
      hilog.error(DOMAIN, 'FIMTest', `Failed to switch SDK mode: ${error}`);
    } finally {
      this.isRunning = false;
    }
  }

  // 获取当前SDK模式
  private async getCurrentSDKMode() {
    try {
      const result = await this.apiAdapter.fim_get_sdk_mode('{}');
      hilog.info(DOMAIN, 'FIMTest', `getCurrentSDKMode result: ${result}`);

      const resultObj: Record<string, ESObject> = JSON.parse(result) as Record<string, ESObject>;
      if (resultObj.success && resultObj.data) {
        const dataObj = resultObj.data as Record<string, ESObject>;
        this.currentSDKMode = dataObj.mode as string;
        hilog.info(DOMAIN, 'FIMTest', `SDK mode set to: ${this.currentSDKMode}`);
      } else {
        this.currentSDKMode = 'real';
      }
    } catch (error) {
      hilog.error(DOMAIN, 'FIMTest', `Failed to get SDK mode: ${error}`);
      this.currentSDKMode = 'real';
    }
  }

  // 清空测试结果
  private clearResults() {
    this.testResults = [];
    hilog.info(DOMAIN, 'FIMTest', 'Test results cleared');
  }

  // 切换结果展开状态
  private toggleResultExpanded(result: TestResult) {
    const index = this.testResults.findIndex(r => r.id === result.id);
    if (index !== -1) {
      const originalResult = this.testResults[index];
      const updatedResult: TestResult = {
        id: originalResult.id,
        methodName: originalResult.methodName,
        params: originalResult.params,
        result: originalResult.result,
        success: originalResult.success,
        timestamp: originalResult.timestamp,
        duration: originalResult.duration,
        expanded: originalResult.expanded ? !originalResult.expanded : true
      };
      this.testResults.splice(index, 1, updatedResult);
    }
  }

  build() {
    Row() {
      // 左侧：方法列表区域
      Column() {
        // 标题和控制区域
        Column() {
          // 返回按钮 - 左上角
          Row() {
            Button('← 返回')
              .fontSize(14)
              .height(32)
              .backgroundColor('#6b7280')
              .onClick(() => {
                if (this.onBack) {
                  this.onBack();
                }
              })
          }
          .width('100%')
          .justifyContent(FlexAlign.Start)
          .margin({ bottom: 10 })

          // 标题区域 - 居中
          Column() {
            Text('FIM NAPI Bridge 测试 (适配版)')
              .fontSize(20)
              .fontWeight(FontWeight.Bold)

            Text(`当前模式: ${this.currentSDKMode === 'real' ? '真实SDK' : '模拟SDK'}`)
              .fontSize(12)
              .fontColor(this.currentSDKMode === 'real' ? '#059669' : '#6b7280')
              .margin({ top: 4 })

            Text('🔗 通过 fliggy_im.har 适配')
              .fontSize(10)
              .fontColor('#8b5cf6')
              .margin({ top: 2 })
          }
          .width('100%')
          .alignItems(HorizontalAlign.Center)
          .margin({ bottom: 15 })

          // 分类选择器
          Row() {
            Text('分类:')
              .fontSize(14)
              .margin({ right: 10 })
              .flexShrink(0)

            Scroll(this.scroller) {
              Row() {
                ForEach(this.categories, (category: string) => {
                  Text(category)
                    .fontSize(12)
                    .padding({ left: 8, right: 8, top: 4, bottom: 4 })
                    .backgroundColor(this.selectedCategory === category ? '#3b82f6' : '#e5e7eb')
                    .fontColor(this.selectedCategory === category ? Color.White : '#374151')
                    .borderRadius(4)
                    .margin({ right: 5 })
                    .onClick(() => {
                      this.selectedCategory = category;
                    })
                })
              }
            }
            .scrollable(ScrollDirection.Horizontal)
            .scrollBar(BarState.Off)
            .layoutWeight(1)
          }
          .width('100%')
          .margin({ bottom: 10 })

          // 搜索框
          TextInput({ placeholder: '搜索方法名称...' })
            .width('100%')
            .height(35)
            .fontSize(12)
            .onChange((value: string) => {
              this.searchText = value;
            })
            .margin({ bottom: 10 })

          // 批量操作按钮
          Scroll() {
            Row() {
              Button('测试全部')
                .fontSize(12)
                .height(30)
                .backgroundColor('#10b981')
                .enabled(!this.isRunning)
                .onClick(() => {
                  this.testAllMethods();
                })
                .margin({ right: 5 })
                .flexShrink(0)

              Button('清空结果')
                .fontSize(12)
                .height(30)
                .backgroundColor('#ef4444')
                .onClick(() => {
                  this.clearResults();
                })
                .margin({ right: 5 })
                .flexShrink(0)

              Button(`切换到${this.currentSDKMode === 'real' ? '模拟' : '真实'}SDK`)
                .fontSize(12)
                .height(30)
                .backgroundColor(this.currentSDKMode === 'real' ? '#6b7280' : '#059669')
                .onClick(() => {
                  this.switchSDKMode();
                })
                .flexShrink(0)
            }
          }
          .scrollable(ScrollDirection.Horizontal)
          .scrollBar(BarState.Off)
          .width('100%')
          .margin({ bottom: 10 })

          // 当前测试状态
          if (this.isRunning) {
            Text(`正在测试: ${this.currentTestingMethod}`)
              .fontSize(12)
              .fontColor('#d97706')
              .margin({ bottom: 10 })
          }
        }
        .padding(15)
        .backgroundColor('#ffffff')
        .borderRadius(8)
        .margin({ bottom: 10 })

        // 方法列表
        List({ space: 5 }) {
          ForEach(this.getFilteredMethods(), (method: FIMTestMethod) => {
            ListItem() {
              this.MethodItemComponent(method)
            }
          })
        }
        .layoutWeight(1)
        .backgroundColor('#ffffff')
        .borderRadius(8)
        .padding(10)
      }
      .width('45%')
      .height('100%')
      .margin({ right: 10 })

      // 右侧：测试结果区域
      Column() {
        Text(`测试结果 (${this.testResults.length})`)
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 15 })

        if (this.testResults.length === 0) {
          Column() {
            Text('暂无测试结果')
              .fontSize(14)
              .fontColor('#9ca3af')
              .margin({ top: 50 })
          }
          .width('100%')
          .layoutWeight(1)
          .justifyContent(FlexAlign.Center)
        } else {
          List({ space: 8 }) {
            ForEach(this.testResults, (result: TestResult) => {
              ListItem() {
                this.ResultItemComponent(result)
              }
            })
          }
          .layoutWeight(1)
        }
      }
      .width('55%')
      .height('100%')
      .backgroundColor('#ffffff')
      .borderRadius(8)
      .padding(15)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#f3f4f6')
    .padding(15)
  }

  // 方法项组件
  @Builder
  MethodItemComponent(method: FIMTestMethod) {
    Column() {
      Row() {
        Column() {
          Text(method.displayName)
            .fontSize(14)
            .fontWeight(FontWeight.Medium)
            .fontColor('#1f2937')
            .textAlign(TextAlign.Start)
            .width('100%')

          Text(method.name)
            .fontSize(12)
            .fontColor('#6b7280')
            .textAlign(TextAlign.Start)
            .width('100%')
            .margin({ top: 2 })

          Text(`分类: ${method.category}`)
            .fontSize(11)
            .fontColor('#9ca3af')
            .textAlign(TextAlign.Start)
            .width('100%')
            .margin({ top: 2 })
        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.Start)

        Button('测试')
          .fontSize(12)
          .height(32)
          .backgroundColor(this.isRunning ? '#9ca3af' : '#3b82f6')
          .enabled(!this.isRunning)
          .onClick(() => {
            this.testMethod(method);
          })
      }
      .width('100%')
      .alignItems(VerticalAlign.Center)
    }
    .width('100%')
    .padding(12)
    .backgroundColor('#ffffff')
    .borderRadius(6)
    .border({ width: 1, color: '#e5e7eb' })
  }

  // 结果项组件
  @Builder
  ResultItemComponent(result: TestResult) {
    Column() {
      // 结果头部
      Row() {
        Column() {
          Row() {
            Text(result.success ? '✅' : '❌')
              .fontSize(14)
              .margin({ right: 8 })

            Text(result.methodName)
              .fontSize(14)
              .fontWeight(FontWeight.Medium)
              .fontColor(result.success ? '#065f46' : '#dc2626')
              .layoutWeight(1)
          }
          .width('100%')

          Row() {
            Text(`${result.timestamp} | ${result.duration}ms`)
              .fontSize(11)
              .fontColor('#6b7280')
          }
          .width('100%')
          .margin({ top: 4 })
        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.Start)

        Button(result.expanded ? '收起' : '展开')
          .fontSize(11)
          .height(24)
          .backgroundColor('#f3f4f6')
          .fontColor('#374151')
          .onClick(() => {
            this.toggleResultExpanded(result);
          })
      }
      .width('100%')
      .alignItems(VerticalAlign.Top)

      // 展开的详细信息
      if (result.expanded) {
        Column() {
          Text('参数:')
            .fontSize(12)
            .fontWeight(FontWeight.Medium)
            .fontColor('#374151')
            .margin({ top: 10, bottom: 5 })

          Text(result.params)
            .fontSize(11)
            .fontColor('#6b7280')
            .backgroundColor('#f9fafb')
            .padding(8)
            .borderRadius(4)
            .width('100%')

          Text('结果:')
            .fontSize(12)
            .fontWeight(FontWeight.Medium)
            .fontColor('#374151')
            .margin({ top: 10, bottom: 5 })

          Text(result.result)
            .fontSize(11)
            .fontColor(result.success ? '#065f46' : '#dc2626')
            .backgroundColor(result.success ? '#ecfdf5' : '#fef2f2')
            .padding(8)
            .borderRadius(4)
            .width('100%')
        }
        .width('100%')
        .alignItems(HorizontalAlign.Start)
      }
    }
    .width('100%')
    .padding(12)
    .backgroundColor(result.success ? '#f0fdf4' : '#fef2f2')
    .borderRadius(6)
    .border({
      width: 1,
      color: result.success ? '#bbf7d0' : '#fecaca'
    })
  }
}
