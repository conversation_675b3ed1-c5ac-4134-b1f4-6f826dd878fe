/*
 * The MIT License (MIT)
 *
 * Copyright (c) 2019 Alibaba Group
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
import UIAbility from '@ohos.app.ability.UIAbility';
import window from '@ohos.window';
import Want from '@ohos.app.ability.Want';
import AbilityConstant from '@ohos.app.ability.AbilityConstant';

import { FlutterManager } from '@ohos/flutter_ohos';
import { FlutterBoostDelegate, FlutterBoostRouteOptions, } from '@fliggy-ohos/flutter_boost';
import { atomLogger } from './AtomRouterManager';
import  './AtomPanePage';

export default class AtomAbility extends UIAbility {

  private mainWindow?: window.Window | null;

  async onCreate(want: Want, launchParam: AbilityConstant.LaunchParam) {
    atomLogger.d('AtomAbility', 'atom Ability onCreate');
    FlutterManager.getInstance().pushUIAbility(this)
  }

  onDestroy(): void {
    atomLogger.d('AtomAbility', 'atom Ability onDestroy');
    FlutterManager.getInstance().popUIAbility(this)
  }

  onWindowStageCreate(windowStage: window.WindowStage): void {
    // Main window is created, set main page for this ability
    atomLogger.d('AtomAbility', 'atom Ability onWindowStageCreate');
    FlutterManager.getInstance().pushWindowStage(this, windowStage);

    // 启动页
    // UI注册必须放到loadContent里！！！
    windowStage.loadContent('atom/AtomIndexPage', (err, data) => {
      if (err.code) {
        atomLogger.e('onWindowStageCreate', JSON.stringify(err) ?? '');
        return;
      }

      this.mainWindow = windowStage.getMainWindowSync();
      this.mainWindow?.setWindowLayoutFullScreen(true);

      atomLogger.d('AtomAbility', `onWindowStageCreate: ${JSON.stringify(data)}`);
    });

    atomLogger.d('AtomAbility', 'atom Ability onCreate');

  }

  onWindowStageDestroy(): void {
    // Main window is destroyed, release UI related resources
    atomLogger.d('AtomAbility', 'atom Ability onWindowStageDestroy');
    AppStorage.delete('entryWindowStage');
    FlutterManager.getInstance().popWindowStage(this)
  }

  onForeground(): void {
    // Ability has brought to foreground
    atomLogger.d('AtomAbility', 'atom Ability onForeground');
  }

  onBackground(): void {
    // Ability has back to background
    atomLogger.d('AtomAbility', 'atom Ability onBackground');
  }

}