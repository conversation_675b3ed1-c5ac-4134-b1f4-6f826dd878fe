import { <PERSON><PERSON>, navigator, Na<PERSON><PERSON><PERSON><PERSON>, PageContext, U<PERSON> } from '@fliggy-ohos/router';
import { TitleBar } from '@fliggy-ohos/titlebar';
import { FliggyKV } from '@fliggy-ohos/fliggykv';
import { PermissionHelper } from '@fliggy-ohos/permission';
import { loading, WindowUtils } from '@fliggy-ohos/appcompat';
import { env, EnvConstant } from '@fliggy-ohos/env';
import promptAction from '@ohos.promptAction';
import picker from '@ohos.file.picker';
import { FliggyMediaCreatorPlugin, MediaFileUploadPlugin } from '@fliggy-ohos/jsbridge';
import { Para<PERSON>, JsCallBackContext, H5Utils } from '@fliggy-ohos/unicorn';

import { EntranceSplashTestPageBtn } from '@fliggy-ohos/homepage';
import web_webview from '@ohos.web.webview';
import { SavePicToAlbumPlugin } from '@fliggy-ohos/jsbridge/src/main/ets/plugin/SaveToAlbumPlugin';
import { MyAgooMsgReceiver, AgooNotifyClick} from '@fliggy-ohos/launcherimpl/src/main/ets/tasks/InitAccsTask';
import { window } from '@kit.ArkUI';
import json from '@ohos.util.json';
import { mtop } from '@fliggy-ohos/mtop';
import { Logger } from '@fliggy-ohos/logger';
import { BioPayPlugin } from '@fliggy-ohos/pay';
import { TLogEngine } from '@taobao-ohos/tlog_engine';
import { safetyDetect } from '@kit.DeviceSecurityKit';
import { BusinessError } from '@ohos.base';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { EnvUtil } from '@fliggy-ohos/dynamicrouter/src/main/ets/utils/EnvUtil';
import { login } from '@fliggy-ohos/login';
import { MtopPlugin } from '@fliggy-ohos/jsbridge/src/main/ets/plugin/MtopPlugin';
import {
  closeMiniLiveWindow,
  openMiniLiveWindow
} from '@taobao-ohos/taoliveroom/src/main/ets/liveRoomPage/components/LiveAbility/MiniLive/TaoMiniLiveComponent';
import { Nav } from '@taobao-ohos/nav';
import { FliggyLivePage } from '@fliggy-ohos/fliggylive';

const logger = new Logger('TestPage');

@Entry
@Component
export struct TestPage {
  pageContext?: PageContext;
  private env?: string
  private kv?: FliggyKV

  aboutToAppear(): void {
    this.pageContext?.bind(this);
    this.kv = FliggyKV.getFliggyKVWithMMapID('env')
    this.env = this.kv.decodeString('env', EnvConstant[EnvConstant.RELEASE])
    logger.d('TestPage', 'aboutToAppear' + this.env)
  }

  onPageShow() {
    logger.d('TestPage', 'onPageShow');
  }

  // 只有被@Entry装饰的组件才可以调用页面的生命周期
  onPageHide() {
    logger.d('TestPage', 'onPageHide');
  }

  build() {

    Flex({
      direction: FlexDirection.Column
    }) {
      TitleBar({
        showLeftItem: false,
        title: "我的消息",
      })

      List() {

        ListItem() {
          Button('open fbridge_demo')
            .margin({ top: 10 })
            .onClick(() => {

              navigator.openPage(this.pageContext, {
                pageUrl: "page://flutter_view/fbridge_demo/bridge_demo",
                params: {},
                anim: Anim.push
              });
            })
        }
        ListItem() {
          Button('系统完整性检测')
            .onClick(async () => {
              // 请求系统完整性检测，并处理结果
              let req : safetyDetect.SysIntegrityRequest = {
                nonce : 'imEe1PCRcjGkBCAhOCh6ImADztOZ8ygxlWRs' // 从服务器生成的随机的nonce值
              };
              try {
                hilog.info(0x0000, 'TestPage', 'CheckSysIntegrity begin.');
                const data: safetyDetect.SysIntegrityResponse = await safetyDetect.checkSysIntegrity(req);
                hilog.info(0x0000, 'TestPage', 'Succeeded in checkSysIntegrity: %{public}s', data.result);
              } catch (err) {
                let e: BusinessError = err as BusinessError;
                hilog.error(0x0000, 'TestPage', 'CheckSysIntegrity failed: %{public}d %{public}s', e.code, e.message);
              }
            }).margin({ top: 10 })
        }
        ListItem() {
          Button('获取生物信息')
            .onClick(() => {
              let plugin = new BioPayPlugin()
              let jsonObject : object = new Map<string, string>();
              let params = new Params(jsonObject);
              plugin.executeSafe('get_user_biopay_type',params,new JsCallBackContext((res)=>{
                console.log(res)
              }, (error)=>{
                console.log(error)
              }));
            }).margin({ top: 10 })
        }

        ListItem() {
          Button('开通生物支付')
            .margin({ top: 10 })
            .onClick(() => {
              let plugin = new BioPayPlugin()
              let jsonObject : object = new Map<string, string>();
              jsonObject['targetURL'] = 'abc'
              let params = new Params(jsonObject);
              plugin.executeSafe('set_user_biopay',params,new JsCallBackContext((res)=>{
                console.log(res)
              }, (error)=>{
                console.log(error)
              }));
            })
        }

        ListItem() {
          Button('读取cookie')
            .margin({ top: 10 })
            .onClick(() => {
              let cookie = web_webview.WebCookieManager.fetchCookieSync('https://market.m.taobao.com/app/trip/rx-bus-buy/pages/confirm?quantity=2')
              console.log("[cookie] cookie: " + cookie)
            })
        }
        ListItem() {
          Button('设置cookie')
            .margin({ top: 10 })
            .onClick(() => {
              web_webview.WebCookieManager.configCookieSync('.taobao.com', '_nk_=unsunset;Domain=.taobao.com;Path=/;Expires=Mon, 27-May-2024 04:05:01 GMT;')
              console.log("[cookie] set cookie: _nk_")
            })
        }
        ListItem() {
          Button('上报itrace')
            .margin({ top: 10 })
            .onClick(() => {
              Logger.fitrace('harmony_test', 'test_message_1', 1);
              Logger.fitrace('harmony_test', 'test_message_1', 2);
            })
        }
        ListItem() {
          Button('上报itrace自定义')
            .margin({ top: 10 })
            .onClick(() => {
              Logger.fitraceCustomEvent(128, 'harmony_test_custom', {
                "key1": "value1",
                "key2": "value2"
              });
            })
        }

        ListItem() {
          Button('open h5-qa-test')
            .margin({ top: 10 })
            .onClick(() => {

              navigator.openPage(this.pageContext, {
                pageUrl: "https://market.wapa.taobao.com/app/trip/h5-qa-test/pages/home/<USER>",
                params: {},
                anim: Anim.push
              });
            })
        }

        ListItem() {
          Button('测试大数据mtop')
            .margin({ top: 10 })
            .onClick(() => {
              let plugin = new MtopPlugin()
              const req: object = new Object();
              if (EnvUtil.isPrecast()) {
                req['group'] = env.getAppConfigVale('drouter_group_precast');
              } else {
                req['group'] = env.getAppConfigVale('drouter_group');
              }
              req['clientType'] = 'H5';
              req['appName'] = 'qua_common';
              req['mockUserId'] = login.getUserId();
              req['mockUserId'] = login.getUserId();

              let param : object= new Object();
              param['data'] = req;
              param['v'] = '1.0';
              param['api'] = 'mtop.alitrip.tripabtest.checkabtest';

              let params = new Params(param);

              plugin.executeSafe("mtop", params, new JsCallBackContext((res)=>{
                console.log(res)
              }, (error)=>{
                console.log(error)
              }));
            })
        }

        ListItem() {
          Button('进入直播')
            .margin({ top: 10 })
            .onClick(() => {

              navigator.openPage(this.pageContext, {
                pageUrl: "https://h5.m.taobao.com/taolive/video.html?id=493977006668&livesource=fliggylive&spm=181.29770385.22141.card-0&spmUrl=181.9476855.fy24channel.baokuanzhibo&ttid=100000%40travel_android_9.9.95.108&fpt=pageStrategyId(100)index_type(normal)",
                params: {},
                anim: Anim.push
              });
            })
        }

        ListItem() {
          Button('进入直播Nav')
            .margin({ top: 10 })
            .onClick(() => {
              Nav.from(this).toUri("https://h5.m.taobao.com/taolive/video.html?id=493977006668&livesource=fliggylive&spm=181.29770385.22141.card-0&spmUrl=181.9476855.fy24channel.baokuanzhibo&ttid=100000%40travel_android_9.9.95.108&fpt=pageStrategyId(100)index_type(normal)");
            })
        }

        ListItem() {
          Button('进入直播Nav_跳过定制代理')
            .margin({ top: 10 })
            .onClick(() => {
              Nav.from(this).toUri("https://h5.m.taobao.com/taolive/video.html?id=493977006668&skip_custom_delegate=true")
            })
        }

        ListItem() {
          Button('进入直播Nav_toPage')
            .margin({ top: 10 })
            .onClick(() => {
              Nav.from(this).toPage(FliggyLivePage)
            })
        }

        ListItem() {
          Button('打开小窗')
            .margin({ top: 10 })
            .onClick(() => {
              openMiniLiveWindow()
            })
        }

        ListItem() {
          Button('关闭小窗')
            .margin({ top: 10 })
            .onClick(() => {
              closeMiniLiveWindow()
            })
        }

        ListItem() {
          Button('open bus_list')
            .margin({ top: 10 })
            .onClick(() => {
              // let params: Record<string, Object> = {
              //   "linkId":"","pageSize":"1000","searchSource":"","standardFromStationIds":"","toName":"蚌埠市",
              //   "standardToAreaCode":"340300","spm":"181.7473487.0.0","standardFromAreaCode":"340100","version":"2.0",
              //   "bizTypeIndex":"","sortBy":"{}","supportExpand":true,"t-app-ver":"9.9.66.103","depDate":"2024-04-21",
              //   "requestSource":"home-page","standardFromStationName":"合肥客运中心站","fromName":"合肥市","pageIndex":"0",
              //   "fetchType":"2","standardFromStationId":"1206745","filters":"{}","scene":"common"
              // };
              let params: Record<string, Object> = {
                "linkId": "",
                "pageSize": "1000",
                "searchSource": "",
                "standardFromStationIds": "",
                "fromDivisionName": "",
                "standardToAreaCode": "511100",
                "toName": "乐山市",
                "spm": "181.7473487.0.0",
                "standardFromAreaCode": "510100",
                "version": "2.0",
                "bizTypeIndex": "",
                "fromDivisionCode": "",
                "sortBy": "{}",
                "supportExpand": true,
                "toDivisionCode": "",
                "toDivisionName": "",
                "depDate": "2024-05-01",
                "requestSource": "home-page",
                "standardFromStationName": "",
                "fromName": "成都市",
                "pageIndex": "0",
                "t-app-ver": "9.9.82.103",
                "fetchType": "",
                "standardFromStationId": "",
                "filters": "{}",
                "bus_type": "common",
                "scene": "common"
              };
              navigator.openPage(this.pageContext, {
                pageUrl: "page://flutter_view/bus_list_page",
                params: params,
                anim: Anim.push
              });
            })
        }

        ListItem() {
          Button('打开hotel_home')
            .margin({top: 10})
            .onClick(() => {
              navigator.openPage(this.pageContext, {
                pageUrl: "page://flutter_view/hotel_home_flutter",
                anim: Anim.push
              });
            })
        }
        ListItem() {
          Button('打开交通首页')
            .margin({ top: 10 })
            .onClick(() => {
              let params: Record<string, Object> = {
                'url': 'https://market.m.taobao.com/app/trip/h5-traffic-search/pages/bus/index.html',
              };
              navigator.openPage(this.pageContext, {
                pageUrl: "page://flutter_view/ftraffic_home",
                params: params,
                anim: Anim.push
              });
            })
        }

        ListItem() {
          Button('打开播放器-native')
            .onClick(() => {
              navigator.openPage(this.pageContext, {
                pageUrl: "page://player_demo",
                anim: Anim.none
              })
            }).margin({ top: 10 })
        }

        ListItem() {
          Button('打开OCR证件扫描-native')
            .onClick(() => {
              let params: Record<string,Object> = {};
              params["appkey"] = "flight";

              let scanInfo: Record<string,Object> = {};
              scanInfo['code'] = "210";

              scanInfo['defaultType'] = "0";
              // if (manager.currentCertType == "1") {
              //   if (textIsEmpty(pageParams?.modifyPassenger?.basicInfo?.nationality) ||
              //     pageParams?.modifyPassenger?.basicInfo?.nationality == "中国大陆") {
              //     scanInfo['defaultType'] = "1";
              //   } else {
              //     scanInfo['defaultType'] = "19";
              //   }
              // }

              let typeArrays: Record<string,Object>[] = [];

              let idCard: Record<string,Object> = {};
              idCard['name'] = "身份证";
              idCard['type'] = "0";
              idCard['title'] = "请将身份证人像页置于框内并尝试对齐";
              typeArrays.push(idCard);

              let passportTips : string = "请将护照个人资料页底部条码置于框内，且证件表面无反光，并拍照识别";
              let passport: Record<string,Object> = {};
              passport['name'] = "护照（中国内地）";
              passport['type'] = "1";
              passport['title'] = passportTips;
              typeArrays.push(passport);

              let interPassport : Record<string,Object> = {};
              interPassport['name'] = "护照（其他国家或地区）";
              interPassport['type'] = "19";
              interPassport['title'] = passportTips;
              typeArrays.push(interPassport);

              scanInfo['typeArray'] = typeArrays;
              params["scanInfo"] = json.stringify(scanInfo);

              params['onErrorPopFlag'] = "1";
              params['suportLocalOCR'] = "1";
              params['skipTips'] = false;

              navigator.openPage(this.pageContext, {
                pageUrl: "page://commbiz_ocr_scan",
                anim: Anim.none,
                params:params
              })
            }).margin({ top: 10 })
        }

        ListItem() {
          Button('会场测试')
            .onClick(() => {
              navigator.openPage(this.pageContext, {
                pageUrl: "https://outfliggys.m.taobao.com/wow/pone/pcraft/common/fupr?wh_pid=common%2Ftopbar-test-1&titleBarHidden=2&disableNav=YES&enableLoadingView=true&webViewBackgroundColor=f41c1c",
                anim: Anim.city_guide
              });
            }).margin({ top: 10 })
        }

        ListItem() {
          Button('透明页测试')
            .onClick(() => {
              import("./NativePage");
              navigator.openPage(this.pageContext, {
                pageUrl: "page://native_page",
                params: {
                  '_fli_background_transparent': true
                },
                anim: Anim.push
              }).then((result: NavigatorResult) => {
                promptAction.showToast({ message: JSON.stringify(result.params) });
              }).catch((err:object) => {
                promptAction.showToast({ message: JSON.stringify(err) });
              })
            }).margin({ top: 10 })
        }

        ListItem() {
          Button('mtop测试')
            .onClick(() => {
              navigator.openPage(this.pageContext, {
                pageUrl: "page://easy_mock",
                anim: Anim.city_guide
              });
            }).margin({ top: 10 })
        }

        ListItem() {
          Button('上传测试')
            .onClick(() => {
              try {
                /// 选择图片
                let PhotoSelectOptions = new picker.PhotoSelectOptions();
                PhotoSelectOptions.MIMEType = picker.PhotoViewMIMETypes.IMAGE_TYPE;
                PhotoSelectOptions.maxSelectNumber = 1;
                let photoPicker = new picker.PhotoViewPicker();
                photoPicker.select(PhotoSelectOptions).then(async (PhotoSelectResult) => {
                  console.info('PhotoViewPicker.select successfully, PhotoSelectResult uri: ' + JSON.stringify(PhotoSelectResult));
                  let file_url = PhotoSelectResult.photoUris[0];

                  /// ---------------- JSBridge --------------
                  let plugin = new MediaFileUploadPlugin()

                  let jsonObject : object = new Map<string, string>();
                  jsonObject['file_url'] = file_url
                  jsonObject['media_type'] = 'image'
                  let params = new Params(jsonObject);

                  plugin.executeSafe("media_file_upload", params, new JsCallBackContext((res)=>{
                    console.log(res)
                  }, (error)=>{
                    console.log(error)
                  }));

                  /// ---------------- MediaFileUploadManager --------------
                  /// 声明上传Task
                  // let task: MediaFileUploadTask = {
                  //   bizType: UploadBiz.Image,
                  //   filePath: file_url,
                  //   fileType: 'jpg',
                  //   onSuccess:(fileUrl, result) =>{
                  //     console.log(fileUrl)
                  //     console.log(JSON.stringify(result))
                  //   },
                  //   onProgress:(progress) => {
                  //     console.log('progress : %d', progress)
                  //   },
                  //   onFailure:(errorCode, errorMessage) => {
                  //     console.log(errorMessage)
                  //   },
                  // }
                  // /// 开始上传
                  // MediaFileUploadManager.upload(task)

                })
              } catch (err) {
                console.error('PhotoViewPicker failed with err: ' + err);
              }
            }).margin({ top: 10 })
        }

        ListItem() {
          Button('保存图片到相册')
            .onClick(() => {
              let plugin = new SavePicToAlbumPlugin()
              let jsonObject : object = new Map<string, string>();
              jsonObject['url'] = 'https://gw.alicdn.com/imgextra/i4/O1CN01AM79Rw1fWSh82XHiJ_!!6000000004014-2-tps-187-73.png'
              let params = new Params(jsonObject);
              plugin.executeSafe("save_image_to_album", params, new JsCallBackContext((res)=>{
                console.log(res)
                promptAction.showToast({ message: JSON.stringify(res) });
              }, (error)=>{
                console.log(error)
                promptAction.showToast({ message: JSON.stringify(error) });
              }));
            }).margin({ top: 10 })
        }

        ListItem() {
          Button('打开播放器-flutter')
            .onClick(() => {
              // FlutterBoost.getInstance().open('/fplayer/harmony_demo', {})
              navigator.openPage(this.pageContext, {
                pageUrl: "page://flutter_view/fplayer/harmony_demo",
                params: {},
                anim: Anim.push
              });
            }).margin({ top: 10 })
        }

        ListItem() {
          Button('打开flutter媒体选择页')
            .onClick(() => {
              // FlutterBoost.getInstance().open('/fplayer/harmony_demo', {})
              navigator.openPage(this.pageContext, {
                pageUrl: "page://flutter_view/fmeida_library/producer",
                params: {},
                anim: Anim.push
              });
            }).margin({ top: 10 })
        }

        ListItem() {
          Button('打开flutter分享')
            .onClick(() => {
              // FlutterBoost.getInstance().open('/fplayer/harmony_demo', {})
              navigator.openPage(this.pageContext, {
                pageUrl: "page://flutter_view/fshare/home",
                params: {"title":"行程地图",
                  "h5_url":"https://market.wapa.taobao.com/app/trip/rx-journey-ssr/pages/journey-share?id=13097&_projVer=1.7.0&titleBarHidden=2&disableNav=YES",
                  "content":"玩得酷，靠得住！",
                  "_fli_background_transparent":true

                },
                anim: Anim.push
              });
            }).margin({ top: 10 })
        }

        ListItem() {
          Button('打开flutter发布器')
            .onClick(() => {
              // FlutterBoost.getInstance().open('/fplayer/harmony_demo', {})
              navigator.openPage(this.pageContext, {
                pageUrl: "page://flutter_view/fliggy_common_publish/publish",
                params: {},
                anim: Anim.push
              });
            }).margin({ top: 10 })
        }

        ListItem() {
          Button('权限申请测试')
            .onClick(() => {
              PermissionHelper.requestDemo();
            }).margin({ top: 10 })
        }

        ListItem() {
          Button('获取生物信息')
            .onClick(() => {
              let plugin = new BioPayPlugin()
              let jsonObject : object = new Map<string, string>();
              let params = new Params(jsonObject);
              plugin.executeSafe('get_user_biopay_type',params,new JsCallBackContext((res)=>{
                console.log(res)
              }, (error)=>{
                console.log(error)
              }));
            }).margin({ top: 10 })
        }

        ListItem() {
          Button('loading测试')
            .onClick(() => {
              loading.show()
            }).margin({ top: 10 })
        }
        ListItem() {
          Button('flutter iconfont测试')
            .onClick(() => {
              navigator.openPage(this.pageContext, {
                pageUrl: "page://flutter_view/iconfont_demo",
                params: {},
                anim: Anim.push
              });
            }).margin({top: 10, bottom:10})
        }
        ListItem() {
          Button('相册测试')
            .onClick(() => {
              // let plugin = new GetPhotoPlugin()
              // plugin.executeSafe("getPhotosForFliggyAlbum", Params.parseJson("{\"need_latlan\": \"1\"}"), new JsCallBackContext(null, null));
              // //plugin.executeSafe("getAsignPhotosForFliggyAlbum", Params.parseJson("{\"bridge_type\": \"1\"}"), new JsCallBackContext(null, null));

              // let plugin = new TakePhotoPlugin()
              // //plugin.executeSafe("", Params.parseJson("{\"upload_type\": \"3\", \"pick_type\": \"0\"}"), new JsCallBackContext(null, null));
              // plugin.executeSafe("", Params.parseJson("{\"upload_type\": \"3\", \"pick_type\": 2, \"maxSelect\": \"1\", \"minTime\": \"300\", \"maxTime\": \"5\"}"), new JsCallBackContext(null, null));

              // let plugin = new GetImageDataPlugin()
              // plugin.executeSafe("", Params.parseJson("{\"photoId\": \"file://media/Photo/1/IMG_1711594997_000/IMG_20240328_110137.jpg\", \"photoWidth\": \"0\", \"photoHeight\": \"0\"}"), new JsCallBackContext(null, null));

              let plugin = new FliggyMediaCreatorPlugin();
              plugin.executeSafe("", Params.parseJson("{\"image_count\": \"1\", \"video_count\": \"1\"}"), new JsCallBackContext(null, null));

              //let plugin = new FliggyCpPayPlugin()
              // //plugin.executeSafe("", Params.parseJson("{\"pay_params\":{\"signStr\":\"https://market.m.taobao.com/app/trip/fliggy-cashier?bizOrderIds=%5B%223876974030324069619%22%5D&cashierMode=POST_STANDARD_MULTI_PAY_SYSTEM&renderScene=RENDER_PAY_OPTION\",\"alipayId\":\"\",\"cashierType\":\"FLIGGY_CASHIER\"}}"), new JsCallBackContext(null, null));
              // plugin.executeSafe("", Params.parseJson("{\"pay_params\":{\"signStr\":\"service=\\\"mobile.securitypay.pay\\\"&_input_charset=\\\"utf-8\\\"&app_name=\\\"tb\\\"&appenv=\\\"appid=taobao^system=null^version=5.0\\\"&partner=\\\"PARTNER_TAOBAO_ORDER\\\"&biz_type=\\\"trade\\\"&trade_no=\\\"2024050722001145511413834695\\\"&sign_date=\\\"2024-05-0711:03:10\\\"&extern_token=\\\"114f50bf22e79fb982f8a00ee2b7e663\\\"&sign=\\\"fqs_n_c_e_z%2B_w_a_ndc_sut_z_t_s8_n%2B53r8ck_l_jk_wj_n5_j_l5q_q_aet39ko_paf6c_x_q%3D%3D\\\"&sign_type=\\\"DSA\\\"\",\"alipayId\":\"\",\"cashierType\":\"CALL_ALIPAY_SDK\"}}"), new JsCallBackContext(null, null));

              // let plugin = new CashierPayResultPlugin();
              // plugin.executeSafe("", Params.parseJson("{\"successed\":false,\"result\":{\"memo\":\"手动关闭收银台\"}}"), new JsCallBackContext(null, null));

              // let plugin = new FliggyCpPayInstallPlugin();
              // plugin.executeSafe("", Params.parseJson("{}"), new JsCallBackContext(null, null));

            }).margin({ top: 10 })
        }

        ListItem() {
          Button('地图测试')
            .onClick(() => {
              navigator.openPage(this.pageContext, {
                pageUrl: "page://map_demo",
                anim: Anim.city_guide
              });
            }).margin({ top: 10 })
        }

        ListItem() {
          Button('商详测试')
            .onClick(() => {
              navigator.openPage(this.pageContext, {
                pageUrl: "https://market.m.taobao.com/app/trip/rx-travel-detail/pages/index?id=************&fsharettid=fliggysharesend.0000&popStyle=1&shareId=ding_talk_card&_projVer=2.0.1",
                anim: Anim.city_guide
              });
            }).margin({ top: 10 })
        }

        ListItem() {
          Button('fluter boost问题测试')
            .onClick(() => {
              navigator.openPage(this.pageContext, {
                pageUrl: "page://flutter_view/flutterPage",
                anim: Anim.city_guide
              });
            }).margin({ top: 10 })
        }

        ListItem() {
          Button('发送短信测试')
            .onClick(() => {
              const url = 'sms:12306?body=666'
              const uri = Uri.parse(url)
              const telephone = url.split('?')[0].substring(4);
              let tel : number = 0;
              try {
                tel = Number.parseInt(telephone);
              } catch (e) {
                logger.e('onLoadIntercept', e);
              }
              const content :string = uri.getQueryParameter('body') ?? '';
              H5Utils.gotoSendSmsPage({
                contactsName: telephone,
                telephone: tel,
              }, content).then((data) => {
                logger.d('onLoadIntercept', 'send sms succeeded. data = '+data);
              }).catch((err: Error) => {
                logger.e('onLoadIntercept', 'send sms fail, err is:' + JSON.stringify(err));
              });
            }).margin({ top: 10 })
        }
        ListItem() {
          Button('构建crash')
            .onClick(() => {
              throw new Error(`我是认为构建的crash，时间：${new Date().toLocaleString()}`);
            }).margin({ top: 10 })
        }
        ListItem() {
          Button('构建mtop crash')
            .onClick(() => {
              let api: string | undefined = undefined
              mtop.request<Object>({
                data: new Object(),
                api: api!,
                v: '1.0',
              })
            }).margin({ top: 10 })
        }

        ListItem() {
          Button('构建logger crash')
            .onClick(() => {
              logger.d('logger crash', `test`);
              let pid: ESObject = 1
              let tid: ESObject = 1
              let ts: ESObject = 1
              let level: ESObject = 1
              let module: ESObject = 'true'
              let subModule: ESObject = 'true'
              let category: ESObject = 1
              let content: ESObject = false
              TLogEngine.writeLog(pid, tid,
                ts,
                level,
                module,
                subModule,
                category,
                content);
            }).margin({ top: 10 })
        }
        ListItem() {
          Button('构建一个普通通知')
            .onClick(() => {
              const receiver = new MyAgooMsgReceiver()
              let body: Record<string, Object> = {
                'title': '测试普通通知tile',
                'text': '测试通知内容text',
                'url': 'https://market.m.taobao.com/app/trip/rx-vehicle-search/pages/home',
              }
              let data: Record<string, Object> = {
                "body": body,
                "id": `${new Date().getTime()}`,
              }
              receiver.onMessage(JSON.stringify(data));
            }).margin({ top: 10 })
        }
        ListItem() {
          Button('构建一个大图通知')
            .onClick(() => {
              const receiver = new MyAgooMsgReceiver()
              let body: Record<string, Object> = {
                'title': '测试大图通知tile',
                'text': '测试通知内容text',
                'url': 'https://market.m.taobao.com/app/trip/rx-vehicle-search/pages/home',
                'big-icon': 'https://gw.alicdn.com/imgextra/i4/O1CN01AM79Rw1fWSh82XHiJ_!!6000000004014-2-tps-187-73.png',
                'index': '',
                'exts': {
                  'clearTag':'1'
                } as Record<string, Object>,
              }
              let data: Record<string, Object> = {
                "body": body,
                "id": `${new Date().getTime()}`,
              }
              receiver.onMessage(JSON.stringify(data));
            }).margin({ top: 10 })
        }
        ListItem() {
          Button('构建一个图片通知')
            .onClick(() => {
              const receiver = new MyAgooMsgReceiver()
              let body: Record<string, Object> = {
                'title': '测试图片通知tile',
                'text': '测试通知内容text',
                'url': 'https://market.m.taobao.com/app/trip/rx-vehicle-search/pages/home',
                'img': 'https://gw.alicdn.com/imgextra/i3/O1CN014uexxK1wxLS5xRHKN_!!6000000006374-2-tps-270-54.png',
                'index': '',
              }
              let data: Record<string, Object> = {
                "body": body,
                "id": `${new Date().getTime()}`,
              }
              receiver.onMessage(JSON.stringify(data));
            }).margin({ top: 10 })
          }
        ListItem() {
          Button('旋转屏幕')
            .onClick(() => {
              if(WindowUtils.getWindowStage()?.getMainWindowSync()?.getPreferredOrientation() === window.Orientation.PORTRAIT) {
                WindowUtils.getWindowStage()?.getMainWindowSync()?.setPreferredOrientation(window.Orientation.LANDSCAPE);
              } else {
                WindowUtils.getWindowStage()?.getMainWindowSync()?.setPreferredOrientation(window.Orientation.PORTRAIT);
              }
            }).margin({ top: 10 })
        }
        ListItem() {
          EntranceSplashTestPageBtn()
        }

      }.listDirection(Axis.Vertical)
      .alignListItem(ListItemAlign.Center)
      .flexGrow(1)
      .height('100%')
      .width('100%')

      // Row() {
      //   Column() {
      //
      //
      //
      //
      //   }
      //   .width(CommonConstants.MAIN_PAGE_COLUMN_WIDTH)
      // }
      // .height(CommonConstants.MAIN_PAGE_ROW_WIDTH)
    }
  }
}
