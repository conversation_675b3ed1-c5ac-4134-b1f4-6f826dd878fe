// Copyright (c) 2025 Fliggy Team. All rights reserved.
// Created by guyan on 2025-08-20.
// FIM Engine Start Test Page - Engine启动配置测试页面

import { PageContext } from '@fliggy-ohos/router';
import { TitleBar } from '@fliggy-ohos/titlebar';
import { FliggyKV } from '@fliggy-ohos/fliggykv';
import { EnvConstant } from '@fliggy-ohos/env';
import { Logger } from '@fliggy-ohos/logger';
import { fimsdk_Napi } from '@fliggy-ohos/fliggy_im';
import { fim_engine_start } from 'cpp_nexus'; // portal项目的调试版本FIM Engine

import { promptAction } from '@kit.ArkUI';
import { fileIo } from '@kit.CoreFileKit';

const logger = new Logger('FIMEngineTestPage');
const DOMAIN = 0x0000;

// 配置项接口定义
interface FIMEngineConfig {
  appId: string;
  appKey: string;
  appName: string;
  appVersion: string;
  deviceId: string;
  deviceName: string;
  deviceType: string;
  osName: string;
  osVersion: string;
  dataPath: string;
  timeZone: string;
  envType: string;
}

// 测试结果接口定义
interface TestResult {
  id: string;
  type: 'config' | 'start';
  title: string;
  content: string;
  success: boolean;
  timestamp: string;
  expanded?: boolean;
}

// 配置项接口定义
interface ConfigItem {
  label: string;
  key: string;
  value: string;
  readonly?: boolean; // 是否只读
}

@Entry
@Component
export struct TestFIMPage {
  pageContext?: PageContext;
  private env?: string
  private kv?: FliggyKV

  @State testResults: TestResult[] = [];
  @State isRunning: boolean = false;
  @State useDebugVersion: boolean = true; // 默认使用调试版本
  @State currentOperation: string = '';

  // 配置状态
  @State config: FIMEngineConfig = {
    appId: 'fliggy',
    appKey: 'f5337bdb5dea4868b6c310576aab039a', // 预发环境可用
    appName: 'fliggy',
    appVersion: '1.0.0',
    deviceId: 'test_utdid_67890',
    deviceName: 'test_device_12345',
    deviceType: 'harmony',
    osName: 'harmony',
    osVersion: '12.0.0',
    dataPath: '/data/storage/el2/base/haps/entry/files/fim',
    timeZone: 'Asia/Shanghai',
    envType: 'daily'
  };

  aboutToAppear(): void {
    this.pageContext?.bind(this);
    this.kv = FliggyKV.getFliggyKVWithMMapID('env')
    this.env = this.kv.decodeString('env', EnvConstant[EnvConstant.RELEASE])

    // 同步环境变量到配置中
    if (this.env) {
      this.config.envType = this.env;
    }

    logger.d('FIMEngineTestPage', `aboutToAppear env: ${this.env}, config.envType: ${this.config.envType}`)
    this.addResult('info', '页面初始化', '页面已加载，开始自动配置Engine参数', true);

    // 自动配置Engine参数
    this.autoConfigureEngine();
  }

  onPageShow() {
    logger.d('FIMEngineTestPage', 'onPageShow');
  }

  // 只有被@Entry装饰的组件才可以调用页面的生命周期
  onPageHide() {
    logger.d('FIMEngineTestPage', 'onPageHide');
  }

  // 添加测试结果
  private addResult(type: 'config' | 'start' | 'info', title: string, content: string, success: boolean) {
    const result: TestResult = {
      id: `${type}_${Date.now()}`,
      type: type as 'config' | 'start',
      title: title,
      content: content,
      success: success,
      timestamp: new Date().toLocaleTimeString(),
      expanded: true
    };
    this.testResults.unshift(result);
  }

  // 自动配置Engine参数（页面加载时调用）
  private async autoConfigureEngine() {
    try {
      logger.d('FIMEngineTest', 'Auto-configuring Engine parameters...');

      const fim = fimsdk_Napi;

      // 依次调用各个配置方法
      await fim.set_app_id(this.config.appId);
      fim.setAppKey(this.config.appKey);
      fim.setAppName(this.config.appName);
      fim.setAppVersion(this.config.appVersion);
      fim.setDeviceId(this.config.deviceId);
      fim.setDeviceName(this.config.deviceName);
      fim.setDeviceType(this.config.deviceType);
      fim.setOsName(this.config.osName);
      fim.setOsVersion(this.config.osVersion);

      // 创建数据目录
      try {
        if (!fileIo.accessSync(this.config.dataPath)) {
          fileIo.mkdirSync(this.config.dataPath, true);
          logger.d('FIMEngineTest', `数据目录创建成功: ${this.config.dataPath}`);
        } else {
          logger.d('FIMEngineTest', `数据目录已存在: ${this.config.dataPath}`);
        }
      } catch (error) {
        logger.w('FIMEngineTest', `数据目录创建失败: ${error}, 继续使用默认路径`);
      }

      fim.setDataPath(this.config.dataPath);

      this.addResult('config', '自动配置完成',
        `Engine参数已自动配置完成:\n` +
        `AppID: ${this.config.appId}\n` +
        `AppKey: ${this.config.appKey}\n` +
        `DeviceId: ${this.config.deviceId}\n` +
        `DataPath: ${this.config.dataPath}\n` +
        `EnvType: ${this.config.envType}`,
        true);

      logger.d('FIMEngineTest', 'Auto-configuration completed successfully');

    } catch (error) {
      let errorMessage = `自动配置过程中出现错误: ${error}`;

      if (error && typeof error === 'object') {
        const errorObj = error as Record<string, ESObject>;
        if (errorObj.reason) {
          errorMessage += `\n详细原因: ${errorObj.reason}`;
        }
        if (errorObj.developer_message) {
          errorMessage += `\n开发者信息: ${errorObj.developer_message}`;
        }
      }

      this.addResult('config', '自动配置失败', errorMessage, false);
      logger.e('FIMEngineTest', `Auto-configuration failed: ${errorMessage}`);
    }
  }

  // 配置Engine参数
  private async configureEngine() {
    if (this.isRunning) return;

    this.isRunning = true;
    this.currentOperation = '配置Engine参数';

    try {
      const fim = fimsdk_Napi;

      // 依次调用各个配置方法
      await fim.set_app_id(this.config.appId);
      fim.setAppKey(this.config.appKey);
      fim.setAppName(this.config.appName);
      fim.setAppVersion(this.config.appVersion);
      fim.setDeviceId(this.config.deviceId);
      fim.setDeviceName(this.config.deviceName);
      fim.setDeviceType(this.config.deviceType);
      fim.setOsName(this.config.osName);
      fim.setOsVersion(this.config.osVersion);

      // 创建数据目录（参考 iOS 实现）
      try {
        if (!fileIo.accessSync(this.config.dataPath)) {
          fileIo.mkdirSync(this.config.dataPath, true);
          logger.d('FIMEngineTest', `数据目录创建成功: ${this.config.dataPath}`);
        } else {
          logger.d('FIMEngineTest', `数据目录已存在: ${this.config.dataPath}`);
        }
      } catch (error) {
        logger.w('FIMEngineTest', `数据目录创建失败: ${error}, 继续使用默认路径`);
      }

      fim.setDataPath(this.config.dataPath);

      this.addResult('config', '配置完成',
        `所有配置项已设置:\n` +
        `AppID: ${this.config.appId}\n` +
        `AppKey: ${this.config.appKey}\n` +
        `AppName: ${this.config.appName}\n` +
        `AppVersion: ${this.config.appVersion}\n` +
        `DeviceId: ${this.config.deviceId}\n` +
        `DeviceName: ${this.config.deviceName}\n` +
        `DeviceType: ${this.config.deviceType}\n` +
        `OSName: ${this.config.osName}\n` +
        `OSVersion: ${this.config.osVersion}\n` +
        `DataPath: ${this.config.dataPath}\n` +
        `TimeZone: ${this.config.timeZone}\n` +
        `EnvType: ${this.config.envType}`,
        true);

      logger.d('FIMEngineTest', 'Engine configuration completed successfully');

    } catch (error) {
      // 提取详细的错误信息
      let errorMessage = `配置过程中出现错误: ${error}`;

      // 尝试提取 reason 字段
      if (error && typeof error === 'object') {
        const errorObj = error as Record<string, ESObject>;
        if (errorObj.reason) {
          errorMessage += `\n详细原因: ${errorObj.reason}`;
        }
        if (errorObj.developer_message) {
          errorMessage += `\n开发者信息: ${errorObj.developer_message}`;
        }
        if (errorObj.code) {
          errorMessage += `\n错误代码: ${errorObj.code}`;
        }
      }

      this.addResult('config', '配置失败', errorMessage, false);
      logger.e('FIMEngineTest', `Configuration failed: ${error}, reason: ${error && typeof error === 'object' ? (error as Record<string, ESObject>).reason : 'unknown'}`);
    } finally {
      this.isRunning = false;
      this.currentOperation = '';
    }
  }

  // 启动Engine
  private async startEngine() {
    if (this.isRunning) return;

    this.isRunning = true;
    this.currentOperation = '启动FIM Engine';

    try {
      logger.d('FIMEngineTest', 'Starting FIM Engine...');

      // 根据开关选择使用调试版本还是har包版本
      const versionInfo: string = this.useDebugVersion ? '调试版本 (portal项目)' : 'har包版本 (fliggy_im)';

      logger.d('FIMEngineTest', `Using ${versionInfo}`);

      this.addResult('start', `Engine启动中 (${versionInfo})`,
        `正在使用${versionInfo}启动FIM Engine...`,
        true);

      let result: string;

      if (this.useDebugVersion) {
        // 使用portal项目的调试版本
        result = await fim_engine_start() as string;
      } else {
        // 使用har包版本
        const fim: ESObject = fimsdk_Napi;
        if (fim.fim_engine_start) {
          result = await fim.fim_engine_start() as string;
        } else {
          throw new Error('fim_engine_start 方法不可用');
        }
      }

      this.addResult('start', 'Engine启动成功',
        `FIM Engine已成功启动\n版本: ${versionInfo}\n结果: ${result}`,
        true);

      logger.d('FIMEngineTest', `Engine started successfully with ${versionInfo}: ${result}`);

    } catch (error) {
      // 提取详细的错误信息
      let errorMessage = `启动过程中出现错误: ${error}`;

      // 尝试提取 reason 字段
      if (error && typeof error === 'object') {
        const errorObj = error as Record<string, ESObject>;
        if (errorObj.reason) {
          errorMessage += `\n详细原因: ${errorObj.reason}`;
        }
        if (errorObj.developer_message) {
          errorMessage += `\n开发者信息: ${errorObj.developer_message}`;
        }
        if (errorObj.code) {
          errorMessage += `\n错误代码: ${errorObj.code}`;
        }
      }

      this.addResult('start', 'Engine启动失败', errorMessage, false);
      logger.e('FIMEngineTest', `Engine start failed: ${error}, reason: ${error && typeof error === 'object' ? (error as Record<string, ESObject>).reason : 'unknown'}`);
    } finally {
      this.isRunning = false;
      this.currentOperation = '';
    }
  }

  // 完整测试流程
  private async runFullTest() {
    if (this.isRunning) return;

    this.addResult('info', '开始完整测试', '开始执行配置 -> 启动的完整流程', true);

    // 先配置
    await this.configureEngine();

    // 等待一下再启动
    await new Promise<void>(resolve => setTimeout(resolve, 500));

    // 再启动
    await this.startEngine();

    this.addResult('info', '完整测试结束', '配置和启动流程已完成', true);
  }

  // 清空测试结果
  private clearResults() {
    this.testResults = [];
    logger.d('FIMEngineTest', 'Test results cleared');
  }

  // 切换结果展开状态
  private toggleResultExpanded(result: TestResult) {
    const index = this.testResults.findIndex(r => r.id === result.id);
    if (index !== -1) {
      const originalResult = this.testResults[index];
      const updatedResult: TestResult = {
        id: originalResult.id,
        type: originalResult.type,
        title: originalResult.title,
        content: originalResult.content,
        success: originalResult.success,
        timestamp: originalResult.timestamp,
        expanded: !originalResult.expanded
      };
      this.testResults.splice(index, 1, updatedResult);
    }
  }

  // 获取基础配置项
  private getBasicConfigs(): ConfigItem[] {
    return [
      { label: 'App ID', key: 'appId', value: this.config.appId, readonly: true },
      { label: 'App Key', key: 'appKey', value: this.config.appKey, readonly: true },
      { label: 'App Name', key: 'appName', value: this.config.appName, readonly: true },
      { label: 'App Version', key: 'appVersion', value: this.config.appVersion, readonly: true }
    ];
  }

  // 获取设备配置项
  private getDeviceConfigs(): ConfigItem[] {
    return [
      { label: 'Device ID', key: 'deviceId', value: this.config.deviceId, readonly: true },
      { label: 'Device Name', key: 'deviceName', value: this.config.deviceName, readonly: true },
      { label: 'Device Type', key: 'deviceType', value: this.config.deviceType, readonly: true },
      { label: 'OS Name', key: 'osName', value: this.config.osName, readonly: true },
      { label: 'OS Version', key: 'osVersion', value: this.config.osVersion, readonly: true }
    ];
  }

  // 获取环境配置项
  private getEnvConfigs(): ConfigItem[] {
    return [
      { label: 'Data Path', key: 'dataPath', value: this.config.dataPath, readonly: true },
      { label: 'Time Zone', key: 'timeZone', value: this.config.timeZone, readonly: true },
      { label: 'Env Type', key: 'envType', value: this.config.envType } // 环境类型可以修改
    ];
  }

  // 更新配置值
  private updateConfigValue(key: string, value: string) {
    switch (key) {
      case 'appId':
        this.config.appId = value;
        break;
      case 'appKey':
        this.config.appKey = value;
        break;
      case 'appName':
        this.config.appName = value;
        break;
      case 'appVersion':
        this.config.appVersion = value;
        break;
      case 'deviceId':
        this.config.deviceId = value;
        break;
      case 'deviceName':
        this.config.deviceName = value;
        break;
      case 'deviceType':
        this.config.deviceType = value;
        break;
      case 'osName':
        this.config.osName = value;
        break;
      case 'osVersion':
        this.config.osVersion = value;
        break;
      case 'dataPath':
        this.config.dataPath = value;
        break;
      case 'timeZone':
        this.config.timeZone = value;
        break;
      case 'envType':
        this.config.envType = value;
        break;
    }
  }

  build() {
    Column() {
      // 标题栏
      Row() {
        Text("FIM Engine 测试")
          .fontSize(20)
          .fontWeight(FontWeight.Bold)
          .margin({ left: 16 })
      }
      .width('100%')
      .height(56)
      .backgroundColor('#f8f9fa')
      .justifyContent(FlexAlign.Start)
      .alignItems(VerticalAlign.Center)

      Row() {
        // 左侧：配置区域
        Column() {
          // 当前操作状态
          if (this.isRunning) {
            Text(`正在执行: ${this.currentOperation}`)
              .fontSize(12)
              .fontColor('#d97706')
              .margin({ bottom: 10 })
          }

          // 配置表单区域
          Scroll() {
            Column() {
              this.ConfigSectionComponent('基础配置', this.getBasicConfigs())
              this.ConfigSectionComponent('设备配置', this.getDeviceConfigs())
              this.ConfigSectionComponent('环境配置', this.getEnvConfigs())

              // 版本选择开关
              Column() {
                Row() {
                  Text('使用调试版本')
                    .fontSize(14)
                    .fontColor('#374151')

                  Blank()

                  Toggle({ type: ToggleType.Switch, isOn: this.useDebugVersion })
                    .selectedColor('#10b981')
                    .onChange((isOn: boolean) => {
                      this.useDebugVersion = isOn;
                      this.addResult('config', '版本切换',
                        `已切换到${isOn ? '调试版本 (portal项目)' : 'har包版本 (fliggy_im)'}`,
                        true);
                    })
                }
                .width('100%')
                .padding({ left: 15, right: 15, top: 10, bottom: 10 })
                .backgroundColor('#f8fafc')
                .borderRadius(8)
                .margin({ bottom: 10 })

                Text(this.useDebugVersion ?
                  '🔧 调试版本：可设置断点，查看详细日志' :
                  '📦 har包版本：使用fliggy_im正式版本')
                  .fontSize(12)
                  .fontColor('#6b7280')
                  .textAlign(TextAlign.Center)
                  .width('100%')
                  .margin({ bottom: 15 })
              }
              .padding(15)
              .backgroundColor('#ffffff')
              .borderRadius(8)
              .margin({ bottom: 10 })

              // 操作按钮
              Column() {
                Button('配置Engine参数')
                  .width('100%')
                  .height(40)
                  .backgroundColor('#3b82f6')
                  .enabled(!this.isRunning)
                  .onClick(() => {
                    this.configureEngine();
                  })
                  .margin({ bottom: 10 })

                Button('启动Engine')
                  .width('100%')
                  .height(40)
                  .backgroundColor('#10b981')
                  .enabled(!this.isRunning)
                  .onClick(() => {
                    this.startEngine();
                  })
                  .margin({ bottom: 10 })

                Button('完整测试')
                  .width('100%')
                  .height(40)
                  .backgroundColor('#8b5cf6')
                  .enabled(!this.isRunning)
                  .onClick(() => {
                    this.runFullTest();
                  })
                  .margin({ bottom: 10 })

                Button('清空结果')
                  .width('100%')
                  .height(40)
                  .backgroundColor('#ef4444')
                  .onClick(() => {
                    this.clearResults();
                  })
              }
              .padding(15)
              .backgroundColor('#ffffff')
              .borderRadius(8)
              .margin({ top: 10 })
            }
          }
          .layoutWeight(1)
        }
        .width('45%')
        .height('100%')
        .margin({ right: 10 })
        .padding(10)

        // 右侧：测试结果区域
        Column() {
          Text(`测试结果 (${this.testResults.length})`)
            .fontSize(18)
            .fontWeight(FontWeight.Bold)
            .margin({ bottom: 15 })

          if (this.testResults.length === 0) {
            Column() {
              Text('暂无测试结果')
                .fontSize(14)
                .fontColor('#9ca3af')
                .margin({ top: 50 })
            }
            .width('100%')
            .layoutWeight(1)
            .justifyContent(FlexAlign.Center)
          } else {
            List({ space: 8 }) {
              ForEach(this.testResults, (result: TestResult) => {
                ListItem() {
                  this.ResultItemComponent(result)
                }
              })
            }
            .layoutWeight(1)
          }
        }
        .width('55%')
        .height('100%')
        .backgroundColor('#ffffff')
        .borderRadius(8)
        .padding(15)
      }
      .width('100%')
      .layoutWeight(1)
      .backgroundColor('#f3f4f6')
      .padding(15)
    }
  }

  // 配置分组组件
  @Builder
  ConfigSectionComponent(title: string, configs: ConfigItem[]) {
    Column() {
      Text(title)
        .fontSize(16)
        .fontWeight(FontWeight.Medium)
        .fontColor('#1f2937')
        .margin({ bottom: 10 })

      ForEach(configs, (configItem: ConfigItem) => {
        Column() {
          // 标签
          Text(configItem.label)
            .fontSize(12)
            .fontColor('#374151')
            .width('100%')
            .textAlign(TextAlign.Start)
            .margin({ bottom: 4 })

          if (configItem.readonly) {
            // 只读模式：直接全部展示
            Text(configItem.value)
              .fontSize(12)
              .fontColor('#1f2937')
              .copyOption(CopyOptions.LocalDevice) // 允许复制
              .width('100%')
              .textAlign(TextAlign.Start)
              .wordBreak(WordBreak.BREAK_ALL) // 支持单词换行
              .lineHeight(16) // 设置行高
              .backgroundColor('#f9fafb')
              .borderRadius(4)
              .padding({ left: 8, right: 8, top: 6, bottom: 6 })
              .constraintSize({ minHeight: 32 })
          } else {
            // 可编辑模式：使用TextInput
            TextInput({ text: configItem.value })
              .width('100%')
              .height(32)
              .fontSize(12)
              .onChange((value: string) => {
                // 更新配置值
                this.updateConfigValue(configItem.key, value);
              })
          }
        }
        .width('100%')
        .alignItems(HorizontalAlign.Start)
        .margin({ bottom: 12 })
      })
    }
    .width('100%')
    .padding(15)
    .backgroundColor('#ffffff')
    .borderRadius(8)
    .margin({ bottom: 10 })
    .alignItems(HorizontalAlign.Start)
  }

  // 结果项组件
  @Builder
  ResultItemComponent(result: TestResult) {
    Column() {
      // 结果头部
      Row() {
        Column() {
          Row() {
            Text(result.success ? '✅' : '❌')
              .fontSize(14)
              .margin({ right: 8 })

            Text(result.title)
              .fontSize(14)
              .fontWeight(FontWeight.Medium)
              .fontColor(result.success ? '#065f46' : '#dc2626')
              .layoutWeight(1)
          }
          .width('100%')

          Row() {
            Text(`${result.timestamp} | ${result.type}`)
              .fontSize(11)
              .fontColor('#6b7280')
          }
          .width('100%')
          .margin({ top: 4 })
        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.Start)

        Button(result.expanded ? '收起' : '展开')
          .fontSize(11)
          .height(24)
          .backgroundColor('#f3f4f6')
          .fontColor('#374151')
          .onClick(() => {
            this.toggleResultExpanded(result);
          })
      }
      .width('100%')
      .alignItems(VerticalAlign.Top)

      // 展开的详细信息
      if (result.expanded) {
        Column() {
          Text('详细信息:')
            .fontSize(12)
            .fontWeight(FontWeight.Medium)
            .fontColor('#374151')
            .margin({ top: 10, bottom: 5 })

          Text(result.content)
            .fontSize(11)
            .fontColor(result.success ? '#065f46' : '#dc2626')
            .backgroundColor(result.success ? '#ecfdf5' : '#fef2f2')
            .padding(8)
            .borderRadius(4)
            .width('100%')
        }
        .width('100%')
        .alignItems(HorizontalAlign.Start)
      }
    }
    .width('100%')
    .padding(12)
    .backgroundColor(result.success ? '#f0fdf4' : '#fef2f2')
    .borderRadius(6)
    .border({
      width: 1,
      color: result.success ? '#bbf7d0' : '#fecaca'
    })
  }
}
