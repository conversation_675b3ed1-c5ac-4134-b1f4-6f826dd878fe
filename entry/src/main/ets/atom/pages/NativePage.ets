import { Fliggy<PERSON><PERSON><PERSON>or, navigator, PageContext } from '@fliggy-ohos/router';

/*
 * The MIT License (MIT)
 *
 * Copyright (c) 2019 Alibaba Group
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */

const EVENT_BACK_PRESS = 'EVENT_BACK_PRESS'

@Entry
@Component
export struct NativePage {
  @State currentIndex: number = 0;
  pageContext = new PageContext("page://nativepage", new Object());

  aboutToAppear() {
  }

  onBackPress() {
    // window.findWindow("subWindow").destroyWindow().then((res) => {
    //   console.log("destroyWindow success")
    // }).catch(() => {
    //   console.log("destroyWindow fail")
    // })
    return true
  }

  build() {
    Column() {
      Text("原生artui界面")
        .fontSize("20fp")
        .fontColor(Color.Black)
        .fontWeight(500)

      Button('会场测试')
        .onClick(() => {
          navigator.openPage(this.pageContext, {
            pageUrl: "https://outfliggys.m.taobao.com/wow/pone/pcraft/common/fupr?wh_pid=common%2Ftopbar-test-1&titleBarHidden=2&disableNav=YES&enableLoadingView=true&webViewBackgroundColor=f41c1c",
          });
        }).margin({ top: 10 })

      Button('pop')
        .onClick(() => {
          navigator.popToBack(this.pageContext, {
            params: {
              arg1: "abc",
              arg2: 123,
              arg3: true
            }
          });
        }).margin({ top: 10 })
    }
    // .margin({top: 200})
    .width('100%')
    .height('100')
    .backgroundColor(Color.Red)
  }

  pageTransition() {
    PageTransitionEnter({ type: RouteType.None, duration: 0 })
    PageTransitionExit({ type: RouteType.None, duration: 0 })
  }
}
