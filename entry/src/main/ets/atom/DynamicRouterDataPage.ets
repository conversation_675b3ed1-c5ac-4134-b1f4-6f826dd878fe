import { TitleBar } from '@fliggy-ohos/titlebar';
import { PageContext } from '@fliggy-ohos/router';
import { BackWidget } from './Widgets';
import { atomRouter } from './AtomRouterManager';
import { PreferencesUtil } from '@fliggy-ohos/dynamicrouter/src/main/ets/utils/PreferencesUtil';

@Entry()
@Component
export struct DynamicRouterDataPage {
  pageContext?: PageContext;
  private data: object = new Object();

  aboutToAppear(): void {
    this.data = PreferencesUtil.getDynamicRouterData().toObject();
  }

  @Builder
  leftItem() {
    BackWidget()
  }

  build() {
    Flex({
      direction: FlexDirection.Column
    }) {
      TitleBar({
        title: '司南数据', leftItemParam: this.leftItem, showLeftItem: true, onLeftItemPressed: {
          onPressed: (event) => {
            this.onBackPress()
          }
        }
      }).onClick(() => {
        this.onBackPress()
      })
      List() {
        ForEach(Object.entries(this.data), (item: [string, Object], index: number) => {
          ItemWidget({ item })
        })
      }
      .width('100%')
      .height('100%')
      .margin({ top: 20,bottom: 20 })
      .divider({ strokeWidth: 1, color: Color.Gray })
      .padding(10)
      .listDirection(Axis.Vertical)
      .alignListItem(ListItemAlign.Start)
      .flexGrow(1)
    }
  }

  onBackPress(): boolean | void {
    atomRouter.pop();
    return true;
  }
}

@Component
struct ItemWidget {
  item?:  [string, Object]

  build() {
    Column() {
      Text(`${this.item?.[0]}`).fontSize(16).fontColor(Color.Green)
      Text(`${JSON.stringify(this.item?.[1])}`).fontSize(14)
    }.onClick(async () => {
      console.log('click');
    }).margin({ left: 20 }).align(Alignment.End)
  }
}