import { env, EnvConstant } from '@fliggy-ohos/env';
import { atomLogger, changeWindow } from './AtomRouterManager';
import { PageContext } from '@fliggy-ohos/router';


@Entry
@Component
export struct AtomFloatBtn {

  pageContext?: PageContext

  name: string = AppStorage.get<string>('atomWindowFloatBtnType') ?? 'entryWindowAtomFloatBtn';

  @StorageLink('atomLeft') left : number = 50;
  @StorageLink('atomTop') top: number = 300;

  lastX = -1
  lastY = -1

  build() {
    Stack({ alignContent: Alignment.Bottom }) {
      Image($r('app.media.icon_atomkit'))
      Text(`${this.name === 'entryWindowAtomFloatBtn' ? '' : 'atom\r\n'}${env?.getEnvironmentName() === EnvConstant.PRECAST ? '预发' : env?.getEnvironmentName() === EnvConstant.SPEC ? '安全生产' : '线上'}`).fontSize(10)
    }.width('45').height('45').onClick(async () => {
      atomLogger.d('AtomFloatBtn',`onTouch onClick`)
      changeWindow(this.name)
    }).onTouch((e) => {
      console.log(`onTouch display name=${this.name}} type=${e.type} ${e.touches.map((t) => `x:${t.displayX} y:${t.displayY}`).join('  ')}`)
      console.log(`onTouch display this.left:${this.left} this.top:${this.top}`)
      if(e.type == TouchType.Down) {
        this.lastX = e.touches[0].displayX
        this.lastY = e.touches[0].displayY
      } else if(e.type == TouchType.Move) {
        let moveX = e.touches[0].displayX
        let moveY = e.touches[0].displayY
        let deltaX = moveX - this.lastX
        let deltaY = moveY - this.lastY
        if(Math.abs(deltaX) > 4 || Math.abs(deltaY) > 4) {
          this.lastX = moveX
          this.lastY = moveY
          this.left = this.left + deltaX
          this.top = this.top + deltaY
        }
      } else {
        this.lastX = -1
        this.lastY = -1
      }
    }).backgroundColor(Color.Transparent).draggable(true).margin({top: this.top, left: this.left})
  }
}