import { env, EnvConstant } from '@fliggy-ohos/env'
import { FliggyKV } from '@fliggy-ohos/fliggykv'
import { FliggyNavigator, navigator, PageContext } from '@fliggy-ohos/router'
import { AtomShellPage } from './AtomShellPage'
import { changeWindow, setAtomRouter } from './AtomRouterManager'
import { BackWidget } from './Widgets'


@Entry({ routeName: "atom_index_page" })
@Component
export struct AtomIndexPage {
  pageContext = new PageContext("page://atom_index_page", new Object());

  @State atomRouter: NavPathStack = new NavPathStack();

  private env?: string
  private kv?: FliggyKV

  @Builder
  leftItem() {
    BackWidget()
  }

  aboutToAppear(): void {
    setAtomRouter(this.atomRouter)
    this.kv = FliggyKV.getFliggyKVWithMMapID('env')
    this.env = this.kv.decodeString('env', EnvConstant[EnvConstant.RELEASE])
    this.atomRouter.pushPath({name: 'atom_pane_page', param: new Object()}, false)
  }

  @Builder
  routerMap(pageUrl: string, params: object) {
    AtomShellPage({ pageContext: new PageContext(pageUrl, params) });
  };

  @Builder
  atomIcon(pageContext: PageContext) {
    FliggyNavigator.getBuilder('atom_icon')?.builder(pageContext);
  }

  build() {
    Stack({ alignContent: Alignment.TopStart }) {
      Navigation(this.atomRouter).navDestination(this.routerMap)
        .hideNavBar(true)
        .width("100%").height("100%");
      this.atomIcon(this.pageContext)
    }
  }

  onBackPress(): boolean | void {
    changeWindow()
    return true;
  }

}