import { FliggyNavigator, PageContext } from '@fliggy-ohos/router';
import router from '@ohos.router';
import { RouterUtils } from '@fliggy-ohos/router/src/main/ets/utils/RouterUtils';
import { builderMap } from './AtomRouterManager';

@Component
export struct AtomShellPage {
  pageContext?: PageContext;
  isTransparent: boolean = false;

  //前一个页面
  previewPageContext?: PageContext;

  @Builder
  routerMap(pageContext: PageContext) {
    if (builderMap.has(RouterUtils.getRouterName(pageContext.pageUrl.toString()))) {
      builderMap.get(RouterUtils.getRouterName(pageContext.pageUrl.toString()))?.builder(pageContext);
    } else {
      FliggyNavigator.getBuilder(RouterUtils.getRouterName(pageContext.pageUrl.toString()))?.builder(pageContext);
    }
  };

  @Builder
  atomIcon(pageContext: PageContext) {
    if(FliggyNavigator.getBuilder('atom_icon')) {
      FliggyNavigator.getBuilder('atom_icon')?.builder(pageContext);
    } else {
      Stack();
    }
  };

  build() {
    NavDestination() {
      this.routerMap(this.pageContext!);
    }
    .title('')
    .hideTitleBar(true)
    .width('100%')
    .height('100%')
    .mode(this.isTransparent ? NavDestinationMode.DIALOG : NavDestinationMode.STANDARD)
    .onShown(() => {
      if (this.isTransparent) {
        this.previewPageContext?.onPageHide();
      }
      this.pageContext?.onPageShow();
    })
    .onHidden(() => {
      this.pageContext?.onPageHide();
      if (this.isTransparent) {
        this.previewPageContext?.onPageShow();
      }
    })
    .onBackPressed((): boolean => {
      return this.pageContext!.onBackPress();
    })
  }

  aboutToAppear(): void {
    if (this.pageContext == undefined) {
      let params = router.getParams() as PageContext;
      this.pageContext = new PageContext(params.pageUrl, params.params);
    }
    if (RouterUtils.isBackgroundTransparent(this.pageContext.pageUrl) || !!this.pageContext.params["_fli_background_transparent"]) {
      this.isTransparent = true;
    }

  }

  aboutToDisappear(): void {
  }

  onPageShow(): void {
    this.pageContext?.onPageShow();
  }

  onPageHide(): void {
    this.pageContext?.onPageHide();
  }

  onBackPress(): boolean {
    return this.pageContext!.onBackPress();
  }
}
