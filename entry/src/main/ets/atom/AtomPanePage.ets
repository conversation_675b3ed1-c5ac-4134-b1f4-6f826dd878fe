import { TitleBar } from '@fliggy-ohos/titlebar'
import { env, EnvConstant } from '@fliggy-ohos/env'
import { FliggyKV } from '@fliggy-ohos/fliggykv'
import { promptAction } from '@kit.ArkUI'
import { Fliggy<PERSON>avi<PERSON>or, navigator, PageContext } from '@fliggy-ohos/router'
import { atomRouter, changeWindow, setAtomRouter } from './AtomRouterManager'
import { BackWidget } from './Widgets'
import { Logger } from '@fliggy-ohos/logger'
import { registerGlobalWindow } from '@taobao-ohos/nav/src/main/ets/windows/GlobalWindows'


@Entry({ routeName: "atom_pane_page" })
@Component
export struct AtomPanePage {
  pageContext = new PageContext("page://atom_pane_page", new Object());

  private env?: string
  private kv?: FliggyKV

  @Builder
  leftItem() {
    BackWidget()
  }

  aboutToAppear(): void {
    this.kv = FliggyKV.getFliggyKVWithMMapID('env')
  }

  build() {
    Stack({ alignContent: Alignment.TopStart }) {
      Flex({
        direction: FlexDirection.Column
      }) {
        TitleBar({
          title: '阿童木面板', leftItemParam: this.leftItem, showLeftItem: true, onLeftItemPressed: {
            onPressed: (event) => {
              this.onBackPress()
            }
          }
        }).onClick(() => {
          this.onBackPress()
        })
        List() {
          ListItem() {
            Button(`切环境-(当前${env.getEnvironmentName() === EnvConstant.RELEASE ? '线上' : env.getEnvironmentName() === EnvConstant.SPEC ? '安全生产' : '预发'})`)
              .onClick(async () => {
                let result = await promptAction.showDialog({
                  title: '切换环境',
                  buttons: [{
                    text: '线上',
                    color: '#000000',
                  },
                    {
                      text: '预发',
                      color: '#000000',
                    }, {
                      text: '安全生产',
                      color: '#000000',
                    }],
                });
                let index = result.index
                let e: string | undefined
                if (index == 1) {
                  e = EnvConstant[EnvConstant.PRECAST]
                } else if (index == 2) {
                  e = EnvConstant[EnvConstant.SPEC]
                } else if (index == 0) {
                  e = EnvConstant[EnvConstant.RELEASE]
                }
                if (e) {
                  this.kv!.encodeString('env', e!)
                  promptAction.showToast({ message: '重启app生效', duration: 1000 });
                }

              }).margin({ top: 10 })
          }

          ListItem() {
            Button(`EasyMock设置页面`)
              .onClick(async () => {
                atomRouter.pushPath({name: 'ezmock_page', param: new Object()})
              }).margin({ top: 10 })
          }
          ListItem() {
            Button(`输出debug日志:${Logger.releaseShowDebug ? '已开启' : '未开启' }}`)
              .onClick(async () => {
                Logger.releaseShowDebug = true
                env.setDebugConfig('printLog', 'true')
                promptAction.showToast({ message: '已开启debug日志', duration: 1000 });
              }).margin({ top: 10 })
          }

          ListItem() {
            Button('router测试')
              .onClick(() => {
                changeWindow()
                navigator.openPage(this.pageContext, {pageUrl:'https://outfliggys.m.taobao.com/app/trip/rx-trip-ticket/pages/detail?_fli_newpage=1&un_flutter=true&flutter_path=/poi_detail_page&_fli_unify=false&titleBarHidden=2&disableNav=YES&poiId=2152&fpt=fsk(5259992869e8900)&propertyParam=%7b%22detailSrc%22%3a%22search%22%7d'})
              }).margin({top: 10, bottom:10})
          }
          ListItem() {
            Button(`test fim`)
              .onClick(async () => {
                changeWindow()
                navigator.openPage(this.pageContext, {pageUrl:'page://temp_test_fim'})
              }).margin({ top: 10 })
          }
          ListItem() {
            Button(`test page页面`)
              .onClick(async () => {
                changeWindow()
                navigator.openPage(this.pageContext, {pageUrl:'page://temp_test'})
              }).margin({ top: 10 })
          }
          ListItem() {
            Button(`onlinetools page页面`)
              .onClick(async () => {
                changeWindow()
                navigator.openPage(this.pageContext, {pageUrl:'page://onlinetools_debug'})
              }).margin({ top: 10 })
          }
          ListItem() {
            Button(`关闭atom`)
              .onClick(async () => {
                registerGlobalWindow("atom_icon", null);
              }).margin({ top: 10 })
          }
          ListItem() {
            Button(`司南配置查看页面`)
              .onClick(async () => {
                atomRouter.pushPath({name: 'sinan_page', param: new Object()})
              }).margin({ top: 10 })
          }
        }
        .width('100%')
        .height('100%')
        .margin({ top: 20,bottom: 20 })
        .padding(10)
        .listDirection(Axis.Vertical)
        .alignListItem(ListItemAlign.Start)
        .flexGrow(1)
      };
    }.width('100%')
    .height('100%')
  }

  onBackPress(): boolean | void {
    changeWindow()
    return true;
  }

}