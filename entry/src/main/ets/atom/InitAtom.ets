import { FliggyNavigator, PageContext } from '@fliggy-ohos/router';
import { AtomFloatBtn } from './AtomFloatBtn';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { EnvironUtils } from '@fliggy-ohos/appcompat';
import { initAtomNav } from './AtomRouterManager';
import { registerGlobalWindow } from '@taobao-ohos/nav/src/main/ets/windows/GlobalWindows';

@Builder
function buildAtomIcon(pageContext: PageContext) {
  AtomFloatBtn({pageContext: pageContext});
}

PersistentStorage.persistProp('need_show_atom_icon', false);
let needShowAtomIcon = AppStorage.get<boolean>('need_show_atom_icon');
hilog.debug(0x00, 'InitAtom', 'needShowAtomIcon:'+needShowAtomIcon);
if(needShowAtomIcon || EnvironUtils.debuggable()) {
  setTimeout(() => {
    FliggyNavigator.registerBuilder("atom_icon", wrapBuilder(buildAtomIcon));
    registerGlobalWindow("atom_icon", FliggyNavigator.getBuilder('atom_icon'));
    initAtomNav()
    hilog.debug(0x00, 'InitAtom', 'registerBuilder success');
  }, 2000);
}

