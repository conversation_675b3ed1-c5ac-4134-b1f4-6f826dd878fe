import { FliggyNavigator, PageContext } from '@fliggy-ohos/router';
import { Logger } from '@fliggy-ohos/logger';
import { common, Want } from '@kit.AbilityKit';
import { BusinessError } from '@kit.BasicServicesKit';
import { DynamicRouterDataPage } from './DynamicRouterDataPage';
import { env } from '@fliggy-ohos/env';
import EzmockPlaygroundIndex from '@taobao-ohos/ezmock/src/main/ets/pages/EzmockPlaygroundIndex';
import { PlayerDemoPage } from '@fliggy-ohos/player';
import { TestPage } from './pages/TestPage';
import { NativePage } from './pages/NativePage';
import { AtomPanePage } from './AtomPanePage';


import { DXPreviewPage } from '@fliggy-ohos/homepage/src/main/ets/preview/DXPreviewPage';
// import { TestFIMPage } from './pages/TestFIMPage';

export let atomLogger = new Logger('atom')

export let atomRouter: NavPathStack
export function setAtomRouter(router: NavPathStack) {
  atomRouter = router;
}

// 存放atom独有的页面
export let builderMap: Map<string, WrappedBuilder<[PageContext]>> = new Map<string, WrappedBuilder<[PageContext]>>();

@Builder
function buildAtomPanePage(pageContext: PageContext) {
  AtomPanePage()
}

@Builder
function buildEzmockPage(pageContext: PageContext) {
  EzmockPlaygroundIndex()
}

@Builder
function buildSiNanPage(pageContext: PageContext) {
  DynamicRouterDataPage({ pageContext: pageContext });
}

@Builder
function buildPlayerPage(pageContext: PageContext) {
  PlayerDemoPage({pageContext: pageContext});
}

@Builder
function buildTempTestPage(pageContext: PageContext) {
  TestPage({ pageContext: pageContext });
}
// @Builder
// function buildTempTestFimPage(pageContext: PageContext) {
//   TestFIMPage({ pageContext: pageContext });
// }

@Builder
export function harBuilder(pageContext: PageContext) {
  NativePage({ pageContext: pageContext });
}

let nowName : string = 'entryWindowAtomFloatBtn'

export function initAtomNav(){
  // 注册到飞猪业务路由栈
  FliggyNavigator.registerBuilder("temp_test", wrapBuilder(buildTempTestPage));
  // FliggyNavigator.registerBuilder("temp_test_fim", wrapBuilder(buildTempTestFimPage));
  FliggyNavigator.registerBuilder("player_demo", wrapBuilder(buildPlayerPage));
  FliggyNavigator.registerBuilder("native_page", wrapBuilder(harBuilder));

  // 注册到atom进程的路由栈
  builderMap.set('atom_pane_page', wrapBuilder(buildAtomPanePage))
  builderMap.set('ezmock_page', wrapBuilder(buildEzmockPage))
  builderMap.set('sinan_page', wrapBuilder(buildSiNanPage))
}

export function changeWindow(name?: string){
  let context = getContext() as common.UIAbilityContext; // UIAbilityContext
  if(!name) {
    name = nowName
  }
  let ability = 'AtomAbility'
  if(name === 'atomWindowAtomFloatBtn') {
    ability = 'EntryAbility'
  }
  let want: Want = {
    bundleName: env.getPackageName(),
    abilityName: ability,
    parameters: {
      keyForString: ability,
    },
  };
  context.startAbility(want, (err: BusinessError) => {
    if (err.code) {
      atomLogger.e('AtomRouterManager', `Failed to startAbility ${ability} . Code: ${err.code}, message: ${err.message}`);
      return
    }
    if(name === 'atomWindowAtomFloatBtn') {
      nowName = 'entryWindowAtomFloatBtn'
    } else {
      nowName = 'atomWindowAtomFloatBtn'
    }
    AppStorage.setOrCreate('atomWindowFloatBtnType', nowName)
  });
}
