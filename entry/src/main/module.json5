{
  "module": {
    "name": "entry",
    "type": "entry",
    "srcEntry": "./ets/FliggyEntryAbilityStage.ets",
    "description": "$string:module_desc",
    "mainElement": "EntryAbility",
    "deviceTypes": [
      "phone"
    ],
    "deliveryWithInstall": true,
    "compressNativeLibs": true,
    "installationFree": false,
    "pages": "$profile:main_pages",
    "abilities": [
      {
        "name": "EntryAbility",
        "srcEntry": "./ets/entryability/EntryAbility.ets",
        "description": "$string:EntryAbility_desc",
        "icon": "$media:app_icon",
        "label": "$string:app_name",
        "launchType": "singleton",
        "startWindowIcon": "$media:launcher_center",
        "orientation": "portrait",
        "startWindowBackground": "$color:start_window_background",
        "exported": true,
        "skills": [
          // 系统默认，里面不能添加任何配置，否则可能导致push失败
          {
            "entities": [
              "entity.system.home"
            ],
            "actions": [
              "action.system.home"
            ]
          },
          // deep linking配置
          {
            "entities": [
              "entity.system.browsable"
            ],
            "actions": [
              "ohos.want.action.viewData"
            ],
            "uris": [
              // deep linking配置
              {
                "scheme": "taobaotravel",
                "host": "smartbanner"
              },
              // 淘宝&支付宝唤端
              {
                "scheme" : "taobaosso34638021"
              },
              {
                "scheme" : "alipay34638021"
              }
            ]
          },
          // app linking配置
          {
            "entities": [
              "entity.system.browsable"
            ],
            "actions": [
              "ohos.want.action.viewData"
            ],
            "uris": [
              {
                "scheme": "https",
                "host": "v.bcvbw.com"
              }
            ],
            "domainVerify": true
          },
          // push&agoo唤端
          {
            "entities": [
              "entity.system.browsable"
            ],
            "actions": [
              "ohos.want.action.viewData",
              "com.ali.trip"
            ],
            "uris": [
              {
                "scheme": "https",
                "host": "com.ali.trip"
              }
            ]
          }
        ]
      },
      {
        "name": "AtomAbility",
        "srcEntry": "./ets/atom/AtomAbility.ets",
        "description": "$string:EntryAbility_desc",
        "icon": "$media:layered_icon",
        "label": "$string:app_name",
        "launchType": "singleton",
        "startWindowIcon": "$media:launcher_center",
        "startWindowBackground": "$color:start_window_background",
        "orientation": "portrait",
        "exported": true,
        "skills": [
        ]
      }
    ],
    "extensionAbilities": [
      {
        "name": "FliggyBackupAbility",
        "srcEntry": "./ets/backup/FliggyBackupAbility.ets",
        "type": "backup",
        "exported": false,
        "metadata": [
          {
            "name": "ohos.extension.backup",
            "resource": "$profile:backup_config"
          }
        ]
      }
    ],
    "querySchemes": [
      "tbaccount34692017",
      "tbopen",
      "apmqpdispatch",
      "amapuri",
      "alipays",
      "store",
      "baidumap",
      "weixin",
    ],
    "metadata": [
      {
        "name": "client_id",
        // 配置为步骤1中获取的Client ID
        "value": "*********"
      }
    ],
    "requestPermissions": [
      {
        "name": "ohos.permission.INTERNET"
      },
      {
        "name": "ohos.permission.CAMERA",
        "reason": "$string:permission_reason_camera",
        "usedScene": {
          "when": "inuse"
        }
      },{
        "name":'ohos.permission.APPROXIMATELY_LOCATION',
        "reason": "$string:permission_reason_location",
        "usedScene": {
          "when": "inuse"
        }
      },{
        "name":'ohos.permission.LOCATION',
        "reason": "$string:permission_reason_location",
        "usedScene": {
          "when": "inuse"
        }
      },{
        "name":'ohos.permission.MICROPHONE',
        "reason": "$string:permission_reason_microphone",
        "usedScene": {
          "when": "inuse"
        }
      },
      {
        "name":'ohos.permission.READ_CALENDAR',
        "reason": "$string:permission_reason_read_calendar",
        "usedScene": {
          "when": "inuse"
        }
      },
      {
        "name":'ohos.permission.WRITE_CALENDAR',
        "reason": "$string:permission_reason_write_calendar",
        "usedScene": {
          "when": "inuse"
        }
      },
      {
        "name": "ohos.permission.GET_NETWORK_INFO",
        "reason": "$string:permission_reason_get_network_info",
        "usedScene": {
          "abilities": [
            "EntryAbility"
          ],
          "when":"always"
        }
      },
      {
        "name": "ohos.permission.GET_WIFI_INFO",
        "reason": "$string:permission_reason_get_network_info",
        "usedScene": {
          "abilities": [
            "EntryAbility"
          ],
          "when":"always"
        }
      },
      {
        "name": "ohos.permission.READ_PASTEBOARD",
        "reason": "$string:permission_reason_read_pasteboard",
        "usedScene": {
          "abilities": [
            "EntryAbility"
          ],
          "when":"always"
        }
      },
      {
        "name": "ohos.permission.SET_NETWORK_INFO",
        "reason": "$string:request_permission_network_info",
        "usedScene": {
          "abilities": [
            "EntryAbility"
          ]
        }
      },
      {
        "name": "ohos.permission.GET_BUNDLE_INFO"
      },
      {
        "name" : "ohos.permission.PRIVACY_WINDOW"
      },
      {
        "name": "ohos.permission.ACCELEROMETER"
      },
      {
        "name": "ohos.permission.ACCESS_BIOMETRIC",
        "reason": "$string:request_permission_bio",
        "usedScene": {
          "abilities": [
            "EntryAbility"
          ],
          "when": "inuse"
        }
      }
    ]
  }
}