import os

total_lines = 0
fileCount = {}
dirCount = {}


def count_lines_in_ets_files2(directory):
    global total_lines, fileCount, dirCount
    dir_count = 0

    for root, dirs, files in os.walk(directory):
        for dir in dirs:
            dirpath = os.path.join(root, dir)
            # dir_count += count_lines_in_ets_files2(dirpath)
        for filename in files:
            if filename.endswith('.ets'):
                filepath = os.path.join(root, filename)
                with open(filepath, encoding='utf-8') as f:
                    lines = f.readlines()
                    count = len(lines)
                    total_lines += count
                    dir_count += count
                    fileCount[filepath] = count
                    # print(f"文件: {filename}, 行数: {count}")
    return dir_count


def count_lines_in_ets_files(directory):
    global dirCount

    for root, dirs, files in os.walk(directory):
        for dir in dirs:
            if dir.startswith('flutter_boost') or dir.startswith('sqflite') or dir.startswith('shared_preferences_ohos'):
                continue
            if dir.startswith('permission_handler_ohos') or dir.startswith('path_provider_ohos') or dir.startswith('package_info_plus') or dir.startswith('url_launcher_ohos'):
                continue
            dirpath = os.path.join(root, dir)
            dir_count = count_lines_in_ets_files2(dirpath)
            dirCount[dir] = dir_count
            # print(f"目录: {dir}, 行数: {dir_count}")


if __name__ == "__main__":
    count_lines_in_ets_files('/Users/<USER>/DevEcoWorkSpace/portal/oh_modules/.ohpm/oh_modules/@fliggy-ohos')
    
    print('---------------------------------------------------')
    sorted_items = sorted(fileCount.items(), key=lambda x: x[1])
    real_file_count = 0
    for key, value in sorted_items:
        real_file_count += value
        print(f"{key}: {value}")
    print(f"------real_file_count = {real_file_count}-------------------")    
    print(f"------fileCount size = {len(fileCount)}-------------------")    
    print('---------------------------------------------------')    
    sorted_items = sorted(dirCount.items(), key=lambda x: x[1])
    for key, value in sorted_items:
        print(f"{key}: {value}")
    print(f"------dirCount size = {len(dirCount)}-------------------")    
    print('---------------------------------------------------')    
    print(f"总计行数为: {total_lines}")