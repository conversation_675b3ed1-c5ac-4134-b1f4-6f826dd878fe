import json5 as json
from argparse import Argument<PERSON><PERSON><PERSON>

def update_overrides(oh_package_file, overrides):
    print("file: " + oh_package_file)
    # 读取oh-package.json5文件
    with open(oh_package_file, "r") as f:
        data = json.load(f)

    # 更新或新增overrides字段的值
    if "overrides" not in data:
        data["overrides"] = {}
    items = overrides.split(",")
    for item in items:
        item = item.strip()
        data["overrides"][f"@fliggy-ohos/{item}"] = f"file:./localdeps/{item}.har"

    # 将结果保存回oh-package.json5文件
    with open(oh_package_file, "w") as f:
        json.dump(data, f, indent=2)

if __name__ == "__main__":
    parser = ArgumentParser(description="Update or add an override in oh-package.json5")
    parser.add_argument("--overrides", "-o" ,help="The key of the override to update or add")
    parser.add_argument("--oh_package_file", "-f" , help="Path to oh-package.json5 file")

    args = parser.parse_args()

    print(args)

    update_overrides(args.oh_package_file, args.overrides)
