import subprocess
import sys

try:
    import json5 as json
except ImportError:
    print("Installing the required module...")
    subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'json5'])
    import json5 as json

import os
from argparse import ArgumentParser


def update_build_profile(product):
    if product == 'default':
        return
    # 读取并解析 JSON 文件 (JSON5 格式)
    with open('./build-profile.json5', 'r',encoding='utf-8') as file:
        build_profile = json.load(file)

    # 修改数据结构中的特定字段
    if build_profile['app']['products'][0]['name'] == 'default':
        build_profile['app']['products'][0]['signingConfig'] = product
        print(f"mtl_build_init: update_build_profile: product = {product}")
    else:
        raise Exception('default signingConfig not found')
    # 写入修改后的数据回到文件中，使用缩进使其更易读
    with open('build-profile.json5', 'w',encoding='utf-8') as file:
        json.dump(build_profile, file, indent=4)

def update_module_file(product):
    if product != 'inhouse':
        return
    # 读取并解析 JSON 文件 (JSON5 格式)
    with open('./entry/src/main/module.json5', 'r',encoding='utf-8') as file:
        build_profile = json.load(file)

    build_profile['module']['abilities'][0]['label'] = '$string:app_name_bata'

    # 修改数据结构中的特定字段
    if build_profile['module']['metadata'][0]['name'] == 'client_id':
        build_profile['module']['metadata'][0]['value'] = '112627975'
        print(f"mtl_build_init: update_module_file: {build_profile['module']['metadata'][0]['value']}")
    else:
        raise Exception('client_id not found')
    # 写入修改后的数据回到文件中，使用缩进使其更易读
    with open('./entry/src/main/module.json5', 'w', encoding='utf-8') as file:
        json.dump(build_profile, file, indent=4)

def update_app_json_file(product):
    if product != 'inhouse':
        return
    # 读取并解析 JSON 文件 (JSON5 格式)
    with open('AppScope/app.json5', 'r',encoding='utf-8') as file:
        build_profile = json.load(file)

    # 修改数据结构中的特定字段
    build_profile['app']['bundleName'] = build_profile['app']['bundleName']+'.beta'
    build_profile['app']['label'] = '$string:app_name_bata'
    print(f"mtl_build_init: update_app_json_file: {build_profile['app']['bundleName']} {build_profile['app']['label']}")

    # 写入修改后的数据回到文件中，使用缩进使其更易读
    with open('AppScope/app.json5', 'w', encoding='utf-8') as file:
        json.dump(build_profile, file, ensure_ascii=False, indent=4)

def update_manifeast_file(product):
    if product != 'internal' :
        return
    # 读取并解析 JSON 文件 (JSON5 格式)
    with open('AppScope/generate_manifest.py', 'r',encoding='utf-8') as file:
        content = file.read()

    # 修改数据结构中的特定字段
    modified_content = content.replace("com.fliggy.hmos.beta", "com.fliggy.hmos")
    modified_content = modified_content.replace("飞猪beta版", "飞猪内部测试版")

    print(f"mtl_build_init: update_manifeast_file: {modified_content}")

    # 写入修改后的数据回到文件中，使用缩进使其更易读
    with open('AppScope/generate_manifest.py', 'w',encoding='utf-8') as file:
        file.write(modified_content)

    # 读取并解析 JSON 文件 (JSON5 格式)
    with open('AppScope/generate_qrcode.py', 'r',encoding='utf-8') as file:
        content = file.read()

    # 修改数据结构中的特定字段
    modified_content = content.replace("in-house_download.html", "internal_download.html")

    print(f"mtl_build_init: generate_qrcode.py: {modified_content}")

    # 写入修改后的数据回到文件中，使用缩进使其更易读
    with open('AppScope/generate_qrcode.py', 'w',encoding='utf-8') as file:
        file.write(modified_content)

def move_image(product):
    if product != 'inhouse' :
       return
    source_path = 'entry/src/main/resources/base/media/yw_1222_inhouse.jpg'
    destination_path = 'entry/src/main/resources/base/media/yw_1222.jpg'

    try:
        os.rename(source_path, destination_path)
        print(f"File moved from {source_path} to {destination_path}")
    except FileNotFoundError as e:
        print(f"Error: File not found at path {source_path}. Error details: {e}")
    except OSError as e:
        print(f"Error: Failed to rename file. Error details: {e}")


if __name__ == "__main__":
    # 在portal目录下运行
    parser = ArgumentParser(description="Update or add an override in oh-package.json5")
    parser.add_argument("--product", "-p" ,help="编译选项 inhouse,internal,default,release")
    args = parser.parse_args()
    product = args.product
    print(f"product: {product}")

    print(f"mtl_build_init: update_build_profile")
    update_build_profile(product)
    print(f"mtl_build_init: update_module_file")
    update_module_file(product)
    print(f"mtl_build_init: update_app_json_file")
    update_app_json_file(product)
    print(f"mtl_build_init: update_manifeast_file")
    update_manifeast_file(product)
    print(f"mtl_build_init: move_image")
    move_image(product)