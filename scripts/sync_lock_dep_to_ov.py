import json5 as json

def update_overrides():
    # 读取oh-package-lock.json5文件
    with open('entry/oh-package-lock.json5', 'r',encoding='utf-8') as f:
        lock_data = json.load(f)
        for key, value in lock_data['packages'].items():
            lock_names = {value['name'] for value in lock_data.get('packages', {}).values()}
    print(lock_names)
    # 读取oh-package.json5文件
    with open('oh-package.json5', 'r',encoding='utf-8') as f:
        oh_data = json.load(f)
    # 更新或新增overrides字段的值
    oh_data['overrides'] = {
        k: v 
        for k, v in oh_data.get('overrides', {}).items() 
        if k in lock_names
    }


    print("未overrides的库:")

    no_in_overrides = set()
    for key, value in lock_data['packages'].items():
        dependency = value['name']
        resolved_url = value['resolved']
        if resolved_url.startswith('http') and dependency not in oh_data['overrides']:
            no_in_overrides.add(dependency)
            oh_data['overrides'][dependency] = value['version']

    print(no_in_overrides)

      # 将结果保存回oh-package.json5文件
    with open('oh-package.json5', "w") as f:
        json.dump(oh_data, f, indent=2)


if __name__ == "__main__":
    print('----- start sync_lock_dep_to_ov.py -----')
    update_overrides()
    print('----- end sync_lock_dep_to_ov.py -----')
