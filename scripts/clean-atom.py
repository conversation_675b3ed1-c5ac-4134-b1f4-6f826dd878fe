import os

def delete_files_in_directory(directory):
    # 检查目录是否存在
    if not os.path.exists(directory):
        print(f"The directory '{directory}' does not exist.")
        return

    # 遍历目录中的所有文件和子目录
    for filename in os.listdir(directory):
        file_path = os.path.join(directory, filename)

        try:
            # 删除文件
            if (os.path.isfile(file_path) or os.path.islink(file_path)):
                if(filename == "InitAtom.ets"):
                    with open(file_path, 'w', encoding='utf-8') as file:
                        pass  # 不做任何事情，只是简单地打开和关闭文件，从而清空内容
                else:
                    os.unlink(file_path)
            # 删除空目录
            elif os.path.isdir(file_path):
                os.rmdir(file_path)
        except Exception as e:
            print(f'Failed to delete {file_path}. Reason: {e}')


# 在打release包的时候情况atom相关的代码
if __name__ == "__main__":
    curPath = os.getcwd()
    parent_dir = os.path.dirname(curPath)
    path = os.path.join(parent_dir, "ohos", "entry", "src","main","ets","atom")
    delete_files_in_directory(path)

