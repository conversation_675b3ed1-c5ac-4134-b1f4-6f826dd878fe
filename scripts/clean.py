import sys
import json5 as json

def clean(oh_package_file):
    # 读取oh-package.json5文件
    with open(oh_package_file, "r") as f:
        data = json.load(f)

    # 遍历overrides字段，删除符合条件的项
    if "overrides" in data:
        overrides = data["overrides"]
        keys_to_remove = [k for k, v in overrides.items() if k.startswith("@fliggy-ohos") and isinstance(v, str) and v.startswith("file:")]
        for key in keys_to_remove:
            del overrides[key]

    # 将结果保存回oh-package.json5文件
    with open(oh_package_file, "w") as f:
        json.dump(data, f, indent=2)

if __name__ == "__main__":
    oh_package_file = sys.argv[1]
    clean(oh_package_file)
