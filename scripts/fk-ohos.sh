#!/bin/bash

# 工作目录，指向portal/ohos
workspace="$OHOS_WORKSPACE"

# 定义一个数组来存储localbuilds字段的值
localbuilds=()

# 检查并创建localdeps目录（如果不存在）
function prepare() {
  echo "[fk-ohos] prepare start"
  if [ ! -d "localdeps" ]; then
    mkdir "$workspace"/localdeps
  fi

  if ! python3 -c "import json5"; then
    echo "安装json5插件"
    # 使用 pip 安装 json5 模块
    pip3 install --quiet json5
  fi

  echo "[fk-ohos] prepare done"
}

# 收集所有需要构建的子模块
function gather_dependencies() {
  # 定义一个数组来存储不存在的项目目录
  non_exists_projects=()

  # 检查父目录的父目录中是否存在与localbuilds中元素同名的目录
  for build in "${localbuilds[@]}"; do
    parent_dir="$(dirname "$workspace")"
    target_dir="$parent_dir/$build"
    echo "$target_dir"
    if [ ! -d "$target_dir" ]; then
      non_exists_projects+=("$build")
    fi
  done

  # 如果non_exists_projects数组不为空，则输出提示信息
  if [ ${#non_exists_projects[@]} -gt 0 ]; then
    for project in "${non_exists_projects[@]}"; do
      echo "[fk-ohos] 检查到工程 $project 不存在，请先在portal同级目录下clone工程"
    done
    exit 1
  fi
  echo "[fk-ohos] 所有依赖项："
  for build in "${localbuilds[@]}"; do
    echo "[fk-ohos]  - $build"
  done
}

# 构建每一个子模块
function assemble_submodules() {
  if [ ${#localbuilds[@]} -eq 0 ]; then
    echo "[fk-ohos] 传入参数为空无需构建子模块"
    return 0
  fi

  # 确保当前工作目录为父目录的父目录
  workspace_parent_dir=$(dirname "$workspace")

  # 遍历localbuilds中的每个变量
  for module in "${localbuilds[@]}"; do
    # 定义目标目录为当前遍历的变量值
    target_dir="$workspace_parent_dir/$module"

    # 进入目标目录下的module目录
    cd "$target_dir/module" || exit

    echo "[fk-ohos] 编译 $module"

    # 执行ohpm update
    ohpm update --registry=http://ohpm.alibaba-inc.com/repos/ohpm/,https://repo.harmonyos.com/ohpm/,https://scmcenterclient.cn-hangzhou.alipay.aliyun-inc.com/AlipayClient/HarRepo --strict_ssl false

    # 返回上级目录，当前为子模块项目根目录
    cd "$target_dir" || exit

    # 执行清理与打包命令
    hvigorw clean
    hvigorw assembleHar
    if [ $? -ne 0 ]; then
      echo "[fk-ohos] 编译 $module 失败"
      exit 1
    fi

    echo "拷贝 $module 编译产物"
    # 拷贝module.har到父目录的localdeps文件夹，并重命名

    target_file="module/build/default/outputs/default/module.har"
    if [ ! -f "$target_file" ]; then
      target_file="module/build/default/outputs/default/${module}.har"
    fi
    if [ ! -f "$target_file" ]; then
      echo "构建产物不存在，请检查构建日志及产物路径 module/build/default/outputs/default/"
      exit 1
    fi

    cp "$target_file" "../portal/localdeps/${module}.har"

  done

  cd "$workspace" || exit
}

function replace_overrides() {
  if [ ${#localbuilds[@]} -eq 0 ]; then
    echo "[fk-ohos] 传入参数为空无需替换本地构建子模块"
    return 0
  fi
  printf -v result '%s,' "${localbuilds[@]}"
  # 去掉最后一个多余的逗号
  result=${result%,}
  echo "[fk-ohos] 替换依赖 $result"
  python3 "$workspace"/scripts/overrides.py -o "$result" -f "$workspace"/oh-package.json5
}

# 构建壳工程 hap
function build_hap() {
  echo "[fk-ohos] ohpm update"
  cd "$workspace"/entry || exit
  ohpm update --registry=http://ohpm.alibaba-inc.com/repos/ohpm/,https://repo.harmonyos.com/ohpm/,https://scmcenterclient.cn-hangzhou.alipay.aliyun-inc.com/AlipayClient/HarRepo --strict_ssl false
  cd "$workspace" || exit
  echo "[fk-ohos] hvigorw assembleHap --mode module -p module=entry@default -p product=default --analyze --parallel --incremental --daemon"
  hvigorw assembleHap --mode module -p module=entry@default -p product=default --analyze --parallel --incremental --daemon
  if [ $? -ne 0 ]; then
    echo "[fk-ohos] 编译 壳工程 失败"
    exit 1
  fi
}

# 安装hap
function install_hap() {
  echo "[fk-ohos] install hap"
  hdc install -r "$workspace"/entry/build/default/outputs/default/entry-default-signed.hap
}

function start_hap() {
  echo "[fk-ohos] hdc shell aa start"
  if [ -z "$1" ]; then
    hdc shell aa start -a EntryAbility -b com.fliggy.hmos -m entry
  else
    hdc shell aa start -a EntryAbility -b com.fliggy.hmos -m entry -D
  fi
}

function stop_hap() {
  hdc shell aa force-stop com.fliggy.hmos
}

function check_run() {
  local command="$1"

  "$command"

  if [ $? -ne 0 ]; then
    echo "[fk-ohos] '$command' 失败"
    exit 1
  fi
}

function clean {
  echo "[fk-ohos] 清理本地依赖产物"
  if ! python3 -c "import json5"; then
    echo "安装json5插件"
    # 使用 pip 安装 json5 模块
    pip3 install --quiet json5
  fi
  cd "$workspace" || exit
  rm -rf "$workspace"/localdeps/*
  python3 "$workspace"/scripts/clean.py "$workspace"/oh-package.json5
  hvigorw clean
}

# 构建
function assemble() {
  local i=0
  for arg in "$@"; do
    localbuilds[i++]="$arg"
  done

  # 1. 环境准备
  echo "[fk-ohos] 准备环境"
  check_run "prepare"

  # 2. 收集依赖
  echo "[fk-ohos] 收集依赖"
  check_run "gather_dependencies"

  # 3. 构建所有子模块
  echo "[fk-ohos] 编译子模块"
  check_run "assemble_submodules"

  # 6. 替换overrides
  echo "[fk-ohos] 强制替换依赖"
  check_run "replace_overrides"

  # 4. 构建壳工程
  echo "[fk-ohos] 构建壳工程"
  check_run "build_hap"

  echo "[fk-ohos] 终止应用"
  check_run "stop_hap"

  # 5. 安装应用
  echo "[fk-ohos] 安装应用"
  check_run "install_hap"

  echo "[fk-ohos] 启动应用"
  check_run "start_hap"
}

function main() {
  if [[ "$1" == "clean" ]]; then
    clean
  elif [[ "$1" == "assemble" ]]; then
    shift
    assemble "$@"
  elif [[ "$1" == "debug" ]]; then
    stop_hap
    start_hap -d
  else
    echo "命令暂不支持"
    echo "$@"
  fi
}

main "$@"
