import { describe, beforeAll, beforeEach, afterEach, afterAll, it, expect } from '@ohos/hypium';
import { FligggySDkManager, fimsdk_Napi, FIMSendMessageParams } from '../main/ets/fim_sdk_method';

export default function FIMSendMessageTest() {
  describe('FIM Send Message Test', () => {
    let sdkManager: FligggySDkManager;

    beforeAll(() => {
      // 测试套件开始前的准备工作
      console.info('FIM Send Message Test Suite Started');
    });

    beforeEach(() => {
      // 每个测试用例开始前的准备工作
      sdkManager = FligggySDkManager.getInstance();
    });

    afterEach(() => {
      // 每个测试用例结束后的清理工作
    });

    afterAll(() => {
      // 测试套件结束后的清理工作
      console.info('FIM Send Message Test Suite Finished');
    });

    /**
     * 测试基础的 add 方法
     */
    it('should call basic add method', 0, () => {
      const result = fimsdk_Napi.add(2, 3);
      expect(result).assertEqual(17); // 根据当前实现，应该返回 2 + 3 + 3 + 3 + 3 + 3 = 17
    });

    /**
     * 测试 fim_send_message 方法
     */
    it('should send text message successfully', 0, async () => {
      const messageParams: FIMSendMessageParams = {
        conversationId: 'test_conversation_123',
        text: 'Hello, this is a test message!',
        messageType: 'text',
        receiverId: 'user_456'
      };

      try {
        const result = await sdkManager.sendMessageText(messageParams);
        console.info('Send message result:', result);
        
        // 解析返回的JSON
        const response = JSON.parse(result);
        
        // 验证响应结构
        expect(response.success).assertEqual(true);
        expect(response.code).assertEqual(0);
        expect(response.message).assertEqual('消息发送成功');
        expect(response.data).assertInstanceOf('Object');
        expect(response.data.messageId).assertInstanceOf('String');
        expect(response.data.timestamp).assertInstanceOf('Number');
        expect(response.data.status).assertEqual('sent');
        
      } catch (error) {
        console.error('Send message failed:', error);
        expect().assertFail('Send message should not throw error');
      }
    });

    /**
     * 测试直接调用 NAPI 方法
     */
    it('should call fim_send_message NAPI directly', 0, async () => {
      const messageJson = JSON.stringify({
        conversationId: 'direct_test_conversation',
        text: 'Direct NAPI call test',
        messageType: 'text'
      });

      try {
        const result = await fimsdk_Napi.fim_send_message(messageJson);
        console.info('Direct NAPI result:', result);
        
        const response = JSON.parse(result);
        expect(response.success).assertEqual(true);
        expect(response.code).assertEqual(0);
        
      } catch (error) {
        console.error('Direct NAPI call failed:', error);
        expect().assertFail('Direct NAPI call should not throw error');
      }
    });

    /**
     * 测试错误参数处理
     */
    it('should handle invalid parameters', 0, async () => {
      try {
        // 测试空字符串参数
        const result = await fimsdk_Napi.fim_send_message('');
        console.info('Empty string result:', result);
        
        // 即使是空字符串，也应该返回一个有效的响应
        const response = JSON.parse(result);
        expect(response).assertInstanceOf('Object');
        
      } catch (error) {
        console.info('Expected error for empty string:', error);
        // 这里可能会抛出错误，这是正常的
      }
    });
  });
}
