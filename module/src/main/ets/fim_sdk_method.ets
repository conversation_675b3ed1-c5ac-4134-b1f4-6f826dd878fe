import fimsdk from 'libfimsdk.so'

// 定义消息发送参数接口
export interface FIMSendMessageParams {
  conversationId: string;
  text: string;
  messageType?: string;
  receiverId?: string;
}

// 定义消息重发参数接口
export interface FIMResendMessageParams {
  messageId: string;
  conversationId?: string;
}

// 定义消息回复参数接口
export interface FIMReplyMessageParams {
  originalMessageId: string;
  text: string;
  conversationId?: string;
}

// 定义获取消息参数接口
export interface FIMGetMessageParams {
  conversationId: string;
  messageId: string;
}

// 定义撤回消息参数接口
export interface FIMRecallMessageParams {
  conversationId: string;
  messageId: string;
}

// 定义获取会话列表参数接口
export interface FIMGetConversationsParams {
  conversationIds?: string[];
}

// 定义登录参数接口
export interface FIMLoginParams {
  userId: string;
  token: string;
}

// 定义事件监听参数接口
export interface FIMEventParams {
  eventType?: string;
}

// 定义发送自定义消息参数接口
export interface FIMSendMessageCustomParams {
  conversationId: string;
  content: object;
}

// 定义发送图片消息参数接口
export interface FIMSendMessageImageParams {
  conversationId: string;
  imagePath: string;
}

// 定义发送@消息参数接口
export interface FIMSendMessageAtParams {
  conversationId: string;
  text: string;
  atUserIds: string[];
}

// 定义发送结构化消息参数接口
export interface FIMSendMessageStructParams {
  conversationId: string;
  structData: object;
}

// 定义发送视频消息参数接口
export interface FIMSendMessageVideoParams {
  conversationId: string;
  videoPath: string;
}

// 定义激活会话参数接口
export interface FIMActiveConversationParams {
  conversationId: string;
}

// 定义获取静音状态参数接口
export interface FIMGetMuteStatusParams {
  conversationId: string;
}

// 定义创建自定义单人会话参数接口
export interface FIMCreateCustomSingleConversationParams {
  userId: string;
  appCid?: string;
}

// 定义退群参数接口
export interface FIMLeaveGroupParams {
  conversationId: string;
}

// 定义获取fuid参数接口
export interface FIMGetMyFuidParams {
  // 可以为空对象或包含额外参数
}

// 定义搜索聊天内容参数接口
export interface FIMSearchChatContentParams {
  keyword: string;
  conversationId?: string;
}

// 定义获取媒体本地路径参数接口
export interface FIMGetMediaLocalPathParams {
  mediaId: string;
}

// 定义删除本地消息参数接口
export interface FIMDeleteLocalMessageParams {
  conversationId: string;
  localIds: string[];
}

// 定义清空消息参数接口
export interface FIMClearMessagesParams {
  conversationId: string;
}

// 定义更新消息为已读参数接口
export interface FIMUpdateMessageToReadParams {
  conversationId: string;
  messageIds: string[];
}

// 定义获取消息已读状态参数接口
export interface FIMListMessagesReadStatusParams {
  conversationId: string;
  messageIds: string[];
}

// 定义获取本地消息参数接口
export interface FIMGetLocalMessageParams {
  conversationId: string;
  localId: string;
}

// 定义分页查询消息参数接口
export interface FIMListMessagesParams {
  conversationId: string;
  cursor: string;
  count: number;
}

// 定义删除消息参数接口
export interface FIMDeleteMessageParams {
  conversationId: string;
  messageIds: string[];
}

// 定义API响应接口
export interface FIMAPIResponse {
  success: boolean;
  data: ESObject | null;
  message: string;
  code: number;
  error?: string;
}

// 直接导出 fim_send_message 方法和 fimsdk_Napi
export const fimsdk_Napi = fimsdk;

export default class FligggySDkManager {
  private static instance: FligggySDkManager;
  static getInstance() {
    if (!FligggySDkManager.instance) {
      FligggySDkManager.instance = new FligggySDkManager();
    }
    return FligggySDkManager.instance;
  }

  public getFliggySDK() {
    return fimsdk.add(1, 1);
  }

  // ============================================================================
  // 消息相关功能
  // ============================================================================

  /**
   * 发送文本消息
   * @param params 消息参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async sendMessageText(params: FIMSendMessageParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimSendMessageText(paramsJson);
  }

  /**
   * 重发消息
   * @param params 重发参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async resendMessage(params: FIMResendMessageParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimResendMessage(paramsJson);
  }

  /**
   * 回复消息
   * @param params 回复参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async replyMessage(params: FIMReplyMessageParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimReplyMessage(paramsJson);
  }

  /**
   * 获取消息
   * @param params 获取消息参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async getMessage(params: FIMGetMessageParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimGetMessage(paramsJson);
  }

  /**
   * 撤回消息
   * @param params 撤回参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async recallMessage(params: FIMRecallMessageParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimRecallMessage(paramsJson);
  }

  /**
   * 获取会话列表
   * @param params 获取会话参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async getConversations(params: FIMGetConversationsParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimGetConversations(paramsJson);
  }

  // ============================================================================
  // 会话管理功能 (真实API)
  // ============================================================================

  /**
   * 激活会话
   * @param params 激活会话参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async activeConversation(params: FIMActiveConversationParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimActiveConversation(paramsJson);
  }

  /**
   * 获取静音状态
   * @param params 获取静音状态参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async getMuteStatus(params: FIMGetMuteStatusParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimGetMuteStatus(paramsJson);
  }

  /**
   * 创建自定义单人会话
   * @param params 创建会话参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async createCustomSingleConversation(params: FIMCreateCustomSingleConversationParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimCreateCustomSingleConversation(paramsJson);
  }

  /**
   * 退群
   * @param params 退群参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async leaveGroup(params: FIMLeaveGroupParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimLeaveGroup(paramsJson);
  }

  // ============================================================================
  // 搜索和工具功能 (真实API)
  // ============================================================================

  /**
   * 获取fuid
   * @param params 获取fuid参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async getMyFuid(params: FIMGetMyFuidParams = {}): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimGetMyFuid(paramsJson);
  }

  /**
   * 搜索聊天内容
   * @param params 搜索参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async searchChatContent(params: FIMSearchChatContentParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimSearchChatContent(paramsJson);
  }

  /**
   * 获取媒体本地路径
   * @param params 获取媒体本地路径参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async getMediaLocalPath(params: FIMGetMediaLocalPathParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimGetMediaLocalPath(paramsJson);
  }

  // ============================================================================
  // 登录认证功能 (真实API)
  // ============================================================================

  /**
   * 登录认证
   * @param params 登录参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async login(params: FIMLoginParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimLogin(paramsJson);
  }

  // ============================================================================
  // 事件监听功能 (真实API)
  // ============================================================================

  /**
   * 消息事件监听
   * @param params 监听参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async onMessageEvent(params: FIMEventParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimOnMessageEvent(paramsJson);
  }

  /**
   * 会话事件监听
   * @param params 监听参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async onConversationEvent(params: FIMEventParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimOnConversationEvent(paramsJson);
  }

  /**
   * 群组事件监听
   * @param params 监听参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async onGroupEvent(params: FIMEventParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimOnGroupEvent(paramsJson);
  }

  // ============================================================================
  // 消息发送功能 (真实API)
  // ============================================================================

  /**
   * 发送自定义消息
   * @param params 消息参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async sendMessageCustom(params: FIMSendMessageCustomParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimSendMessageCustom(paramsJson);
  }

  /**
   * 发送图片消息
   * @param params 消息参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async sendMessageImage(params: FIMSendMessageImageParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimSendMessageImage(paramsJson);
  }

  /**
   * 发送@消息
   * @param params 消息参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async sendMessageAt(params: FIMSendMessageAtParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimSendMessageAt(paramsJson);
  }

  /**
   * 发送结构化消息
   * @param params 消息参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async sendMessageStruct(params: FIMSendMessageStructParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimSendMessageStruct(paramsJson);
  }

  /**
   * 发送视频消息
   * @param params 消息参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async sendMessageVideo(params: FIMSendMessageVideoParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimSendMessageVideo(paramsJson);
  }

  // ============================================================================
  // 新增的消息操作功能 (真实API)
  // ============================================================================

  /**
   * 删除本地消息
   * @param params 删除参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async deleteLocalMessage(params: FIMDeleteLocalMessageParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimDeleteLocalMessage(paramsJson);
  }

  /**
   * 清空消息
   * @param params 清空参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async clearMessages(params: FIMClearMessagesParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimClearMessages(paramsJson);
  }

  /**
   * 更新消息为已读
   * @param params 更新参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async updateMessageToRead(params: FIMUpdateMessageToReadParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimUpdateMessageToRead(paramsJson);
  }

  /**
   * 获取消息已读状态
   * @param params 获取参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async listMessagesReadStatus(params: FIMListMessagesReadStatusParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimListMessagesReadStatus(paramsJson);
  }

  /**
   * 获取本地消息
   * @param params 获取本地消息参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async getLocalMessage(params: FIMGetLocalMessageParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimGetLocalMessage(paramsJson);
  }

  /**
   * 获取上一页消息
   * @param params 获取消息参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async listPreviousMsgs(params: FIMListMessagesParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimListPreviousMsgs(paramsJson);
  }

  /**
   * 获取下一页消息
   * @param params 获取消息参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async listNextMsgs(params: FIMListMessagesParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimListNextMsgs(paramsJson);
  }

  /**
   * 获取上一页本地消息
   * @param params 获取本地消息参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async listPreviousLocalMsgs(params: FIMListMessagesParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimListPreviousLocalMsgs(paramsJson);
  }

  /**
   * 获取下一页本地消息
   * @param params 获取本地消息参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async listNextLocalMsgs(params: FIMListMessagesParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimListNextLocalMsgs(paramsJson);
  }

  /**
   * 删除消息
   * @param params 删除消息参数
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async deleteMessage(params: FIMDeleteMessageParams): Promise<string> {
    const paramsJson = JSON.stringify(params);
    return fimsdk.fimDeleteMessage(paramsJson);
  }

  // ============================================================================
  // 引擎和设置功能
  // ============================================================================

  /**
   * 启动FIM引擎
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async startEngine(): Promise<string> {
    return fimsdk.fimEngineStart();
  }

  /**
   * 设置应用ID
   * @param appId 应用ID
   * @returns Promise<string> 返回JSON格式的响应字符串
   */
  public async setAppId(appId: string): Promise<string> {
    return fimsdk.setAppId(appId);
  }

  /**
   * 设置应用名称
   * @param appName 应用名称
   * @returns string 返回设置结果
   */
  public setAppName(appName: string): string {
    return fimsdk.setAppName(appName);
  }

  /**
   * 设置应用密钥
   * @param appKey 应用密钥
   * @returns string 返回设置结果
   */
  public setAppKey(appKey: string): string {
    return fimsdk.setAppKey(appKey);
  }

  /**
   * 设置设备类型
   * @param deviceType 设备类型
   * @returns string 返回设置结果
   */
  public setDeviceType(deviceType: string): string {
    return fimsdk.setDeviceType(deviceType);
  }

  /**
   * 设置设备名称
   * @param deviceName 设备名称
   * @returns string 返回设置结果
   */
  public setDeviceName(deviceName: string): string {
    return fimsdk.setDeviceName(deviceName);
  }

  /**
   * 设置设备ID
   * @param deviceId 设备ID
   * @returns string 返回设置结果
   */
  public setDeviceId(deviceId: string): string {
    return fimsdk.setDeviceId(deviceId);
  }

  /**
   * 设置数据路径
   * @param dataPath 数据路径
   * @returns string 返回设置结果
   */
  public setDataPath(dataPath: string): string {
    return fimsdk.setDataPath(dataPath);
  }

  /**
   * 设置操作系统名称
   * @param osName 操作系统名称
   * @returns string 返回设置结果
   */
  public setOsName(osName: string): string {
    return fimsdk.setOsName(osName);
  }

  /**
   * 设置操作系统版本
   * @param osVersion 操作系统版本
   * @returns string 返回设置结果
   */
  public setOsVersion(osVersion: string): string {
    return fimsdk.setOsVersion(osVersion);
  }

  /**
   * 设置应用版本
   * @param appVersion 应用版本
   * @returns string 返回设置结果
   */
  public setAppVersion(appVersion: string): string {
    return fimsdk.setAppVersion(appVersion);
  }
}


