// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <functional>
#include <memory>
#include <string>
#include <vector>

namespace alibaba {
namespace dps {

class DPSSyncPlusAck;
struct DPSSyncData;
struct DPSSyncDataExtend;

class DPSSyncPlusPackageDataHandler {
 public:
  virtual ~DPSSyncPlusPackageDataHandler() {}

  /**
   * 支持的数据类型
   * @return object type 列表
   */
  virtual std::vector<int32_t> OnSupportTypes() = 0;

  /**
   * 获取监听标识符，用于记录日志
   * @return 标识符
   */
  virtual std::string OnGetTag() = 0;

  /**
   * 接收到数据推送
   * @param data 数据
   * @param entend 扩展信息
   * @param ack 业务回调ack对象，完成后需要调用ack->OnSuccess,
   * 失败调用ack->OnFailed
   */
  virtual void OnReceived(const std::vector<DPSSyncData>& data,
                          const DPSSyncDataExtend& entend,
                          const std::shared_ptr<DPSSyncPlusAck>& ack) = 0;
};

using DPSSyncPlusPackageDataHandlerPtr =
    std::shared_ptr<DPSSyncPlusPackageDataHandler>;
using DPSSyncPlusPackageDataHandlerWeakPtr =
    std::weak_ptr<DPSSyncPlusPackageDataHandler>;

}  // namespace dps
}  // namespace alibaba
