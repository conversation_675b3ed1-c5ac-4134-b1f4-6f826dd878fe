// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <string>
#include <utility>
#include "dps_err_domain.h"

namespace alibaba {
namespace dps {

/**
 * DPSError
 */
struct DPSError final {
  DPSErrDomain domain = DPSErrDomain::DPS_ERR_DOMAIN_CLIENT;
  /**
   * 错误值
   * 如果domain为client，错误值请参考DPS_Err_ClientCode
   * 如果domain为server，错误值请咨询服务端
   */
  int32_t code = 0;
  std::string developer_message;
  /**
   * 以下成员只有在domain为server时有效
   */
  std::string reason;
  std::string extra_info;
  std::string scope;

  DPSError(DPSErrDomain domain_,
           int32_t code_,
           std::string developer_message_,
           std::string reason_,
           std::string extra_info_,
           std::string scope_)
      : domain(std::move(domain_)),
        code(std::move(code_)),
        developer_message(std::move(developer_message_)),
        reason(std::move(reason_)),
        extra_info(std::move(extra_info_)),
        scope(std::move(scope_)) {}

  DPSError() {}
};

}  // namespace dps
}  // namespace alibaba
