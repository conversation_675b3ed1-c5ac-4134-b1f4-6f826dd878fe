// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

struct DPSError;

/**
 * 退出监听
 */
class DPSLogoutListener {
 public:
  virtual ~DPSLogoutListener() {}

  /**
   * 成功
   */
  virtual void OnSuccess() = 0;

  /**
   * 失败
   */
  virtual void OnFailure(const DPSError& error) = 0;
};

using DPSLogoutListenerPtr = std::shared_ptr<DPSLogoutListener>;
using DPSLogoutListenerWeakPtr = std::weak_ptr<DPSLogoutListener>;

}  // namespace dps
}  // namespace alibaba
