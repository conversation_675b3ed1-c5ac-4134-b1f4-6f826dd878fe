// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * DPSUserID
 */
struct DPSUserId final {
  /**
   * 最大32 byte
   */
  std::string uid;
  /**
   * 最大16 byte
   */
  std::string domain;

  DPSUserId(std::string uid_, std::string domain_)
      : uid(std::move(uid_)), domain(std::move(domain_)) {}

  DPSUserId() {}
};

}  // namespace dps
}  // namespace alibaba
