// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class DPSConnectionType : int {
  /**
   * 使用ACCS作为网络连接通道，仅可作为主连接通道
   */
  CONNECTION_TYPE_ACCS = 0,
  /**
   * 使用Bifrost作为网络连接通道，可作为主连接及文件上传通道
   */
  CONNECTION_TYPE_BIFROST = 1,
  /**
   * 使用DingTalk文件上传下载通道，仅可作为文件相关通道
   */
  CONNECTION_TYPE_DINGTALK_FILE = 2,
};

}
}  // namespace alibaba

namespace std {

template <>
struct hash<::alibaba::dps::DPSConnectionType> {
  size_t operator()(::alibaba::dps::DPSConnectionType type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

}  // namespace std
