// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <functional>
#include <map>
#include <memory>
#include <string>
#include <vector>
#include "dps_define.h"

namespace alibaba {
namespace dps {

class DPSEngineStartListener;
class DPSLogHandler;
class DPSModuleInfo;
class DPSPubEngineListener;
class DPSPubManager;
class DPSPubManagerCreateListener;
class DPSPubSettingService;
class DPSReleaseManagerListener;
class DPSResetUserDataListener;
enum class DPSLogLevel;
struct DPSError;

/**
 * DPS Engine
 */
class DPSPubEngine {
 public:
  virtual ~DPSPubEngine() {}
  static const int32_t MAX_MANAGER_NUM = 10;

  /**
   * 注册service
   */
  virtual DPSError RegisterModule(
      const std::shared_ptr<DPSModuleInfo>& service_info) = 0;

  /**
   * App切到后台后通知SDK
   */
  virtual void OnAppDidEnterBackground() = 0;

  /**
   * App切到前台前通知SDK
   */
  virtual void OnAppWillEnterForeground() = 0;

  /**
   * 获取设置服务
   */
  virtual std::shared_ptr<DPSPubSettingService> GetSettingService() const = 0;

  /**
   * 获取引擎是否启动
   */
  virtual bool IsStarted() = 0;

  /**
   * 启动
   * @param listener Engine Start回调
   */
  virtual void Start(
      const std::shared_ptr<DPSEngineStartListener>& listener) = 0;
  virtual void Start(
      const std::function<void()>& OnSuccess,
      const std::function<void(const DPSError& error)>& OnFailure) = 0;

  /**
   * 设置事件监听器
   * @param listener 监听器
   */
  virtual void SetListener(
      const std::shared_ptr<DPSPubEngineListener>& listener) = 0;

  /**
   * 获取所有当前存在的DPSUserId
   */
  virtual std::vector<std::string> GetUserIds() = 0;

  /**
   * 创建Manager,注意只允许同时存在MAX_MANAGER_NUM以下的Manager实例
   * @param user_id 登录帐号
   * @param biz_params 业务特殊参数，如无传入空map即可
   * "always_fetch_msg_in_single_conv": "1"/"0" 开启单聊会话强制拉取
   * "always_fetch_msg_in_group_conv": "1"/"0" 开启群聊会话强制拉取
   * @param listener 监听器
   */
  virtual void CreateDPSManager(
      const std::string& user_id,
      const std::map<std::string, std::string>& biz_params,
      const std::shared_ptr<DPSPubManagerCreateListener>& listener) = 0;
  virtual void CreateDPSManager(
      const std::string& user_id,
      const std::map<std::string, std::string>& biz_params,
      const std::function<void(const std::shared_ptr<DPSPubManager>& manager)>&
          OnSuccess,
      const std::function<void(const DPSError& error)>& OnFailure) = 0;

  /**
   * 获取DPSManager
   * @param user_id 登录帐号
   */
  virtual std::shared_ptr<DPSPubManager> GetDPSManager(
      const std::string& user_id) = 0;

  /**
   * 释放DPSManager
   * @param user_id 登录帐号
   */
  virtual void ReleaseDPSManager(
      const std::string& user_id,
      const std::shared_ptr<DPSReleaseManagerListener>& listener) = 0;
  virtual void ReleaseDPSManager(
      const std::string& user_id,
      const std::function<void()>& OnSuccess,
      const std::function<void(const DPSError& error)>& OnFailure) = 0;

  /**
   * 创建DPSEngine,如果已经有实例存在,会返回已创建的DPSEngine
   */
  DPS_DECL static std::shared_ptr<DPSPubEngine> CreateDPSEngine();

  /**
   * 获取DPSEngine
   */
  DPS_DECL static std::shared_ptr<DPSPubEngine> GetDPSEngine();

  /**
   * 设置log处理函数,处理函数可能会在多个线程调用,处理函数不能长时间阻塞
   * @param min_log_level
   * 需要输出的最低日志级别（日志级别大于等于这个值才会输出）
   * @param handler 日志处理函数
   */
  DPS_DECL static void SetLogHandler(
      DPSLogLevel min_log_level,
      const std::shared_ptr<DPSLogHandler>& handler);

  /**
   * 释放DPSEngine
   */
  DPS_DECL static void ReleaseDPSEngine();

  /**
   * 清理用户数据接口，可通过该接口清理用户数据，本地存储的消息/会话等数据将被清空
   * @param data_path
   * 数据存储路径，与之前设置到DPSSettingService内的DataPath一致
   * @param user_id 需清理的账号
   * @param app_id 账号关联的appID
   * @param onSuccess 重置成功
   * @param onFailure 重置失败，部分文件无法删除
   * 注意事项：
   * 1. 需在user_id 对应DPSManager启动之前进行该操作
   * 2. 部分文件由于io错误，或被其他应用使用，可能导致无法删除
   */
  DPS_DECL static void ResetUserData(
      const std::string& data_path,
      const std::string& user_id,
      const std::string& app_id,
      const std::shared_ptr<DPSResetUserDataListener>& listener);
  DPS_DECL static void ResetUserData(
      const std::string& data_path,
      const std::string& user_id,
      const std::string& app_id,
      const std::function<void()>& OnSuccess,
      const std::function<void(const DPSError& error)>& OnFailure);

  /**
   * 获取服务端时钟
   * @return 单位毫秒，如果获取失败，返回-1
   */
  virtual int64_t GetServerTimeClock() = 0;
};

using DPSPubEnginePtr = std::shared_ptr<DPSPubEngine>;
using DPSPubEngineWeakPtr = std::weak_ptr<DPSPubEngine>;

}  // namespace dps
}  // namespace alibaba
