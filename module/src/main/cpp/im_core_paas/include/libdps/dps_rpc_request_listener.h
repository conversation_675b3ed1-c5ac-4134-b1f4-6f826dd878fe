// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <functional>
#include <map>
#include <memory>
#include <string>
#include <vector>

namespace alibaba {
namespace dps {

struct DPSError;

/**
 * RPC 请求监听
 */
class DPSRpcRequestListener {
 public:
  virtual ~DPSRpcRequestListener() {}

  virtual void OnSuccess(const std::map<std::string, std::string>& kv_params,
                         const std::vector<uint8_t>& response_body) = 0;

  virtual void OnFailure(const DPSError& error) = 0;
};

using DPSRpcRequestListenerPtr = std::shared_ptr<DPSRpcRequestListener>;
using DPSRpcRequestListenerWeakPtr = std::weak_ptr<DPSRpcRequestListener>;

}  // namespace dps
}  // namespace alibaba
