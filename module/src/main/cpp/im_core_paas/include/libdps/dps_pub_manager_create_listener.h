// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

class DPSPubManager;
struct DPSError;

/**
 * DPSManager 创建监听
 */
class DPSPubManagerCreateListener {
 public:
  virtual ~DPSPubManagerCreateListener() {}

  /**
   * DPSManager 创建成功
   */
  virtual void OnSuccess(const std::shared_ptr<DPSPubManager>& manager) = 0;

  /**
   * DPSManager 创建失败
   */
  virtual void OnFailure(const DPSError& error) = 0;
};

using DPSPubManagerCreateListenerPtr =
    std::shared_ptr<DPSPubManagerCreateListener>;
using DPSPubManagerCreateListenerWeakPtr =
    std::weak_ptr<DPSPubManagerCreateListener>;

}  // namespace dps
}  // namespace alibaba
