// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <functional>
#include <map>
#include <memory>
#include <string>
#include <vector>

namespace alibaba {
namespace dps {

class DPSUtListener {
 public:
  virtual ~DPSUtListener() {}

  /**
   * 成功打点上报
   * @param module    模块
   * @param monitor_point    打点名称
   * @param extensions      附加参数
   */
  virtual void OnSuccessReport(
      const std::string& module,
      const std::string& monitor_point,
      const std::map<std::string, std::string>& extensions) = 0;

  /**
   * 失败打点上报
   * @param module    模块
   * @param monitor_point    打点名称
   * @param error_code     错误码
   * @param error_msg     错误信息
   * @param extensions      附加参数
   */
  virtual void OnFailReport(
      const std::string& module,
      const std::string& monitor_point,
      int32_t error_code,
      const std::string& error_msg,
      const std::map<std::string, std::string>& extensions) = 0;

  /**
   * 计数打点上报
   * @param module    模块
   * @param monitor_point    打点名称
   * @param count     计数值
   * @param extensions      附加参数
   */
  virtual void OnCountReport(
      const std::string& module,
      const std::string& monitor_point,
      double count,
      const std::map<std::string, std::string>& extensions) = 0;

  /**
   * 性能打点注册
   * @param module    模块
   * @param monitor_point    打点名称
   * @param dimensions 维度
   * @param measures 测量数据
   */
  virtual void OnStatRegister(const std::string& module,
                              const std::string& monitor_point,
                              const std::vector<std::string>& dimensions,
                              const std::vector<std::string>& measures) = 0;

  /**
   * 性能打点上报
   * @param module    模块
   * @param monitor_point    打点名称
   * @param dimension_values 维度
   * @param measure_values 测量数据
   */
  virtual void OnStatReport(
      const std::string& module,
      const std::string& monitor_point,
      const std::map<std::string, std::string>& dimension_values,
      const std::map<std::string, double>& measure_values) = 0;
};

using DPSUtListenerPtr = std::shared_ptr<DPSUtListener>;
using DPSUtListenerWeakPtr = std::weak_ptr<DPSUtListener>;

}  // namespace dps
}  // namespace alibaba
