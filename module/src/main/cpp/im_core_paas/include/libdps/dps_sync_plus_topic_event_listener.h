// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

class DPSSyncPlusTopicEventListener {
 public:
  virtual ~DPSSyncPlusTopicEventListener() {}

  /**
   * 开始同步服务端数据事件，一般在重连后触发
   */
  virtual void OnStartSyncServer() = 0;

  /**
   * 完成服务端数据同步事件
   * @param is_toolong2 是否是toolong2
   */
  virtual void OnEndSyncServer(bool is_toolong2) = 0;
};

using DPSSyncPlusTopicEventListenerPtr =
    std::shared_ptr<DPSSyncPlusTopicEventListener>;
using DPSSyncPlusTopicEventListenerWeakPtr =
    std::weak_ptr<DPSSyncPlusTopicEventListener>;

}  // namespace dps
}  // namespace alibaba
