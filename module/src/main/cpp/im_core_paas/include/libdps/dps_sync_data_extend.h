// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 同步协议扩展信息
 */
struct DPSSyncDataExtend final {
  /**
   * toolong2之前最后一个收到的消息时间
   */
  int64_t min_create_time = -1;
  /**
   * 双推数据
   */
  bool is_failover = false;

  DPSSyncDataExtend(int64_t min_create_time_, bool is_failover_)
      : min_create_time(std::move(min_create_time_)),
        is_failover(std::move(is_failover_)) {}

  DPSSyncDataExtend() {}
};

}  // namespace dps
}  // namespace alibaba
