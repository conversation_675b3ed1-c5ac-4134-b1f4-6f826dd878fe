// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <functional>
#include <memory>
#include <string>
#include <vector>

namespace alibaba {
namespace dps {

class DPSAuthTokenGotCallback;
class DPSPubAuthTokenCallback;
enum class DPSAuthTokenExpiredReason;
enum class DPSEnvType;
struct DPSMediaHost;
struct DPSSyncProtocolInfo;

/**
 * DPSSettingService
 */
class DPSPubSettingService {
 public:
  virtual ~DPSPubSettingService() {}

  /**
   * 设置多媒体Media Host, 若不设置则采用内置默认Host
   */
  virtual void SetMediaHost(const std::vector<DPSMediaHost>& media_host) = 0;

  /**
   * 设置应用appid
   * @param app_id DingPaaS分配给应用的appID
   */
  virtual void SetAppID(const std::string& app_id) = 0;

  /**
   * 设置应用key
   * @param app_key IM_PAAS分配给应用的key
   */
  virtual void SetAppKey(const std::string& app_key) = 0;

  /**
   * 设置数据存储路径
   * @param data_path
   * 数据存取路径(utf-8),app需提前创建好此目录并对此目录有读写权限
   */
  virtual void SetDataPath(const std::string& data_path) = 0;

  /**
   * 设置应用名称
   * @param app_name 应用名称
   */
  virtual void SetAppName(const std::string& app_name) = 0;

  /**
   * 设置应用版本
   * @param app_version 应用版本号
   */
  virtual void SetAppVersion(const std::string& version) = 0;

  /**
   * 设置App语言区域
   * @param app_locale 语言区域（默认zh_CN）
   */
  virtual void SetAppLocale(const std::string& app_local) = 0;

  /**
   * 设置操作系统名称
   * @param os_name 操作系统名称，如Win、macOS、iOS、Android等
   */
  virtual void SetOSName(const std::string& os_name) = 0;

  /**
   * 设置操作系统版本
   * @param os_version 操作系统版本
   */
  virtual void SetOSVersion(const std::string& os_version) = 0;

  /**
   * 设置设备名称
   * @param device_name 设备名称
   */
  virtual void SetDeviceName(const std::string& device_name) = 0;

  /**
   * 设置设备型号
   * @param device_type 设备型号
   */
  virtual void SetDeviceType(const std::string& device_type) = 0;

  /**
   * 设置设备语言
   * @param device_local 设备语言（默认zh_CN）
   */
  virtual void SetDeviceLocale(const std::string& device_local) = 0;

  /**
   * 设置设备唯一id
   * @param device_id 设备唯一id
   *  device_id生成算法不合适（容易冲突）可能会影响正常使用
   */
  virtual void SetDeviceId(const std::string& device_id) = 0;

  /**
   * 设置时区
   * @param time_zone 时区（默认Asia/Shanghai）
   */
  virtual void SetTimeZone(const std::string& time_zone) = 0;

  /**
   * 设置长链接地址, 若不设置则采用内置默认地址
   * @param url 长链接地址
   */
  virtual void SetLonglinkServerAddress(const std::string& url) = 0;

  /**
   * 文件上传服务器地址, 若不设置则采用内置默认地址
   * @param url 服务器地址
   */
  virtual void SetFileUploadServerAddress(const std::string& url) = 0;

  /**
   * 设置获取登录token的回调函数
   * @param on_callback
   * 获取登录token的回调函数（注意需要做成异步操作，不能有阻塞）
   */
  virtual void SetAuthTokenCallback(
      const std::shared_ptr<DPSPubAuthTokenCallback>& on_callback) = 0;
  virtual void SetAuthTokenCallback(
      const std::function<
          void(const std::string& user_id,
               const std::shared_ptr<DPSAuthTokenGotCallback>& on_got,
               DPSAuthTokenExpiredReason reason)>& OnCallback) = 0;

  /**
   * 设置Sync协议相关设置
   * @param info Sync协议设置
   */
  virtual void AddSyncProtocolSetting(
      const std::vector<DPSSyncProtocolInfo>& info) = 0;

  /**
   * 设置自定义UserAgent：可选配置，如不设定，则自动生成UserAgent，设置则使用该UA
   */
  virtual void SetCustomUserAgent(const std::string& ua) = 0;

  /**
   * 设置是否开启ipv6(默认不开启)
   */
  virtual void SetEnableIpv6(bool enable) = 0;

  /**
   * 设置关闭ssl校验(默认不关闭，日常环境请设置为true)
   */
  virtual void SetDisableSslVerify(bool disable) = 0;

  /**
   * 设置当前连接的服务端环境(日常，预发或线上)
   * 需要与长连接地址匹配，不然无法登陆成功
   */
  virtual void SetEnvType(DPSEnvType type) = 0;

  /**
   * 设置首次登陆时拉取的会话数,如果不设置默认值为500
   * @param size 会话数
   */
  virtual void SetFirstLoginConvSize(int32_t size) = 0;

  /**
   * 设置是否支持二级会话功能(默认不支持)
   */
  virtual void SetMultiConvSupport(bool is_support) = 0;
};

using DPSPubSettingServicePtr = std::shared_ptr<DPSPubSettingService>;
using DPSPubSettingServiceWeakPtr = std::weak_ptr<DPSPubSettingService>;

}  // namespace dps
}  // namespace alibaba
