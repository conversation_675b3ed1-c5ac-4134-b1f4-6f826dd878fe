// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class DPSNetType : int {
  /**
   * 无网络
   */
  NT_NOT_REACHABLE = 0,
  /**
   * WIFI
   */
  NT_WIFI = 1,
  /**
   * 有线网络
   */
  NT_WIRE = 2,
  /**
   * 2G
   */
  NT_2G = 3,
  /**
   * 3G
   */
  NT_3G = 4,
  /**
   * 4G
   */
  NT_4G = 5,
  /**
   * 5G
   */
  NT_5G = 6,
  /**
   * 其他
   */
  NT_OTHER = 100,
};

}
}  // namespace alibaba

namespace std {

template <>
struct hash<::alibaba::dps::DPSNetType> {
  size_t operator()(::alibaba::dps::DPSNetType type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

}  // namespace std
