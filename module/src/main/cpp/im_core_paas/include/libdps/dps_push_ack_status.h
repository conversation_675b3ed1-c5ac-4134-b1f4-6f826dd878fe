// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

/**
 * Push ack 状态
 */
class DPSPushAckStatus {
 public:
  virtual ~DPSPushAckStatus() {}

  /**
   * @brief 回复客户端处理成功（状态码:200）
   */
  virtual void AckSuccess() = 0;

  /**
   * @brief 回复客户端无法识别此消息包（状态码:400）
   */
  virtual void AckInvalid() = 0;

  /**
   * @brief 回复客户端处理异常（状态码:500）
   */
  virtual void AckException() = 0;

  /**
   * @brief 回复特定的状态码
   * @param code   状态码
   */
  virtual void AckStatus(int32_t code) = 0;
};

using DPSPushAckStatusPtr = std::shared_ptr<DPSPushAckStatus>;
using DPSPushAckStatusWeakPtr = std::weak_ptr<DPSPushAckStatus>;

}  // namespace dps
}  // namespace alibaba
