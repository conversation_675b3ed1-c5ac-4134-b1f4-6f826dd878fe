// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

class DPSRpcService;
class DPSSyncService;

class DPSServiceProvider {
 public:
  virtual ~DPSServiceProvider() {}

  virtual std::shared_ptr<DPSRpcService> GetRpcService() = 0;

  virtual std::shared_ptr<DPSSyncService> GetSyncService() = 0;
};

using DPSServiceProviderPtr = std::shared_ptr<DPSServiceProvider>;
using DPSServiceProviderWeakPtr = std::weak_ptr<DPSServiceProvider>;

}  // namespace dps
}  // namespace alibaba
