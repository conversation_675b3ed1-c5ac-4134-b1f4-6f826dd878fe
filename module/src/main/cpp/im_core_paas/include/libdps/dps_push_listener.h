// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <functional>
#include <memory>
#include <vector>

namespace alibaba {
namespace dps {

class DPSPushAckStatus;

/**
 * RPC push监听
 */
class DPSPushListener {
 public:
  virtual ~DPSPushListener() {}

  /**
   * @brief 推送处理
   * @param body   消息体内容
   * @param ack    本次推送回复服务端处理状态（服务端依赖此回复进行下次推送）
   */
  virtual void OnPush(const std::vector<uint8_t>& body,
                      const std::shared_ptr<DPSPushAckStatus>& ack) = 0;
};

using DPSPushListenerPtr = std::shared_ptr<DPSPushListener>;
using DPSPushListenerWeakPtr = std::weak_ptr<DPSPushListener>;

}  // namespace dps
}  // namespace alibaba
