// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class DPSRpcDataType : int {
  /**
   * msgpack格式
   */
  DT_MSGPACK = 0,
  /**
   * json格式
   */
  DT_JSON = 1,
};

}
}  // namespace alibaba

namespace std {

template <>
struct hash<::alibaba::dps::DPSRpcDataType> {
  size_t operator()(::alibaba::dps::DPSRpcDataType type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

}  // namespace std
