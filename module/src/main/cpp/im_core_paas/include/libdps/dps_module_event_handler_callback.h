// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

struct DPSError;

class DPSModuleEventHandlerCallback {
 public:
  virtual ~DPSModuleEventHandlerCallback() {}

  virtual void OnSuccess() = 0;

  virtual void OnFailure(const DPSError& error) = 0;
};

using DPSModuleEventHandlerCallbackPtr =
    std::shared_ptr<DPSModuleEventHandlerCallback>;
using DPSModuleEventHandlerCallbackWeakPtr =
    std::weak_ptr<DPSModuleEventHandlerCallback>;

}  // namespace dps
}  // namespace alibaba
