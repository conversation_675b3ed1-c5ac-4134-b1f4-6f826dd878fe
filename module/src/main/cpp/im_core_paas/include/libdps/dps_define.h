// Copyright (c) 2016 The Alibaba DingTalk Authors. All rights reserved.

#ifndef dps_define_h
#define dps_define_h

#ifdef _WIN32

#if defined(DPS_EXPORT)
#define DPS_DECL __declspec(dllexport)
#else
#define DPS_DECL __declspec(dllimport)
#endif

#else

#if defined(DPS_EXPORT)
#define DPS_DECL __attribute__((visibility("default")))
#else
#define DPS_DECL
#endif

#endif  // _WIN32

#define DPS_NAMESPACE alibaba::dps

#define DPS_NAMESPACE_BEGIN \
  namespace alibaba {       \
  namespace dps {
#define DPS_NAMESPACE_END \
  }                       \
  }

#define DPS_MOCK virtual

#endif /* dps_define_h */
