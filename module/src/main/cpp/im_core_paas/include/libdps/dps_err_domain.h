// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class DPSErrDomain : int {
  /**
   * 客户端错误
   */
  DPS_ERR_DOMAIN_CLIENT,
  /**
   * 服务端错误
   */
  DPS_ERR_DOMAIN_SERVER,
  /**
   * 外部错误
   */
  DPS_ERR_EXTERNAL,
};

}
}  // namespace alibaba

namespace std {

template <>
struct hash<::alibaba::dps::DPSErrDomain> {
  size_t operator()(::alibaba::dps::DPSErrDomain type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

}  // namespace std
