// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <string>
#include <utility>
#include "dps_sync_protocol_type.h"

namespace alibaba {
namespace dps {

/**
 * Sync协议内容
 */
struct DPSSyncProtocolInfo final {
  /**
   * 通道标识
   */
  std::string topic;
  /**
   * 业务标示
   */
  int64_t biz_type = 0;
  /**
   * 业务名称
   */
  std::string biz_name;
  /**
   * 协议类型
   */
  DPSSyncProtocolType protocol_type = DPSSyncProtocolType::RELIABLE;

  DPSSyncProtocolInfo(std::string topic_,
                      int64_t biz_type_,
                      std::string biz_name_,
                      DPSSyncProtocolType protocol_type_)
      : topic(std::move(topic_)),
        biz_type(std::move(biz_type_)),
        biz_name(std::move(biz_name_)),
        protocol_type(std::move(protocol_type_)) {}

  DPSSyncProtocolInfo() {}
};

}  // namespace dps
}  // namespace alibaba
