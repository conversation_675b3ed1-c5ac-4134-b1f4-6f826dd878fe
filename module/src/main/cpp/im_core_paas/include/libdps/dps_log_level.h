// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class DPSLogLevel : int {
  DPS_LOG_LEVEL_DEBUG = 0,
  DPS_LOG_LEVEL_INFO = 1,
  DPS_LOG_LEVEL_WARNING = 2,
  DPS_LOG_LEVEL_ERROR = 3,
  DPS_LOG_LEVEL_FATAL = 4,
};

}
}  // namespace alibaba

namespace std {

template <>
struct hash<::alibaba::dps::DPSLogLevel> {
  size_t operator()(::alibaba::dps::DPSLogLevel type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

}  // namespace std
