// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <string>
#include <utility>
#include "dps_media_host_type.h"

namespace alibaba {
namespace dps {

/**
 * 多媒体服务端配置
 */
struct DPSMediaHost final {
  /**
   * 类型
   */
  DPSMediaHostType type = DPSMediaHostType::MEDIA_HOST_TYPE_AUTH;
  /**
   * 地址
   */
  std::string host;

  DPSMediaHost(DPSMediaHostType type_, std::string host_)
      : type(std::move(type_)), host(std::move(host_)) {}

  DPSMediaHost() {}
};

}  // namespace dps
}  // namespace alibaba
