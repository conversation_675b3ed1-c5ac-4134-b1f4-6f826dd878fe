// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <functional>
#include <memory>
#include <string>

namespace alibaba {
namespace dps {

class DPSAuthListener;
class DPSKickoutListener;
class DPSLogoutListener;
enum class DPSConnectionStatus;
enum class DPSNetType;
struct DPSError;

/**
 * Auth Service
 */
class DPSAuthService {
 public:
  virtual ~DPSAuthService() {}

  /**
   * 获取网络是否可用
   */
  virtual bool IsNetworkAvailable() const = 0;

  /**
   * 获取网络连接类型
   */
  virtual DPSNetType GetNetType() const = 0;

  /**
   * 获取连接状态
   */
  virtual DPSConnectionStatus GetConnectionStatus() const = 0;

  /**
   * 是否处于本地登录状态（该状态和是否联网无关）
   */
  virtual bool IsLocalLogin() const = 0;

  /**
   * 登录接口, 允许账号连接网络进行登录
   */
  virtual void Login() = 0;

  /**
   * 退出登录接口
   * @param listener 监听器
   */
  virtual void Logout(const std::shared_ptr<DPSLogoutListener>& listener) = 0;
  virtual void Logout(
      const std::function<void()>& OnSuccess,
      const std::function<void(const DPSError& error)>& OnFailure) = 0;

  /**
   * 踢人接口（暂不可用）
   * @param type      被踢下线的设备类型， 0：web，1：app
   * @param message   被踢下线设备收到的消息
   * @param listener 监听器
   */
  virtual void Kickout(int32_t type,
                       const std::string& message,
                       const std::shared_ptr<DPSKickoutListener>& listener) = 0;
  virtual void Kickout(
      int32_t type,
      const std::string& message,
      const std::function<void()>& OnSuccess,
      const std::function<void(const DPSError& error)>& OnFailure) = 0;

  /**
   * 注册监听器
   * @param listener 监听器
   */
  virtual bool AddListener(
      const std::shared_ptr<DPSAuthListener>& listener) = 0;

  /**
   * 删除监听器
   * @param listener 用户监听器
   */
  virtual bool RemoveListener(
      const std::shared_ptr<DPSAuthListener>& listener) = 0;

  /**
   * 删除所有监听器
   */
  virtual void RemoveAllListeners() = 0;
};

using DPSAuthServicePtr = std::shared_ptr<DPSAuthService>;
using DPSAuthServiceWeakPtr = std::weak_ptr<DPSAuthService>;

}  // namespace dps
}  // namespace alibaba
