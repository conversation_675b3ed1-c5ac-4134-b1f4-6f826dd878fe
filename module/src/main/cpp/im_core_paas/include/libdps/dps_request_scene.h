// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class DPSRequestScene : int {
  /**
   * 主连接请求
   */
  SCENE_MAIN_ADDRESS = 0,
  /**
   * 弱网请求
   */
  SCENE_BACKUP_ADDRESS = 1,
};

}
}  // namespace alibaba

namespace std {

template <>
struct hash<::alibaba::dps::DPSRequestScene> {
  size_t operator()(::alibaba::dps::DPSRequestScene type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

}  // namespace std
