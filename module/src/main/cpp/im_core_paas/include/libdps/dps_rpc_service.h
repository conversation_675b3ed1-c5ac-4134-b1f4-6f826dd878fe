// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <functional>
#include <map>
#include <memory>
#include <string>
#include <vector>

namespace alibaba {
namespace dps {

class DPSPushAckStatus;
class DPSPushListener;
class DPSRpcRequestListener;
struct DPSError;
struct DPSRpcRequestHeader;

/**
 * DPSRpcService
 */
class DPSRpcService {
 public:
  virtual ~DPSRpcService() {}

  /**
   * @brief 发送rpc请求
   * @param name rpc名称
   * @param body 包体内容，格式由header中的data_type指定
   * @param header 包头
   * @param listener 返回监听器
   */
  virtual void Request(
      const std::string& name,
      const std::vector<uint8_t>& body,
      const DPSRpcRequestHeader& header,
      const std::shared_ptr<DPSRpcRequestListener>& listener) = 0;
  virtual void Request(
      const std::string& name,
      const std::vector<uint8_t>& body,
      const DPSRpcRequestHeader& header,
      const std::function<
          void(const std::map<std::string, std::string>& kv_params,
               const std::vector<uint8_t>& response_body)>& OnSuccess,
      const std::function<void(const DPSError& error)>& OnFailure) = 0;

  /**
   * @brief 通道名称
   * @param name 通道名称
   * @param lisenter 通道消息监听器
   */
  virtual void Subscribe(const std::string& name,
                         const std::shared_ptr<DPSPushListener>& lisenter) = 0;
};

using DPSRpcServicePtr = std::shared_ptr<DPSRpcService>;
using DPSRpcServiceWeakPtr = std::weak_ptr<DPSRpcService>;

}  // namespace dps
}  // namespace alibaba
