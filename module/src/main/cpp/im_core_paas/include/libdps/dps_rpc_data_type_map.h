// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <map>
#include <string>
#include <utility>
#include "dps_rpc_data_type.h"

namespace alibaba {
namespace dps {

/**
 * RPC数据结构
 */
struct DPSRpcDataTypeMap final {
  /**
   * 数据类型
   */
  DPSRpcDataType data_type = DPSRpcDataType::DT_MSGPACK;
  /**
   * 是否允许断线后自动重试
   */
  bool enable_retry = false;
  /**
   * rpc超时时间，一般设为40s
   */
  int64_t timeout_ms = 40000;
  /**
   * 其他自定义参数，一般为空
   */
  std::map<std::string, std::string> kv_params;

  DPSRpcDataTypeMap(DPSRpcDataType data_type_,
                    bool enable_retry_,
                    int64_t timeout_ms_,
                    std::map<std::string, std::string> kv_params_)
      : data_type(std::move(data_type_)),
        enable_retry(std::move(enable_retry_)),
        timeout_ms(std::move(timeout_ms_)),
        kv_params(std::move(kv_params_)) {}

  DPSRpcDataTypeMap() {}
};

}  // namespace dps
}  // namespace alibaba
