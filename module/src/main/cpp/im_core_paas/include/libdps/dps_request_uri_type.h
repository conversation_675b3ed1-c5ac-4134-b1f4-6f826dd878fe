// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class DPSRequestUriType : int {
  /**
   * udp
   */
  REQUEST_URI_UDP = 0,
  /**
   * http
   */
  REQUEST_URI_HTTP = 1,
  /**
   * quic
   */
  REQUEST_URI_QUIC = 2,
  /**
   * tls
   */
  REQUEST_URI_TLS = 3,
  /**
   * lws
   */
  REQUEST_URI_LWS = 4,
};

}
}  // namespace alibaba

namespace std {

template <>
struct hash<::alibaba::dps::DPSRequestUriType> {
  size_t operator()(::alibaba::dps::DPSRequestUriType type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

}  // namespace std
