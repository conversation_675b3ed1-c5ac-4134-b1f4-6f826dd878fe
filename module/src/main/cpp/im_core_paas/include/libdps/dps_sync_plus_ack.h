// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>
#include <string>

namespace alibaba {
namespace dps {

class DPSSyncPlusAck {
 public:
  virtual ~DPSSyncPlusAck() {}

  /**
   * 业务回调掉成功
   */
  virtual void OnSuccess() = 0;

  /**
   * 业务回调掉失败
   * @param msg 失败原因，可以为空
   */
  virtual void OnFailed(const std::string& msg) = 0;
};

using DPSSyncPlusAckPtr = std::shared_ptr<DPSSyncPlusAck>;
using DPSSyncPlusAckWeakPtr = std::weak_ptr<DPSSyncPlusAck>;

}  // namespace dps
}  // namespace alibaba
