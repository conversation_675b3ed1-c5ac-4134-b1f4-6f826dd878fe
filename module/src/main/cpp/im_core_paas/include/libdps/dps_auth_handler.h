// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <map>
#include <memory>
#include <string>

namespace alibaba {
namespace dps {

class DPSAuthListener;

class DPSAuthHandler {
 public:
  virtual ~DPSAuthHandler() {}

  /**
   * 可在此添加 reg 时的 header，无需添加，则返回空
   */
  virtual std::map<std::string, std::string> OnGetRegHeader() = 0;

  /**
   * 监听登陆/连接状态，不感知，可返回空
   */
  virtual std::shared_ptr<DPSAuthListener> OnGetAuthListener() = 0;
};

using DPSAuthHandlerPtr = std::shared_ptr<DPSAuthHandler>;
using DPSAuthHandlerWeakPtr = std::weak_ptr<DPSAuthHandler>;

}  // namespace dps
}  // namespace alibaba
