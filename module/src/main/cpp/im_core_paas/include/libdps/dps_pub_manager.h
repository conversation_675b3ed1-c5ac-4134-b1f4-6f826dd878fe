// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>
#include <string>

namespace alibaba {
namespace dps {

class DPSAuthService;
class DPSRpcService;
class DPSSyncService;
class DPSUtService;

class DPSPubManager {
 public:
  virtual ~DPSPubManager() {}

  /**
   * 获取该DPSManager对应的DPSUserID
   */
  virtual std::string GetUserId() const = 0;

  /**
   * 获取登录授权Service
   */
  virtual std::shared_ptr<DPSAuthService> GetAuthService() const = 0;

  /**
   * 获取同步协议相关Service
   */
  virtual std::shared_ptr<DPSSyncService> GetSyncService() const = 0;

  /**
   * 获取Rpc相关Service
   */
  virtual std::shared_ptr<DPSRpcService> GetRpcService() const = 0;

  /**
   * 获取打点相关Service
   */
  virtual std::shared_ptr<DPSUtService> GetUtService() const = 0;
};

using DPSPubManagerPtr = std::shared_ptr<DPSPubManager>;
using DPSPubManagerWeakPtr = std::weak_ptr<DPSPubManager>;

}  // namespace dps
}  // namespace alibaba
