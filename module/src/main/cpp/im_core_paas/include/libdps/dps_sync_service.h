// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <functional>
#include <memory>
#include <string>
#include <vector>

namespace alibaba {
namespace dps {

class DPSSyncPlusAck;
class DPSSyncPlusBizEventListener;
class DPSSyncPlusPackageDataHandler;
class DPSSyncPlusTopicEventListener;
struct DPSSyncData;
struct DPSSyncDataExtend;

/**
 * DPSSyncServer
 */
class DPSSyncService {
 public:
  virtual ~DPSSyncService() {}

  /**
   * 添加通道事件监听
   * @param topic 通道名
   * @param listener 回调对象
   */
  virtual bool AddSyncTopicEventListener(
      const std::string& topic,
      const std::shared_ptr<DPSSyncPlusTopicEventListener>& listener) = 0;

  /**
   * 删除通道事件监听
   * @param topic 通道名
   * @param listener 回调对象
   */
  virtual bool RemoveSyncTopicEventListener(
      const std::string& topic,
      const std::shared_ptr<DPSSyncPlusTopicEventListener>& listener) = 0;

  /**
   * 添加业务事件监听
   * @param biz_type 业务类型
   * @param listener 回调对象
   */
  virtual bool AddSyncBizEventListener(
      int32_t biz_type,
      const std::shared_ptr<DPSSyncPlusBizEventListener>& listener) = 0;

  /**
   * 删除业务事件监听
   * @param biz_type 业务类型
   * @param listener 回调对象
   */
  virtual bool RemoveSyncBizEventListener(
      int32_t biz_type,
      const std::shared_ptr<DPSSyncPlusBizEventListener>& listener) = 0;

  /**
   * 添加业务数据监听
   * @param biz_type 业务类型
   * @param handler 回调对象
   */
  virtual bool AddSyncDataHandler(
      int32_t biz_type,
      const std::shared_ptr<DPSSyncPlusPackageDataHandler>& handler) = 0;

  /**
   * 删除业务数据监听
   * @param biz_type 业务类型
   * @param handler 回调对象
   */
  virtual bool RemoveSyncDataHandler(
      int32_t biz_type,
      const std::shared_ptr<DPSSyncPlusPackageDataHandler>& handler) = 0;
};

using DPSSyncServicePtr = std::shared_ptr<DPSSyncService>;
using DPSSyncServiceWeakPtr = std::weak_ptr<DPSSyncService>;

}  // namespace dps
}  // namespace alibaba
