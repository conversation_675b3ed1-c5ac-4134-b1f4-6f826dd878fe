// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>
#include <string>

namespace alibaba {
namespace dps {

class DPSModuleEventHandler;

class DPSModuleInfo {
 public:
  virtual ~DPSModuleInfo() {}

  virtual std::string GetModuleName() = 0;

  virtual std::shared_ptr<DPSModuleEventHandler> GetModuleEventHandler() = 0;
};

using DPSModuleInfoPtr = std::shared_ptr<DPSModuleInfo>;
using DPSModuleInfoWeakPtr = std::weak_ptr<DPSModuleInfo>;

}  // namespace dps
}  // namespace alibaba
