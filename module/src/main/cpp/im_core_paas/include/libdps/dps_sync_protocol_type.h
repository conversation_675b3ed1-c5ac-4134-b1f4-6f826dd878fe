// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class DPSSyncProtocolType : int {
  /**
   * 可靠推送
   */
  RELIABLE,
  /**
   * 不可靠推送
   */
  UNRELIABLE,
};

}
}  // namespace alibaba

namespace std {

template <>
struct hash<::alibaba::dps::DPSSyncProtocolType> {
  size_t operator()(::alibaba::dps::DPSSyncProtocolType type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

}  // namespace std
