// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <functional>
#include <memory>
#include <string>

namespace alibaba {
namespace dps {

struct DPSAuthToken;

/**
 * 通知获取到Auth token回调
 */
class DPSAuthTokenGotCallback {
 public:
  virtual ~DPSAuthTokenGotCallback() {}

  /**
   * 通知DPSEngine token获取成功
   */
  virtual void OnSuccess(const DPSAuthToken& auth_token) = 0;

  /**
   * 通知DPSEngine token获取失败
   */
  virtual void OnFailure(int32_t error_code, const std::string& error_msg) = 0;
};

using DPSAuthTokenGotCallbackPtr = std::shared_ptr<DPSAuthTokenGotCallback>;
using DPSAuthTokenGotCallbackWeakPtr = std::weak_ptr<DPSAuthTokenGotCallback>;

}  // namespace dps
}  // namespace alibaba
