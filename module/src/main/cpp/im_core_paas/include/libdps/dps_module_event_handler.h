// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>
#include <string>
#include <vector>

namespace alibaba {
namespace dps {

class DPSAuthHandler;
class DPSModuleEventHandlerCallback;
class DPSServiceProvider;
struct DPSError;
struct DPSSyncProtocolInfo;
struct DPSUserId;

class DPSModuleEventHandler {
 public:
  virtual ~DPSModuleEventHandler() {}

  /**
   * 引擎启动前的回调，可进行用户无关的初始化设置
   * @param appID 应用的AppID
   */
  virtual void OnBeforeEngineStart(const std::string& appID) = 0;

  /**
   * 引擎启动完成通知
   */
  virtual void OnEngineStarted() = 0;

  /**
   * 用户相关Manager开始创建，用于创建该module内相应用户实例
   * @param uid 用户ID
   */
  virtual void OnBeforeManagerCreate(const DPSUserId& uid) = 0;

  /**
   * 初始化用户实例回调,
   * 可异步初始化，完成后需调用成功或失败回调，否则将影响DPS的初始化
   * @param uid 用户ID
   * @param callback 初始化成功或失败回调，必须调用
   * @param service_provider DPSBase 提供的基础服务
   */
  virtual void OnInitModuleForUser(
      const DPSUserId& uid,
      const std::shared_ptr<DPSModuleEventHandlerCallback>& callback,
      const std::shared_ptr<DPSServiceProvider>& service_provider) = 0;
  virtual void OnInitModuleForUser(
      const DPSUserId& uid,
      const std::function<void()>& OnSuccess,
      const std::function<void(const DPSError& error)>& OnFailure,
      const std::shared_ptr<DPSServiceProvider>& service_provider) = 0;

  /**
   * 用户相关Manager创建完成，可获取其他service
   * @param uid 用户ID
   * @param error 错误信息
   */
  virtual void OnManagerCreateFinished(const DPSUserId& uid,
                                       const DPSError& error) = 0;

  /**
   * 释放该uid的manager
   * @param uid 用户ID
   */
  virtual void OnReleaseManager(const DPSUserId& uid) = 0;

  /**
   * App 切到后台
   */
  virtual void OnAppDidEnterBackground() = 0;

  /**
   * App 切回前台
   */
  virtual void OnAppWillEnterForeground() = 0;

  /**
   * Engine 被释放
   */
  virtual void OnEngineReleased() = 0;

  /**
   * 获取登陆相关监听，如需要设置reg header或获取监听，需返回 DPSAuthHandler
   * 实例
   * @param uid 用户ID
   */
  virtual std::shared_ptr<DPSAuthHandler> OnGetAuthHandler(
      const DPSUserId& uid) = 0;

  /**
   * 返回同步协议设置
   */
  virtual std::vector<DPSSyncProtocolInfo> OnGetSyncProtocolInfo() = 0;
};

using DPSModuleEventHandlerPtr = std::shared_ptr<DPSModuleEventHandler>;
using DPSModuleEventHandlerWeakPtr = std::weak_ptr<DPSModuleEventHandler>;

}  // namespace dps
}  // namespace alibaba
