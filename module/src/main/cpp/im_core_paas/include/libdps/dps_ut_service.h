// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <functional>
#include <map>
#include <memory>
#include <string>
#include <vector>

namespace alibaba {
namespace dps {

class DPSUtListener;

class DPSUtService {
 public:
  virtual ~DPSUtService() {}

  /**
   * 注册监听器
   * @param listener 监听器
   */
  virtual bool AddListener(const std::shared_ptr<DPSUtListener>& listener) = 0;

  /**
   * 删除监听器
   * @param listener 监听器
   */
  virtual bool RemoveListener(
      const std::shared_ptr<DPSUtListener>& listener) = 0;

  /**
   * 删除所有监听器
   */
  virtual void RemoveAllListeners() = 0;
};

using DPSUtServicePtr = std::shared_ptr<DPSUtService>;
using DPSUtServiceWeakPtr = std::weak_ptr<DPSUtService>;

}  // namespace dps
}  // namespace alibaba
