// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

struct DPSError;

/**
 * ResetUserData 用户目录重置监听
 */
class DPSResetUserDataListener {
 public:
  virtual ~DPSResetUserDataListener() {}

  /**
   * 重置成功
   */
  virtual void OnSuccess() = 0;

  /**
   * 重置失败
   */
  virtual void OnFailure(const DPSError& error) = 0;
};

using DPSResetUserDataListenerPtr = std::shared_ptr<DPSResetUserDataListener>;
using DPSResetUserDataListenerWeakPtr = std::weak_ptr<DPSResetUserDataListener>;

}  // namespace dps
}  // namespace alibaba
