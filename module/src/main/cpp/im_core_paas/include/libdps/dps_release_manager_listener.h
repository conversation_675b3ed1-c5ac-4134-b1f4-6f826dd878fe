// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

struct DPSError;

/**
 * DPSManager 释放监听
 */
class DPSReleaseManagerListener {
 public:
  virtual ~DPSReleaseManagerListener() {}

  /**
   * DPSManager 创建成功
   */
  virtual void OnSuccess() = 0;

  /**
   * DPSManager 创建失败
   */
  virtual void OnFailure(const DPSError& error) = 0;
};

using DPSReleaseManagerListenerPtr = std::shared_ptr<DPSReleaseManagerListener>;
using DPSReleaseManagerListenerWeakPtr =
    std::weak_ptr<DPSReleaseManagerListener>;

}  // namespace dps
}  // namespace alibaba
