// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <map>
#include <string>
#include <utility>
#include "dps_rpc_data_type.h"

namespace alibaba {
namespace dps {

/**
 * RPC请求header
 */
struct DPSRpcRequestHeader final {
  DPSRpcDataType data_type = DPSRpcDataType::DT_MSGPACK;
  /**
   * 是否运行自动重试（需要确认服务端此RPC是否支持去重）
   */
  bool enable_retry = false;
  /**
   * 本次RPC超时时间
   */
  int64_t timeout_ms = 40000;
  /**
   * 此RPC是否不需要认证(仅用于特殊场景，如登录前需要扫码认证)
   */
  bool no_need_auth = false;
  /**
   * 其他自定义参数，一般为空
   */
  std::map<std::string, std::string> kv_params;

  DPSRpcRequestHeader(DPSRpcDataType data_type_,
                      bool enable_retry_,
                      int64_t timeout_ms_,
                      bool no_need_auth_,
                      std::map<std::string, std::string> kv_params_)
      : data_type(std::move(data_type_)),
        enable_retry(std::move(enable_retry_)),
        timeout_ms(std::move(timeout_ms_)),
        no_need_auth(std::move(no_need_auth_)),
        kv_params(std::move(kv_params_)) {}

  DPSRpcRequestHeader() {}
};

}  // namespace dps
}  // namespace alibaba
