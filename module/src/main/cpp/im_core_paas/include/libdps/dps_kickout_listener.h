// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

struct DPSError;

/**
 * 被踢出监听
 */
class DPSKickoutListener {
 public:
  virtual ~DPSKickoutListener() {}

  /**
   * 成功
   */
  virtual void OnSuccess() = 0;

  /**
   * 失败
   */
  virtual void OnFailure(const DPSError& error) = 0;
};

using DPSKickoutListenerPtr = std::shared_ptr<DPSKickoutListener>;
using DPSKickoutListenerWeakPtr = std::weak_ptr<DPSKickoutListener>;

}  // namespace dps
}  // namespace alibaba
