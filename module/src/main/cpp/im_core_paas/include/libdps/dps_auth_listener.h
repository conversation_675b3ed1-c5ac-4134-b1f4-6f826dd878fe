// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <functional>
#include <memory>
#include <string>

namespace alibaba {
namespace dps {

enum class DPSConnectionStatus;

/**
 * 登录监听
 */
class DPSAuthListener {
 public:
  virtual ~DPSAuthListener() {}

  /**
   * 连接状态事件
   * @param status      网络状态
   */
  virtual void OnConnectionStatusChanged(DPSConnectionStatus status) = 0;

  /**
   * 登录token获取失败事件
   * @param error_code  获取登录token失败错误值
   * @param error_msg   获取登录token失败错误信息
   */
  virtual void OnGetAuthCodeFailed(int32_t error_code,
                                   const std::string& error_msg) = 0;

  /**
   * 本地登录事件
   * 如果本地已有登录信息，调用Login接口后会立即回调；反之会等待网络登录成功之后回调
   */
  virtual void OnLocalLogin() = 0;

  /**
   * 被踢事件
   * @param message     被踢下线时附带的消息
   */
  virtual void OnKickout(const std::string& message) = 0;

  /**
   * 其他端设备在（离）线情况
   * @param type 事件类型（1：事件通知，包括上下线，2：状态通知，在线状态）
   * @param device_type 设备类型
   * （0:default,1:web,2:Android,3:iOS,4:Mac,5:Windows,6:iPad）
   * @param status      设备状态（1：上线或在线，2：下线或离线）
   * @param time        时间（上线或下线时间）
   */
  virtual void OnDeviceStatus(int32_t type,
                              int32_t device_type,
                              int32_t status,
                              int64_t time) = 0;

  /**
   * 下载资源cookie变更事件
   * @param cookie      新cookie
   */
  virtual void OnMainServerCookieRefresh(const std::string& cookie) = 0;
};

using DPSAuthListenerPtr = std::shared_ptr<DPSAuthListener>;
using DPSAuthListenerWeakPtr = std::weak_ptr<DPSAuthListener>;

}  // namespace dps
}  // namespace alibaba
