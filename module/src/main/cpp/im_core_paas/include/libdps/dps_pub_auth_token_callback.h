// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>
#include <string>

namespace alibaba {
namespace dps {

class DPSAuthTokenGotCallback;
enum class DPSAuthTokenExpiredReason;

/**
 * 请求获取Token回调
 */
class DPSPubAuthTokenCallback {
 public:
  virtual ~DPSPubAuthTokenCallback() {}

  virtual void OnCallback(
      const std::string& user_id,
      const std::shared_ptr<DPSAuthTokenGotCallback>& on_got,
      DPSAuthTokenExpiredReason reason) = 0;
};

using DPSPubAuthTokenCallbackPtr = std::shared_ptr<DPSPubAuthTokenCallback>;
using DPSPubAuthTokenCallbackWeakPtr = std::weak_ptr<DPSPubAuthTokenCallback>;

}  // namespace dps
}  // namespace alibaba
