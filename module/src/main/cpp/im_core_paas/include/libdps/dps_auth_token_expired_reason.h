// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class DPSAuthTokenExpiredReason : int {
  /**
   * 未知原因
   */
  UNKNOWN = 0,
  /**
   * 本地无缓存的token，首次登陆或无法解析本地token
   */
  NO_LOCAL_TOKEN = 1,
  /**
   * 本地token超期（30天）
   */
  LOCAL_TOKEN_EXPIRED = 2,
  /**
   * 服务端判定 uid 和 token 不匹配
   */
  UID_TOKEN_NOT_MATCH = 3,
  /**
   * refreshToken为空，无法刷新accessToken
   */
  EMPTY_REFRESH_TOKEN = 4,
  /**
   * 刷新token失败, 服务端返回刷新失败
   */
  REFRESH_TOKEN_FAILED = 5,
  /**
   * 刷新token失效，需要更新refreshToken
   */
  REFRESH_TOKEN_EXPIRED = 6,
};

}
}  // namespace alibaba

namespace std {

template <>
struct hash<::alibaba::dps::DPSAuthTokenExpiredReason> {
  size_t operator()(::alibaba::dps::DPSAuthTokenExpiredReason type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

}  // namespace std
