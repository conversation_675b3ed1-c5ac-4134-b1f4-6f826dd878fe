// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class DPSConnectionStatus : int {
  /**
   * 未连接
   */
  CS_UNCONNECTED = 0,
  /**
   * 正在连接
   */
  CS_CONNECTING = 1,
  /**
   * 已连接
   */
  CS_CONNECTED = 2,
  /**
   * 正在登录
   */
  CS_AUTHING = 3,
  /**
   * 登录成功
   */
  CS_AUTHED = 4,
};

}
}  // namespace alibaba

namespace std {

template <>
struct hash<::alibaba::dps::DPSConnectionStatus> {
  size_t operator()(::alibaba::dps::DPSConnectionStatus type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

}  // namespace std
