// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <string>
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 同步协议数据内容结构
 */
struct DPSSyncData final {
  /**
   * 数据类型
   */
  int32_t object_type = -1;
  /**
   * stirng封装二进制数据
   */
  std::string data;
  /**
   * 二进制数据
   */
  std::vector<uint8_t> binary_data;
  /**
   * 业务类型
   */
  int32_t biz_type = -1;
  /**
   * 数据id
   */
  std::string serve_id;
  /**
   * 是否是离线消息
   */
  bool is_offline = false;

  DPSSyncData(int32_t object_type_,
              std::string data_,
              std::vector<uint8_t> binary_data_,
              int32_t biz_type_,
              std::string serve_id_,
              bool is_offline_)
      : object_type(std::move(object_type_)),
        data(std::move(data_)),
        binary_data(std::move(binary_data_)),
        biz_type(std::move(biz_type_)),
        serve_id(std::move(serve_id_)),
        is_offline(std::move(is_offline_)) {}

  DPSSyncData() {}
};

}  // namespace dps
}  // namespace alibaba
