// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class DPSMediaHostType : int {
  /**
   * cdn
   */
  MEDIA_HOST_TYPE_CDN = 0,
  /**
   * auth
   */
  MEDIA_HOST_TYPE_AUTH = 1,
};

}
}  // namespace alibaba

namespace std {

template <>
struct hash<::alibaba::dps::DPSMediaHostType> {
  size_t operator()(::alibaba::dps::DPSMediaHostType type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

}  // namespace std
