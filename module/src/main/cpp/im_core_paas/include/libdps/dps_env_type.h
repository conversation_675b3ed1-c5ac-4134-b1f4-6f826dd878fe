// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class DPSEnvType : int {
  /**
   * 日常
   */
  ENV_TYPE_DAILY = 0,
  /**
   * 预发
   */
  ENV_TYPE_PRE_RELEASE = 1,
  /**
   * 线上
   */
  ENV_TYPE_ONLINE = 2,
};

}
}  // namespace alibaba

namespace std {

template <>
struct hash<::alibaba::dps::DPSEnvType> {
  size_t operator()(::alibaba::dps::DPSEnvType type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

}  // namespace std
