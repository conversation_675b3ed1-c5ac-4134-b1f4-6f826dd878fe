// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>
#include <string>

namespace alibaba {
namespace dps {

enum class DPSLogLevel;

/**
 * Log 监听
 */
class DPSLogHandler {
 public:
  virtual ~DPSLogHandler() {}

  virtual void OnLog(DPSLogLevel log_level, const std::string& log_content) = 0;
};

using DPSLogHandlerPtr = std::shared_ptr<DPSLogHandler>;
using DPSLogHandlerWeakPtr = std::weak_ptr<DPSLogHandler>;

}  // namespace dps
}  // namespace alibaba
