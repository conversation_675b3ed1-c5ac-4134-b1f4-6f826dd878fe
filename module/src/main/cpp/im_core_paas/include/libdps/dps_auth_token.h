// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * Auth token信息
 */
struct DPSAuthToken final {
  std::string access_token;
  std::string refresh_token;

  DPSAuthToken(std::string access_token_, std::string refresh_token_)
      : access_token(std::move(access_token_)),
        refresh_token(std::move(refresh_token_)) {}

  DPSAuthToken() {}
};

}  // namespace dps
}  // namespace alibaba
