// Copyright (c) 2016 The Alibaba DingTalk Authors. All rights reserved.

#ifndef DPS_pub_engine_c_api_h
#define DPS_pub_engine_c_api_h

#include <map>
#include <memory>
#include <string>

#include "dps_define.h"
#include "dps_pub_engine.h"
#if defined(__cplusplus)
extern "C" {
#endif

typedef void (*DPSLogHandlerFunc)(alibaba::dps::DPSLogLevel log_level,
                                  const char* log_content);

/**
 * 设置log处理函数,处理函数可能会在多个线程调用,处理函数不能长时间阻塞
 * @param min_log_level 需要输出的最低日志级别（日志级别大于等于这个值才会输出）
 * @param handler 日志处理函数
 */
DPS_DECL void SetDPSLogHandler(alibaba::dps::DPSLogLevel min_log_level,
                               DPSLogHandlerFunc handler);

/**
 * 创建DPSEngine,如果已经有实例存在,会返回已创建的DPSEngine
 * @param version
 * 接口版本,传入dps_version.h中的DPS_VERSION
 */
DPS_DECL DPS_NAMESPACE::DPSPubEngine* CreateDPSPubEngine(const char* version);

/**
 * 获取DPSEngine
 */
DPS_DECL DPS_NAMESPACE::DPSPubEngine* GetDPSPubEngine();

/**
 * 释放DPSEngine
 */
DPS_DECL void ReleaseDPSPubEngine();

/**
 * 注册DPS Module
 */
DPS_DECL bool RegisterDPSPubModule(alibaba::dps::DPSModuleInfo* info);

#if defined(__cplusplus)
}  // extern "C"
#endif

#endif /* DPS_pub_engine_hpp */
