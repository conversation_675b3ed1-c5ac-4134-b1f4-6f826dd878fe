// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>
#include <string>
#include <vector>

namespace alibaba {
namespace dps {

class DPSSyncPlusAck;
struct DPSSyncData;
struct DPSSyncDataExtend;

class DPSSyncPlusBizEventListener {
 public:
  virtual ~DPSSyncPlusBizEventListener() {}

  /**
   * Toolong2事件，数据积压，需要业务拉取补偿
   * @param entend 扩展信息
   * @param ack 业务回调ack对象，完成后需要调用ack->OnSuccess,
   * 失败调用ack->OnFailed
   */
  virtual void OnTooLong2(const DPSSyncDataExtend& entend,
                          const std::shared_ptr<DPSSyncPlusAck>& ack) = 0;

  /**
   * 获取监听标识符，用于记录日志
   * @return 标识符
   */
  virtual std::string OnGetTag() = 0;

  /**
   * 回调处理重试失败
   * @param data 失败的数据
   * @return 是否丢弃，继续处理下一个数据
   */
  virtual bool OnDispatchRetryFailed(const std::vector<DPSSyncData>& data) = 0;
};

using DPSSyncPlusBizEventListenerPtr =
    std::shared_ptr<DPSSyncPlusBizEventListener>;
using DPSSyncPlusBizEventListenerWeakPtr =
    std::weak_ptr<DPSSyncPlusBizEventListener>;

}  // namespace dps
}  // namespace alibaba
