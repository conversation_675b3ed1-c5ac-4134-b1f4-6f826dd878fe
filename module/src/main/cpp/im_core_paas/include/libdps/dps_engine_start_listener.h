// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

struct DPSError;

/**
 * Engine 启动监听
 */
class DPSEngineStartListener {
 public:
  virtual ~DPSEngineStartListener() {}

  /**
   * Enigne 启动成功
   */
  virtual void OnSuccess() = 0;

  /**
   * Engine 启动失败
   */
  virtual void OnFailure(const DPSError& error) = 0;
};

using DPSEngineStartListenerPtr = std::shared_ptr<DPSEngineStartListener>;
using DPSEngineStartListenerWeakPtr = std::weak_ptr<DPSEngineStartListener>;

}  // namespace dps
}  // namespace alibaba
