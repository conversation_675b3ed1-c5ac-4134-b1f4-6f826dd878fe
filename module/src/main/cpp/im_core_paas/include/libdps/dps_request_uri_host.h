// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <string>
#include <utility>
#include "dps_request_scene.h"
#include "dps_request_uri_type.h"

namespace alibaba {
namespace dps {

/**
 * 弱网backup Request请求Uri配置
 */
struct DPSRequestUriHost final {
  /**
   * 场景
   */
  DPSRequestScene scene = DPSRequestScene::SCENE_BACKUP_ADDRESS;
  /**
   * 类型
   */
  DPSRequestUriType type = DPSRequestUriType::REQUEST_URI_UDP;
  /**
   * 地址
   */
  std::string host;
  /**
   * domain域
   */
  std::string domain;

  DPSRequestUriHost(DPSRequestScene scene_,
                    DPSRequestUriType type_,
                    std::string host_,
                    std::string domain_)
      : scene(std::move(scene_)),
        type(std::move(type_)),
        host(std::move(host_)),
        domain(std::move(domain_)) {}

  DPSRequestUriHost() {}
};

}  // namespace dps
}  // namespace alibaba
