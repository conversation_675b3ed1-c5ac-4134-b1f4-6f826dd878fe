// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

struct AIMPubMessage;

/**
 * 发送本地消息监听
 */
class AIMPubMsgSendMsgToLocalListener {
public:
  virtual ~AIMPubMsgSendMsgToLocalListener() {}

  /**
   * 处理成功
   * @param msg 发送成功的消息
   */
  virtual void OnSuccess(const AIMPubMessage &msg) = 0;

  /**
   * 处理失败，返回失败原因
   * @param error 失败信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubMsgSendMsgToLocalListenerPtr =
    std::shared_ptr<AIMPubMsgSendMsgToLocalListener>;
using AIMPubMsgSendMsgToLocalListenerWeakPtr =
    std::weak_ptr<AIMPubMsgSendMsgToLocalListener>;

} // namespace dps
} // namespace alibaba
