// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class AIMMsgSourceType : int {
  /**
   * 未知
   */
  SOURCE_TYPE_UNKNOWN = -1,
  /**
   * 发送消息
   */
  SOURCE_TYPE_SEND = 0,
  /**
   * 拉取消息
   */
  SOURCE_TYPE_PULL_ONLINE = 1,
  /**
   * 接收在线消息
   */
  SOURCE_TYPE_RECV_ONLINE = 2,
  /**
   * 接收离线消息
   */
  SOURCE_TYPE_RECV_OFFLINE = 3,
  /**
   * 接收轻量化消息
   */
  SOURCE_TYPE_RECV_LITE_MSG = 4,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::dps::AIMMsgSourceType> {
  size_t operator()(::alibaba::dps::AIMMsgSourceType type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
