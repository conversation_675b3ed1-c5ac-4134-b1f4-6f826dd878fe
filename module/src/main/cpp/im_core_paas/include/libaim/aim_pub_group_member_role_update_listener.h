// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>
#include <vector>

namespace alibaba {
namespace dps {

struct AIMPubGroupMember;

/**
 * 更新群成员角色回调
 */
class AIMPubGroupMemberRoleUpdateListener {
public:
  virtual ~AIMPubGroupMemberRoleUpdateListener() {}

  /**
   * 返回成功
   * @param members 成功更新的成员列表
   */
  virtual void OnSuccess(const std::vector<AIMPubGroupMember> &members) = 0;

  /**
   * 返回错误
   * @param error 错误信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubGroupMemberRoleUpdateListenerPtr =
    std::shared_ptr<AIMPubGroupMemberRoleUpdateListener>;
using AIMPubGroupMemberRoleUpdateListenerWeakPtr =
    std::weak_ptr<AIMPubGroupMemberRoleUpdateListener>;

} // namespace dps
} // namespace alibaba
