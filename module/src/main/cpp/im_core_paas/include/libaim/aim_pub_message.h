// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_msg_biz_info.h"
#include "aim_msg_display_style.h"
#include "aim_msg_send_status.h"
#include "aim_pub_msg_content.h"
#include "aim_pub_msg_recall_feature.h"
#include <cstdint>
#include <map>
#include <string>
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 消息体
 */
struct AIMPubMessage final {

  std::string appCid;
  std::string mid;
  std::string localid;
  std::string sender;
  int64_t sender_tag = 0;
  int64_t created_at = -1;
  int32_t unread_count = -1;
  int32_t receiver_count = -1;
  std::vector<std::string> receivers;
  bool is_read = false;
  std::map<std::string, std::string> extension;
  std::map<std::string, std::string> local_extension;
  std::map<std::string, std::string> user_extension;
  AIMPubMsgContent content;
  AIMMsgSendStatus status = AIMMsgSendStatus::SEND_STATUS_UNKNOWN;
  bool is_delete = false;
  bool is_recall = false;
  bool is_disable_read = false;
  bool is_local = false;
  AIMMsgBizInfo biz_info;
  AIMMsgDisplayStyle display_style = AIMMsgDisplayStyle::DISPLAY_STYLE_USER;
  AIMPubMsgRecallFeature recall_feature;

  AIMPubMessage(std::string appCid_, std::string mid_, std::string localid_,
                std::string sender_, int64_t sender_tag_, int64_t created_at_,
                int32_t unread_count_, int32_t receiver_count_,
                std::vector<std::string> receivers_, bool is_read_,
                std::map<std::string, std::string> extension_,
                std::map<std::string, std::string> local_extension_,
                std::map<std::string, std::string> user_extension_,
                AIMPubMsgContent content_, AIMMsgSendStatus status_,
                bool is_delete_, bool is_recall_, bool is_disable_read_,
                bool is_local_, AIMMsgBizInfo biz_info_,
                AIMMsgDisplayStyle display_style_,
                AIMPubMsgRecallFeature recall_feature_)
      : appCid(std::move(appCid_)), mid(std::move(mid_)),
        localid(std::move(localid_)), sender(std::move(sender_)),
        sender_tag(std::move(sender_tag_)), created_at(std::move(created_at_)),
        unread_count(std::move(unread_count_)),
        receiver_count(std::move(receiver_count_)),
        receivers(std::move(receivers_)), is_read(std::move(is_read_)),
        extension(std::move(extension_)),
        local_extension(std::move(local_extension_)),
        user_extension(std::move(user_extension_)),
        content(std::move(content_)), status(std::move(status_)),
        is_delete(std::move(is_delete_)), is_recall(std::move(is_recall_)),
        is_disable_read(std::move(is_disable_read_)),
        is_local(std::move(is_local_)), biz_info(std::move(biz_info_)),
        display_style(std::move(display_style_)),
        recall_feature(std::move(recall_feature_)) {}

  AIMPubMessage() {}
};

} // namespace dps
} // namespace alibaba
