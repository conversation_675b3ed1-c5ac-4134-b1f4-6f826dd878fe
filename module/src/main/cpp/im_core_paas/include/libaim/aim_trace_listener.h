// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

struct AIMFullLinkPointBase;
struct AIMFullLinkPointConv;
struct AIMFullLinkPointMsg;

class AIMTraceListener {
public:
  virtual ~AIMTraceListener() {}

  /**
   * 上报消息链路埋点
   */
  virtual void OnCommitMsgPoint(const AIMFullLinkPointMsg &point) = 0;

  /**
   * 上报会话链路埋点
   */
  virtual void OnCommitConvPoint(const AIMFullLinkPointConv &point) = 0;

  /**
   * 上报基础链路埋点
   */
  virtual void OnCommitBasePoint(const AIMFullLinkPointBase &point) = 0;
};

using AIMTraceListenerPtr = std::shared_ptr<AIMTraceListener>;
using AIMTraceListenerWeakPtr = std::weak_ptr<AIMTraceListener>;

} // namespace dps
} // namespace alibaba
