// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <map>
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 *基础埋点字段
 */
struct AIMFullLinkPointBase final {

  /**
   *固定5
   */
  int32_t type_id = 5;
  /**
   *上行event_id，下行rpc_id(不可为空)
   */
  std::string tc_id;
  /**
   *上行不填，下行rpcid(可为空)
   */
  std::string serve_id;
  /**
   *sdk版本
   */
  std::string sdk_version;
  std::map<std::string, std::string> ext;
  /**
   *链路类型
   */
  std::string trace_type;
  /**
   *子链路类型
   */
  std::string sub_trace_type;
  /**
   *阶段
   */
  std::string step_id;
  /**
   *上一阶段
   */
  std::string parent;
  /**
   *succss/failure
   */
  std::string code;
  /**
   *时间戳
   */
  int64_t time_stamp = 0;
  /**
   *上行/下行
   */
  int32_t direction = 0;
  std::string app_key;
  std::string app_version;
  std::string device_id;
  /**
   *索引字段，可填msgi/cid/uid等
   */
  std::string index;
  /**
   *userid
   */
  std::string uid;

  AIMFullLinkPointBase(int32_t type_id_, std::string tc_id_,
                       std::string serve_id_, std::string sdk_version_,
                       std::map<std::string, std::string> ext_,
                       std::string trace_type_, std::string sub_trace_type_,
                       std::string step_id_, std::string parent_,
                       std::string code_, int64_t time_stamp_,
                       int32_t direction_, std::string app_key_,
                       std::string app_version_, std::string device_id_,
                       std::string index_, std::string uid_)
      : type_id(std::move(type_id_)), tc_id(std::move(tc_id_)),
        serve_id(std::move(serve_id_)), sdk_version(std::move(sdk_version_)),
        ext(std::move(ext_)), trace_type(std::move(trace_type_)),
        sub_trace_type(std::move(sub_trace_type_)),
        step_id(std::move(step_id_)), parent(std::move(parent_)),
        code(std::move(code_)), time_stamp(std::move(time_stamp_)),
        direction(std::move(direction_)), app_key(std::move(app_key_)),
        app_version(std::move(app_version_)), device_id(std::move(device_id_)),
        index(std::move(index_)), uid(std::move(uid_)) {}

  AIMFullLinkPointBase() {}
};

} // namespace dps
} // namespace alibaba
