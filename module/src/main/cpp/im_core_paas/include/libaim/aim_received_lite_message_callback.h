// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

class AIMReceivedLiteMessageCallback {
public:
  virtual ~AIMReceivedLiteMessageCallback() {}

  virtual void OnSuccess() = 0;

  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMReceivedLiteMessageCallbackPtr =
    std::shared_ptr<AIMReceivedLiteMessageCallback>;
using AIMReceivedLiteMessageCallbackWeakPtr =
    std::weak_ptr<AIMReceivedLiteMessageCallback>;

} // namespace dps
} // namespace alibaba
