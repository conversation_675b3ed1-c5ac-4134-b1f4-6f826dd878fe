// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_msg_image_file_type.h"
#include <cstdint>
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 位置消息内容
 */
struct AIMMsgGeoContent final {

  /**
   * 位置图片本地路径（发送方）
   */
  std::string pic_local_path;
  /**
   * 上传本地路径（发送方）
   */
  std::string pic_upload_path;
  /**
   * 上传媒体类型（发送方）
   */
  std::string mime_type;
  /**
   * 位置图片
   */
  std::string pic_url;
  /**
   * 位置图片 media id
   */
  std::string pic_media_id;
  /**
   * 位置图片类型 AIMMsgImageFileType
   */
  AIMMsgImageFileType pic_file_type =
      AIMMsgImageFileType::IMAGE_FILE_TYPE_UNKNOWN;
  /**
   * 宽度
   */
  int32_t pic_width = -1;
  /**
   * 高度
   */
  int32_t pic_height = -1;
  /**
   * 维度
   */
  double latitude = 0;
  /**
   * 经度
   */
  double longitude = 0;
  /**
   * 地理位置名称
   */
  std::string location_name;

  AIMMsgGeoContent(std::string pic_local_path_, std::string pic_upload_path_,
                   std::string mime_type_, std::string pic_url_,
                   std::string pic_media_id_,
                   AIMMsgImageFileType pic_file_type_, int32_t pic_width_,
                   int32_t pic_height_, double latitude_, double longitude_,
                   std::string location_name_)
      : pic_local_path(std::move(pic_local_path_)),
        pic_upload_path(std::move(pic_upload_path_)),
        mime_type(std::move(mime_type_)), pic_url(std::move(pic_url_)),
        pic_media_id(std::move(pic_media_id_)),
        pic_file_type(std::move(pic_file_type_)),
        pic_width(std::move(pic_width_)), pic_height(std::move(pic_height_)),
        latitude(std::move(latitude_)), longitude(std::move(longitude_)),
        location_name(std::move(location_name_)) {}

  AIMMsgGeoContent() {}
};

} // namespace dps
} // namespace alibaba
