// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

struct AIMPubGroupMember;

/**
 * 更新群成员昵称
 */
class AIMPubGroupMemberNickUpdateListener {
public:
  virtual ~AIMPubGroupMemberNickUpdateListener() {}

  /**
   * 返回成功
   * @param members 成功更新的成员列表
   */
  virtual void OnSuccess(const AIMPubGroupMember &members) = 0;

  /**
   * 返回错误
   * @param error 错误信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubGroupMemberNickUpdateListenerPtr =
    std::shared_ptr<AIMPubGroupMemberNickUpdateListener>;
using AIMPubGroupMemberNickUpdateListenerWeakPtr =
    std::weak_ptr<AIMPubGroupMemberNickUpdateListener>;

} // namespace dps
} // namespace alibaba
