// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 图片尺寸
 */
struct AIMImageDimensions final {

  int32_t width = 0;
  int32_t height = 0;

  AIMImageDimensions(int32_t width_, int32_t height_)
      : width(std::move(width_)), height(std::move(height_)) {}

  AIMImageDimensions() {}
};

} // namespace dps
} // namespace alibaba
