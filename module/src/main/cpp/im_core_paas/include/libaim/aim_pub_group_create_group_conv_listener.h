// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

struct AIMPubConversation;

/**
 * 创建群聊监听
 */
class AIMPubGroupCreateGroupConvListener {
public:
  virtual ~AIMPubGroupCreateGroupConvListener() {}

  /**
   * 返回成功
   * @param conv 创建的conversation
   */
  virtual void OnSuccess(const AIMPubConversation &conv) = 0;

  /**
   * 返回统一失败结果
   * @param error 错误信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubGroupCreateGroupConvListenerPtr =
    std::shared_ptr<AIMPubGroupCreateGroupConvListener>;
using AIMPubGroupCreateGroupConvListenerWeakPtr =
    std::weak_ptr<AIMPubGroupCreateGroupConvListener>;

} // namespace dps
} // namespace alibaba
