// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <cstdint>
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

/**
 * 会话置顶监听
 */
class AIMConvSetTopListener {
public:
  virtual ~AIMConvSetTopListener() {}

  /**
   * 置顶成功
   */
  virtual void OnSuccess(int64_t top_rank) = 0;

  /**
   * 置顶失败
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMConvSetTopListenerPtr = std::shared_ptr<AIMConvSetTopListener>;
using AIMConvSetTopListenerWeakPtr = std::weak_ptr<AIMConvSetTopListener>;

} // namespace dps
} // namespace alibaba
