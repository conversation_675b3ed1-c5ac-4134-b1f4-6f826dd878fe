// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 更新群头像参数
 */
struct AIMPubGroupUpdateIcon final {

  /**
   * 群appCid
   */
  std::string appCid;
  /**
   * 群头像
   */
  std::string icon;

  AIMPubGroupUpdateIcon(std::string appCid_, std::string icon_)
      : appCid(std::move(appCid_)), icon(std::move(icon_)) {}

  AIMPubGroupUpdateIcon() {}
};

} // namespace dps
} // namespace alibaba
