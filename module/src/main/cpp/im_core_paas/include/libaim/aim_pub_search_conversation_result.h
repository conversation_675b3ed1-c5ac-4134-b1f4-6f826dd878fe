// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_pub_conversation.h"
#include "aim_pub_message.h"
#include "aim_search_highlight_range.h"
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 会话内容搜索返回结果
 */
struct AIMPubSearchConversationResult final {

  /**
   * 搜索匹配的会话信息
   */
  AIMPubConversation conversation;
  /**
   * 该会话匹配的第一条消息
   */
  AIMPubMessage first_message;
  /**
   * 消息内容高亮位置
   */
  std::vector<AIMSearchHighlightRange> ranges;

  AIMPubSearchConversationResult(AIMPubConversation conversation_,
                                 AIMPubMessage first_message_,
                                 std::vector<AIMSearchHighlightRange> ranges_)
      : conversation(std::move(conversation_)),
        first_message(std::move(first_message_)), ranges(std::move(ranges_)) {}

  AIMPubSearchConversationResult() {}
};

} // namespace dps
} // namespace alibaba
