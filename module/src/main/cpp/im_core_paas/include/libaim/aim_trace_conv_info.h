// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 *会话信息字段
 */
struct AIMTraceConvInfo final {

  std::string cid;
  std::string biz_type;
  std::string target_uid;

  AIMTraceConvInfo(std::string cid_, std::string biz_type_,
                   std::string target_uid_)
      : cid(std::move(cid_)), biz_type(std::move(biz_type_)),
        target_uid(std::move(target_uid_)) {}

  AIMTraceConvInfo() {}
};

} // namespace dps
} // namespace alibaba
