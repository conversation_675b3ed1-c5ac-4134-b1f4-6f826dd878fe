// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class AIMMsgContentType : int {
  CONTENT_TYPE_UNKNOW = -1,
  CONTENT_TYPE_TEXT = 1,
  CONTENT_TYPE_IMAGE = 2,
  CONTENT_TYPE_AUDIO = 3,
  CONTENT_TYPE_VIDEO = 4,
  CONTENT_TYPE_GEO = 5,
  CONTENT_TYPE_STRUCT = 6,
  CONTENT_TYPE_LINK = 7,
  CONTENT_TYPE_AT = 8,
  CONTENT_TYPE_FILE = 9,
  CONTENT_TYPE_REPLY = 10,
  CONTENT_TYPE_COMBINE_FORWARD = 11,
  CONTENT_TYPE_CUSTOM = 101,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::dps::AIMMsgContentType> {
  size_t operator()(::alibaba::dps::AIMMsgContentType type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
