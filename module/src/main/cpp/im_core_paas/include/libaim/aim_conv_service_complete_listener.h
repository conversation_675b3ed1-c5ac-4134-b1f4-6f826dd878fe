// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

/**
 * 导入会话列表监听
 */
class AIMConvServiceCompleteListener {
public:
  virtual ~AIMConvServiceCompleteListener() {}

  /**
   * 导入成功
   */
  virtual void OnSuccess() = 0;

  /**
   * 导入失败
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMConvServiceCompleteListenerPtr =
    std::shared_ptr<AIMConvServiceCompleteListener>;
using AIMConvServiceCompleteListenerWeakPtr =
    std::weak_ptr<AIMConvServiceCompleteListener>;

} // namespace dps
} // namespace alibaba
