// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

struct AIMPubMessage;

/**
 * 通过mid获取本地消息监听
 */
class AIMPubMsgGetLocalMsgListener {
public:
  virtual ~AIMPubMsgGetLocalMsgListener() {}

  /**
   * 处理成功，返回连续的消息对象
   * @param msg 发送成功的消息
   */
  virtual void OnSuccess(const AIMPubMessage &msg) = 0;

  /**
   * 发送错误
   * @param error 错误信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubMsgGetLocalMsgListenerPtr =
    std::shared_ptr<AIMPubMsgGetLocalMsgListener>;
using AIMPubMsgGetLocalMsgListenerWeakPtr =
    std::weak_ptr<AIMPubMsgGetLocalMsgListener>;

} // namespace dps
} // namespace alibaba
