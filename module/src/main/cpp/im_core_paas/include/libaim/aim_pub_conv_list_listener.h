// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>
#include <string>
#include <vector>

namespace alibaba {
namespace dps {

struct AIMPubConversation;

/**
 * 全量变更的监听器
 */
class AIMPubConvListListener {
public:
  virtual ~AIMPubConvListListener() {}

  /**
   * 新增会话
   * @param convs 新增的会话集合
   */
  virtual void
  OnAddedConversations(const std::vector<AIMPubConversation> &convs) = 0;

  /**
   * 删除会话
   * @param appCids 删除的会话appCid集合
   */
  virtual void
  OnRemovedConversations(const std::vector<std::string> &appCids) = 0;

  /**
   * 所有会话被更新替换
   * @param convs 更新后的会话集合
   */
  virtual void
  OnRefreshedConversations(const std::vector<AIMPubConversation> &convs) = 0;
};

using AIMPubConvListListenerPtr = std::shared_ptr<AIMPubConvListListener>;
using AIMPubConvListListenerWeakPtr = std::weak_ptr<AIMPubConvListListener>;

} // namespace dps
} // namespace alibaba
