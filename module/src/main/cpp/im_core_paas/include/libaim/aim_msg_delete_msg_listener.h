// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

/**
 * 删除消息监听
 */
class AIMMsgDeleteMsgListener {
public:
  virtual ~AIMMsgDeleteMsgListener() {}

  /**
   * 成功
   */
  virtual void OnSuccess() = 0;

  /**
   * 失败
   * @param error 错误信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMMsgDeleteMsgListenerPtr = std::shared_ptr<AIMMsgDeleteMsgListener>;
using AIMMsgDeleteMsgListenerWeakPtr = std::weak_ptr<AIMMsgDeleteMsgListener>;

} // namespace dps
} // namespace alibaba
