// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_file_auth_type.h"
#include <cstdint>
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 多媒体上传相关参数
 */
struct AIMUploadFileParam final {

  std::string path;
  std::string mime_type;
  std::string biz_type;
  AIMFileAuthType auth_type = AIMFileAuthType::STRICT_AUTH;
  /**
   * 过期时间，单位分钟（取决于服务端实现），默认值为0，
   */
  int32_t expired_time = 0;

  AIMUploadFileParam(std::string path_, std::string mime_type_,
                     std::string biz_type_, AIMFileAuthType auth_type_,
                     int32_t expired_time_)
      : path(std::move(path_)), mime_type(std::move(mime_type_)),
        biz_type(std::move(biz_type_)), auth_type(std::move(auth_type_)),
        expired_time(std::move(expired_time_)) {}

  AIMUploadFileParam() {}
};

} // namespace dps
} // namespace alibaba
