// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>
#include <vector>

namespace alibaba {
namespace dps {

struct AIMPubMessage;

/**
 * 批量获取本地消息监听
 */
class AIMPubMsgGetLocalMsgsListener {
public:
  virtual ~AIMPubMsgGetLocalMsgsListener() {}

  /**
   * 处理成功，返回连续的消息对象
   * @param msg 发送成功的消息
   */
  virtual void OnSuccess(const std::vector<AIMPubMessage> &msgs) = 0;

  /**
   * 发送错误
   * @param error 错误信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubMsgGetLocalMsgsListenerPtr =
    std::shared_ptr<AIMPubMsgGetLocalMsgsListener>;
using AIMPubMsgGetLocalMsgsListenerWeakPtr =
    std::weak_ptr<AIMPubMsgGetLocalMsgsListener>;

} // namespace dps
} // namespace alibaba
