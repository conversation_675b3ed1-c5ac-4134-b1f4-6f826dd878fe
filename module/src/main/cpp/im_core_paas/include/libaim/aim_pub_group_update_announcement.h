// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 更新群公告参数
 */
struct AIMPubGroupUpdateAnnouncement final {

  /**
   * 群appCid
   */
  std::string appCid;
  /**
   * 群公告，最大2048字节
   */
  std::string announcement;

  AIMPubGroupUpdateAnnouncement(std::string appCid_, std::string announcement_)
      : appCid(std::move(appCid_)), announcement(std::move(announcement_)) {}

  AIMPubGroupUpdateAnnouncement() {}
};

} // namespace dps
} // namespace alibaba
