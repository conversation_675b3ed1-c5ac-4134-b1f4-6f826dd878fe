// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

/**
 * 更新本地消息biz_info监听
 */
class AIMMsgUpdateLocalMsgsBizInfoListener {
public:
  virtual ~AIMMsgUpdateLocalMsgsBizInfoListener() {}

  /**
   * 更新biz_info成功
   */
  virtual void OnSuccess() = 0;

  /**
   * 更新失败
   * @param error 错误信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMMsgUpdateLocalMsgsBizInfoListenerPtr =
    std::shared_ptr<AIMMsgUpdateLocalMsgsBizInfoListener>;
using AIMMsgUpdateLocalMsgsBizInfoListenerWeakPtr =
    std::weak_ptr<AIMMsgUpdateLocalMsgsBizInfoListener>;

} // namespace dps
} // namespace alibaba
