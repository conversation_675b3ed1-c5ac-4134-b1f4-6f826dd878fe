// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <cstdint>
#include <functional>
#include <memory>
#include <string>
#include <vector>

namespace alibaba {
namespace dps {

class AIMGroupUpdateListener;
class AIMPubGroupAddMembersListener;
class AIMPubGroupChangeListener;
class AIMPubGroupCreateGroupConvListener;
class AIMPubGroupGetAnnouncementListener;
class AIMPubGroupGetMembersListener;
class AIMPubGroupGetSilencedInfoListener;
class AIMPubGroupListAllAdminsListener;
class <PERSON>MPubG<PERSON>ListAllMemberListener;
class <PERSON>MP<PERSON>G<PERSON><PERSON>istLocalMemberListener;
class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>berChangeListener;
class AIMPubGroup<PERSON>ember<PERSON>ickUpdateListener;
class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>emberRoleUpdateListener;
class <PERSON><PERSON><PERSON><PERSON><PERSON>istener;
struct AIMPubConversation;
struct AIMPubGroupAnnouncement;
struct AIMPubGroupCreateGroupConvParam;
struct AIMPubGroupJoin;
struct AIMPubGroupKick;
struct AIMPubGroupLeave;
struct AIMPubGroupMember;
struct AIMPubGroupMemberUpdateNick;
struct AIMPubGroupMemberUpdateRole;
struct AIMPubGroupRoleChangedNotify;
struct AIMPubGroupSetMemberPermission;
struct AIMPubGroupSetOwner;
struct AIMPubGroupSilencedInfo;
struct AIMPubGroupUpdateAdmins;
struct AIMPubGroupUpdateAnnouncement;
struct AIMPubGroupUpdateIcon;
struct AIMPubGroupUpdateSilenceAll;
struct AIMPubGroupUpdateSilencedBlackList;
struct AIMPubGroupUpdateSilencedWhiteList;
struct AIMPubGroupUpdateTitle;

class AIMPubGroupService {
public:
  virtual ~AIMPubGroupService() {}
  /**
   * 最大时间戳
   */
  static const int8_t AIM_MAX_GROUP_MEMBER_CURSOR = -1;

  /**
   * 创建群聊
   * @param param AIMGroupCreateGroupConvParam 结构
   * @param listener 监听器
   */
  virtual void CreateGroupConversation(
      const AIMPubGroupCreateGroupConvParam &param,
      const std::shared_ptr<AIMPubGroupCreateGroupConvListener> &listener) = 0;
  virtual void CreateGroupConversation(
      const AIMPubGroupCreateGroupConvParam &param,
      const std::function<void(const AIMPubConversation &conv)> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 更新群的默认title
   * @param param AIMGroupUpdateTitle 结构
   * @param listener 监听器
   */
  virtual void UpdateDefaultTitle(
      const AIMPubGroupUpdateTitle &param,
      const std::shared_ptr<AIMGroupUpdateListener> &listener) = 0;
  virtual void UpdateDefaultTitle(
      const AIMPubGroupUpdateTitle &param,
      const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 更新群的icon
   * @param param AIMGroupUpdateIcon 结构
   * @param listener 监听器
   */
  virtual void
  UpdateIcon(const AIMPubGroupUpdateIcon &param,
             const std::shared_ptr<AIMGroupUpdateListener> &listener) = 0;
  virtual void
  UpdateIcon(const AIMPubGroupUpdateIcon &param,
             const std::function<void()> &OnSuccess,
             const std::function<void(const ::alibaba::dps::DPSError &error)>
                 &OnFailure) = 0;

  /**
   * 解散群
   * @param appCid 会话appCid
   * @param listener 监听器
   */
  virtual void
  Dismiss(const std::string &appCid,
          const std::shared_ptr<AIMGroupUpdateListener> &listener) = 0;
  virtual void
  Dismiss(const std::string &appCid, const std::function<void()> &OnSuccess,
          const std::function<void(const ::alibaba::dps::DPSError &error)>
              &OnFailure) = 0;

  /**
   * 退出群
   * @param param AIMGroupLeave 结构
   * @param listener 监听器
   */
  virtual void
  Leave(const AIMPubGroupLeave &param,
        const std::shared_ptr<AIMGroupUpdateListener> &listener) = 0;
  virtual void
  Leave(const AIMPubGroupLeave &param, const std::function<void()> &OnSuccess,
        const std::function<void(const ::alibaba::dps::DPSError &error)>
            &OnFailure) = 0;

  /**
   * 拉取本地群成员
   * @param appCid 会话appCid
   * @param cursor 成员游标(create_at)
   *   1.若存在同一时间多个群成员入群(create_at一致,批量入群)的场景,建议使用全量接口
   *   2.本接口没有返回new_cursor,所以遍历时如果用上次的某个crate_at作为cursor,需要调用方对返回的记录去重
   *   3.参数(cursor=-1,count=LONG_MAX)的时候查询本地所有记录(全量),跟listAllMembers的区别是不发rpc消息
   * @parma count 偏移,正数则正向查询,负数则反向查询
   * @param listener 监听器
   */
  virtual void ListLocalMembers(
      const std::string &appCid, int64_t cursor, int64_t count,
      const std::shared_ptr<AIMPubGroupListLocalMemberListener> &listener) = 0;
  virtual void ListLocalMembers(
      const std::string &appCid, int64_t cursor, int64_t count,
      const std::function<void(const std::vector<AIMPubGroupMember> &members)>
          &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 拉取群成员
   * @param appCid 会话appCid
   * @param cur_version 成员版本号
   * @param listener 监听器
   */
  virtual void ListAllMembers(
      const std::string &appCid,
      const std::shared_ptr<AIMPubGroupListAllMemberListener> &listener) = 0;
  virtual void ListAllMembers(
      const std::string &appCid,
      const std::function<void(const std::vector<AIMPubGroupMember> &members)>
          &OnLocal,
      const std::function<void(const std::vector<AIMPubGroupMember> &members)>
          &OnRefresh,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 获取群成员
   * @param appCid 会话appCid
   * @param uids 群成员uid列表
   * @param listener 监听器
   */
  virtual void GetMembers(
      const std::string &appCid, const std::vector<std::string> &uids,
      const std::shared_ptr<AIMPubGroupGetMembersListener> &listener) = 0;
  virtual void GetMembers(
      const std::string &appCid, const std::vector<std::string> &uids,
      const std::function<void(const std::vector<AIMPubGroupMember> &member)>
          &OnLocal,
      const std::function<void(const std::vector<AIMPubGroupMember> &member)>
          &OnRefresh,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 添加群成员
   * @param param AIMGroupJoin 结构
   * @param listener 监听器
   */
  virtual void AddMembers(
      const AIMPubGroupJoin &param,
      const std::shared_ptr<AIMPubGroupAddMembersListener> &listener) = 0;
  virtual void AddMembers(
      const AIMPubGroupJoin &param,
      const std::function<void(const std::vector<AIMPubGroupMember> &members)>
          &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 删除群成员
   * @param param AIMGroupKick 结构
   * @param listener 监听器
   */
  virtual void
  RemoveMembers(const AIMPubGroupKick &param,
                const std::shared_ptr<AIMGroupUpdateListener> &listener) = 0;
  virtual void
  RemoveMembers(const AIMPubGroupKick &param,
                const std::function<void()> &OnSuccess,
                const std::function<void(const ::alibaba::dps::DPSError &error)>
                    &OnFailure) = 0;

  /**
   * 更新群成员昵称
   * @param param AIMGroupNickChange 结构
   * @param listener 监听器
   */
  virtual void UpdateGroupMemberNick(
      const AIMPubGroupMemberUpdateNick &param,
      const std::shared_ptr<AIMPubGroupMemberNickUpdateListener> &listener) = 0;
  virtual void UpdateGroupMemberNick(
      const AIMPubGroupMemberUpdateNick &param,
      const std::function<void(const AIMPubGroupMember &members)> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 设置全员禁言
   * 调用权限：群主或群管理员
   * 禁言后仅群主和管理员可发言
   * @param param AIMGroupUpdateSilenceAll 结构
   * @param listener 监听器
   */
  virtual void
  SilenceAll(const AIMPubGroupUpdateSilenceAll &param,
             const std::shared_ptr<AIMGroupUpdateListener> &listener) = 0;
  virtual void
  SilenceAll(const AIMPubGroupUpdateSilenceAll &param,
             const std::function<void()> &OnSuccess,
             const std::function<void(const ::alibaba::dps::DPSError &error)>
                 &OnFailure) = 0;

  /**
   * 取消全员禁言
   * 调用权限：群主或群管理员
   * @param param AIMGroupUpdateSilenceAll 结构
   * @param listener 监听器
   */
  virtual void
  CancelSilenceAll(const AIMPubGroupUpdateSilenceAll &param,
                   const std::shared_ptr<AIMGroupUpdateListener> &listener) = 0;
  virtual void CancelSilenceAll(
      const AIMPubGroupUpdateSilenceAll &param,
      const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 增加白名单用户(在设置全员禁言后，可以设置白名单，白名单中的用户可以发言)
   * 调用权限：群主或群管理员,
   * @param param AIMGroupUpdateSilencedWhiteList 结构
   * @param listener 监听器
   */
  virtual void AddSilencedWhitelist(
      const AIMPubGroupUpdateSilencedWhiteList &param,
      const std::shared_ptr<AIMGroupUpdateListener> &listener) = 0;
  virtual void AddSilencedWhitelist(
      const AIMPubGroupUpdateSilencedWhiteList &param,
      const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 删除白名单用户
   * 调用权限：群主或群管理员
   * @param param AIMGroupUpdateSilencedWhiteList 结构
   * @param listener 监听器
   */
  virtual void RemoveSilencedWhitelist(
      const AIMPubGroupUpdateSilencedWhiteList &param,
      const std::shared_ptr<AIMGroupUpdateListener> &listener) = 0;
  virtual void RemoveSilencedWhitelist(
      const AIMPubGroupUpdateSilencedWhiteList &param,
      const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 增加禁言用户
   * 调用权限：群主或群管理员
   * @param param AIMGroupUpdateSilencedBlackList 结构
   * @param listener 监听器
   */
  virtual void AddSilencedBlacklist(
      const AIMPubGroupUpdateSilencedBlackList &param,
      const std::shared_ptr<AIMGroupUpdateListener> &listener) = 0;
  virtual void AddSilencedBlacklist(
      const AIMPubGroupUpdateSilencedBlackList &param,
      const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 删除禁言用户
   * 调用权限：群主或群管理员
   * @param param AIMGroupUpdateSilencedBlackList 结构
   * @param listener 监听器
   */
  virtual void RemoveSilencedBlacklist(
      const AIMPubGroupUpdateSilencedBlackList &param,
      const std::shared_ptr<AIMGroupUpdateListener> &listener) = 0;
  virtual void RemoveSilencedBlacklist(
      const AIMPubGroupUpdateSilencedBlackList &param,
      const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 拉取禁言列表
   * 调用权限：群主或群管理员
   * @param appCid 群appCid
   * @param listener 监听器
   */
  virtual void GetSilencedInfo(
      const std::string &appCid,
      const std::shared_ptr<AIMPubGroupGetSilencedInfoListener> &listener) = 0;
  virtual void GetSilencedInfo(
      const std::string &appCid,
      const std::function<void(const AIMPubGroupSilencedInfo &silenced_info)>
          &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 转让群主
   * 调用权限：群主或群管理员
   * @param param AIMGroupSetOwner结构
   * @param listener 监听器
   */
  virtual void
  SetOwner(const AIMPubGroupSetOwner &param,
           const std::shared_ptr<AIMGroupUpdateListener> &listener) = 0;
  virtual void
  SetOwner(const AIMPubGroupSetOwner &param,
           const std::function<void()> &OnSuccess,
           const std::function<void(const ::alibaba::dps::DPSError &error)>
               &OnFailure) = 0;

  /**
   * 设置群权限
   * 调用权限：群主或群管理员
   * @param param AIMGroupSetMemberPermission
   * @param listener 监听器
   */
  virtual void SetMemberPermission(
      const AIMPubGroupSetMemberPermission &param,
      const std::shared_ptr<AIMGroupUpdateListener> &listener) = 0;
  virtual void SetMemberPermission(
      const AIMPubGroupSetMemberPermission &param,
      const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 增加群变更的监听器
   * @param listener 监听器
   */
  virtual void AddGroupChangeListener(
      const std::shared_ptr<AIMPubGroupChangeListener> &listener) = 0;

  /**
   * 删除群变更的监听器
   * @param listener 监听器
   */
  virtual void RemoveGroupChangeListener(
      const std::shared_ptr<AIMPubGroupChangeListener> &listener) = 0;

  /**
   * 删除所有会话的监听器
   */
  virtual void RemoveAllGroupChangeListener() = 0;

  /**
   * 注册会话成员增量变更的监听器
   * @param listener 监听器
   */
  virtual void AddGroupMemberChangeListener(
      const std::shared_ptr<AIMPubGroupMemberChangeListener> &listener) = 0;

  /**
   * 删除会话成员增量变更的监听器
   * @param listener 监听器
   */
  virtual void RemoveGroupMemberChangeListener(
      const std::shared_ptr<AIMPubGroupMemberChangeListener> &listener) = 0;

  /**
   * 删除所有会话成员的监听器
   */
  virtual void RemoveAllGroupMemberChangeListener() = 0;

  /**
   * 更新群成员角色
   * @param param AIMGroupMemberUpdateRole 结构
   * @param listener 监听器
   */
  virtual void UpdateGroupMemberRole(
      const AIMPubGroupMemberUpdateRole &param,
      const std::shared_ptr<AIMPubGroupMemberRoleUpdateListener> &listener) = 0;
  virtual void UpdateGroupMemberRole(
      const AIMPubGroupMemberUpdateRole &param,
      const std::function<void(const std::vector<AIMPubGroupMember> &members)>
          &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 更新群公告
   * @param param AIMGroupUpdateAnnouncement 结构
   */
  virtual void
  UpdateAnnouncement(const AIMPubGroupUpdateAnnouncement &param,
                     const std::shared_ptr<AIMSuccessListener> &listener) = 0;
  virtual void UpdateAnnouncement(
      const AIMPubGroupUpdateAnnouncement &param,
      const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 获取群公告
   * @param cid 需要获取群公告的cid
   * @param listener 获取群公告的回调
   */
  virtual void GetAnnouncement(
      const std::string &cid,
      const std::shared_ptr<AIMPubGroupGetAnnouncementListener> &listener) = 0;
  virtual void GetAnnouncement(
      const std::string &cid,
      const std::function<void(const AIMPubGroupAnnouncement &announcement)>
          &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 添加群管理员
   * 调用权限：群主或群管理员
   * @param param AIMGroupUpdateAdmins 结构
   * @param listener 监听器
   */
  virtual void
  AddAdmins(const AIMPubGroupUpdateAdmins &param,
            const std::shared_ptr<AIMGroupUpdateListener> &listener) = 0;
  virtual void
  AddAdmins(const AIMPubGroupUpdateAdmins &param,
            const std::function<void()> &OnSuccess,
            const std::function<void(const ::alibaba::dps::DPSError &error)>
                &OnFailure) = 0;

  /**
   * 删除群管理员
   * 调用权限：群主或群管理员
   * @param param AIMGroupUpdateAdmins 结构
   * @param listener 监听器
   */
  virtual void
  RemoveAdmins(const AIMPubGroupUpdateAdmins &param,
               const std::shared_ptr<AIMGroupUpdateListener> &listener) = 0;
  virtual void
  RemoveAdmins(const AIMPubGroupUpdateAdmins &param,
               const std::function<void()> &OnSuccess,
               const std::function<void(const ::alibaba::dps::DPSError &error)>
                   &OnFailure) = 0;

  /**
   * 查询全量群管理员
   * 调用权限：群主或群管理员
   * @param cid cid 会话cid
   * @param listener 监听器
   */
  virtual void ListAllAdmins(
      const std::string &appCid,
      const std::shared_ptr<AIMPubGroupListAllAdminsListener> &listener) = 0;
  virtual void ListAllAdmins(
      const std::string &appCid,
      const std::function<void(const std::vector<AIMPubGroupMember> &members)>
          &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;
};

using AIMPubGroupServicePtr = std::shared_ptr<AIMPubGroupService>;
using AIMPubGroupServiceWeakPtr = std::weak_ptr<AIMPubGroupService>;

} // namespace dps
} // namespace alibaba
