// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class AIMMsgImageCompressType : int {
  IMAGE_COMPRESS_TYPE_UNKNOWN = -1,
  IMAGE_COMPRESS_TYPE_COMPRESS = 0,
  IMAGE_COMPRESS_TYPE_ORIGINAL = 1,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::dps::AIMMsgImageCompressType> {
  size_t operator()(::alibaba::dps::AIMMsgImageCompressType type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
