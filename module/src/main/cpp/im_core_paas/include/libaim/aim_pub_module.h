// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_module_info.h"
#include <functional>
#include <memory>
#include <string>
#include <vector>

namespace alibaba {
namespace dps {

class AIMExtensionService;
class AIMMediaService;
class AIMPubConvService;
class AIMPubGroupService;
class AIMPubMsgHookPreSendMsgListener;
class AIMPubMsgService;
class AIMPubMsgServiceHook;
class AIMPubSearchService;
class AIMTraceService;
struct AIMPubMessage;

class AIMPubModule {
public:
  virtual ~AIMPubModule() {}

  static std::shared_ptr<AIMPubModule>
  GetModuleInstance(const std::string &uid);

  static std::shared_ptr<::alibaba::dps::DPSModuleInfo> GetModuleInfo();

  /**
   * 获取会话相关Service
   */
  virtual std::shared_ptr<AIMPubConvService> GetConvService() const = 0;

  /**
   * 获取消息相关Service
   */
  virtual std::shared_ptr<AIMPubMsgService> GetMsgService() const = 0;

  /**
   * 获取群聊相关Service
   */
  virtual std::shared_ptr<AIMPubGroupService> GetGroupService() const = 0;

  /**
   * 获取多媒体相关Service
   */
  virtual std::shared_ptr<AIMMediaService> GetMediaService() const = 0;

  /**
   * 获取消息搜索相关Service
   */
  virtual std::shared_ptr<AIMPubSearchService> GetSearchService() const = 0;

  /**
   * 获取trace相关Service
   */
  virtual std::shared_ptr<AIMTraceService> GetTraceService() const = 0;

  /**
   * 获取extension相关Service
   */
  virtual std::shared_ptr<AIMExtensionService> GetExtensionService() const = 0;

  /**
   * 设置 AIMMSGServiceHook, 用户消息预处理
   */
  virtual void SetMsgServiceHook(
      const std::shared_ptr<AIMPubMsgServiceHook> &media_service) = 0;
};

using AIMPubModulePtr = std::shared_ptr<AIMPubModule>;
using AIMPubModuleWeakPtr = std::weak_ptr<AIMPubModule>;

} // namespace dps
} // namespace alibaba
