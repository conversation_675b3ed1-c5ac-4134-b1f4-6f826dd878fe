// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

struct AIMPubConversation;

/**
 * 获取会话监听
 */
class AIMPubConvGetSingleConvListener {
public:
  virtual ~AIMPubConvGetSingleConvListener() {}

  /**
   * 获取成功
   */
  virtual void OnSuccess(const AIMPubConversation &conv) = 0;

  /**
   * 获取失败
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubConvGetSingleConvListenerPtr =
    std::shared_ptr<AIMPubConvGetSingleConvListener>;
using AIMPubConvGetSingleConvListenerWeakPtr =
    std::weak_ptr<AIMPubConvGetSingleConvListener>;

} // namespace dps
} // namespace alibaba
