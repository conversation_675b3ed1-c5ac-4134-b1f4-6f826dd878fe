// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <cstdint>
#include <functional>
#include <memory>
#include <string>

namespace alibaba {
namespace dps {

class AIMDownloadFileListener;
class AIMUploadFileListener;
enum class AIMImageSizeType;
enum class AIMMediaThumbnailFileType;
struct AIMDownloadFileParam;
struct AIMImageDimensions;
struct AIMMediaAuthInfo;
struct AIMUploadFileParam;

class AIMMediaService {
public:
  virtual ~AIMMediaService() {}

  /**
   * 上传文件
   * @param param 发送内容
   * @param listener 回调
   */
  virtual void
  UploadFile(const AIMUploadFileParam &param,
             const std::shared_ptr<AIMUploadFileListener> &listener) = 0;
  virtual void
  UploadFile(const AIMUploadFileParam &param,
             const std::function<void(const std::string &task_id)> &OnCreate,
             const std::function<void()> &OnStart,
             const std::function<void(int64_t current_size, int64_t total_size)>
                 &OnProgress,
             const std::function<void(const std::string &media_id)> &OnSuccess,
             const std::function<void(const ::alibaba::dps::DPSError &error)>
                 &OnFailure) = 0;

  /**
   * 下载文件
   * @param param 下载内容
   * @param listener 回调
   */
  virtual void
  DownloadFile(const AIMDownloadFileParam &param,
               const std::shared_ptr<AIMDownloadFileListener> &listener) = 0;
  virtual void DownloadFile(
      const AIMDownloadFileParam &param,
      const std::function<void(const std::string &task_id)> &OnCreate,
      const std::function<void()> &OnStart,
      const std::function<void(int64_t current_size, int64_t total_size)>
          &OnProgress,
      const std::function<void(const std::string &path)> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 取消文件任务
   * @param task_id 任务id
   */
  virtual void CancelFileTask(const std::string &task_id) = 0;

  /**
   * media 解析(image)
   * @param media_id 媒体id
   * @param size 图片大小
   */
  virtual std::string TransferMediaIdToImageUrl(const std::string &media_id,
                                                AIMImageSizeType size) = 0;

  /**
   * media 解析(image)
   * @param media_id 媒体id
   * @param size 图片大小
   * @param biz_type 业务类型
   */
  virtual std::string
  TransferMediaIdToImageUrlWithBizType(const std::string &media_id,
                                       AIMImageSizeType size,
                                       const std::string &biz_type) = 0;

  /**
   * media 解析(file)
   * @param media_id 媒体id
   */
  virtual std::string TransferMediaIdToUrl(const std::string &media_id) = 0;

  /**
   * media 解析(file)
   * @param media_id 媒体id
   * @param biz_type 业务类型
   */
  virtual std::string
  TransferMediaIdToUrlWithBizType(const std::string &media_id,
                                  const std::string &biz_type) = 0;

  /**
   * media 解析鉴权(image)
   * @param media_id 媒体id
   * @param size 图片大小
   * @param auth_info 鉴权信息
   */
  virtual std::string
  TransferMediaIdToAuthImageUrl(const std::string &media_id,
                                AIMImageSizeType size,
                                const AIMMediaAuthInfo &auth_info) = 0;

  /**
   * media 解析鉴权(image)
   * @param media_id 媒体id
   * @param size 图片大小
   * @param auth_info 鉴权信息
   * @param biz_type 业务类型
   */
  virtual std::string TransferMediaIdToAuthImageUrlBizType(
      const std::string &media_id, AIMImageSizeType size,
      const AIMMediaAuthInfo &auth_info, const std::string &biz_type) = 0;

  /**
   * media 解析鉴权(file)
   * @param media_id 媒体id
   * @param auth_info 鉴权信息
   */
  virtual std::string
  TransferMediaIdToAuthUrl(const std::string &media_id,
                           const AIMMediaAuthInfo &auth_info) = 0;

  /**
   * media 解析鉴权(file)
   * @param media_id 媒体id
   * @param auth_info 鉴权信息
   * @param biz_type 业务类型
   */
  virtual std::string
  TransferMediaIdToAuthUrlBizType(const std::string &media_id,
                                  const AIMMediaAuthInfo &auth_info,
                                  const std::string &biz_type) = 0;

  /**
   * 解析url常量部分
   * @param url 下载url
   */
  virtual std::string GetUrlConstantPart(const std::string &url) = 0;

  /**
   * 获取图片尺寸
   */
  virtual AIMImageDimensions
  TransferMediaIdToImageDimensions(const std::string &media_id) = 0;

  /**
   * media 根据文件格式解析缩略图(image),默认JPG格式,支持GIF
   * @param media_id 媒体id
   * @param file_type 文件格式
   * @param biz_type 业务类型
   */
  virtual std::string TransferMediaIdToThumbnailUrlWithFileType(
      const std::string &media_id, const std::string &biz_type,
      AIMMediaThumbnailFileType file_type) = 0;

  /**
   * media 根据文件格式解析鉴权缩略图(image),默认JPG格式,支持GIF
   * @param media_id 媒体id
   * @param auth_info 鉴权信息
   * @param file_type 文件格式
   * @param biz_type 业务类型
   */
  virtual std::string TransferMediaIdToAuthThumbnailUrlWithFileType(
      const std::string &media_id, const AIMMediaAuthInfo &auth_info,
      const std::string &biz_type, AIMMediaThumbnailFileType file_type) = 0;
};

using AIMMediaServicePtr = std::shared_ptr<AIMMediaService>;
using AIMMediaServiceWeakPtr = std::weak_ptr<AIMMediaService>;

} // namespace dps
} // namespace alibaba
