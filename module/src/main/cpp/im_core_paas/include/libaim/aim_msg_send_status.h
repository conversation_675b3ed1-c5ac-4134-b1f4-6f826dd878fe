// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class AIMMsgSendStatus : int {
  /**
   * 未知
   */
  SEND_STATUS_UNKNOWN = -1,
  /**
   * 消息发送成功
   */
  SEND_STATUS_SENT_SUCCESS = 0,
  /**
   * 消息发送中
   */
  SEND_STATUS_SENDING = 1,
  /**
   * 消息发送失败
   */
  SEND_STATUS_SEND_FAIL = 2,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::dps::AIMMsgSendStatus> {
  size_t operator()(::alibaba::dps::AIMMsgSendStatus type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
