// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class AIMGroupMemberRoleType : int {
  /**
   * 未知类型
   */
  GROUP_MEMBER_ROLE_TYPE_UNKNOW = 0,
  /**
   * 群主
   */
  GROUP_MEMBER_ROLE_TYPE_OWNER = 1,
  /**
   * 管理员
   */
  GROUP_MEMBER_ROLE_TYPE_ADMIN = 2,
  /**
   * 普通
   */
  GROUP_MEMBER_ROLE_TYPE_NORMAL = 3,
  /**
   * 自定义角色
   */
  GROUP_MEMBER_ROLE_TYPE_CUSTOM = 100,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::dps::AIMGroupMemberRoleType> {
  size_t operator()(::alibaba::dps::AIMGroupMemberRoleType type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
