// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <string>
#include <utility>

namespace alibaba {
namespace dps {

struct AIMPubGroupMemberUpdateNick final {

  /**
   *会话appCid
   */
  std::string appCid;
  /**
   *用户id
   */
  std::string uid;
  /**
   *用户昵称
   */
  std::string nick;

  AIMPubGroupMemberUpdateNick(std::string appCid_, std::string uid_,
                              std::string nick_)
      : appCid(std::move(appCid_)), uid(std::move(uid_)),
        nick(std::move(nick_)) {}

  AIMPubGroupMemberUpdateNick() {}
};

} // namespace dps
} // namespace alibaba
