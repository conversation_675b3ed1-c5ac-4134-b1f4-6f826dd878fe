// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class AIMMsgDisplayStyle : int {
  DISPLAY_STYLE_UNKNOWN = 0,
  DISPLAY_STYLE_USER = 1,
  DISPLAY_STYLE_SYSTEM = 2,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::dps::AIMMsgDisplayStyle> {
  size_t operator()(::alibaba::dps::AIMMsgDisplayStyle type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
