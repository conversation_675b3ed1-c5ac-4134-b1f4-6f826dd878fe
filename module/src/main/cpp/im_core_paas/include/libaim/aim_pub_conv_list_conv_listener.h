// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <cstdint>
#include <functional>
#include <memory>
#include <vector>

namespace alibaba {
namespace dps {

struct AIMPubConversation;

/**
 * 拉取会话列表监听
 */
class AIMPubConvListConvListener {
public:
  virtual ~AIMPubConvListConvListener() {}

  /**
   * 拉取成功
   */
  virtual void OnSuccess(const std::vector<AIMPubConversation> &result,
                         bool has_more, int64_t next_cursor) = 0;

  /**
   * 拉取失败
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubConvListConvListenerPtr =
    std::shared_ptr<AIMPubConvListConvListener>;
using AIMPubConvListConvListenerWeakPtr =
    std::weak_ptr<AIMPubConvListConvListener>;

} // namespace dps
} // namespace alibaba
