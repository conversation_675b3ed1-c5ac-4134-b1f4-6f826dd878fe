// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_pub_msg_inner_reply_content.h"
#include "aim_pub_msg_reference.h"
#include <utility>

namespace alibaba {
namespace dps {

struct AIMPubMsgReplyContent final {

  AIMPubMsgReference reference_msg;
  AIMPubMsgInnerReplyContent reply_content;

  AIMPubMsgReplyContent(AIMPubMsgReference reference_msg_,
                        AIMPubMsgInnerReplyContent reply_content_)
      : reference_msg(std::move(reference_msg_)),
        reply_content(std::move(reply_content_)) {}

  AIMPubMsgReplyContent() {}
};

} // namespace dps
} // namespace alibaba
