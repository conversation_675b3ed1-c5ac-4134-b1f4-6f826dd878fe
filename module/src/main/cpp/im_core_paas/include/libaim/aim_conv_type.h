// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class AIMConvType : int {
  /**
   * 未知类型
   */
  CONV_TYPE_UNKNOW = -1,
  /**
   * 单聊
   */
  CONV_TYPE_SINGLE = 1,
  /**
   * 群聊
   */
  CONV_TYPE_GROUP = 2,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::dps::AIMConvType> {
  size_t operator()(::alibaba::dps::AIMConvType type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
