// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_msg_content_type.h"
#include <cstdint>
#include <string>
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * @brief 搜索消息文本参数
 */
struct AIMPubSearchChatContentParams final {

  /**
   * 搜索关键字
   */
  std::string keyword;
  /**
   * 返回结果起始位置，用于搜索结果分页，offset >= 0，默认为第一页
   */
  int32_t offset = 0;
  /**
   * 单次返回结果个数， 0 < max_num <= 1000, 默认为20个
   */
  int32_t max_num = 20;
  /**
   * 查询条件：按消息创建时间开始, (时间标准:time_since_epoch)
   */
  int64_t start_time = 0;
  /**
   * 查询条件：按消息创建时间结束, 默认为所有消息
   */
  int64_t end_time = 9223372036854775807;
  /**
   * 是否自动高亮，true 则在onSuccess 内返回需高亮的 Range 信息
   */
  bool is_auto_highlight = true;
  /**
   * 返回结果是否按消息创建时间从小到大排列，默认为升序
   */
  bool is_asc = true;
  /**
   *  查询条件：需要查询的消息类型列表，如文本，图片，多媒体，自定义等，为空则仅查询文本类型
   */
  std::vector<AIMMsgContentType> support_msg_types;
  /**
   * 查询条件：消息子类型
   */
  std::vector<int32_t> support_sub_types;
  /**
   * 查询条件：需要查询的biz_tag列表，为空则查所有 (biz_tag
   * 为调用方回写的biz_info)
   */
  std::vector<std::string> biz_tags;
  /**
   * 查询条件：需要查询的会话列表，为空则查所有
   */
  std::vector<std::string> appCids;
  /**
   * 查询条件：需要查询的发送者列表
   */
  std::vector<std::string> sender_ids;

  AIMPubSearchChatContentParams(
      std::string keyword_, int32_t offset_, int32_t max_num_,
      int64_t start_time_, int64_t end_time_, bool is_auto_highlight_,
      bool is_asc_, std::vector<AIMMsgContentType> support_msg_types_,
      std::vector<int32_t> support_sub_types_,
      std::vector<std::string> biz_tags_, std::vector<std::string> appCids_,
      std::vector<std::string> sender_ids_)
      : keyword(std::move(keyword_)), offset(std::move(offset_)),
        max_num(std::move(max_num_)), start_time(std::move(start_time_)),
        end_time(std::move(end_time_)),
        is_auto_highlight(std::move(is_auto_highlight_)),
        is_asc(std::move(is_asc_)),
        support_msg_types(std::move(support_msg_types_)),
        support_sub_types(std::move(support_sub_types_)),
        biz_tags(std::move(biz_tags_)), appCids(std::move(appCids_)),
        sender_ids(std::move(sender_ids_)) {}

  AIMPubSearchChatContentParams() {}
};

} // namespace dps
} // namespace alibaba
