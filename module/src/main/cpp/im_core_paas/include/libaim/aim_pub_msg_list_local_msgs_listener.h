// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>
#include <vector>

namespace alibaba {
namespace dps {

struct AIMPubMessage;

/**
 * 获取上一页特定类型消息，按时间升序排列监听
 */
class AIMPubMsgListLocalMsgsListener {
public:
  virtual ~AIMPubMsgListLocalMsgsListener() {}

  /**
   * 处理成功，返回连续的消息对象
   * @param msgs 消息列表
   * @param has_more 是否有更多消息
   */
  virtual void OnSuccess(const std::vector<AIMPubMessage> &msgs,
                         bool has_more) = 0;

  /**
   * 处理失败，返回失败原因
   * @param error 错误信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubMsgListLocalMsgsListenerPtr =
    std::shared_ptr<AIMPubMsgListLocalMsgsListener>;
using AIMPubMsgListLocalMsgsListenerWeakPtr =
    std::weak_ptr<AIMPubMsgListLocalMsgsListener>;

} // namespace dps
} // namespace alibaba
