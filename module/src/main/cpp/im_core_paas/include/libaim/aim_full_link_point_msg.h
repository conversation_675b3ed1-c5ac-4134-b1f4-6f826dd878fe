// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_full_link_point_base.h"
#include "aim_trace_msg_info.h"
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 *消息链路埋点字段
 */
struct AIMFullLinkPointMsg final {

  AIMFullLinkPointBase base_point;
  std::vector<AIMTraceMsgInfo> msg_infos;

  AIMFullLinkPointMsg(AIMFullLinkPointBase base_point_,
                      std::vector<AIMTraceMsgInfo> msg_infos_)
      : base_point(std::move(base_point_)), msg_infos(std::move(msg_infos_)) {}

  AIMFullLinkPointMsg() {}
};

} // namespace dps
} // namespace alibaba
