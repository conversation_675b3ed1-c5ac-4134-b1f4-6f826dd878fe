// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class AIMGroupType : int {
  /**
   * 未知类型
   */
  GROUP_TYPE_UNKNOW = -1,
  /**
   * 普通群
   */
  GROUP_TYPE_NORMAL = 1,
  /**
   * 大群
   */
  GROUP_TYPE_GIG = 2,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::dps::AIMGroupType> {
  size_t operator()(::alibaba::dps::AIMGroupType type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
