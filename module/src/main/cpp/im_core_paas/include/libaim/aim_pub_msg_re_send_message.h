// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <map>
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 重新发送消息体
 */
struct AIMPubMsgReSendMessage final {

  std::string appCid;
  std::string localid;
  std::map<std::string, std::string> callback_ctx;

  AIMPubMsgReSendMessage(std::string appCid_, std::string localid_,
                         std::map<std::string, std::string> callback_ctx_)
      : appCid(std::move(appCid_)), localid(std::move(localid_)),
        callback_ctx(std::move(callback_ctx_)) {}

  AIMPubMsgReSendMessage() {}
};

} // namespace dps
} // namespace alibaba
