// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>
#include <vector>

namespace alibaba {
namespace dps {

struct AIMPubGroupMember;

/**
 * 拉取群成员
 */
class AIMPubGroupListAllAdminsListener {
public:
  virtual ~AIMPubGroupListAllAdminsListener() {}

  /**
   * 返回成功
   * @param members 成员列表
   */
  virtual void OnSuccess(const std::vector<AIMPubGroupMember> &members) = 0;

  /**
   * 失败结果
   * @param error 错误信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubGroupListAllAdminsListenerPtr =
    std::shared_ptr<AIMPubGroupListAllAdminsListener>;
using AIMPubGroupListAllAdminsListenerWeakPtr =
    std::weak_ptr<AIMPubGroupListAllAdminsListener>;

} // namespace dps
} // namespace alibaba
