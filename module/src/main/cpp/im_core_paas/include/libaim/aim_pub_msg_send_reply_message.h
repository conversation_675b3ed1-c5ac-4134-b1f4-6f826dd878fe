// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_pub_msg_content.h"
#include <map>
#include <string>
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 回复消息
 */
struct AIMPubMsgSendReplyMessage final {

  std::string appCid;
  std::string reference_mid;
  AIMPubMsgContent reply_content;
  std::vector<std::string> receivers;
  std::map<std::string, std::string> extension;
  std::map<std::string, std::string> local_extension;
  std::map<std::string, std::string> callback_ctx;

  AIMPubMsgSendReplyMessage(std::string appCid_, std::string reference_mid_,
                            AIMPubMsgContent reply_content_,
                            std::vector<std::string> receivers_,
                            std::map<std::string, std::string> extension_,
                            std::map<std::string, std::string> local_extension_,
                            std::map<std::string, std::string> callback_ctx_)
      : appCid(std::move(appCid_)), reference_mid(std::move(reference_mid_)),
        reply_content(std::move(reply_content_)),
        receivers(std::move(receivers_)), extension(std::move(extension_)),
        local_extension(std::move(local_extension_)),
        callback_ctx(std::move(callback_ctx_)) {}

  AIMPubMsgSendReplyMessage() {}
};

} // namespace dps
} // namespace alibaba
