// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

class AIMSetRedPointCallback {
public:
  virtual ~AIMSetRedPointCallback() {}

  virtual void OnSuccess() = 0;

  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMSetRedPointCallbackPtr = std::shared_ptr<AIMSetRedPointCallback>;
using AIMSetRedPointCallbackWeakPtr = std::weak_ptr<AIMSetRedPointCallback>;

} // namespace dps
} // namespace alibaba
