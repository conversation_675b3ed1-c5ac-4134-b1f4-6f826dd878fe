// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_pub_msg_reference_content.h"
#include <cstdint>
#include <map>
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

struct AIMPubMsgReference final {

  std::string sender;
  std::string appCid;
  std::string mid;
  int64_t created_at = 0;
  AIMPubMsgReferenceContent reference_content;
  std::map<std::string, std::string> extension;

  AIMPubMsgReference(std::string sender_, std::string appCid_, std::string mid_,
                     int64_t created_at_,
                     AIMPubMsgReferenceContent reference_content_,
                     std::map<std::string, std::string> extension_)
      : sender(std::move(sender_)), appCid(std::move(appCid_)),
        mid(std::move(mid_)), created_at(std::move(created_at_)),
        reference_content(std::move(reference_content_)),
        extension(std::move(extension_)) {}

  AIMPubMsgReference() {}
};

} // namespace dps
} // namespace alibaba
