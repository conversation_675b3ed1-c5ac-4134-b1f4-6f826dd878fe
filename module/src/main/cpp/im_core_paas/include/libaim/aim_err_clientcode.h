// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class AIMErrClientCode : int {
  /**
   * [DPS]AIMConvService实例不存在
   */
  AIM_CONV_IS_NULL = 300,
  /**
   * [DPS]AIMSearchService实例不存在
   */
  AIM_SEARCH_IS_NULL,
  /**
   * [DPS]AIMMsgService实例不存在
   */
  AIM_MSG_IS_NULL,
  /**
   * [DPS]AIMTraceService实例不存在
   */
  AIM_TRACE_IS_NULL,
  /**
   * [IM]消息实例不存在
   */
  IM_MSG_IS_NULL,
  /**
   * [IM]拉取消息失败
   */
  IM_LIST_MSG_FAILED,
  /**
   * [IM]消息已读状态写入失败
   */
  IM_SYNC_READ_FAILED,
  /**
   * [IM]写入消息扩展失败
   */
  IM_SYNC_EXT_FAILED,
  /**
   * [IM]合并消息失败
   */
  IM_MSG_MERGE_FAILED,
  /**
   * [IM]消息内容为空
   */
  IM_MSG_TEXT_EMPTY,
  /**
   * [IM]会话实例不存在
   */
  IM_CONV_IS_NULL,
  /**
   * [IM]找不到会话
   */
  IM_GET_CONV_FAILED,
  /**
   * [IM]撤回消息失败
   */
  IM_RECALL_MSG_FAILED,
  /**
   * [IM]外部消息Hook回调发生错误
   */
  IM_MSG_HOOK_FAILED,
  /**
   * [IM]数据unpack失败
   */
  IM_UNPCAK_FAILED,
  /**
   * [SEARCH]搜索数据库操作失败
   */
  SEARCH_DB_FAILED = 500,
  /**
   * [SEARCH]FTS引擎操作失败
   */
  SEARCH_FTS_FAILED,
  /**
   * [SEARCH]搜索参数错误
   */
  SEARCH_PARAMS_FAILED,
  /**
   * [SEARCH]FTS引擎未初始化
   */
  SEARCH_FTS_NOT_INITIALIZED,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::dps::AIMErrClientCode> {
  size_t operator()(::alibaba::dps::AIMErrClientCode type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
