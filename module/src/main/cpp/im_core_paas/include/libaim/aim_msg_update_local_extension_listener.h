// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

/**
 * 更新本地消息extension监听
 */
class AIMMsgUpdateLocalExtensionListener {
public:
  virtual ~AIMMsgUpdateLocalExtensionListener() {}

  /**
   * 更新成功
   */
  virtual void OnSuccess() = 0;

  /**
   * 更新失败
   * @param error 错误信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMMsgUpdateLocalExtensionListenerPtr =
    std::shared_ptr<AIMMsgUpdateLocalExtensionListener>;
using AIMMsgUpdateLocalExtensionListenerWeakPtr =
    std::weak_ptr<AIMMsgUpdateLocalExtensionListener>;

} // namespace dps
} // namespace alibaba
