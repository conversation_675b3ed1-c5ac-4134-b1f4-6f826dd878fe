// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_pub_group_silenced_black_list_member_info.h"
#include "aim_pub_group_silenced_white_list_member_info.h"
#include <string>
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 群禁言信息
 */
struct AIMPubGroupSilencedInfo final {

  /**
   * 群id
   */
  std::string appCid;
  /**
   * 黑名单
   */
  std::vector<AIMPubGroupSilencedBlackListMemberInfo> blacklistInfo;
  /**
   * 白名单
   */
  std::vector<AIMPubGroupSilencedWhiteListMemberInfo> whitelist;

  AIMPubGroupSilencedInfo(
      std::string appCid_,
      std::vector<AIMPubGroupSilencedBlackListMemberInfo> blacklistInfo_,
      std::vector<AIMPubGroupSilencedWhiteListMemberInfo> whitelist_)
      : appCid(std::move(appCid_)), blacklistInfo(std::move(blacklistInfo_)),
        whitelist(std::move(whitelist_)) {}

  AIMPubGroupSilencedInfo() {}
};

} // namespace dps
} // namespace alibaba
