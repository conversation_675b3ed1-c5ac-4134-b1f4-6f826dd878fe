// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

/**
 * 群信息更新监听
 */
class AIMGroupUpdateListener {
public:
  virtual ~AIMGroupUpdateListener() {}

  /**
   * 返回成功
   */
  virtual void OnSuccess() = 0;

  /**
   * 返回统一错误结果
   * @param error 错误信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMGroupUpdateListenerPtr = std::shared_ptr<AIMGroupUpdateListener>;
using AIMGroupUpdateListenerWeakPtr = std::weak_ptr<AIMGroupUpdateListener>;

} // namespace dps
} // namespace alibaba
