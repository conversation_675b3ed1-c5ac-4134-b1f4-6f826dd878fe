// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class AIMMsgImageFileType : int {
  IMAGE_FILE_TYPE_UNKNOWN = -1,
  IMAGE_FILE_TYPE_WEBP = 1,
  IMAGE_FILE_TYPE_PNG = 2,
  IMAGE_FILE_TYPE_JPG = 3,
  IMAGE_FILE_TYPE_GIF = 4,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::dps::AIMMsgImageFileType> {
  size_t operator()(::alibaba::dps::AIMMsgImageFileType type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
