// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

struct AIMPubMessage;

/**
 * 通过mid获取消息,本地不存在到服务端拉取监听
 */
class AIMPubMsgGetMsgListener {
public:
  virtual ~AIMPubMsgGetMsgListener() {}

  /**
   * 处理成功，返回连续的消息对象
   * @param msg 发送成功的消息
   */
  virtual void OnSuccess(const AIMPubMessage &msg) = 0;

  /**
   * 发送错误
   * @param error 错误信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubMsgGetMsgListenerPtr = std::shared_ptr<AIMPubMsgGetMsgListener>;
using AIMPubMsgGetMsgListenerWeakPtr = std::weak_ptr<AIMPubMsgGetMsgListener>;

} // namespace dps
} // namespace alibaba
