// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <map>
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 更新本地消息extension
 */
struct AIMPubMsgLocalExtensionUpdateInfo final {

  /**
   * 会话id
   */
  std::string appCid;
  /**
   * 消息localid
   */
  std::string localid;
  /**
   * 扩展内容
   */
  std::map<std::string, std::string> extension;

  AIMPubMsgLocalExtensionUpdateInfo(
      std::string appCid_, std::string localid_,
      std::map<std::string, std::string> extension_)
      : appCid(std::move(appCid_)), localid(std::move(localid_)),
        extension(std::move(extension_)) {}

  AIMPubMsgLocalExtensionUpdateInfo() {}
};

} // namespace dps
} // namespace alibaba
