// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_msg_xpn_push.h"
#include "aim_pub_message.h"
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 消息接收hook返回数据
 */
struct AIMPubMsgHookPreSendMsgResult final {

  AIMPubMessage msg;
  AIMMsgXpnPush xpn_push;

  AIMPubMsgHookPreSendMsgResult(AIMPubMessage msg_, AIMMsgXpnPush xpn_push_)
      : msg(std::move(msg_)), xpn_push(std::move(xpn_push_)) {}

  AIMPubMsgHookPreSendMsgResult() {}
};

} // namespace dps
} // namespace alibaba
