// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>
#include <vector>

namespace alibaba {
namespace dps {

class AIMPubMsgHookPreSendMsgListener;
struct AIMPubMessage;

/**
 *消息处理hook
 */
class AIMPubMsgServiceHook {
public:
  virtual ~AIMPubMsgServiceHook() {}

  /**
   * 预处理消息
   * @param msg 待发送的消息
   * @param listener media service 回调
   */
  virtual void PreSendMessage(
      const AIMPubMessage &msg,
      const std::shared_ptr<AIMPubMsgHookPreSendMsgListener> &listener) = 0;

  /**
   * 是否启用预发送消息
   */
  virtual bool IsEnablePreSendMessage() = 0;

  /**
   * 预处理接收消息
   * @param sur_msg 待发送的消息
   * @param dst_msg 处理完的消息
   * @return callback
   */
  virtual std::vector<AIMPubMessage>
  PreReceiveMessage(const std::vector<AIMPubMessage> &sur_msg) = 0;

  /**
   * 是否启用预处理接受消息
   */
  virtual bool IsEnablePreReceiveMessage() = 0;

  /**
   * 预处理查询消息
   * @param sur_msg 待发送的消息
   * @param dst_msg 处理完的消息
   * @return callback
   */
  virtual std::vector<AIMPubMessage>
  PreQueryMessage(const std::vector<AIMPubMessage> &sur_msg) = 0;

  /**
   * 是否启用预处理查询消息
   */
  virtual bool IsEnablePreQueryMessage() = 0;
};

using AIMPubMsgServiceHookPtr = std::shared_ptr<AIMPubMsgServiceHook>;
using AIMPubMsgServiceHookWeakPtr = std::weak_ptr<AIMPubMsgServiceHook>;

} // namespace dps
} // namespace alibaba
