// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>
#include <string>
#include <vector>

namespace alibaba {
namespace dps {

struct AIMPubConversation;
struct AIMPubGroupAnnouncement;
struct AIMPubGroupRoleChangedNotify;

/**
 * 群变更（增量）
 */
class AIMPubGroupChangeListener {
public:
  virtual ~AIMPubGroupChangeListener() {}

  /**
   * title变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnGroupTitleChanged(const std::vector<AIMPubConversation> &convs) = 0;

  /**
   * icon变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnGroupIconChanged(const std::vector<AIMPubConversation> &convs) = 0;

  /**
   * 群成员数变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnGroupMemberCountChanged(const std::vector<AIMPubConversation> &convs) = 0;

  /**
   * 群主变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnGroupOwnerChanged(const std::vector<AIMPubConversation> &convs) = 0;

  /**
   * 群禁言状态变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnGroupSilenceAllChanged(const std::vector<AIMPubConversation> &convs) = 0;

  /**
   * 群禁言黑白名单状态变更
   * @param convs 全量的会话结构
   */
  virtual void OnGroupSilencedStatusChanged(
      const std::vector<AIMPubConversation> &convs) = 0;

  /**
   * 群禁言截止时间变更
   * @param convs 全量的会话结构
   */
  virtual void OnGroupSilencedEndtimeChanged(
      const std::vector<AIMPubConversation> &convs) = 0;

  /**
   * 群成员角色变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnGroupMemberRoleChanged(const AIMPubGroupRoleChangedNotify &param) = 0;

  /**
   * 群成员权限变更
   * @param convs 全量的会话结构
   */
  virtual void OnGroupMemberPermissionsChanged(
      const std::vector<AIMPubConversation> &convs) = 0;

  /**
   * 群解散
   * @param appCids  解散的会话appCid集合
   */
  virtual void OnGroupDismissed(const std::vector<std::string> &appCids) = 0;

  /**
   * 被移除
   * @param appCids  解散的会话appCid集合
   */
  virtual void OnGroupKicked(const std::vector<std::string> &appCids) = 0;

  /**
   * 群公告变更
   * @param cid 群公告变更的cid
   * @param announcement 公告变更内容
   */
  virtual void
  OnGroupAnnouncementChanged(const std::string &appCid,
                             const AIMPubGroupAnnouncement &announcement) = 0;

  /**
   * 群已读功能开启变更
   * @param convs 全量的会话结构
   */
  virtual void OnGroupReadReceiptsEnabledChanged(
      const std::vector<AIMPubConversation> &convs) = 0;

  /**
   * 群管理员变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnGroupAdminChanged(const std::vector<AIMPubConversation> &convs) = 0;

  /**
   * 群成员数量限制变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnGroupMemberLimitChanged(const std::vector<AIMPubConversation> &convs) = 0;
};

using AIMPubGroupChangeListenerPtr = std::shared_ptr<AIMPubGroupChangeListener>;
using AIMPubGroupChangeListenerWeakPtr =
    std::weak_ptr<AIMPubGroupChangeListener>;

} // namespace dps
} // namespace alibaba
