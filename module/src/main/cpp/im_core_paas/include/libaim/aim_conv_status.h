// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class AIMConvStatus : int {
  /**
   * 未知类型
   */
  CONV_STATUS_UNKNOW = -1,
  /**
   * 隐藏
   */
  CONV_STATUS_HIDE = 0,
  /**
   * 正常状态，一般会话都在该状态
   */
  CONV_STATUS_NORMAL = 1,
  /**
   * 会话处于离线状态，未同步入云端
   */
  CONV_STATUS_OFFLINE = 2,
  /**
   * 群会话被踢
   */
  CONV_STATUS_KICKED = 3,
  /**
   * 群会话被解散
   */
  CONV_STATUS_DISMISSED = 4,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::dps::AIMConvStatus> {
  size_t operator()(::alibaba::dps::AIMConvStatus type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
