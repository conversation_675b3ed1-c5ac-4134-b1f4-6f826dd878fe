// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_group_member_role.h"
#include <cstdint>
#include <map>
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 群成员信息
 */
struct AIMPubGroupMember final {

  /**
   * 群id
   */
  std::string appCid;
  /**
   * 用户id
   */
  std::string uid;
  /**
   * 用户角色
   */
  AIMGroupMemberRole role;
  /**
   * 创建时间
   */
  int64_t created_at = 0;
  /**
   * 群昵称
   */
  std::string group_nick;
  /**
   *扩展信息
   */
  std::map<std::string, std::string> extension;

  AIMPubGroupMember(std::string appCid_, std::string uid_,
                    AIMGroupMemberRole role_, int64_t created_at_,
                    std::string group_nick_,
                    std::map<std::string, std::string> extension_)
      : appCid(std::move(appCid_)), uid(std::move(uid_)),
        role(std::move(role_)), created_at(std::move(created_at_)),
        group_nick(std::move(group_nick_)), extension(std::move(extension_)) {}

  AIMPubGroupMember() {}
};

} // namespace dps
} // namespace alibaba
