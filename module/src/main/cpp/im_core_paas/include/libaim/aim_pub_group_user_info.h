// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <map>
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 群相关操作时的群成员信息（目前用于给服务端发送系统消息）
 */
struct AIMPubGroupUserInfo final {

  /**
   * 用户id
   */
  std::string uid;
  /**
   * 昵称
   */
  std::string nick_name;
  /**
   * 扩展信息
   */
  std::map<std::string, std::string> extension;

  AIMPubGroupUserInfo(std::string uid_, std::string nick_name_,
                      std::map<std::string, std::string> extension_)
      : uid(std::move(uid_)), nick_name(std::move(nick_name_)),
        extension(std::move(extension_)) {}

  AIMPubGroupUserInfo() {}
};

} // namespace dps
} // namespace alibaba
