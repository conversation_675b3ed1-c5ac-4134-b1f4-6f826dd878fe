// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 结构化消息uid元素
 */
struct AIMPubMsgStructElementUid final {

  std::string uid;
  std::string default_nick;
  std::string prefix;

  AIMPubMsgStructElementUid(std::string uid_, std::string default_nick_,
                            std::string prefix_)
      : uid(std::move(uid_)), default_nick(std::move(default_nick_)),
        prefix(std::move(prefix_)) {}

  AIMPubMsgStructElementUid() {}
};

} // namespace dps
} // namespace alibaba
