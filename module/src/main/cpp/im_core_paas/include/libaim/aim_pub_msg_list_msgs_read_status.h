// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

struct AIMPubMsgReadStatus;

/**
 * 读取消息已读状态
 */
class AIMPubMsgListMsgsReadStatus {
public:
  virtual ~AIMPubMsgListMsgsReadStatus() {}

  /**
   * 获取成功
   * @param status 消息已读状态
   */
  virtual void OnSuccess(const AIMPubMsgReadStatus &status) = 0;

  /**
   * 返回错误
   * @param error 错误信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubMsgListMsgsReadStatusPtr =
    std::shared_ptr<AIMPubMsgListMsgsReadStatus>;
using AIMPubMsgListMsgsReadStatusWeakPtr =
    std::weak_ptr<AIMPubMsgListMsgsReadStatus>;

} // namespace dps
} // namespace alibaba
