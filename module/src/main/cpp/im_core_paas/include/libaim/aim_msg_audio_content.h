// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_msg_audio_type.h"
#include <cstdint>
#include <string>
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 音频消息内容
 */
struct AIMMsgAudioContent final {

  /**
   * 语音本地路径（发送方），本地原始语音数据路径
   */
  std::string local_path;
  /**
   * 上传本地路径（发送方）
   * 需要上传到服务端的语音文件路径，如加密，压缩的数据
   */
  std::string upload_path;
  /**
   * 上传媒体类型（发送方）
   */
  std::string mime_type;
  /**
   * 语音文件url，服务端上传成功后返回
   */
  std::string url;
  /**
   * media id，SDK 内部将语音文件上传成功后得到的 media_id
   * 发送文件时，如果 media_id 不为空，则使用该media_id发送
   */
  std::string media_id;
  /**
   * 语音数据,只是下行使用
   */
  std::vector<uint8_t> binary_data;
  /**
   * 语音数据类型  AIMMsgAudioType
   */
  AIMMsgAudioType audio_type = AIMMsgAudioType::AUDIO_TYPE_UNKNOWN;
  /**
   * 语音时长
   */
  int64_t duration = 0;

  AIMMsgAudioContent(std::string local_path_, std::string upload_path_,
                     std::string mime_type_, std::string url_,
                     std::string media_id_, std::vector<uint8_t> binary_data_,
                     AIMMsgAudioType audio_type_, int64_t duration_)
      : local_path(std::move(local_path_)),
        upload_path(std::move(upload_path_)), mime_type(std::move(mime_type_)),
        url(std::move(url_)), media_id(std::move(media_id_)),
        binary_data(std::move(binary_data_)),
        audio_type(std::move(audio_type_)), duration(std::move(duration_)) {}

  AIMMsgAudioContent() {}
};

} // namespace dps
} // namespace alibaba
