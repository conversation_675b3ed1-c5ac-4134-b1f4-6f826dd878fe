// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <cstdint>
#include <functional>
#include <memory>
#include <vector>

namespace alibaba {
namespace dps {

struct AIMPubSearchConversationResult;

/**
 * SearchConversationByContent监听
 */
class AIMPubSearchConvByContentListener {
public:
  virtual ~AIMPubSearchConvByContentListener() {}

  virtual void
  OnSuccess(const std::vector<AIMPubSearchConversationResult> &result,
            int32_t total_count) = 0;

  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubSearchConvByContentListenerPtr =
    std::shared_ptr<AIMPubSearchConvByContentListener>;
using AIMPubSearchConvByContentListenerWeakPtr =
    std::weak_ptr<AIMPubSearchConvByContentListener>;

} // namespace dps
} // namespace alibaba
