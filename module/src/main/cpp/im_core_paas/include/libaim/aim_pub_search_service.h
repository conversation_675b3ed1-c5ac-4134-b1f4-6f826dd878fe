// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <cstdint>
#include <functional>
#include <memory>
#include <vector>

namespace alibaba {
namespace dps {

class AIMPubSearchChatContentListener;
class AIMPubSearchConvByContentListener;
class AIMPubSearchGroupByNameListener;
struct AIMPubConversation;
struct AIMPubSearchChatContentParams;
struct AIMPubSearchChatResult;
struct AIMPubSearchConversationResult;
struct AIMSearchGroupByGroupNickParams;
struct AIMSearchGroupParams;

/**
 * AIMSearchService
 */
class AIMPubSearchService {
public:
  virtual ~AIMPubSearchService() {}

  /**
   * 搜索消息文本
   * @param params 搜索消息参数
   * @param listener 监听器
   * 如何分页:
   * 通过offset, max_num来做分页, total_cout 为返回结果
   * 1. 当offset为0时，total_count返回总的本地搜索匹配个数
   * 2. offset > 0 时, total_count为0，可以忽略
   * 示例，如何使用：
   * 1. 以20条每页进行分页，则
   * 2. offset = 0, max_num = 20, 返回 total_count为110，及第一页的20个消息
   * 3. 根据total_count计算总页码：110 / 20 + 1 = 6
   * 4. 获取第二页：offset = 20, max_num = 20, 返回下一个20条消息，total_count =
   * 0可忽略
   */
  virtual void SearchChatContent(
      const AIMPubSearchChatContentParams &params,
      const std::shared_ptr<AIMPubSearchChatContentListener> &listener) = 0;
  virtual void SearchChatContent(
      const AIMPubSearchChatContentParams &params,
      const std::function<
          void(const std::vector<AIMPubSearchChatResult> &result,
               int32_t total_count)> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 搜索消息文本，按照会话返回，每个会话返回一个消息
   * @param params 搜索参数，offset, maxnum以消息个数作为单位，非会话
   * @param listener 监听器
   * 如何分页:
   * 通过offset, max_num来做分页, total_cout 为返回结果
   * 1. 当offset为0时，total_count返回总的本地搜索匹配个数
   * 2. offset > 0 时，total_count为0，可以忽略
   * 示例，如何使用：
   * 1. 以20条每页进行分页，则
   * 2. offset = 0, max_num = 20, 返回 total_count为110，及第一页的20个消息
   * 3. 根据total_count计算总页码：110 / 20 + 1 = 6
   * 4. 获取第二页：offset = 20, max_num = 20, 返回下一个20条消息，total_count =
   * 0可忽略
   */
  virtual void SearchConversationByContent(
      const AIMPubSearchChatContentParams &params,
      const std::shared_ptr<AIMPubSearchConvByContentListener> &listener) = 0;
  virtual void SearchConversationByContent(
      const AIMPubSearchChatContentParams &params,
      const std::function<
          void(const std::vector<AIMPubSearchConversationResult> &result,
               int32_t total_count)> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 搜索会话名称
   * @param params 群名称搜索参数
   * @param listener 监听器
   * 如何分页:
   * 通过offset, max_num来做分页, total_cout 为返回结果
   * 1. 当offset为0时，total_count返回总的本地搜索匹配个数
   * 2. offset > 0 时，total_count为0，可以忽略
   * 示例，如何使用：
   * 1. 以20条每页进行分页，则
   * 2. offset = 0, max_num = 20, 返回 total_count为110，及第一页的20个消息
   * 3. 根据total_count计算总页码：110 / 20 + 1 = 6
   * 4. 获取第二页：offset = 20, max_num = 20, 返回下一个20条消息，total_count =
   * 0可忽略
   */
  virtual void SearchGroupByName(
      const AIMSearchGroupParams &params,
      const std::shared_ptr<AIMPubSearchGroupByNameListener> &listener) = 0;
  virtual void SearchGroupByName(
      const AIMSearchGroupParams &params,
      const std::function<void(const std::vector<AIMPubConversation> &result,
                               int32_t total_count)> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 根据群昵称搜索相关群
   * @param params 群昵称搜索参数
   * @param listener 监听器
   * 如何分页:
   * 通过offset, max_num来做分页, total_cout 为返回结果
   * 1. 当offset为0时，total_count返回总的本地搜索匹配个数
   * 2. offset > 0 时，total_count为0，可以忽略
   * 示例，如何使用：
   * 1. 以20条每页进行分页，则
   * 2. offset = 0, max_num = 20, 返回 total_count为110，及第一页的20个消息
   * 3. 根据total_count计算总页码：110 / 20 + 1 = 6
   * 4. 获取第二页：offset = 20, max_num = 20, 返回下一个20条消息，total_count =
   * 0可忽略
   */
  virtual void SearchGroupByGroupNick(
      const AIMSearchGroupByGroupNickParams &params,
      const std::shared_ptr<AIMPubSearchGroupByNameListener> &listener) = 0;
  virtual void SearchGroupByGroupNick(
      const AIMSearchGroupByGroupNickParams &params,
      const std::function<void(const std::vector<AIMPubConversation> &result,
                               int32_t total_count)> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;
};

using AIMPubSearchServicePtr = std::shared_ptr<AIMPubSearchService>;
using AIMPubSearchServiceWeakPtr = std::weak_ptr<AIMPubSearchService>;

} // namespace dps
} // namespace alibaba
