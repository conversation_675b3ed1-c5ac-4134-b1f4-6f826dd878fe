// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <map>
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 消息信息字段
 */
struct AIMTraceMsgInfo final {

  /**
   *会话id
   */
  std::string cid;
  /**
   *消息id
   */
  std::string msg_id;
  /**
   *sdk不填
   */
  std::string biz_chain_id;
  /**
   *消息类型
   */
  std::string msg_type;
  /**
   *发送者
   */
  std::string sender;
  /**
   *接收者
   */
  std::string receiver;
  std::map<std::string, std::string> extension;

  AIMTraceMsgInfo(std::string cid_, std::string msg_id_,
                  std::string biz_chain_id_, std::string msg_type_,
                  std::string sender_, std::string receiver_,
                  std::map<std::string, std::string> extension_)
      : cid(std::move(cid_)), msg_id(std::move(msg_id_)),
        biz_chain_id(std::move(biz_chain_id_)), msg_type(std::move(msg_type_)),
        sender(std::move(sender_)), receiver(std::move(receiver_)),
        extension(std::move(extension_)) {}

  AIMTraceMsgInfo() {}
};

} // namespace dps
} // namespace alibaba
