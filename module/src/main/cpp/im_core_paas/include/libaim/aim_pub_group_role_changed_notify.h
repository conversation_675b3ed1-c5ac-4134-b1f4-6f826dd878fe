// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_group_member_role.h"
#include <string>
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 群成员角色变更
 */
struct AIMPubGroupRoleChangedNotify final {

  /**
   * 群appCid
   */
  std::string appCid;
  /**
   * 角色
   */
  AIMGroupMemberRole role;
  /**
   * 变更成员的uid
   */
  std::vector<std::string> uids;

  AIMPubGroupRoleChangedNotify(std::string appCid_, AIMGroupMemberRole role_,
                               std::vector<std::string> uids_)
      : appCid(std::move(appCid_)), role(std::move(role_)),
        uids(std::move(uids_)) {}

  AIMPubGroupRoleChangedNotify() {}
};

} // namespace dps
} // namespace alibaba
