// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_group_member_role.h"
#include <string>
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

struct AIMPubGroupMemberUpdateRole final {

  /**
   *会话appCid
   */
  std::string appCid;
  /**
   *更新的角色
   */
  AIMGroupMemberRole role;
  /**
   *用户id
   */
  std::vector<std::string> uids;

  AIMPubGroupMemberUpdateRole(std::string appCid_, AIMGroupMemberRole role_,
                              std::vector<std::string> uids_)
      : appCid(std::move(appCid_)), role(std::move(role_)),
        uids(std::move(uids_)) {}

  AIMPubGroupMemberUpdateRole() {}
};

} // namespace dps
} // namespace alibaba
