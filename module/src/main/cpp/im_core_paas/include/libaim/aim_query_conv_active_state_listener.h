// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

class AIMQueryConvActiveStateListener {
public:
  virtual ~AIMQueryConvActiveStateListener() {}

  /**
   * 查询成功
   */
  virtual void OnSuccess(bool result) = 0;

  /**
   * 查询失败
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMQueryConvActiveStateListenerPtr =
    std::shared_ptr<AIMQueryConvActiveStateListener>;
using AIMQueryConvActiveStateListenerWeakPtr =
    std::weak_ptr<AIMQueryConvActiveStateListener>;

} // namespace dps
} // namespace alibaba
