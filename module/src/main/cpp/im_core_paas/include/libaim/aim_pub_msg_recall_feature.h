// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_msg_recall_type.h"
#include <map>
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 消息撤回信息
 */
struct AIMPubMsgRecallFeature final {

  /**
   * 操作类型
   */
  AIMMsgRecallType operator_type = AIMMsgRecallType::RECALL_TYPE_UNKNOWN;
  /**
   * 业务方自定义code
   */
  std::string code;
  /**
   * 撤回者UID
   */
  std::string operator_uid;
  /**
   * 撤回扩展字段
   */
  std::map<std::string, std::string> extension;

  AIMPubMsgRecallFeature(AIMMsgRecallType operator_type_, std::string code_,
                         std::string operator_uid_,
                         std::map<std::string, std::string> extension_)
      : operator_type(std::move(operator_type_)), code(std::move(code_)),
        operator_uid(std::move(operator_uid_)),
        extension(std::move(extension_)) {}

  AIMPubMsgRecallFeature() {}
};

} // namespace dps
} // namespace alibaba
