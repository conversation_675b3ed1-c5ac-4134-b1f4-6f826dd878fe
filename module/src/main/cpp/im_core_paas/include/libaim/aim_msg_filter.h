// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_msg_content_filter.h"
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 消息过滤条件
 */
struct AIMMsgFilter final {

  /**
   * 消息类型选项（可选）
   */
  std::vector<AIMMsgContentFilter> content;
  /**
   * 是否过滤撤回消息（可选）
   */
  bool filter_recall = false;
  /**
   * 是否按照时间升序排列（可选）
   */
  bool order_in_asc = false;

  AIMMsgFilter(std::vector<AIMMsgContentFilter> content_, bool filter_recall_,
               bool order_in_asc_)
      : content(std::move(content_)), filter_recall(std::move(filter_recall_)),
        order_in_asc(std::move(order_in_asc_)) {}

  AIMMsgFilter() {}
};

} // namespace dps
} // namespace alibaba
