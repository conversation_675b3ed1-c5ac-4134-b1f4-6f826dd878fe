// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <cstdint>
#include <functional>
#include <map>
#include <memory>
#include <string>
#include <vector>

namespace alibaba {
namespace dps {

class AIMMsgDeleteLocalMsgListener;
class AIMMsgDeleteMsgListener;
class AIMMsgForwardMsgListener;
class AIMMsgRecallMsgListener;
class AIMMsgUpdateLocalExtensionListener;
class AIMMsgUpdateLocalMsgsBizInfoListener;
class AIMPubMsgChangeListener;
class AIMPubMsgCombineForwardMsgListener;
class AIMPubMsgGetLocalMsgListener;
class AIMPubMsgGetLocalMsgsListener;
class AIMPubMsgGetMsgListener;
class AIMPubMsgListLocalMsgsListener;
class <PERSON>MPubMsgListMsgsReadStatus;
class <PERSON>MPubMsg<PERSON>istNextMsgsListener;
class AIMPubMsgListPreviousMsgsListener;
class AIMPubMsgListener;
class AIMPubMsgReSendMsgListener;
class AIMPubMsgSendMsgListener;
class AIMPubMsgSendMsgToLocalListener;
struct AIMMsgFilter;
struct AIMMsgSendMediaProgress;
struct AIMPubMessage;
struct AIMPubMsgBizUpdateInfo;
struct AIMPubMsgLocalExtensionUpdateInfo;
struct AIMPubMsgReSendMessage;
struct AIMPubMsgReadStatus;
struct AIMPubMsgSendForwardMessage;
struct AIMPubMsgSendMessage;
struct AIMPubMsgSendReplyMessage;
struct AIMPubNewMessage;

class AIMPubMsgService {
public:
  virtual ~AIMPubMsgService() {}
  /**
   * 最大时间戳
   */
  static const int64_t AIM_MAX_MSG_CURSOR = -1;

  /**
   * 获取上一页特定类型消息，按时间升序排列
   * 注意：该接口只返回本地连续数据
   * @param appCid 会话唯一id
   * @param cursor 消息事件游标,消息的创建时间，如果是最新的开始
   * AIM_MAX_MSG_CURSOR
   * @param count  返回的结果数量，最大100
   * @param listener 监听器
   */
  virtual void ListPreviousLocalMsgs(
      const std::string &appCid, int64_t cursor, int32_t count,
      const std::shared_ptr<AIMPubMsgListLocalMsgsListener> &listener) = 0;
  virtual void ListPreviousLocalMsgs(
      const std::string &appCid, int64_t cursor, int32_t count,
      const std::function<void(const std::vector<AIMPubMessage> &msgs,
                               bool has_more)> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 获取下一页特定类型消息，按时间升序排列
   * 注意：该接口只返回本地连续数据
   * @param appCid 会话唯一id
   * @param cursor 消息事件游标,消息的创建时间，如果是最新的开始
   * AIM_MAX_MSG_CURSOR
   * @param count  返回的结果数量，最大100
   * @param listener 监听器
   */
  virtual void ListNextLocalMsgs(
      const std::string &appCid, int64_t cursor, int32_t count,
      const std::shared_ptr<AIMPubMsgListLocalMsgsListener> &listener) = 0;
  virtual void ListNextLocalMsgs(
      const std::string &appCid, int64_t cursor, int32_t count,
      const std::function<void(const std::vector<AIMPubMessage> &msgs,
                               bool has_more)> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 获取下一页消息，按时间升序排列
   * @param appCid 会话唯一id
   * @param cursor 消息事件游标,消息的创建时间，如果是最新的开始
   * AIM_MAX_MSG_CURSOR
   * @param count 返回的结果数量，最大100
   * @param listener 监听器
   */
  virtual void ListNextMsgs(
      const std::string &appCid, int64_t cursor, int32_t count,
      const std::shared_ptr<AIMPubMsgListNextMsgsListener> &listener) = 0;
  virtual void
  ListNextMsgs(const std::string &appCid, int64_t cursor, int32_t count,
               const std::function<void(const std::vector<AIMPubMessage> &msgs,
                                        bool has_more)> &OnSuccess,
               const std::function<
                   void(const std::vector<std::vector<AIMPubMessage>> &msgs,
                        const ::alibaba::dps::DPSError &error)> &OnFailure) = 0;

  /**
   * 获取上一页消息，按时间升序排列
   * @param appCid 会话唯一id
   * @param cursor 消息事件游标,消息的创建时间，如果是最新的开始
   * AIM_MAX_MSG_CURSOR
   * @param count 返回的结果数量，最大100
   * @param listener 监听器
   */
  virtual void ListPreviousMsgs(
      const std::string &appCid, int64_t cursor, int32_t count,
      const std::shared_ptr<AIMPubMsgListPreviousMsgsListener> &listener) = 0;
  virtual void ListPreviousMsgs(
      const std::string &appCid, int64_t cursor, int32_t count,
      const std::function<void(const std::vector<AIMPubMessage> &msgs,
                               bool has_more)> &OnSuccess,
      const std::function<
          void(const std::vector<std::vector<AIMPubMessage>> &msgs,
               const ::alibaba::dps::DPSError &error)> &OnFailure) = 0;

  /**
   * 发送消息
   * @param msg 发送的消息对象
   * @param listener 监听器
   * @param user_data 用户数据（"trace_id"："_trace_id_example"）
   */
  virtual void
  SendMessage(const AIMPubMsgSendMessage &msg,
              const std::shared_ptr<AIMPubMsgSendMsgListener> &listener,
              const std::map<std::string, std::string> &user_data) = 0;
  virtual void
  SendMessage(const AIMPubMsgSendMessage &msg,
              const std::function<void(double progress)> &OnProgress,
              const std::function<void(const AIMPubMessage &msg)> &OnSuccess,
              const std::function<void(const ::alibaba::dps::DPSError &error)>
                  &OnFailure,
              const std::map<std::string, std::string> &user_data) = 0;

  /**
   * 重发消息
   * @param resend_msg 重发消息结构
   * @param listener 监听器
   * @param user_data 用户数据
   */
  virtual void
  ResendMessage(const AIMPubMsgReSendMessage &resend_msg,
                const std::shared_ptr<AIMPubMsgReSendMsgListener> &listener,
                const std::map<std::string, std::string> &user_data) = 0;
  virtual void
  ResendMessage(const AIMPubMsgReSendMessage &resend_msg,
                const std::function<void(double progress)> &OnProgress,
                const std::function<void(const AIMPubMessage &msg)> &OnSuccess,
                const std::function<void(const ::alibaba::dps::DPSError &error)>
                    &OnFailure,
                const std::map<std::string, std::string> &user_data) = 0;

  /**
   * 发送消息到本地不发送
   * @param msg 发送的消息对象
   * @param listener 监听器
   */
  virtual void SendMessageTolocal(
      const AIMPubMsgSendMessage &msg,
      const std::shared_ptr<AIMPubMsgSendMsgToLocalListener> &listener) = 0;
  virtual void SendMessageTolocal(
      const AIMPubMsgSendMessage &msg,
      const std::function<void(const AIMPubMessage &msg)> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 通过mid获取消息,本地不存在到服务端拉取
   * @param appCid 会话唯一id
   * @param mid 消息唯一id
   * @param listener 监听器
   */
  virtual void
  GetMessage(const std::string &appCid, const std::string &mid,
             const std::shared_ptr<AIMPubMsgGetMsgListener> &listener) = 0;
  virtual void
  GetMessage(const std::string &appCid, const std::string &mid,
             const std::function<void(const AIMPubMessage &msg)> &OnSuccess,
             const std::function<void(const ::alibaba::dps::DPSError &error)>
                 &OnFailure) = 0;

  /**
   * 通过mid获取本地消息
   * @param appCid 会话唯一id
   * @param local_id 消息本地唯一id
   * @param listener 监听器
   */
  virtual void GetLocalMessage(
      const std::string &appCid, const std::string &local_id,
      const std::shared_ptr<AIMPubMsgGetLocalMsgListener> &listener) = 0;
  virtual void GetLocalMessage(
      const std::string &appCid, const std::string &local_id,
      const std::function<void(const AIMPubMessage &msg)> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 根据条件获取本地消息
   * 注意：该接口只返回本地数据，且不保证连续
   * @param appCid 会话唯一id
   * @param cursor 消息事件游标,消息的创建时间，如果是最新的开始
   * AIM_MAX_MSG_CURSOR
   * @param count  返回的结果数量，最大100
   * @param forward true: cursor 时间更大的数据， false： cursor时间更小的数据
   * @param filter 过滤条件
   * @param listener 监听器
   */
  virtual void GetLocalMessages(
      const std::string &appCid, int64_t cursor, int32_t count, bool forward,
      const AIMMsgFilter &filter,
      const std::shared_ptr<AIMPubMsgGetLocalMsgsListener> &listener) = 0;
  virtual void GetLocalMessages(
      const std::string &appCid, int64_t cursor, int32_t count, bool forward,
      const AIMMsgFilter &filter,
      const std::function<void(const std::vector<AIMPubMessage> &msgs)>
          &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 删除消息，消息将从列表中移除，同步入云端
   * @param appCid 会话唯一id
   * @param mids 消息id列表
   * @param listener 监听器
   */
  virtual void
  DeleteMessage(const std::string &appCid, const std::vector<std::string> &mids,
                const std::shared_ptr<AIMMsgDeleteMsgListener> &listener) = 0;
  virtual void
  DeleteMessage(const std::string &appCid, const std::vector<std::string> &mids,
                const std::function<void()> &OnSuccess,
                const std::function<void(const ::alibaba::dps::DPSError &error)>
                    &OnFailure) = 0;

  /**
   * 删除本地消息，云端不同步
   * @param appCid 会话唯一id
   * @param local_ids 消息id
   * @param listener 监听器
   */
  virtual void DeleteLocalMessage(
      const std::string &appCid, const std::vector<std::string> &local_ids,
      const std::shared_ptr<AIMMsgDeleteLocalMsgListener> &listener) = 0;
  virtual void DeleteLocalMessage(
      const std::string &appCid, const std::vector<std::string> &local_ids,
      const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 撤回一条已发送的消息
   * @param appCid 会话唯一id
   * @param mid 消息id
   * @param listener 监听器
   */
  virtual void
  RecallMessage(const std::string &appCid, const std::string &mid,
                const std::shared_ptr<AIMMsgRecallMsgListener> &listener) = 0;
  virtual void
  RecallMessage(const std::string &appCid, const std::string &mid,
                const std::function<void()> &OnSuccess,
                const std::function<void(const ::alibaba::dps::DPSError &error)>
                    &OnFailure) = 0;

  /**
   * 全量更新本地消息 local extension，不同步到云端
   * @param update_infos 更新消息Extension信息列表
   * @param listener 监听器
   */
  virtual void UpdateLocalExtension(
      const std::vector<AIMPubMsgLocalExtensionUpdateInfo> &update_infos,
      const std::shared_ptr<AIMMsgUpdateLocalExtensionListener> &listener) = 0;
  virtual void UpdateLocalExtension(
      const std::vector<AIMPubMsgLocalExtensionUpdateInfo> &update_infos,
      const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 根据 key 局部更新本地消息 local extension, 不同步到云端
   * @param update_infos 更新消息Extension信息列表
   * @param listener 监听器
   */
  virtual void UpdateLocalExtensionByKey(
      const std::vector<AIMPubMsgLocalExtensionUpdateInfo> &update_infos,
      const std::shared_ptr<AIMMsgUpdateLocalExtensionListener> &listener) = 0;
  virtual void UpdateLocalExtensionByKey(
      const std::vector<AIMPubMsgLocalExtensionUpdateInfo> &update_infos,
      const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 更新本地消息 biz 相关字段
   * @param update_infos 更新消息Biz信息列表
   * @param listener 监听器
   */
  virtual void UpdateLocalMessagesBizInfo(
      const std::vector<AIMPubMsgBizUpdateInfo> &update_infos,
      const std::shared_ptr<AIMMsgUpdateLocalMsgsBizInfoListener>
          &listener) = 0;
  virtual void UpdateLocalMessagesBizInfo(
      const std::vector<AIMPubMsgBizUpdateInfo> &update_infos,
      const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 获取消息已读状态
   * @param appCid 会话唯一id
   * @param mid 消息唯一id
   * @param listener 监听器
   */
  virtual void ListMessagesReadStatus(
      const std::string &appCid, const std::string &mid,
      const std::shared_ptr<AIMPubMsgListMsgsReadStatus> &listener) = 0;
  virtual void ListMessagesReadStatus(
      const std::string &appCid, const std::string &mid,
      const std::function<void(const AIMPubMsgReadStatus &status)> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 读取接收到消息，消息进入已读状态，同步入云端
   * 人数。
   * @param appCid 会话唯一id
   * @param mids 批量消息mid
   */
  virtual void UpdateMessageToRead(const std::string &appCid,
                                   const std::vector<std::string> &mids) = 0;

  /**
   * 生成消息localId,本地唯一
   */
  virtual int64_t GenerateMsgLocalId() = 0;

  /**
   * 注册消息变动的监听器，如，消息新增、删除、更新
   * @param listener 消息监听器
   */
  virtual bool
  AddMsgListener(const std::shared_ptr<AIMPubMsgListener> &listener) = 0;

  /**
   * 删除消息的监听器
   * @param listener 消息监听器
   */
  virtual bool
  RemoveMsgListener(const std::shared_ptr<AIMPubMsgListener> &listener) = 0;

  /**
   * 删除所有消息的监听器
   */
  virtual void RemoveAllMsgListener() = 0;

  /**
   * 注册消息属性变更的监听器
   * @param listener 变更监听器
   */
  virtual bool AddMsgChangeListener(
      const std::shared_ptr<AIMPubMsgChangeListener> &listener) = 0;

  /**
   * 删除消息的属性监听器
   * @param listener 变更监听器
   */
  virtual bool RemoveMsgChangeListener(
      const std::shared_ptr<AIMPubMsgChangeListener> &listener) = 0;

  /**
   * 删除所有消息的属性监听器
   */
  virtual void RemoveAllMsgChangeListener() = 0;

  /**
   * 回复消息
   * @param msg 发送的回复消息对象
   * @param listener 监听器
   */
  virtual void
  ReplyMessage(const AIMPubMsgSendReplyMessage &msg,
               const std::shared_ptr<AIMPubMsgReSendMsgListener> &listener,
               const std::map<std::string, std::string> &user_data) = 0;
  virtual void
  ReplyMessage(const AIMPubMsgSendReplyMessage &msg,
               const std::function<void(double progress)> &OnProgress,
               const std::function<void(const AIMPubMessage &msg)> &OnSuccess,
               const std::function<void(const ::alibaba::dps::DPSError &error)>
                   &OnFailure,
               const std::map<std::string, std::string> &user_data) = 0;

  /**
   * 转发消息
   * @param msg 发送的消息对象
   * @param listener 监听器
   * @param user_data 用户数据（"trace_id"："_trace_id_example"）
   */
  virtual void
  ForwardMessage(const AIMPubMsgSendForwardMessage &mgs,
                 const std::shared_ptr<AIMMsgForwardMsgListener> &listener,
                 const std::map<std::string, std::string> &user_data) = 0;
  virtual void ForwardMessage(
      const AIMPubMsgSendForwardMessage &mgs,
      const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure,
      const std::map<std::string, std::string> &user_data) = 0;

  /**
   * 合并转发消息
   * @param msg 发送的消息对象
   * @param listener 监听器
   * @param user_data 用户数据（"trace_id"："_trace_id_example"）
   */
  virtual void CombineForwardMessage(
      const AIMPubMsgSendForwardMessage &mgs,
      const std::shared_ptr<AIMPubMsgCombineForwardMsgListener> &listener,
      const std::map<std::string, std::string> &user_data) = 0;
  virtual void CombineForwardMessage(
      const AIMPubMsgSendForwardMessage &mgs,
      const std::function<void(const AIMPubMessage &msg)> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure,
      const std::map<std::string, std::string> &user_data) = 0;

  /**
   * 设置拉消息时是否需要receivers字段，默认为true
   * @param need 是否需要
   */
  virtual void SetNeedReceivers(bool need) = 0;
};

using AIMPubMsgServicePtr = std::shared_ptr<AIMPubMsgService>;
using AIMPubMsgServiceWeakPtr = std::weak_ptr<AIMPubMsgService>;

} // namespace dps
} // namespace alibaba
