// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

/**
 * 异步操作是否成功的回调
 */
class AIMSuccessListener {
public:
  virtual ~AIMSuccessListener() {}

  /**
   * 异步操作成功
   */
  virtual void OnSuccess() = 0;

  /**
   * 异步操作失败
   * @param error 错误信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMSuccessListenerPtr = std::shared_ptr<AIMSuccessListener>;
using AIMSuccessListenerWeakPtr = std::weak_ptr<AIMSuccessListener>;

} // namespace dps
} // namespace alibaba
