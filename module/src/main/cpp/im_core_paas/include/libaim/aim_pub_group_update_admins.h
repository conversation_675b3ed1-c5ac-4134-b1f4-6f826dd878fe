// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_pub_group_user_info.h"
#include <string>
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 增加\删除群管理员
 */
struct AIMPubGroupUpdateAdmins final {

  /**
   * 操作者nick
   */
  std::string operator_nick;
  /**
   * 群cid
   */
  std::string appCid;
  /**
   * 操作成员
   */
  std::vector<AIMPubGroupUserInfo> members;

  AIMPubGroupUpdateAdmins(std::string operator_nick_, std::string appCid_,
                          std::vector<AIMPubGroupUserInfo> members_)
      : operator_nick(std::move(operator_nick_)), appCid(std::move(appCid_)),
        members(std::move(members_)) {}

  AIMPubGroupUpdateAdmins() {}
};

} // namespace dps
} // namespace alibaba
