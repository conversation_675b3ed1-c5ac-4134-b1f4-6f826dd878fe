// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>
#include <vector>

namespace alibaba {
namespace dps {

struct AIMPubGroupMember;

/**
 * 添加群成员
 */
class AIMPubGroupAddMembersListener {
public:
  virtual ~AIMPubGroupAddMembersListener() {}

  /**
   * 返回成功
   * @param members 成功添加的成员列表
   */
  virtual void OnSuccess(const std::vector<AIMPubGroupMember> &members) = 0;

  /**
   * 返回错误
   * @param error 错误信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubGroupAddMembersListenerPtr =
    std::shared_ptr<AIMPubGroupAddMembersListener>;
using AIMPubGroupAddMembersListenerWeakPtr =
    std::weak_ptr<AIMPubGroupAddMembersListener>;

} // namespace dps
} // namespace alibaba
