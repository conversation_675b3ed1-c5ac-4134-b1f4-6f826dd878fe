// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class AIMMsgOrientation : int {
  ORIENTATION_UNKNOWN = -1,
  ORIENTATION_NORMAL = 1,
  <PERSON>IENT<PERSON>ION_FLIP_HORIZONTAL = 2,
  <PERSON>IENTATION_ROTATE_180 = 3,
  ORIENTATION_FLIP_VERTICAL = 4,
  ORIENTATION_TRANSPOSE = 5,
  ORIENTATION_ROTATE_90 = 6,
  ORIENTATION_TRANSVERSE = 7,
  ORIENTATION_ROTATE_270 = 8,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::dps::AIMMsgOrientation> {
  size_t operator()(::alibaba::dps::AIMMsgOrientation type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
