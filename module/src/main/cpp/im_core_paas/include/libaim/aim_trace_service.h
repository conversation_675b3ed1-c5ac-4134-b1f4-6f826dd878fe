// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

class AIMTraceListener;
struct AIMFullLinkPointBase;
struct AIMFullLinkPointConv;
struct AIMFullLinkPointMsg;

class AIMTraceService {
public:
  virtual ~AIMTraceService() {}

  virtual void
  RegisterTraceListener(const std::shared_ptr<AIMTraceListener> &listener) = 0;
};

using AIMTraceServicePtr = std::shared_ptr<AIMTraceService>;
using AIMTraceServiceWeakPtr = std::weak_ptr<AIMTraceService>;

} // namespace dps
} // namespace alibaba
