// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class AIMGroupSilencedStatus : int {
  /**
   * 既不在白名单也不在黑名单
   */
  GROUP_SILENCE_STATUS_NORMAL = 0,
  /**
   * 白名单
   */
  GROUP_SILENCE_STATUS_IN_WHITELIST = 1,
  /**
   * 黑名单
   */
  GROUP_SILENCE_STATUS_IN_BLACKLIST = 2,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::dps::AIMGroupSilencedStatus> {
  size_t operator()(::alibaba::dps::AIMGroupSilencedStatus type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
