#ifndef aim_trace_service_h
#define aim_trace_service_h

#include "aim_define.h"
#include <functional>
#include <map>
#include <vector>

ARK_AIM_NAMESPACE_BEGIN

/* trace_type */
#define ARK_TRACE_MSG_CHAIN "1"
#define ARK_TRACE_CONV_CHAIN "2"
#define ARK_TRACE_LOGIN_CHAIN "3"

/* Msg sub_trace_type */
#define ARK_TRACE_SEND_MSG "1"
#define ARK_TRACE_RECV_MSG "2"
#define ARK_TRACE_RESEND_MSG "3"

/* Conv sub_trace_type */
#define ARK_TRACE_CREATE_CONV "1"
#define ARK_TRACE_NETWORK_STATUS "2"

/* code */
#define ARK_TRACE_STEP_SUCCESS "1000"
#define ARK_TRACE_STEP_FAILURE "2000"
#define ARK_TRACE_NET_ERR_INVALID_PARAM "2100"
#define ARK_TRACE_NET_ERR_INVALID_OPERATION "2101"
#define ARK_TRACE_NET_ERR_TIMEOUT "2102"
#define ARK_TRACE_NET_ERR_NETWORK_EXCEPTION "2103"
#define ARK_TRACE_NET_ERR_NO_AUTHTOKEN "2104"
#define ARK_TRACE_NET_ERR_NOT_CONNECTED "2105"
#define ARK_TRACE_NET_ERR_TOKEN_EMPTY "2106"
#define ARK_TRACE_NET_ERR_UNKNOWN "2107"
#define ARK_TRACE_NET_ERR_UNPACK_EXCEPTION "2108"
#define ARK_TRACE_NET_ERR_CODE_REASON_NOT_SET "2109"
#define ARK_TRACE_NET_ERR_LOGOUT "2110"
#define ARK_TRACE_NET_ERR_NETWORK_UNAVAILABLE "2111"
#define ARK_TRACE_NET_ERR_SEND_ERROR "2112"
#define ARK_TRACE_NET_ERR_SERVE "2199"

/* step */
#define ARK_TRACE_STEP_ORIGIN "-1"
/* send msg step */
#define ARK_TRACE_SEND_MSG_START "100"
#define ARK_TRACE_SEND_MSG_SAVE_LOCAL "200"
#define ARK_TRACE_SEND_MSG_PREPROCESSOR "300"

#define ARK_TRACE_SEND_MSG_BEFORE_HOOK "310"
#define ARK_TRACE_SEND_MSG_HOOK_CALLBACK "311"

#define ARK_TRACE_SEND_IMG_BEFORE_UPLOAD "320"
#define ARK_TRACE_SEND_IMG_UPLOAD "321"

#define ARK_TRACE_SEND_AUDIO_BEFORE_UPLOAD "330"
#define ARK_TRACE_SEND_AUDIO_UPLOAD "331"

#define ARK_TRACE_SEND_VIDEO_BEFORE_UPLOAD "340"
#define ARK_TRACE_SEND_VIDEO_UPLOAD "341"
#define ARK_TRACE_SEND_VIDEO_BEFORE_UPLOAD_PIC "342"
#define ARK_TRACE_SEND_VIDEO_UPLOAD_PIC "343"

#define ARK_TRACE_SEND_GEO_BEFORE_UPLOAD "350"
#define ARK_TRACE_SEND_GEO_UPLOAD "351"

#define ARK_TRACE_SEND_MSG_RPC "400"
/* recv msg step */
#define ARK_TRACE_RECV_MSG_PARSE_SYNC_DATA "100"
#define ARK_TRACE_RECV_MSG_BEFORE_SAVE_DB "200"
#define ARK_TRACE_RECV_MSG_EXIST_BULK_MERGE "210"
#define ARK_TRACE_RECV_MSG_ABSENT_GET_CONV "220"
#define ARK_TRACE_RECV_MSG_ABSENT_BULK_MERGE "221"
#define ARK_TRACE_RECV_MSG_AFTER_SAVE_DB "300"
/* resend msg step */
#define ARK_TRACE_RESEND_MSG_START "100"
#define ARK_TRACE_RESEND_MSG_PREPROCESSOR "300"
#define ARK_TRACE_RESEND_MSG_RPC "400"
/* create conv step */
#define ARK_TRACE_CREATE_CONV_SEND_RPC "100"
#define ARK_TRACE_CREATE_CONV_SAVE_LOCAL "200"

/* network status */
#define ARK_TRACE_NETWORK_CONN_UNCONNECTED "0000"
#define ARK_TRACE_NETWORK_CONN_CONNECTING "1000"
#define ARK_TRACE_NEWWORK_CONN_CONNECTED "2000"
#define ARK_TRACE_NETWORK_CONN_AUTHING "3000"
#define ARK_TRACE_NETWORK_CONN_AUTHED "4000"
#define ARK_TRACE_NETWORK_CONN_UNKNOWN "9999"

/* common step */
#define ARK_TRACE_NOTIFY_ADD_NEW_MSG "1000"

/* direction */
#define ARK_TRACE_DIRECTION_UP 0
#define ARK_TRACE_DIRECTION_DOWN 1
#define ARK_TRACE_DIRECTION_UNKNOW 2

/* FullLinkPointBase ext keys */
#define EXT_NETWORK_STATUS "NETWORK_STATUS"

ARK_AIM_NAMESPACE_END
#endif // aim_trace_service_h
