// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <string>
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 自定义消息内容
 */
struct AIMMsgCustomContent final {

  int32_t type = 0;
  std::vector<uint8_t> binary_data;
  std::string title;
  std::string summary;
  std::string degrade;

  AIMMsgCustomContent(int32_t type_, std::vector<uint8_t> binary_data_,
                      std::string title_, std::string summary_,
                      std::string degrade_)
      : type(std::move(type_)), binary_data(std::move(binary_data_)),
        title(std::move(title_)), summary(std::move(summary_)),
        degrade(std::move(degrade_)) {}

  AIMMsgCustomContent() {}
};

} // namespace dps
} // namespace alibaba
