// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_group_member_role_type.h"
#include <cstdint>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 群成员角色
 */
struct AIMGroupMemberRole final {

  /**
   * 角色类型
   */
  AIMGroupMemberRoleType role =
      AIMGroupMemberRoleType::GROUP_MEMBER_ROLE_TYPE_UNKNOW;
  /**
   * 当 role 为业务自定义类型时，使用该字段获取
   */
  int32_t custom_role = 0;

  AIMGroupMemberRole(AIMGroupMemberRoleType role_, int32_t custom_role_)
      : role(std::move(role_)), custom_role(std::move(custom_role_)) {}

  AIMGroupMemberRole() {}
};

} // namespace dps
} // namespace alibaba
