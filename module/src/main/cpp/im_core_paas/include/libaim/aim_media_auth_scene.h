// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class AIMMediaAuthScene : int {
  /**
   * 未知类型
   */
  MAC_UNKNOWN = 0,
  /**
   *会话头像
   */
  MAC_GROUP_AVATOR = 1,
  /**
   *会话消息
   */
  MAC_MSG = 2,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::dps::AIMMediaAuthScene> {
  size_t operator()(::alibaba::dps::AIMMediaAuthScene type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
