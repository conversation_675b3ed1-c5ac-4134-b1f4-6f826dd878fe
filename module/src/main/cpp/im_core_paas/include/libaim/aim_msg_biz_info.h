// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 本地消息biz属性
 */
struct AIMMsgBizInfo final {

  /**
   * tag
   */
  std::string biz_tag;
  /**
   * 内容
   */
  std::string biz_text;

  AIMMsgBizInfo(std::string biz_tag_, std::string biz_text_)
      : biz_tag(std::move(biz_tag_)), biz_text(std::move(biz_text_)) {}

  AIMMsgBizInfo() {}
};

} // namespace dps
} // namespace alibaba
