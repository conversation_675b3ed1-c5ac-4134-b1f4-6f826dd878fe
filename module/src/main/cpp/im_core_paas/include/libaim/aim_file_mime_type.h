// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <string>
#include <utility>

namespace alibaba {
namespace dps {

struct AIMFileMimeType final {
// !!!Please use AIMFileMimeType_MT_IMAGE_JPG instead
// static const std::string MT_IMAGE_JPG;
#define AIMFileMimeType_MT_IMAGE_JPG "image/jpg"

// !!!Please use AIMFileMimeType_MT_IMAGE_GIF instead
// static const std::string MT_IMAGE_GIF;
#define AIMFileMimeType_MT_IMAGE_GIF "image/gif"

// !!!Please use AIMFileMimeType_MT_IMAGE_PNG instead
// static const std::string MT_IMAGE_PNG;
#define AIMFileMimeType_MT_IMAGE_PNG "image/png"

// !!!Please use AIMFileMimeType_MT_IMAGE_BMP instead
// static const std::string MT_IMAGE_BMP;
#define AIMFileMimeType_MT_IMAGE_BMP "image/bmp"

// !!!Please use AIMFileMimeType_MT_IMAGE_JPEG instead
// static const std::string MT_IMAGE_JPEG;
#define AIMFileMimeType_MT_IMAGE_JPEG "image/jpeg"

// !!!Please use AIMFileMimeType_MT_IMAGE_WEBP instead
// static const std::string MT_IMAGE_WEBP;
#define AIMFileMimeType_MT_IMAGE_WEBP "image/webp"

// !!!Please use AIMFileMimeType_MT_AUDIO_AMR instead
// static const std::string MT_AUDIO_AMR;
#define AIMFileMimeType_MT_AUDIO_AMR "audio/amr"

// !!!Please use AIMFileMimeType_MT_AUDIO_OGG instead
// static const std::string MT_AUDIO_OGG;
#define AIMFileMimeType_MT_AUDIO_OGG "audio/ogg"

// !!!Please use AIMFileMimeType_MT_AUDIO_OPUS instead
// static const std::string MT_AUDIO_OPUS;
#define AIMFileMimeType_MT_AUDIO_OPUS "audio/opus"

// !!!Please use AIMFileMimeType_MT_AUDIO_MP3 instead
// static const std::string MT_AUDIO_MP3;
#define AIMFileMimeType_MT_AUDIO_MP3 "audio/mp3"

// !!!Please use AIMFileMimeType_MT_AUDIO_WAV instead
// static const std::string MT_AUDIO_WAV;
#define AIMFileMimeType_MT_AUDIO_WAV "audio/wav"

// !!!Please use AIMFileMimeType_MT_VIDEO_AVI instead
// static const std::string MT_VIDEO_AVI;
#define AIMFileMimeType_MT_VIDEO_AVI "video/avi"

// !!!Please use AIMFileMimeType_MT_VIDEO_RMVB instead
// static const std::string MT_VIDEO_RMVB;
#define AIMFileMimeType_MT_VIDEO_RMVB "video/rmvb"

// !!!Please use AIMFileMimeType_MT_VIDEO_RM instead
// static const std::string MT_VIDEO_RM;
#define AIMFileMimeType_MT_VIDEO_RM "video/rm"

// !!!Please use AIMFileMimeType_MT_VIDEO_MPG instead
// static const std::string MT_VIDEO_MPG;
#define AIMFileMimeType_MT_VIDEO_MPG "video/mpg"

// !!!Please use AIMFileMimeType_MT_VIDEO_WMV instead
// static const std::string MT_VIDEO_WMV;
#define AIMFileMimeType_MT_VIDEO_WMV "video/wmv"

// !!!Please use AIMFileMimeType_MT_VIDEO_MKV instead
// static const std::string MT_VIDEO_MKV;
#define AIMFileMimeType_MT_VIDEO_MKV "video/mkv"

// !!!Please use AIMFileMimeType_MT_VIDEO_VOB instead
// static const std::string MT_VIDEO_VOB;
#define AIMFileMimeType_MT_VIDEO_VOB "video/vob"

// !!!Please use AIMFileMimeType_MT_VIDEO_MP4 instead
// static const std::string MT_VIDEO_MP4;
#define AIMFileMimeType_MT_VIDEO_MP4 "video/mp4"

// !!!Please use AIMFileMimeType_MT_OFFICE_DOC instead
// static const std::string MT_OFFICE_DOC;
#define AIMFileMimeType_MT_OFFICE_DOC "office/doc"

// !!!Please use AIMFileMimeType_MT_OFFICE_DOCX instead
// static const std::string MT_OFFICE_DOCX;
#define AIMFileMimeType_MT_OFFICE_DOCX "office/docx"

// !!!Please use AIMFileMimeType_MT_OFFICE_XLS instead
// static const std::string MT_OFFICE_XLS;
#define AIMFileMimeType_MT_OFFICE_XLS "office/xls"

// !!!Please use AIMFileMimeType_MT_OFFICE_XLSX instead
// static const std::string MT_OFFICE_XLSX;
#define AIMFileMimeType_MT_OFFICE_XLSX "office/xlsx"

// !!!Please use AIMFileMimeType_MT_OFFICE_PPT instead
// static const std::string MT_OFFICE_PPT;
#define AIMFileMimeType_MT_OFFICE_PPT "office/ppt"

// !!!Please use AIMFileMimeType_MT_OFFICE_PPTX instead
// static const std::string MT_OFFICE_PPTX;
#define AIMFileMimeType_MT_OFFICE_PPTX "office/pptx"

// !!!Please use AIMFileMimeType_MT_NORMAL_TXT instead
// static const std::string MT_NORMAL_TXT;
#define AIMFileMimeType_MT_NORMAL_TXT "normal/txt"

// !!!Please use AIMFileMimeType_MT_NORMAL_ZIP instead
// static const std::string MT_NORMAL_ZIP;
#define AIMFileMimeType_MT_NORMAL_ZIP "normal/zip"

// !!!Please use AIMFileMimeType_MT_NORMAL_PDF instead
// static const std::string MT_NORMAL_PDF;
#define AIMFileMimeType_MT_NORMAL_PDF "normal/pdf"

// !!!Please use AIMFileMimeType_MT_NORMAL_RAR instead
// static const std::string MT_NORMAL_RAR;
#define AIMFileMimeType_MT_NORMAL_RAR "normal/rar"

// !!!Please use AIMFileMimeType_MT_NORMAL_PSD instead
// static const std::string MT_NORMAL_PSD;
#define AIMFileMimeType_MT_NORMAL_PSD "normal/psd"

// !!!Please use AIMFileMimeType_MT_NORMAL_AI instead
// static const std::string MT_NORMAL_AI;
#define AIMFileMimeType_MT_NORMAL_AI "normal/ai"

// !!!Please use AIMFileMimeType_MT_FILE_UNKNOWN instead
// static const std::string MT_FILE_UNKNOWN;
#define AIMFileMimeType_MT_FILE_UNKNOWN "file/unknown"
};

} // namespace dps
} // namespace alibaba
