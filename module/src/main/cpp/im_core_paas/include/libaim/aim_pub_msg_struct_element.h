// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_msg_struct_element_type.h"
#include "aim_pub_msg_struct_element_at.h"
#include "aim_pub_msg_struct_element_uid.h"
#include "aim_pub_msg_text_content.h"
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 结构化消息元素
 */
struct AIMPubMsgStructElement final {

  AIMMsgStructElementType element_type =
      AIMMsgStructElementType::ELEMENT_TYPE_UNKNOWN;
  AIMPubMsgTextContent text_content;
  AIMPubMsgStructElementUid uid_element;
  AIMPubMsgStructElementAt at_element;

  AIMPubMsgStructElement(AIMMsgStructElementType element_type_,
                         AIMPubMsgTextContent text_content_,
                         AIMPubMsgStructElementUid uid_element_,
                         AIMPubMsgStructElementAt at_element_)
      : element_type(std::move(element_type_)),
        text_content(std::move(text_content_)),
        uid_element(std::move(uid_element_)),
        at_element(std::move(at_element_)) {}

  AIMPubMsgStructElement() {}
};

} // namespace dps
} // namespace alibaba
