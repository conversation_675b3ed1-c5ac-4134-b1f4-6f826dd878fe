// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>
#include <vector>

namespace alibaba {
namespace dps {

struct AIMPubGroupMember;

/**
 * 拉取本地群成员列表监听
 */
class AIMPubGroupListLocalMemberListener {
public:
  virtual ~AIMPubGroupListLocalMemberListener() {}

  /**
   * 返回成功
   * @param members 本地群成员列表
   */
  virtual void OnSuccess(const std::vector<AIMPubGroupMember> &members) = 0;

  /**
   * 拉取失败
   * 返回统一失败结果
   * @param error 错误信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubGroupListLocalMemberListenerPtr =
    std::shared_ptr<AIMPubGroupListLocalMemberListener>;
using AIMPubGroupListLocalMemberListenerWeakPtr =
    std::weak_ptr<AIMPubGroupListLocalMemberListener>;

} // namespace dps
} // namespace alibaba
