// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <string>
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 消息已读状态
 */
struct AIMPubMsgReadStatus final {

  std::string appCid;
  std::string mid;
  std::vector<std::string> read_uids;
  std::vector<std::string> unread_uids;

  AIMPubMsgReadStatus(std::string appCid_, std::string mid_,
                      std::vector<std::string> read_uids_,
                      std::vector<std::string> unread_uids_)
      : appCid(std::move(appCid_)), mid(std::move(mid_)),
        read_uids(std::move(read_uids_)), unread_uids(std::move(unread_uids_)) {
  }

  AIMPubMsgReadStatus() {}
};

} // namespace dps
} // namespace alibaba
