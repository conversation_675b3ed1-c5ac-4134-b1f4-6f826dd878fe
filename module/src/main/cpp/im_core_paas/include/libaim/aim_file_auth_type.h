// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class AIMFileAuthType : int {
  NO_AUTH = 1,
  STRICT_AUTH = 2,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::dps::AIMFileAuthType> {
  size_t operator()(::alibaba::dps::AIMFileAuthType type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
