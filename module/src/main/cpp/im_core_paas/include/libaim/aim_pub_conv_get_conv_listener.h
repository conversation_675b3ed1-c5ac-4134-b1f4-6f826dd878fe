// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>
#include <vector>

namespace alibaba {
namespace dps {

struct AIMPubConversation;

/**
 * 获取会话列表监听
 */
class AIMPubConvGetConvListener {
public:
  virtual ~AIMPubConvGetConvListener() {}

  /**
   * 获取成功
   */
  virtual void OnSuccess(const std::vector<AIMPubConversation> &result) = 0;

  /**
   * 获取失败
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubConvGetConvListenerPtr = std::shared_ptr<AIMPubConvGetConvListener>;
using AIMPubConvGetConvListenerWeakPtr =
    std::weak_ptr<AIMPubConvGetConvListener>;

} // namespace dps
} // namespace alibaba
