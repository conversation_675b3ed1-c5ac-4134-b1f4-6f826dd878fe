// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>
#include <string>
#include <vector>

namespace alibaba {
namespace dps {

enum class AIMConvTypingCommand;
enum class AIMConvTypingMessageContent;
struct AIMPubConversation;

/**
 * 会话变更（增量）
 */
class AIMPubConvChangeListener {
public:
  virtual ~AIMPubConvChangeListener() {}

  /**
   * 会话状态变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnConvStatusChanged(const std::vector<AIMPubConversation> &convs) = 0;

  /**
   * 会话最后一条消息变更
   * @param convs 全量的会话结构
   * 特殊场景:消息撤回时,last_msg中只有recall和mid有效。
   */
  virtual void
  OnConvLastMessageChanged(const std::vector<AIMPubConversation> &convs) = 0;

  /**
   * 会话未读消息数变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnConvUnreadCountChanged(const std::vector<AIMPubConversation> &convs) = 0;

  /**
   * 会话extension变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnConvExtensionChanged(const std::vector<AIMPubConversation> &convs) = 0;

  /**
   * 会话local extension变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnConvLocalExtensionChanged(const std::vector<AIMPubConversation> &convs) = 0;

  /**
   * 会话user extension变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnConvUserExtensionChanged(const std::vector<AIMPubConversation> &convs) = 0;

  /**
   * 会话是否通知的状态变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnConvNotificationChanged(const std::vector<AIMPubConversation> &convs) = 0;

  /**
   * 会话置顶状态变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnConvTopChanged(const std::vector<AIMPubConversation> &convs) = 0;

  /**
   * 会话草稿变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnConvDraftChanged(const std::vector<AIMPubConversation> &convs) = 0;

  /**
   * 接收到正在输入事件
   * @param appCid  	会话id
   * @param command   TypingCommand
   * @param type TypingMessageContent
   */
  virtual void OnConvTypingEvent(const std::string &appCid,
                                 AIMConvTypingCommand command,
                                 AIMConvTypingMessageContent type) = 0;

  /**
   * 会话消息被清空
   * @param convs 有效字段appCid
   */
  virtual void
  OnConvClearMessage(const std::vector<AIMPubConversation> &convs) = 0;
};

using AIMPubConvChangeListenerPtr = std::shared_ptr<AIMPubConvChangeListener>;
using AIMPubConvChangeListenerWeakPtr = std::weak_ptr<AIMPubConvChangeListener>;

} // namespace dps
} // namespace alibaba
