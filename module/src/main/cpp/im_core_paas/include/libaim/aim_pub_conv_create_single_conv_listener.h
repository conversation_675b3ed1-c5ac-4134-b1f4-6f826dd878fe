// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

struct AIMPubConversation;

/**
 * 创建单聊会话监听
 */
class AIMPubConvCreateSingleConvListener {
public:
  virtual ~AIMPubConvCreateSingleConvListener() {}

  /**
   * 创建成功
   */
  virtual void OnSuccess(const AIMPubConversation &conv) = 0;

  /**
   * 创建失败
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubConvCreateSingleConvListenerPtr =
    std::shared_ptr<AIMPubConvCreateSingleConvListener>;
using AIMPubConvCreateSingleConvListenerWeakPtr =
    std::weak_ptr<AIMPubConvCreateSingleConvListener>;

} // namespace dps
} // namespace alibaba
