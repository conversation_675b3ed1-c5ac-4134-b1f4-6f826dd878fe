// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <map>
#include <string>
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 发送转发消息体
 */
struct AIMPubMsgSendForwardMessage final {

  std::string appCid;
  /**
   * 需转发消息的mid列表
   */
  std::vector<std::string> mids;
  std::string to_appCid;
  std::vector<std::string> receivers;
  /**
   * 如不为空，则替换转发消息的 extension，否则复用原extension
   */
  std::map<std::string, std::string> extension;
  std::map<std::string, std::string> callback_ctx;

  AIMPubMsgSendForwardMessage(std::string appCid_,
                              std::vector<std::string> mids_,
                              std::string to_appCid_,
                              std::vector<std::string> receivers_,
                              std::map<std::string, std::string> extension_,
                              std::map<std::string, std::string> callback_ctx_)
      : appCid(std::move(appCid_)), mids(std::move(mids_)),
        to_appCid(std::move(to_appCid_)), receivers(std::move(receivers_)),
        extension(std::move(extension_)),
        callback_ctx(std::move(callback_ctx_)) {}

  AIMPubMsgSendForwardMessage() {}
};

} // namespace dps
} // namespace alibaba
