// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_pub_msg_inner_combine_content.h"
#include <cstdint>
#include <map>
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

struct AIMPubMsgCombineForward final {

  std::string sender;
  std::string appCid;
  std::string mid;
  int64_t created_at = 0;
  AIMPubMsgInnerCombineContent combine_content;
  std::map<std::string, std::string> extension;

  AIMPubMsgCombineForward(std::string sender_, std::string appCid_,
                          std::string mid_, int64_t created_at_,
                          AIMPubMsgInnerCombineContent combine_content_,
                          std::map<std::string, std::string> extension_)
      : sender(std::move(sender_)), appCid(std::move(appCid_)),
        mid(std::move(mid_)), created_at(std::move(created_at_)),
        combine_content(std::move(combine_content_)),
        extension(std::move(extension_)) {}

  AIMPubMsgCombineForward() {}
};

} // namespace dps
} // namespace alibaba
