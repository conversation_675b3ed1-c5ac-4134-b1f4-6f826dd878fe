// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <cstdint>
#include <functional>
#include <map>
#include <memory>
#include <string>
#include <vector>

namespace alibaba {
namespace dps {

class AIMConvServiceCompleteListener;
class AIMConvSetTopListener;
class AIMPubConvChangeListener;
class AIMPubConvCreateSingleConvListener;
class AIMPubConvGetConvListener;
class AIMPubConvGetConvWithParamListener;
class AIMPubConvGetSingleConvListener;
class AIMPubConvListConvListener;
class AIMPubConvListListener;
class AIMQueryConvActiveStateListener;
enum class AIMConvTypingCommand;
enum class AIMConvTypingMessageContent;
struct AIMPubConvCreateSingleConvParam;
struct AIMPubConversation;

class AIMPubConvService {
public:
  virtual ~AIMPubConvService() {}

  /**
   * 创建单聊会话。
   * @param param  CreateConversationParams结构
   * @param listener 监听器
   */
  virtual void CreateSingleConversation(
      const AIMPubConvCreateSingleConvParam &param,
      const std::shared_ptr<AIMPubConvCreateSingleConvListener> &listener) = 0;
  virtual void CreateSingleConversation(
      const AIMPubConvCreateSingleConvParam &param,
      const std::function<void(const AIMPubConversation &conv)> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 获取会话列表，隐藏的会话不会返回（拉取会话不需要rpc请求,sdk内部有同步会话机制）
   * @param offset 从offset开始拉取（排序index，根据置顶及最后更新时间进行排序）
   * @param count     需要的Conversation个数
   * @param listener 监听器
   */
  virtual void ListLocalConversationsWithOffset(
      int32_t offset, int32_t count,
      const std::shared_ptr<AIMPubConvGetConvListener> &listener) = 0;
  virtual void ListLocalConversationsWithOffset(
      int32_t offset, int32_t count,
      const std::function<void(const std::vector<AIMPubConversation> &result)>
          &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 获取本地会话列表，隐藏的会话不会返回（本地，不会有rpc请求）。
   * @param appCid
   * 从指定appCid开始拉取(当appCid为空时，表示拉首屏，会从第0个会话拉取)
   * @param count     需要的Conversation个数
   * @param listener 监听器
   */
  virtual void ListLocalConversationsWithCid(
      const std::string &appCid, int32_t count,
      const std::shared_ptr<AIMPubConvGetConvListener> &listener) = 0;
  virtual void ListLocalConversationsWithCid(
      const std::string &appCid, int32_t count,
      const std::function<void(const std::vector<AIMPubConversation> &result)>
          &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 根据入口会话ID获取二级会话列表，隐藏的会话不会返回（本地，不会有rpc请求）。
   * @param entrance_app_cid 入口会话id
   * @param cursor    拉取 modify_time 小于 cursor 的会话（从新到老排序）
   * @param count     需要拉取的会话个数
   * @param listener 监听器
   */
  virtual void ListLocalSubConversationsByEntranceAppCid(
      const std::string &entrance_app_cid, int64_t cursor, int32_t count,
      const std::shared_ptr<AIMPubConvGetConvListener> &listener) = 0;
  virtual void ListLocalSubConversationsByEntranceAppCid(
      const std::string &entrance_app_cid, int64_t cursor, int32_t count,
      const std::function<void(const std::vector<AIMPubConversation> &result)>
          &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 获取本地会话列表，包括所有状态的会话（也会返回隐藏的）。
   * @param offset 从offset开始拉取（排序index，根据置顶及最后更新时间进行排序）
   * @param count     需要拉取的Conversation个数
   * @param listener 监听器
   */
  virtual void ListAllStatusLocalConvs(
      int32_t offset, int32_t count,
      const std::shared_ptr<AIMPubConvGetConvListener> &listener) = 0;
  virtual void ListAllStatusLocalConvs(
      int32_t offset, int32_t count,
      const std::function<void(const std::vector<AIMPubConversation> &result)>
          &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 获取会话列表，隐藏的会话不会返回（本地会话不足所需条目时，会 RPC
   * 请求服务端数据）
   * @param cursor       拉取会话的时间戳, 维度：会话更新时间，拉首屏传值 -1
   * @param count        需要拉取的会话个数
   * @param listener     监听器
   */
  virtual void ListConversations(
      int64_t cursor, int32_t count,
      const std::shared_ptr<AIMPubConvListConvListener> &listener) = 0;
  virtual void ListConversations(
      int64_t cursor, int32_t count,
      const std::function<void(const std::vector<AIMPubConversation> &result,
                               bool has_more, int64_t next_cursor)> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 获取会话Id对应的会话。
   * @param appCid    会话id
   * @param listener 监听器
   */
  virtual void GetConversation(
      const std::string &appCid,
      const std::shared_ptr<AIMPubConvGetSingleConvListener> &listener) = 0;
  virtual void GetConversation(
      const std::string &appCid,
      const std::function<void(const AIMPubConversation &conv)> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 获取会话appCid集合对应的会话列表。
   * @param appCids    会话集合id
   * @param listener 监听器
   */
  virtual void GetConversations(
      const std::vector<std::string> &appCids,
      const std::shared_ptr<AIMPubConvGetConvListener> &listener) = 0;
  virtual void GetConversations(
      const std::vector<std::string> &appCids,
      const std::function<void(const std::vector<AIMPubConversation> &result)>
          &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 批量获取会话Id对应的本地会话（如果本地不存在则不会发送rpc）。
   * @param appCids    会话id
   * @param listener 监听器
   */
  virtual void GetLocalConversations(
      const std::vector<std::string> &appCids,
      const std::shared_ptr<AIMPubConvGetConvListener> &listener) = 0;
  virtual void GetLocalConversations(
      const std::vector<std::string> &appCids,
      const std::function<void(const std::vector<AIMPubConversation> &result)>
          &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 根据user_id获取对应的会话列表集合。
   * @param user_id    用户id
   * @param listener 监听器
   */
  virtual void GetSingleConversations(
      const std::string &user_id,
      const std::shared_ptr<AIMPubConvGetConvListener> &listener) = 0;
  virtual void GetSingleConversations(
      const std::string &user_id,
      const std::function<void(const std::vector<AIMPubConversation> &result)>
          &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 批量根据user_ids获取对应的会话列表集合。
   * @param user_ids    用户id集合
   * @param listener 监听器
   */
  virtual void GetSingleConversationsWithUserIds(
      const std::vector<std::string> &user_ids,
      const std::shared_ptr<AIMPubConvGetConvListener> &listener) = 0;
  virtual void GetSingleConversationsWithUserIds(
      const std::vector<std::string> &user_ids,
      const std::function<void(const std::vector<AIMPubConversation> &result)>
          &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 根据入口会话ID获取二级会话列表（强制拉取远端二级会话，会刷新本地缓存和DB）
   * @param entrance_app_cid 入口会话id
   * @param cursor    获取 modify_time 小于 cursor 的会话（从新到老排序）
   * @param count     需要拉取的会话个数
   * @param listener 监听器
   */
  virtual void GetRemoteSubConversationsByEntranceAppCid(
      const std::string &entrance_app_cid, int64_t cursor, int32_t count,
      const std::shared_ptr<AIMPubConvGetConvWithParamListener> &listener) = 0;
  virtual void GetRemoteSubConversationsByEntranceAppCid(
      const std::string &entrance_app_cid, int64_t cursor, int32_t count,
      const std::function<void(const std::vector<AIMPubConversation> &result,
                               int64_t next_cursor, bool has_more)> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 从本地DB物理删除会话，并且会同步删除会话的所有本地消息(再次拉取到会话消息会再次同步)，群会删除本地群成员，没有rpc发送
   * @param appCid    会话id
   * @param listener 监听器
   */
  virtual void RemoveLocalConversation(
      const std::string &appCid,
      const std::shared_ptr<AIMConvServiceCompleteListener> &listener) = 0;
  virtual void RemoveLocalConversation(
      const std::string &appCid, const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 发送正在输入消息事件给接收方，接收者需要精确receiverid，只对单聊会话有效。发送typing事件建议5s为间隔。
   * @param appCid   会话id
   * @param receiverid   接受者id
   * @param command   TypingCommand
   * @param type TypingMessageContent
   * @param listener 监听器
   */
  virtual void UpdateTypingStatus(
      const std::string &appCid, const std::string &receiver_id,
      AIMConvTypingCommand command, AIMConvTypingMessageContent type,
      const std::shared_ptr<AIMConvServiceCompleteListener> &listener) = 0;
  virtual void UpdateTypingStatus(
      const std::string &appCid, const std::string &receiver_id,
      AIMConvTypingCommand command, AIMConvTypingMessageContent type,
      const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 更新草稿，草稿只在本地存储。
   * @param appCid   会话id
   * @param draft   草稿内容
   * @param listener 监听器
   */
  virtual void UpdateDraftMessage(
      const std::string &appCid, const std::string &draft,
      const std::shared_ptr<AIMConvServiceCompleteListener> &listener) = 0;
  virtual void UpdateDraftMessage(
      const std::string &appCid, const std::string &draft,
      const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 全量更新本地extension数据，只在本地存储。
   * @param appCid   会话id
   * @param local_ext 扩展信息
   * @param listener 监听器
   */
  virtual void UpdateLocalExtension(
      const std::string &appCid,
      const std::map<std::string, std::string> &local_ext,
      const std::shared_ptr<AIMConvServiceCompleteListener> &listener) = 0;
  virtual void UpdateLocalExtension(
      const std::string &appCid,
      const std::map<std::string, std::string> &local_ext,
      const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 增量更新数据(add or update),不影响其他key
   * @param appCid   会话id
   * @param local_ext 扩展信息
   * @param listener 监听器
   */
  virtual void UpdateLocalExtensionByKeys(
      const std::string &appCid,
      const std::map<std::string, std::string> &local_ext,
      const std::shared_ptr<AIMConvServiceCompleteListener> &listener) = 0;
  virtual void UpdateLocalExtensionByKeys(
      const std::string &appCid,
      const std::map<std::string, std::string> &local_ext,
      const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 批量增量更新数据(add or update),不影响其他key
   * @param appCid2ext appCid to localext的map
   * @param listener 监听器
   */
  virtual void BulkUpdateLocalExtensionByKeys(
      const std::map<std::string, std::map<std::string, std::string>>
          &appCid2ext,
      const std::shared_ptr<AIMConvServiceCompleteListener> &listener) = 0;
  virtual void BulkUpdateLocalExtensionByKeys(
      const std::map<std::string, std::map<std::string, std::string>>
          &appCid2ext,
      const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 隐藏会话，会话将处于不可见状态，当会话状态变化时，例如会话收到消息，会话内数据变化时，将变为可见状态。
   * @param appCid   会话id
   * @param listener 监听器
   */
  virtual void
  Hide(const std::string &appCid,
       const std::shared_ptr<AIMConvServiceCompleteListener> &listener) = 0;
  virtual void
  Hide(const std::string &appCid, const std::function<void()> &OnSuccess,
       const std::function<void(const ::alibaba::dps::DPSError &error)>
           &OnFailure) = 0;

  /**
   * 更新会话是否免打扰
   * 这里只是更新会话状态的标示，会话变更和消息新增等还是会广播出来，是否通知用户由开发者决定
   * @param appCid   会话id
   * @param mute  是否免打扰
   * @param listener 监听器
   */
  virtual void
  Mute(const std::string &appCid, bool mute,
       const std::shared_ptr<AIMConvServiceCompleteListener> &listener) = 0;
  virtual void
  Mute(const std::string &appCid, bool mute,
       const std::function<void()> &OnSuccess,
       const std::function<void(const ::alibaba::dps::DPSError &error)>
           &OnFailure) = 0;

  /**
   * 会话置顶(返回值为会话置顶服务端时间戳，会同时更新会话的modify_time和top_rank字段)
   * @param appCid   会话id
   * @param top   置顶
   * @param listener 监听器
   */
  virtual void
  SetTop(const std::string &appCid, bool top,
         const std::shared_ptr<AIMConvSetTopListener> &listener) = 0;
  virtual void
  SetTop(const std::string &appCid, bool top,
         const std::function<void(int64_t top_rank)> &OnSuccess,
         const std::function<void(const ::alibaba::dps::DPSError &error)>
             &OnFailure) = 0;

  /**
   * 会话置顶带时间戳，kv的时间戳会存放在local_ext中
   * @param appCid   会话id
   * @param top   置顶
   * @param time_stamp   置顶时间戳字段的kv
   * @param listener 监听器
   */
  virtual void SetTopWithTimeStamp(
      const std::string &appCid, bool top,
      const std::map<std::string, std::string> &time_stamp,
      const std::shared_ptr<AIMConvSetTopListener> &listener) = 0;
  virtual void SetTopWithTimeStamp(
      const std::string &appCid, bool top,
      const std::map<std::string, std::string> &time_stamp,
      const std::function<void(int64_t top_rank)> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 清空会话未读消息 (默认不同步清空子会话)
   * @param appCid 会话id
   * @param mid 会话最后一条消息的id
   * @param listener 监听器
   */
  virtual void ClearRedPoint(
      const std::string &appCid, const std::string &mid,
      const std::shared_ptr<AIMConvServiceCompleteListener> &listener) = 0;
  virtual void
  ClearRedPoint(const std::string &appCid, const std::string &mid,
                const std::function<void()> &OnSuccess,
                const std::function<void(const ::alibaba::dps::DPSError &error)>
                    &OnFailure) = 0;

  /**
   * 清空会话未读消息
   * @param appCid 会话id
   * @param mid 会话最后一条消息的id
   * @param sync_clear_sub_conv 是否同步清理子会话 (该配置仅对入口会话生效，即
   * entrance_cid 为空的会话)
   * @param listener 监听器
   */
  virtual void ClearRedPointWithOption(
      const std::string &appCid, const std::string &mid,
      bool sync_clear_sub_conv,
      const std::shared_ptr<AIMConvServiceCompleteListener> &listener) = 0;
  virtual void ClearRedPointWithOption(
      const std::string &appCid, const std::string &mid,
      bool sync_clear_sub_conv, const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 批量清空所有缓存会话的红点，上层显示或者有过操作的会话都会在缓存中（无notify推送）
   * @param listener 监听器
   */
  virtual void ClearAllConvsRedPoint(
      const std::shared_ptr<AIMConvServiceCompleteListener> &listener) = 0;
  virtual void ClearAllConvsRedPoint(
      const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 清空会话所有消息
   * @param appCid   会话id
   * @param listener 监听器
   */
  virtual void
  Clear(const std::string &appCid,
        const std::shared_ptr<AIMConvServiceCompleteListener> &listener) = 0;
  virtual void
  Clear(const std::string &appCid, const std::function<void()> &OnSuccess,
        const std::function<void(const ::alibaba::dps::DPSError &error)>
            &OnFailure) = 0;

  /**
   * 激活会话，选择会话后需要调用，激活后的会话不会计算红点；如果不再选择会话，需要传入appCid为空字符串
   * 一些特殊情况，比如没有从服务端同步到群的解散和被移除，会补偿解散和移除事件
   * @param appCid    会话id
   */
  virtual void SetActiveCid(const std::string &appCid) = 0;

  /**
   * 注册会话全量变更的监听器
   * @param listener 监听器
   */
  virtual void AddConvListListener(
      const std::shared_ptr<AIMPubConvListListener> &listener) = 0;

  /**
   * 删除全量变更的监听器
   * @param listener 监听器
   */
  virtual void RemoveConvListListener(
      const std::shared_ptr<AIMPubConvListListener> &listener) = 0;

  /**
   * 注册会话增量变更的监听器
   * @param listener 监听器
   */
  virtual void AddConvChangeListener(
      const std::shared_ptr<AIMPubConvChangeListener> &listener) = 0;

  /**
   * 删除会话增量变更的监听器
   * @param listener 监听器
   */
  virtual void RemoveConvChangeListener(
      const std::shared_ptr<AIMPubConvChangeListener> &listener) = 0;

  /**
   * 删除所有会话的监听器
   */
  virtual void RemoveAllConvListListener() = 0;

  /**
   * 删除所有会话的变更监听器
   */
  virtual void RemoveAllConvChangeListener() = 0;

  /**
   * 判断会话是否激活
   */
  virtual void IsConversationActive(
      const std::string &appCid,
      const std::shared_ptr<AIMQueryConvActiveStateListener> &listener) = 0;
  virtual void IsConversationActive(
      const std::string &appCid,
      const std::function<void(bool result)> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  /**
   * 生成标准单聊cid
   * @param senderId 发送者id
   * @param receiverId 接收者id
   */
  static std::string GenerateStandardAppCid(const std::string &senderId,
                                            const std::string &receiverId);

  /**
   * 判断是否为标准单聊
   * @param appCid 会话id
   */
  static bool IsStandard(const std::string &appCid);

  /**
   * 获取标准单聊targetUid
   * @param selfAppUid 自身uid
   * @param appCid 会话id
   */
  static std::string GetTargetAppUid(const std::string &selfAppUid,
                                     const std::string &appCid);
};

using AIMPubConvServicePtr = std::shared_ptr<AIMPubConvService>;
using AIMPubConvServiceWeakPtr = std::weak_ptr<AIMPubConvService>;

} // namespace dps
} // namespace alibaba
