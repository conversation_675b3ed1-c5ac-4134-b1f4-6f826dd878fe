// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>
#include <vector>

namespace alibaba {
namespace dps {

struct AIMPubGroupMember;

/**
 * 拉取群成员
 */
class AIMPubGroupListAllMemberListener {
public:
  virtual ~AIMPubGroupListAllMemberListener() {}

  /**
   * 返回本地群成员
   * @param members 本地群成员列表
   */
  virtual void OnLocal(const std::vector<AIMPubGroupMember> &members) = 0;

  /**
   * 返回服务端数据
   * @param members 服务端数据
   */
  virtual void OnRefresh(const std::vector<AIMPubGroupMember> &members) = 0;

  /**
   * 失败结果
   * @param error 错误信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubGroupListAllMemberListenerPtr =
    std::shared_ptr<AIMPubGroupListAllMemberListener>;
using AIMPubGroupListAllMemberListenerWeakPtr =
    std::weak_ptr<AIMPubGroupListAllMemberListener>;

} // namespace dps
} // namespace alibaba
