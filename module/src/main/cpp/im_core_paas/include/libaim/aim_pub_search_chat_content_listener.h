// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <cstdint>
#include <functional>
#include <memory>
#include <vector>

namespace alibaba {
namespace dps {

struct AIMPubSearchChatResult;

/**
 * SearchChatContent 监听
 */
class AIMPubSearchChatContentListener {
public:
  virtual ~AIMPubSearchChatContentListener() {}

  virtual void OnSuccess(const std::vector<AIMPubSearchChatResult> &result,
                         int32_t total_count) = 0;

  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubSearchChatContentListenerPtr =
    std::shared_ptr<AIMPubSearchChatContentListener>;
using AIMPubSearchChatContentListenerWeakPtr =
    std::weak_ptr<AIMPubSearchChatContentListener>;

} // namespace dps
} // namespace alibaba
