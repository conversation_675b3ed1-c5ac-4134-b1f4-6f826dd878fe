// Copyright (c) 2016 The Alibaba DingTalk Authors. All rights reserved.

#ifndef aim_c_api_h
#define aim_c_api_h

#include "aim_pub_module.h"
#include "dps_define.h"
#include "dps_module_info.h"

#if defined(__cplusplus)
extern "C" {
#endif

/**
 * 获取用户实例
 */
DPS_DECL alibaba::dps::AIMPubModule *
GetAIMPubModuleInstance(const std::string &id);

/**
 * 获取AIM模块信息，用于向DPS注册AIM模块
 */
DPS_DECL alibaba::dps::DPSModuleInfo *GetAIMPubModuleInfo();

#if defined(__cplusplus)
}
#endif

#endif
