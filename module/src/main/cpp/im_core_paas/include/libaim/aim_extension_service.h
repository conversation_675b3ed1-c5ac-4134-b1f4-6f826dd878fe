// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <cstdint>
#include <functional>
#include <memory>
#include <string>
#include <vector>

namespace alibaba {
namespace dps {

class AIMMessageDeletionCallback;
class AIMReceivedLiteMessageCallback;
class AIMSetLastMsgCallback;
class AIMSetRedPointCallback;
struct AIMPubLiteMessage;

class AIMExtensionService {
public:
  virtual ~AIMExtensionService() {}

  virtual void HandleReceivedLiteMessage(
      const std::vector<AIMPubLiteMessage> &msgs,
      const std::shared_ptr<AIMReceivedLiteMessageCallback> &callback) = 0;
  virtual void HandleReceivedLiteMessage(
      const std::vector<AIMPubLiteMessage> &msgs,
      const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  virtual void HandleDeletedLiteMessage(
      const std::string &appCid, const std::string &mid, int64_t created_at,
      const std::shared_ptr<AIMMessageDeletionCallback> &callback) = 0;
  virtual void HandleDeletedLiteMessage(
      const std::string &appCid, const std::string &mid, int64_t created_at,
      const std::function<void()> &OnSuccess,
      const std::function<void(const ::alibaba::dps::DPSError &error)>
          &OnFailure) = 0;

  virtual void
  SetRedPoint(const std::string &appCid, int32_t new_red_point,
              const std::shared_ptr<AIMSetRedPointCallback> &callback) = 0;
  virtual void
  SetRedPoint(const std::string &appCid, int32_t new_red_point,
              const std::function<void()> &OnSuccess,
              const std::function<void(const ::alibaba::dps::DPSError &error)>
                  &OnFailure) = 0;

  virtual void
  SetLastMsg(const std::string &appCid, const AIMPubLiteMessage &last_msg,
             const std::shared_ptr<AIMSetLastMsgCallback> &callback) = 0;
  virtual void
  SetLastMsg(const std::string &appCid, const AIMPubLiteMessage &last_msg,
             const std::function<void()> &OnSuccess,
             const std::function<void(const ::alibaba::dps::DPSError &error)>
                 &OnFailure) = 0;
};

using AIMExtensionServicePtr = std::shared_ptr<AIMExtensionService>;
using AIMExtensionServiceWeakPtr = std::weak_ptr<AIMExtensionService>;

} // namespace dps
} // namespace alibaba
