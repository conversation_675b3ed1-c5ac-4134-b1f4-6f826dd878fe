// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_msg_biz_info.h"
#include "aim_msg_update_mode.h"
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 本地消息biz信息更新结构
 */
struct AIMPubMsgBizUpdateInfo final {

  /**
   * 需要更新的消息appCid
   */
  std::string appCid;
  /**
   * 需要更新的消息localid
   */
  std::string localid;
  /**
   * 消息更新模式
   */
  AIMMsgUpdateMode update_mode = AIMMsgUpdateMode::UPDATE_ALL;
  /**
   * 需更新的消息biz属性
   */
  AIMMsgBizInfo biz_info;

  AIMPubMsgBizUpdateInfo(std::string appCid_, std::string localid_,
                         AIMMsgUpdateMode update_mode_, AIMMsgBizInfo biz_info_)
      : appCid(std::move(appCid_)), localid(std::move(localid_)),
        update_mode(std::move(update_mode_)), biz_info(std::move(biz_info_)) {}

  AIMPubMsgBizUpdateInfo() {}
};

} // namespace dps
} // namespace alibaba
