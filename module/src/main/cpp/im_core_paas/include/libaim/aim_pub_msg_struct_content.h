// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_pub_msg_struct_element.h"
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 结构化消息内容
 */
struct AIMPubMsgStructContent final {

  std::vector<AIMPubMsgStructElement> elements;

  AIMPubMsgStructContent(std::vector<AIMPubMsgStructElement> elements_)
      : elements(std::move(elements_)) {}

  AIMPubMsgStructContent() {}
};

} // namespace dps
} // namespace alibaba
