// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class AIMMsgUpdateMode : int {
  /**
   * 仅更新biz_tag
   */
  UPDATE_BIZ_TAG = 0,
  /**
   * 仅更新biz_text
   */
  UPDATE_BIZ_TEXT = 1,
  /**
   * 全部更新
   */
  UPDATE_ALL = 2,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::dps::AIMMsgUpdateMode> {
  size_t operator()(::alibaba::dps::AIMMsgUpdateMode type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
