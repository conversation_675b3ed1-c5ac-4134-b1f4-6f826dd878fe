// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>
#include <vector>

namespace alibaba {
namespace dps {

struct AIMPubGroupMember;

/**
 *拉取单个群成员
 */
class AIMPubGroupGetMembersListener {
public:
  virtual ~AIMPubGroupGetMembersListener() {}

  /**
   * 返回本地群成员
   * @param member 本地群成员
   */
  virtual void OnLocal(const std::vector<AIMPubGroupMember> &member) = 0;

  /**
   * 返回服务端数据
   * @param member 服务端数据
   */
  virtual void OnRefresh(const std::vector<AIMPubGroupMember> &member) = 0;

  /**
   * 失败结果
   * @param error 错误信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubGroupGetMembersListenerPtr =
    std::shared_ptr<AIMPubGroupGetMembersListener>;
using AIMPubGroupGetMembersListenerWeakPtr =
    std::weak_ptr<AIMPubGroupGetMembersListener>;

} // namespace dps
} // namespace alibaba
