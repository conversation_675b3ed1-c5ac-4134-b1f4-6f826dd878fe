// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_pub_group_user_info.h"
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 转让群主参数
 */
struct AIMPubGroupSetOwner final {

  /**
   * 群appCid
   */
  std::string appCid;
  /**
   * 操作成员
   */
  AIMPubGroupUserInfo owner;

  AIMPubGroupSetOwner(std::string appCid_, AIMPubGroupUserInfo owner_)
      : appCid(std::move(appCid_)), owner(std::move(owner_)) {}

  AIMPubGroupSetOwner() {}
};

} // namespace dps
} // namespace alibaba
