// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_pub_msg_content.h"
#include <map>
#include <string>
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 发送消息体
 */
struct AIMPubMsgSendMessage final {

  std::string appCid;
  AIMPubMsgContent content;
  std::vector<std::string> receivers;
  std::map<std::string, std::string> extension;
  std::map<std::string, std::string> local_extension;
  std::map<std::string, std::string> callback_ctx;
  /**
   * 如果不为空，则使用custom_localid作为localid
   */
  std::string custom_localid;

  AIMPubMsgSendMessage(std::string appCid_, AIMPubMsgContent content_,
                       std::vector<std::string> receivers_,
                       std::map<std::string, std::string> extension_,
                       std::map<std::string, std::string> local_extension_,
                       std::map<std::string, std::string> callback_ctx_,
                       std::string custom_localid_)
      : appCid(std::move(appCid_)), content(std::move(content_)),
        receivers(std::move(receivers_)), extension(std::move(extension_)),
        local_extension(std::move(local_extension_)),
        callback_ctx(std::move(callback_ctx_)),
        custom_localid(std::move(custom_localid_)) {}

  AIMPubMsgSendMessage() {}
};

} // namespace dps
} // namespace alibaba
