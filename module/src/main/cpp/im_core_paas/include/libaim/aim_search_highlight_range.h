// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 消息查询高亮位置信息
 */
struct AIMSearchHighlightRange final {

  /**
   * 高亮开始位置
   */
  int64_t start = 0;
  /**
   * 高亮文本长度
   */
  int64_t length = 0;

  AIMSearchHighlightRange(int64_t start_, int64_t length_)
      : start(std::move(start_)), length(std::move(length_)) {}

  AIMSearchHighlightRange() {}
};

} // namespace dps
} // namespace alibaba
