// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <map>
#include <string>
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 创建单聊参数
 */
struct AIMPubConvCreateSingleConvParam final {

  /**
   * 选填，支持使用客户端自己设置appCid
   */
  std::string appCid;
  /**
   * 选填，业务类型
   */
  std::string biz_type;
  /**
   * 选填，会话扩展信息
   */
  std::map<std::string, std::string> ext;
  /**
   * 选填，单聊双方的uid（FYI：如果ctx为空，此项必填。如果拿不到uids，需要在ctx中带上nick信息）
   */
  std::vector<std::string> uids;
  /**
   * 选填，上下文信息（FYI：如果uids为空，此项必填，需要带上单聊双方的nick信息用于创建会话）
   */
  std::map<std::string, std::string> ctx;
  /**
   * 选填，创建本地会话，本地会话没有rpc同步服务端
   * 创建本地会话需要传appCid和uids，当发送消息时服务端会自动创建对应uids的会话，appCid要保证和服务端的生成规则一致
   */
  bool is_local = false;

  AIMPubConvCreateSingleConvParam(std::string appCid_, std::string biz_type_,
                                  std::map<std::string, std::string> ext_,
                                  std::vector<std::string> uids_,
                                  std::map<std::string, std::string> ctx_,
                                  bool is_local_)
      : appCid(std::move(appCid_)), biz_type(std::move(biz_type_)),
        ext(std::move(ext_)), uids(std::move(uids_)), ctx(std::move(ctx_)),
        is_local(std::move(is_local_)) {}

  AIMPubConvCreateSingleConvParam() {}
};

} // namespace dps
} // namespace alibaba
