// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

struct AIMPubMsgHookPreSendMsgResult;

class AIMPubMsgHookPreSendMsgListener {
public:
  virtual ~AIMPubMsgHookPreSendMsgListener() {}

  /**
   * 开始处理回调
   */
  virtual void OnStart() = 0;

  /**
   * 处理进度回调（progress：0.0 ~ 1.0）
   */
  virtual void OnProgress(double progress) = 0;

  /**
   * 处理成功回调（msg：处理完成后的消息体）
   */
  virtual void OnSuccess(const AIMPubMsgHookPreSendMsgResult &msg,
                         bool save_to_local) = 0;

  /**
   * 处理失败回调
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubMsgHookPreSendMsgListenerPtr =
    std::shared_ptr<AIMPubMsgHookPreSendMsgListener>;
using AIMPubMsgHookPreSendMsgListenerWeakPtr =
    std::weak_ptr<AIMPubMsgHookPreSendMsgListener>;

} // namespace dps
} // namespace alibaba
