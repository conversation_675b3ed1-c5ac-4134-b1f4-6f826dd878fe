// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 群禁言/取消群禁言参数
 */
struct AIMPubGroupUpdateSilenceAll final {

  /**
   * 群appCid
   */
  std::string appCid;

  AIMPubGroupUpdateSilenceAll(std::string appCid_)
      : appCid(std::move(appCid_)) {}

  AIMPubGroupUpdateSilenceAll() {}
};

} // namespace dps
} // namespace alibaba
