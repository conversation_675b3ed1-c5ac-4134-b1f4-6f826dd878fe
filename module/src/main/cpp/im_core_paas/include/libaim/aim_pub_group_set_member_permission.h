// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_group_permission.h"
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 群权限,需要业务方自定义permission_group,用来控制群权限（如是否可以加人，改头像，atall）
 */
struct AIMPubGroupSetMemberPermission final {

  /**
   * 群appCid
   */
  std::string appCid;
  /**
   * 权限名
   */
  AIMGroupPermission member_permission;

  AIMPubGroupSetMemberPermission(std::string appCid_,
                                 AIMGroupPermission member_permission_)
      : appCid(std::move(appCid_)),
        member_permission(std::move(member_permission_)) {}

  AIMPubGroupSetMemberPermission() {}
};

} // namespace dps
} // namespace alibaba
