// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_pub_group_user_info.h"
#include <map>
#include <string>
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 创建群参数
 */
struct AIMPubGroupCreateGroupConvParam final {

  /**
   * 群成员信息列表，里面的uid必填
   */
  std::vector<AIMPubGroupUserInfo> user_infos;
  std::string title;
  std::string icon;
  std::string biz_type;
  std::map<std::string, std::string> ext;
  std::map<std::string, std::string> ctx;
  std::map<std::string, std::string> operator_extension;

  AIMPubGroupCreateGroupConvParam(
      std::vector<AIMPubGroupUserInfo> user_infos_, std::string title_,
      std::string icon_, std::string biz_type_,
      std::map<std::string, std::string> ext_,
      std::map<std::string, std::string> ctx_,
      std::map<std::string, std::string> operator_extension_)
      : user_infos(std::move(user_infos_)), title(std::move(title_)),
        icon(std::move(icon_)), biz_type(std::move(biz_type_)),
        ext(std::move(ext_)), ctx(std::move(ctx_)),
        operator_extension(std::move(operator_extension_)) {}

  AIMPubGroupCreateGroupConvParam() {}
};

} // namespace dps
} // namespace alibaba
