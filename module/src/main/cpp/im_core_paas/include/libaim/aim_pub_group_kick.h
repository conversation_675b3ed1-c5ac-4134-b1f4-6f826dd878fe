// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_pub_group_user_info.h"
#include <string>
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 群踢人参数
 */
struct AIMPubGroupKick final {

  /**
   * 群appCid
   */
  std::string appCid;
  /**
   * 群成员
   */
  std::vector<AIMPubGroupUserInfo> users;

  AIMPubGroupKick(std::string appCid_, std::vector<AIMPubGroupUserInfo> users_)
      : appCid(std::move(appCid_)), users(std::move(users_)) {}

  AIMPubGroupKick() {}
};

} // namespace dps
} // namespace alibaba
