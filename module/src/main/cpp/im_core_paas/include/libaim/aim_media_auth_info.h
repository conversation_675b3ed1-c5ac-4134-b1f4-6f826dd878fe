// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_group_avator_media_auth_info.h"
#include "aim_media_auth_scene.h"
#include "aim_msg_media_auth_info.h"
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 鉴权信息
 */
struct AIMMediaAuthInfo final {

  AIMMediaAuthScene scene = AIMMediaAuthScene::MAC_UNKNOWN;
  std::string biz_type;
  AIMGroupAvatorMediaAuthInfo group_avator_auth;
  AIMMsgMediaAuthInfo msg_auth;

  AIMMediaAuthInfo(AIMMediaAuthScene scene_, std::string biz_type_,
                   AIMGroupAvatorMediaAuthInfo group_avator_auth_,
                   AIMMsgMediaAuthInfo msg_auth_)
      : scene(std::move(scene_)), biz_type(std::move(biz_type_)),
        group_avator_auth(std::move(group_avator_auth_)),
        msg_auth(std::move(msg_auth_)) {}

  AIMMediaAuthInfo() {}
};

} // namespace dps
} // namespace alibaba
