// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

struct AIMPubGroupAnnouncement final {

  /**
   * 公告文本，最大2048字节
   */
  std::string announcement;
  /**
   * 公告的操作者
   */
  std::string operator_uid;
  /**
   * 公告的修改事件，单位毫秒
   */
  int64_t modify_time = 0;

  AIMPubGroupAnnouncement(std::string announcement_, std::string operator_uid_,
                          int64_t modify_time_)
      : announcement(std::move(announcement_)),
        operator_uid(std::move(operator_uid_)),
        modify_time(std::move(modify_time_)) {}

  AIMPubGroupAnnouncement() {}
};

} // namespace dps
} // namespace alibaba
