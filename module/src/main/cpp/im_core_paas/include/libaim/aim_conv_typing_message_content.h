// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class AIMConvTypingMessageContent : int {
  CONV_TYPING_MESSAGE_TYPE_UNKNOWN = 0,
  CONV_TYPING_MESSAGE_TYPE_TEXT = 1,
  CONV_TYPING_MESSAGE_TYPE_AUDIO = 2,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::dps::AIMConvTypingMessageContent> {
  size_t operator()(::alibaba::dps::AIMConvTypingMessageContent type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
