// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

class AIMMessageDeletionCallback {
public:
  virtual ~AIMMessageDeletionCallback() {}

  virtual void OnSuccess() = 0;

  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMMessageDeletionCallbackPtr =
    std::shared_ptr<AIMMessageDeletionCallback>;
using AIMMessageDeletionCallbackWeakPtr =
    std::weak_ptr<AIMMessageDeletionCallback>;

} // namespace dps
} // namespace alibaba
