// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 消息内容过滤条件
 */
struct AIMMsgContentFilter final {

  /**
   * 消息类型（必填）
   */
  int32_t content_type = -1;
  /**
   * 自定义消息子类型（可选，内容类型int32_t, 如果不选择为空）
   */
  std::string custom_content_type;

  AIMMsgContentFilter(int32_t content_type_, std::string custom_content_type_)
      : content_type(std::move(content_type_)),
        custom_content_type(std::move(custom_content_type_)) {}

  AIMMsgContentFilter() {}
};

} // namespace dps
} // namespace alibaba
