// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <cstdint>
#include <functional>
#include <memory>
#include <vector>

namespace alibaba {
namespace dps {

struct AIMPubConversation;

/**
 * 获取会话列表监听[附带额外信息]
 */
class AIMPubConvGetConvWithParamListener {
public:
  virtual ~AIMPubConvGetConvWithParamListener() {}

  /**
   * 获取成功
   */
  virtual void OnSuccess(const std::vector<AIMPubConversation> &result,
                         int64_t next_cursor, bool has_more) = 0;

  /**
   * 获取失败
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubConvGetConvWithParamListenerPtr =
    std::shared_ptr<AIMPubConvGetConvWithParamListener>;
using AIMPubConvGetConvWithParamListenerWeakPtr =
    std::weak_ptr<AIMPubConvGetConvWithParamListener>;

} // namespace dps
} // namespace alibaba
