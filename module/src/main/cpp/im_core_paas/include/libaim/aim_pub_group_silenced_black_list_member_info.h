// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 禁言黑名单成员信息
 */
struct AIMPubGroupSilencedBlackListMemberInfo final {

  /**
   * 操作者uid
   */
  std::string uid;
  /**
   * 禁言截止时间 单位ms
   */
  int64_t end_time = 0;
  /**
   * 操作时间 单位ms
   */
  int64_t operate_time = 0;

  AIMPubGroupSilencedBlackListMemberInfo(std::string uid_, int64_t end_time_,
                                         int64_t operate_time_)
      : uid(std::move(uid_)), end_time(std::move(end_time_)),
        operate_time(std::move(operate_time_)) {}

  AIMPubGroupSilencedBlackListMemberInfo() {}
};

} // namespace dps
} // namespace alibaba
