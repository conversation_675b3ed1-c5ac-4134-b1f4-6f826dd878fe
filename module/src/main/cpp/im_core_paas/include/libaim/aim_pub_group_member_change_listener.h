// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>
#include <memory>
#include <vector>

namespace alibaba {
namespace dps {

struct AIMPubGroupMember;

/**
 * 会话成员增量变更的监听器
 */
class AIMPubGroupMemberChangeListener {
public:
  virtual ~AIMPubGroupMemberChangeListener() {}

  /**
   * 新增成员
   * @param members 成员
   */
  virtual void
  OnAddedMembers(const std::vector<AIMPubGroupMember> &members) = 0;

  /**
   * 删除成员
   * @param members 成员
   */
  virtual void
  OnRemovedMembers(const std::vector<AIMPubGroupMember> &members) = 0;

  /**
   * 成员变更
   * @param members 成员
   */
  virtual void
  OnUpdatedMembers(const std::vector<AIMPubGroupMember> &members) = 0;
};

using AIMPubGroupMemberChangeListenerPtr =
    std::shared_ptr<AIMPubGroupMemberChangeListener>;
using AIMPubGroupMemberChangeListenerWeakPtr =
    std::weak_ptr<AIMPubGroupMemberChangeListener>;

} // namespace dps
} // namespace alibaba
