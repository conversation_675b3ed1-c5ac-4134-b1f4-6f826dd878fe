// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_pub_message.h"
#include "aim_search_highlight_range.h"
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 消息内容搜索返回结果
 */
struct AIMPubSearchChatResult final {

  /**
   * 消息体
   */
  AIMPubMessage message;
  /**
   * 高亮信息
   */
  std::vector<AIMSearchHighlightRange> ranges;

  AIMPubSearchChatResult(AIMPubMessage message_,
                         std::vector<AIMSearchHighlightRange> ranges_)
      : message(std::move(message_)), ranges(std::move(ranges_)) {}

  AIMPubSearchChatResult() {}
};

} // namespace dps
} // namespace alibaba
