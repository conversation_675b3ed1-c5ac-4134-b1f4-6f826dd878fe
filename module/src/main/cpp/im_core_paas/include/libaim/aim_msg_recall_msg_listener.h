// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

/**
 * 撤回消息监听
 */
class AIMMsgRecallMsgListener {
public:
  virtual ~AIMMsgRecallMsgListener() {}

  /**
   * 撤回成功
   */
  virtual void OnSuccess() = 0;

  /**
   * 撤回失败
   * @param error 错误信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMMsgRecallMsgListenerPtr = std::shared_ptr<AIMMsgRecallMsgListener>;
using AIMMsgRecallMsgListenerWeakPtr = std::weak_ptr<AIMMsgRecallMsgListener>;

} // namespace dps
} // namespace alibaba
