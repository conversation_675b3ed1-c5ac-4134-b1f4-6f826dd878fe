// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

struct AIMSearchGroupParams final {

  /**
   * 搜索关键字（群名称）
   */
  std::string keyword;
  /**
   * 返回结果起始位置，用于搜索结果分页，offset >= 0，默认为第一页
   */
  int32_t offset = 0;
  /**
   * 单次返回结果个数， 0 < max_num <= 1000, 默认为20个
   */
  int32_t max_num = 20;
  /**
   * 查询条件：按会话创建时间开始, (时间标准:time_since_epoch)
   */
  int64_t start_time = 0;
  /**
   * 查询条件：按会啊话创建时间结束, 默认为所有消息
   */
  int64_t end_time = 9223372036854775807;
  /**
   * 返回结果是否按会话最近更新时间从小到大排列，默认为升序
   */
  bool is_asc = true;

  AIMSearchGroupParams(std::string keyword_, int32_t offset_, int32_t max_num_,
                       int64_t start_time_, int64_t end_time_, bool is_asc_)
      : keyword(std::move(keyword_)), offset(std::move(offset_)),
        max_num(std::move(max_num_)), start_time(std::move(start_time_)),
        end_time(std::move(end_time_)), is_asc(std::move(is_asc_)) {}

  AIMSearchGroupParams() {}
};

} // namespace dps
} // namespace alibaba
