// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 更新群标题参数
 */
struct AIMPubGroupUpdateTitle final {

  /**
   * 群appCid
   */
  std::string appCid;
  /**
   * 群标题
   */
  std::string title;

  AIMPubGroupUpdateTitle(std::string appCid_, std::string title_)
      : appCid(std::move(appCid_)), title(std::move(title_)) {}

  AIMPubGroupUpdateTitle() {}
};

} // namespace dps
} // namespace alibaba
