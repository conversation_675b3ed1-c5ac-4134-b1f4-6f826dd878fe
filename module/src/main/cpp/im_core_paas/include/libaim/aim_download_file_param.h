// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 多媒体下载相关参数
 */
struct AIMDownloadFileParam final {

  std::string download_url;
  std::string path;

  AIMDownloadFileParam(std::string download_url_, std::string path_)
      : download_url(std::move(download_url_)), path(std::move(path_)) {}

  AIMDownloadFileParam() {}
};

} // namespace dps
} // namespace alibaba
