// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_pub_group_user_info.h"
#include <cstdint>
#include <string>
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 增加/取消黑名单用户
 */
struct AIMPubGroupUpdateSilencedBlackList final {

  /**
   * 群appCid
   */
  std::string appCid;
  /**
   * 操作成员
   */
  std::vector<AIMPubGroupUserInfo> members;
  /**
   * 禁言时长 单位ms
   */
  int64_t duration = 0;

  AIMPubGroupUpdateSilencedBlackList(std::string appCid_,
                                     std::vector<AIMPubGroupUserInfo> members_,
                                     int64_t duration_)
      : appCid(std::move(appCid_)), members(std::move(members_)),
        duration(std::move(duration_)) {}

  AIMPubGroupUpdateSilencedBlackList() {}
};

} // namespace dps
} // namespace alibaba
