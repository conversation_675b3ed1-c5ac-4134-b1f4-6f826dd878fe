// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 群权限,需要业务方自定义permission_group,用来控制群权限（如是否可以加人，改头像，atall）
 */
struct AIMGroupPermission final {

  /**
   * 权限名
   */
  std::string permission_group;
  /**
   * 0:权限关闭 1:权限开启 2:权限废弃
   */
  int32_t status = 0;

  AIMGroupPermission(std::string permission_group_, int32_t status_)
      : permission_group(std::move(permission_group_)),
        status(std::move(status_)) {}

  AIMGroupPermission() {}
};

} // namespace dps
} // namespace alibaba
