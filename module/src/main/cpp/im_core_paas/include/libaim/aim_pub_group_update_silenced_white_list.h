// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_pub_group_user_info.h"
#include <string>
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 增加/取消白名单用户
 */
struct AIMPubGroupUpdateSilencedWhiteList final {

  /**
   * 群appCid
   */
  std::string appCid;
  /**
   * 操作成员
   */
  std::vector<AIMPubGroupUserInfo> members;

  AIMPubGroupUpdateSilencedWhiteList(std::string appCid_,
                                     std::vector<AIMPubGroupUserInfo> members_)
      : appCid(std::move(appCid_)), members(std::move(members_)) {}

  AIMPubGroupUpdateSilencedWhiteList() {}
};

} // namespace dps
} // namespace alibaba
