// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_msg_audio_content.h"
#include "aim_msg_custom_content.h"
#include "aim_msg_file_content.h"
#include "aim_msg_geo_content.h"
#include "aim_msg_image_content.h"
#include "aim_msg_video_content.h"
#include "aim_pub_msg_struct_content.h"
#include "aim_pub_msg_text_content.h"
#include <utility>

namespace alibaba {
namespace dps {

struct AIMPubMsgSimpleContent final {

  AIMPubMsgTextContent text_content;
  AIMMsgImageContent image_content;
  AIMMsgAudioContent audio_content;
  AIMMsgVideoContent video_content;
  AIMMsgGeoContent geo_content;
  AIMMsgCustomContent custom_content;
  AIMPubMsgStructContent struct_content;
  AIMMsgFileContent file_content;

  AIMPubMsgSimpleContent(
      AIMPubMsgTextContent text_content_, AIMMsgImageContent image_content_,
      AIMMsgAudioContent audio_content_, AIMMsgVideoContent video_content_,
      AIMMsgGeoContent geo_content_, AIMMsgCustomContent custom_content_,
      AIMPubMsgStructContent struct_content_, AIMMsgFileContent file_content_)
      : text_content(std::move(text_content_)),
        image_content(std::move(image_content_)),
        audio_content(std::move(audio_content_)),
        video_content(std::move(video_content_)),
        geo_content(std::move(geo_content_)),
        custom_content(std::move(custom_content_)),
        struct_content(std::move(struct_content_)),
        file_content(std::move(file_content_)) {}

  AIMPubMsgSimpleContent() {}
};

} // namespace dps
} // namespace alibaba
