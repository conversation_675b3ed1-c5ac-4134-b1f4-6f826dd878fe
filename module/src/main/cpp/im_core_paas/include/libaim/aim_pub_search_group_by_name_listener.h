// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <cstdint>
#include <functional>
#include <memory>
#include <vector>

namespace alibaba {
namespace dps {

struct AIMPubConversation;

/**
 * SearchConversationByContent监听
 */
class AIMPubSearchGroupByNameListener {
public:
  virtual ~AIMPubSearchGroupByNameListener() {}

  virtual void OnSuccess(const std::vector<AIMPubConversation> &result,
                         int32_t total_count) = 0;

  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubSearchGroupByNameListenerPtr =
    std::shared_ptr<AIMPubSearchGroupByNameListener>;
using AIMPubSearchGroupByNameListenerWeakPtr =
    std::weak_ptr<AIMPubSearchGroupByNameListener>;

} // namespace dps
} // namespace alibaba
