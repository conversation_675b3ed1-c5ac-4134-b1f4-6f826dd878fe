// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_msg_content_type.h"
#include "aim_pub_msg_simple_content.h"
#include <utility>

namespace alibaba {
namespace dps {

struct AIMPubMsgReferenceContent final {

  AIMMsgContentType content_type = AIMMsgContentType::CONTENT_TYPE_UNKNOW;
  AIMPubMsgSimpleContent content;

  AIMPubMsgReferenceContent(AIMMsgContentType content_type_,
                            AIMPubMsgSimpleContent content_)
      : content_type(std::move(content_type_)), content(std::move(content_)) {}

  AIMPubMsgReferenceContent() {}
};

} // namespace dps
} // namespace alibaba
