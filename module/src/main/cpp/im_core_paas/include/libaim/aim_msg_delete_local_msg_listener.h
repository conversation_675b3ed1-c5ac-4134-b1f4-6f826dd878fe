// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

/**
 * 删除本地消息监听
 */
class AIMMsgDeleteLocalMsgListener {
public:
  virtual ~AIMMsgDeleteLocalMsgListener() {}

  /**
   * 成功
   */
  virtual void OnSuccess() = 0;

  /**
   * 失败
   * @param error 错误信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMMsgDeleteLocalMsgListenerPtr =
    std::shared_ptr<AIMMsgDeleteLocalMsgListener>;
using AIMMsgDeleteLocalMsgListenerWeakPtr =
    std::weak_ptr<AIMMsgDeleteLocalMsgListener>;

} // namespace dps
} // namespace alibaba
