// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 群消息鉴权
 */
struct AIMMsgMediaAuthInfo final {

  std::string cid;
  std::string mid;

  AIMMsgMediaAuthInfo(std::string cid_, std::string mid_)
      : cid(std::move(cid_)), mid(std::move(mid_)) {}

  AIMMsgMediaAuthInfo() {}
};

} // namespace dps
} // namespace alibaba
