// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_msg_content_type.h"
#include "aim_pub_msg_reply_content.h"
#include "aim_pub_msg_simple_content.h"
#include <utility>

namespace alibaba {
namespace dps {

struct AIMPubMsgInnerCombineContent final {

  AIMMsgContentType content_type = AIMMsgContentType::CONTENT_TYPE_UNKNOW;
  AIMPubMsgSimpleContent simple_content;
  AIMPubMsgReplyContent reply_content;

  AIMPubMsgInnerCombineContent(AIMMsgContentType content_type_,
                               AIMPubMsgSimpleContent simple_content_,
                               AIMPubMsgReplyContent reply_content_)
      : content_type(std::move(content_type_)),
        simple_content(std::move(simple_content_)),
        reply_content(std::move(reply_content_)) {}

  AIMPubMsgInnerCombineContent() {}
};

} // namespace dps
} // namespace alibaba
