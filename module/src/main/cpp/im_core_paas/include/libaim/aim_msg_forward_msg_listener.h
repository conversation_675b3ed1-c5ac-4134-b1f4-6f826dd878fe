// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

/**
 * 转发发送消息监听
 */
class AIMMsgForwardMsgListener {
public:
  virtual ~AIMMsgForwardMsgListener() {}

  /**
   * 处理成功
   */
  virtual void OnSuccess() = 0;

  /**
   * 处理失败，返回失败原因
   * @param error 失败信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMMsgForwardMsgListenerPtr = std::shared_ptr<AIMMsgForwardMsgListener>;
using AIMMsgForwardMsgListenerWeakPtr = std::weak_ptr<AIMMsgForwardMsgListener>;

} // namespace dps
} // namespace alibaba
