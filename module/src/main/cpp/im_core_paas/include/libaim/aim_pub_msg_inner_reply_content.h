// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_msg_content_type.h"
#include "aim_pub_msg_simple_content.h"
#include <utility>

namespace alibaba {
namespace dps {

struct AIMPubMsgInnerReplyContent final {

  AIMMsgContentType content_type = AIMMsgContentType::CONTENT_TYPE_UNKNOW;
  AIMPubMsgSimpleContent content;

  AIMPubMsgInnerReplyContent(AIMMsgContentType content_type_,
                             AIMPubMsgSimpleContent content_)
      : content_type(std::move(content_type_)), content(std::move(content_)) {}

  AIMPubMsgInnerReplyContent() {}
};

} // namespace dps
} // namespace alibaba
