// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_pub_group_user_info.h"
#include <string>
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 群加人参数
 */
struct AIMPubGroupJoin final {

  /**
   * 群appCid
   */
  std::string appCid;
  /**
   * 群成员
   */
  std::vector<AIMPubGroupUserInfo> users;

  AIMPubGroupJoin(std::string appCid_, std::vector<AIMPubGroupUserInfo> users_)
      : appCid(std::move(appCid_)), users(std::move(users_)) {}

  AIMPubGroupJoin() {}
};

} // namespace dps
} // namespace alibaba
