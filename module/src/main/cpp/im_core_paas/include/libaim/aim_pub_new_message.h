// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_msg_source_type.h"
#include "aim_pub_message.h"
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 添加的消息
 */
struct AIMPubNewMessage final {

  AIMPubMessage msg;
  AIMMsgSourceType type = AIMMsgSourceType::SOURCE_TYPE_UNKNOWN;

  AIMPubNewMessage(AIMPubMessage msg_, AIMMsgSourceType type_)
      : msg(std::move(msg_)), type(std::move(type_)) {}

  AIMPubNewMessage() {}
};

} // namespace dps
} // namespace alibaba
