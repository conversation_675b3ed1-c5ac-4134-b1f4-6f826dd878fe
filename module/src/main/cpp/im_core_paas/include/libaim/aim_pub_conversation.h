// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_conv_status.h"
#include "aim_conv_type.h"
#include "aim_group_permission.h"
#include "aim_group_silenced_status.h"
#include "aim_pub_message.h"
#include <cstdint>
#include <map>
#include <string>
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 会话信息
 */
struct AIMPubConversation final {

  std::string appCid;
  /**
   * type=1单聊
   */
  AIMConvType type = AIMConvType::CONV_TYPE_UNKNOW;
  /**
   * 业务类型
   */
  std::string biz_type;
  /**
   * 会话状态，比如是否可见
   */
  AIMConvStatus status = AIMConvStatus::CONV_STATUS_UNKNOW;
  /**
   * 单聊双方的uid（群聊的uid由单独接口提供）
   */
  std::vector<std::string> userids;
  /**
   * 创建时间
   */
  int64_t created_at = 0;
  /**
   * 修改时间（可用于会话列表排序，目前会变更modify_time的是lastmsg变更和置顶）
   */
  int64_t modify_time = 0;
  /**
   * 未读消息数
   */
  int32_t red_point = 0;
  /**
   * 草稿
   */
  std::string draft;
  /**
   * 是否免打扰
   */
  bool mute_notification = false;
  /**
   * 置顶 （>0表示置顶，越大越靠前）
   */
  int64_t top_rank = 0;
  /**
   * 扩展信息
   */
  std::map<std::string, std::string> extension;
  /**
   * user扩展信息
   */
  std::map<std::string, std::string> user_extension;
  /**
   * 本地扩展信息
   */
  std::map<std::string, std::string> local_extension;
  /**
   * 会话是否有最后一条消息
   */
  bool has_last_msg = false;
  /**
   * 会话最后一条消息（如果其中mid为空，则没有最后一条消息）
   */
  AIMPubMessage last_msg;
  /**
   * 会话加入时间
   */
  int64_t join_time = 0;
  /**
   * 群主uid
   */
  std::string owner_uid;
  /**
   * 群标题
   */
  std::string title;
  /**
   * 群头像
   */
  std::string icon;
  /**
   * 群人数
   */
  int32_t member_count = 0;
  /**
   * 群人数限制
   */
  int32_t member_limit = 0;
  /**
   * 群禁言
   */
  bool silence_all = false;
  /**
   * 禁言状态
   */
  AIMGroupSilencedStatus silenced_status =
      AIMGroupSilencedStatus::GROUP_SILENCE_STATUS_NORMAL;
  /**
   * 禁言截止时间（ms）
   */
  int64_t silenced_endtime = 0;
  /**
   * 群管理员uid
   */
  std::vector<std::string> admins;
  /**
   * 群权限群成员权限
   */
  std::vector<AIMGroupPermission> member_permissions;
  /**
   * 是否支持已读回执
   */
  bool read_receipts_enabled = true;
  /**
   * 入口会话id (此字段不为空时，表示当前会话为该入口会话的二级会话)
   */
  std::string entrance_app_cid;

  AIMPubConversation(
      std::string appCid_, AIMConvType type_, std::string biz_type_,
      AIMConvStatus status_, std::vector<std::string> userids_,
      int64_t created_at_, int64_t modify_time_, int32_t red_point_,
      std::string draft_, bool mute_notification_, int64_t top_rank_,
      std::map<std::string, std::string> extension_,
      std::map<std::string, std::string> user_extension_,
      std::map<std::string, std::string> local_extension_, bool has_last_msg_,
      AIMPubMessage last_msg_, int64_t join_time_, std::string owner_uid_,
      std::string title_, std::string icon_, int32_t member_count_,
      int32_t member_limit_, bool silence_all_,
      AIMGroupSilencedStatus silenced_status_, int64_t silenced_endtime_,
      std::vector<std::string> admins_,
      std::vector<AIMGroupPermission> member_permissions_,
      bool read_receipts_enabled_, std::string entrance_app_cid_)
      : appCid(std::move(appCid_)), type(std::move(type_)),
        biz_type(std::move(biz_type_)), status(std::move(status_)),
        userids(std::move(userids_)), created_at(std::move(created_at_)),
        modify_time(std::move(modify_time_)), red_point(std::move(red_point_)),
        draft(std::move(draft_)),
        mute_notification(std::move(mute_notification_)),
        top_rank(std::move(top_rank_)), extension(std::move(extension_)),
        user_extension(std::move(user_extension_)),
        local_extension(std::move(local_extension_)),
        has_last_msg(std::move(has_last_msg_)), last_msg(std::move(last_msg_)),
        join_time(std::move(join_time_)), owner_uid(std::move(owner_uid_)),
        title(std::move(title_)), icon(std::move(icon_)),
        member_count(std::move(member_count_)),
        member_limit(std::move(member_limit_)),
        silence_all(std::move(silence_all_)),
        silenced_status(std::move(silenced_status_)),
        silenced_endtime(std::move(silenced_endtime_)),
        admins(std::move(admins_)),
        member_permissions(std::move(member_permissions_)),
        read_receipts_enabled(std::move(read_receipts_enabled_)),
        entrance_app_cid(std::move(entrance_app_cid_)) {}

  AIMPubConversation() {}
};

} // namespace dps
} // namespace alibaba
