// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 禁言白名单成员信息
 */
struct AIMPubGroupSilencedWhiteListMemberInfo final {

  /**
   * 操作者uid
   */
  std::string uid;
  /**
   * 操作时间 单位ms
   */
  int64_t operate_time = 0;

  AIMPubGroupSilencedWhiteListMemberInfo(std::string uid_,
                                         int64_t operate_time_)
      : uid(std::move(uid_)), operate_time(std::move(operate_time_)) {}

  AIMPubGroupSilencedWhiteListMemberInfo() {}
};

} // namespace dps
} // namespace alibaba
