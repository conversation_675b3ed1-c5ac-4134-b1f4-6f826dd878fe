// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

struct AIMPubMessage;

/**
 * 消息重发回调
 */
class AIMPubMsgReSendMsgListener {
public:
  virtual ~AIMPubMsgReSendMsgListener() {}

  /**
   * 发送进度（图片、视频、文件）progress: 0.0~1.0
   * @param progress 进度 0.0~1.0
   */
  virtual void OnProgress(double progress) = 0;

  /**
   * 处理成功
   * @param msg 发送成功的消息
   */
  virtual void OnSuccess(const AIMPubMessage &msg) = 0;

  /**
   * 处理失败，返回失败原因
   * @param error 失败信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubMsgReSendMsgListenerPtr =
    std::shared_ptr<AIMPubMsgReSendMsgListener>;
using AIMPubMsgReSendMsgListenerWeakPtr =
    std::weak_ptr<AIMPubMsgReSendMsgListener>;

} // namespace dps
} // namespace alibaba
