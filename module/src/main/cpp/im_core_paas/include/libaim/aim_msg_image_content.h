// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_msg_image_compress_type.h"
#include "aim_msg_image_file_type.h"
#include "aim_msg_orientation.h"
#include <cstdint>
#include <map>
#include <string>
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 * 图片消息
 */
struct AIMMsgImageContent final {

  /**
   * 图片本地路径（发送方）
   */
  std::string local_path;
  /**
   * 上传本地路径（发送方）
   */
  std::string upload_path;
  /**
   * 上传媒体类型（发送方）
   */
  std::string mime_type;
  /**
   * 图片url
   */
  std::string original_url;
  /**
   * 缩略图url
   */
  std::string thumbnail_url;
  /**
   * 模糊图数据
   */
  std::vector<uint8_t> blurred_data;
  /**
   * media id
   */
  std::string media_id;
  /**
   * 图片名字
   */
  std::string file_name;
  /**
   * 高(像素)
   */
  int32_t height = -1;
  /**
   * 宽(像素)
   */
  int32_t width = -1;
  /**
   * 文件大小(B)
   */
  int32_t size = -1;
  /**
   * 图片类型 AIMMsgImageType
   */
  AIMMsgImageCompressType type =
      AIMMsgImageCompressType::IMAGE_COMPRESS_TYPE_UNKNOWN;
  /**
   * 文件类型 AIMMsgImageFileType
   */
  AIMMsgImageFileType file_type = AIMMsgImageFileType::IMAGE_FILE_TYPE_UNKNOWN;
  /**
   * 旋转类型 AIMMsgOrientation
   */
  AIMMsgOrientation orientation = AIMMsgOrientation::ORIENTATION_UNKNOWN;
  /**
   * 扩展信息
   */
  std::map<std::string, std::string> extension;

  AIMMsgImageContent(std::string local_path_, std::string upload_path_,
                     std::string mime_type_, std::string original_url_,
                     std::string thumbnail_url_,
                     std::vector<uint8_t> blurred_data_, std::string media_id_,
                     std::string file_name_, int32_t height_, int32_t width_,
                     int32_t size_, AIMMsgImageCompressType type_,
                     AIMMsgImageFileType file_type_,
                     AIMMsgOrientation orientation_,
                     std::map<std::string, std::string> extension_)
      : local_path(std::move(local_path_)),
        upload_path(std::move(upload_path_)), mime_type(std::move(mime_type_)),
        original_url(std::move(original_url_)),
        thumbnail_url(std::move(thumbnail_url_)),
        blurred_data(std::move(blurred_data_)), media_id(std::move(media_id_)),
        file_name(std::move(file_name_)), height(std::move(height_)),
        width(std::move(width_)), size(std::move(size_)),
        type(std::move(type_)), file_type(std::move(file_type_)),
        orientation(std::move(orientation_)), extension(std::move(extension_)) {
  }

  AIMMsgImageContent() {}
};

} // namespace dps
} // namespace alibaba
