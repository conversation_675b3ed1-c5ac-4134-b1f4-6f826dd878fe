// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

struct AIMPubMessage;

/**
 * 合并转发发送消息监听
 */
class AIMPubMsgCombineForwardMsgListener {
public:
  virtual ~AIMPubMsgCombineForwardMsgListener() {}

  /**
   * 处理成功
   */
  virtual void OnSuccess(const AIMPubMessage &msg) = 0;

  /**
   * 处理失败，返回失败原因
   * @param error 失败信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubMsgCombineForwardMsgListenerPtr =
    std::shared_ptr<AIMPubMsgCombineForwardMsgListener>;
using AIMPubMsgCombineForwardMsgListenerWeakPtr =
    std::weak_ptr<AIMPubMsgCombineForwardMsgListener>;

} // namespace dps
} // namespace alibaba
