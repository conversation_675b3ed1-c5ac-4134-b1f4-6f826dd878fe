// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 退出群参数
 */
struct AIMPubGroupLeave final {

  /**
   * 群appCid
   */
  std::string appCid;

  AIMPubGroupLeave(std::string appCid_) : appCid(std::move(appCid_)) {}

  AIMPubGroupLeave() {}
};

} // namespace dps
} // namespace alibaba
