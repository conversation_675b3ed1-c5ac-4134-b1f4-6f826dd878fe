// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <string>
#include <utility>

namespace alibaba {
namespace dps {

struct AIMMsgXpnPush final {

  std::string template_id;
  std::string sender_nick;

  AIMMsgXpnPush(std::string template_id_, std::string sender_nick_)
      : template_id(std::move(template_id_)),
        sender_nick(std::move(sender_nick_)) {}

  AIMMsgXpnPush() {}
};

} // namespace dps
} // namespace alibaba
