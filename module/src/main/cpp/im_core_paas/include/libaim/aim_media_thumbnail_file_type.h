// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class AIMMediaThumbnailFileType : int {
  IMAGE_FILE_TYPE_UNKNOWN = -1,
  IMAGE_FILE_TYPE_JPG = 0,
  IMAGE_FILE_TYPE_GIF = 1,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::dps::AIMMediaThumbnailFileType> {
  size_t operator()(::alibaba::dps::AIMMediaThumbnailFileType type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
