// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_full_link_point_base.h"
#include "aim_trace_conv_info.h"
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

/**
 *会话链路埋点字段
 */
struct AIMFullLinkPointConv final {

  AIMFullLinkPointBase base_point;
  std::vector<AIMTraceConvInfo> conv_infos;

  AIMFullLinkPointConv(AIMFullLinkPointBase base_point_,
                       std::vector<AIMTraceConvInfo> conv_infos_)
      : base_point(std::move(base_point_)), conv_infos(std::move(conv_infos_)) {
  }

  AIMFullLinkPointConv() {}
};

} // namespace dps
} // namespace alibaba
