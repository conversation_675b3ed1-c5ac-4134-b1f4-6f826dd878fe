// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <cstdint>
#include <functional>
#include <memory>
#include <string>

namespace alibaba {
namespace dps {

/**
 * 多媒体上传状态监听
 */
class AIMDownloadFileListener {
public:
  virtual ~AIMDownloadFileListener() {}

  /**
   *  创建回调
   */
  virtual void OnCreate(const std::string &task_id) = 0;

  /**
   * 开始处理回调
   */
  virtual void OnStart() = 0;

  /**
   * 处理进度回调
   */
  virtual void OnProgress(int64_t current_size, int64_t total_size) = 0;

  /**
   * 处理成功回调（path：保存路径）
   */
  virtual void OnSuccess(const std::string &path) = 0;

  /**
   * 处理失败回调
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMDownloadFileListenerPtr = std::shared_ptr<AIMDownloadFileListener>;
using AIMDownloadFileListenerWeakPtr = std::weak_ptr<AIMDownloadFileListener>;

} // namespace dps
} // namespace alibaba
