// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

struct AIMPubGroupAnnouncement;

/**
 * 获取群公告的回调
 */
class AIMPubGroupGetAnnouncementListener {
public:
  virtual ~AIMPubGroupGetAnnouncementListener() {}

  /**
   * 成功回调
   */
  virtual void OnSuccess(const AIMPubGroupAnnouncement &announcement) = 0;

  /**
   * 失败回调
   * @param error 错误信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubGroupGetAnnouncementListenerPtr =
    std::shared_ptr<AIMPubGroupGetAnnouncementListener>;
using AIMPubGroupGetAnnouncementListenerWeakPtr =
    std::weak_ptr<AIMPubGroupGetAnnouncementListener>;

} // namespace dps
} // namespace alibaba
