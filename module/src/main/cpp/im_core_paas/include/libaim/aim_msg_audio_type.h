// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class AIMMsgAudioType : int {
  AUDIO_TYPE_UNKNOWN = -1,
  AUDIO_TYPE_OPUS = 0,
  AUDIO_TYPE_OGG = 1,
  AUDIO_TYPE_AMR = 2,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::dps::AIMMsgAudioType> {
  size_t operator()(::alibaba::dps::AIMMsgAudioType type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
