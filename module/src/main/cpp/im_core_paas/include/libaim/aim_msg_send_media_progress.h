// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 发送多媒体信息进度
 */
struct AIMMsgSendMediaProgress final {

  std::string cid;
  std::string localid;
  double progress = 0;

  AIMMsgSendMediaProgress(std::string cid_, std::string localid_,
                          double progress_)
      : cid(std::move(cid_)), localid(std::move(localid_)),
        progress(std::move(progress_)) {}

  AIMMsgSendMediaProgress() {}
};

} // namespace dps
} // namespace alibaba
