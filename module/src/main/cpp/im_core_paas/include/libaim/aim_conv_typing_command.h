// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class AIMConvTypingCommand : int {
  CONV_TYPING_COMMAND_UNKNOWN = -1,
  CONV_TYPING_COMMAND_TYPING = 0,
  CONV_TYPING_COMMAND_CANCEL = 1,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::dps::AIMConvTypingCommand> {
  size_t operator()(::alibaba::dps::AIMConvTypingCommand type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
