// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <map>
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 文本消息内容
 */
struct AIMPubMsgTextContent final {

  /**
   * 内容
   */
  std::string text;
  /**
   * 加密内容
   */
  std::string encrypted_text;
  /**
   * 扩展内容
   */
  std::map<std::string, std::string> extension;

  AIMPubMsgTextContent(std::string text_, std::string encrypted_text_,
                       std::map<std::string, std::string> extension_)
      : text(std::move(text_)), encrypted_text(std::move(encrypted_text_)),
        extension(std::move(extension_)) {}

  AIMPubMsgTextContent() {}
};

} // namespace dps
} // namespace alibaba
