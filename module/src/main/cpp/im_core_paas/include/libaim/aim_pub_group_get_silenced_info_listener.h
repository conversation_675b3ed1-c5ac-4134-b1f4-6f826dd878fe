// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

struct AIMPubGroupSilencedInfo;

/**
 * 拉取禁言列表监听
 */
class AIMPubGroupGetSilencedInfoListener {
public:
  virtual ~AIMPubGroupGetSilencedInfoListener() {}

  /**
   * 返回成功
   * @param silenced_info 禁言列表
   */
  virtual void OnSuccess(const AIMPubGroupSilencedInfo &silenced_info) = 0;

  /**
   * 返回统一失败结果
   * @param error 错误信息
   */
  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMPubGroupGetSilencedInfoListenerPtr =
    std::shared_ptr<AIMPubGroupGetSilencedInfoListener>;
using AIMPubGroupGetSilencedInfoListenerWeakPtr =
    std::weak_ptr<AIMPubGroupGetSilencedInfoListener>;

} // namespace dps
} // namespace alibaba
