// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "dps_error.h"
#include <functional>
#include <memory>

namespace alibaba {
namespace dps {

class AIMSetLastMsgCallback {
public:
  virtual ~AIMSetLastMsgCallback() {}

  virtual void OnSuccess() = 0;

  virtual void OnFailure(const ::alibaba::dps::DPSError &error) = 0;
};

using AIMSetLastMsgCallbackPtr = std::shared_ptr<AIMSetLastMsgCallback>;
using AIMSetLastMsgCallbackWeakPtr = std::weak_ptr<AIMSetLastMsgCallback>;

} // namespace dps
} // namespace alibaba
