// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_pub_msg_content.h"
#include <cstdint>
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

struct AIMPubLiteMessage final {

  std::string appCid;
  std::string mid;
  std::string localid;
  AIMPubMsgContent content;
  int64_t created_at = -1;

  AIMPubLiteMessage(std::string appCid_, std::string mid_, std::string localid_,
                    AIMPubMsgContent content_, int64_t created_at_)
      : appCid(std::move(appCid_)), mid(std::move(mid_)),
        localid(std::move(localid_)), content(std::move(content_)),
        created_at(std::move(created_at_)) {}

  AIMPubLiteMessage() {}
};

} // namespace dps
} // namespace alibaba
