// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <string>
#include <utility>

namespace alibaba {
namespace dps {

/**
 * 文件消息
 */
struct AIMMsgFileContent final {

  /**
   * 本地路径
   */
  std::string local_path;
  /**
   * 文件名称
   */
  std::string file_name;
  /**
   * 文件类型
   */
  std::string file_type;
  /**
   * media
   */
  std::string media_id;
  /**
   * 上传媒体类型（发送方）
   */
  std::string mime_type;
  /**
   * url
   */
  std::string url;
  /**
   * 文件大小
   */
  int64_t file_size = 0;

  AIMMsgFileContent(std::string local_path_, std::string file_name_,
                    std::string file_type_, std::string media_id_,
                    std::string mime_type_, std::string url_,
                    int64_t file_size_)
      : local_path(std::move(local_path_)), file_name(std::move(file_name_)),
        file_type(std::move(file_type_)), media_id(std::move(media_id_)),
        mime_type(std::move(mime_type_)), url(std::move(url_)),
        file_size(std::move(file_size_)) {}

  AIMMsgFileContent() {}
};

} // namespace dps
} // namespace alibaba
