// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include "aim_pub_msg_combine_forward.h"
#include <utility>
#include <vector>

namespace alibaba {
namespace dps {

struct AIMPubMsgCombineForwardContent final {

  std::vector<AIMPubMsgCombineForward> combine_forward;

  AIMPubMsgCombineForwardContent(
      std::vector<AIMPubMsgCombineForward> combine_forward_)
      : combine_forward(std::move(combine_forward_)) {}

  AIMPubMsgCombineForwardContent() {}
};

} // namespace dps
} // namespace alibaba
