// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <functional>

namespace alibaba {
namespace dps {

enum class AIMMsgRecallType : int {
  /**
   * 未知
   */
  RECALL_TYPE_UNKNOWN = -1,
  /**
   * 发送者撤回
   */
  RECALL_TYPE_SENDER = 0,
  /**
   * 群主撤回
   */
  RECALL_TYPE_GROUP_OWNER = 1,
  /**
   * 系统撤回
   */
  RECALL_TYPE_SYSTEM = 2,
  /**
   * 安全撤回
   */
  RECALL_TYPE_SECURITY = 3,
  /**
   * 业务方自定义撤回，可通过 AIMMsgRecallFeature.extension 配合使用
   */
  RECALL_TYPE_CUSTOM = 101,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::dps::AIMMsgRecallType> {
  size_t operator()(::alibaba::dps::AIMMsgRecallType type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
