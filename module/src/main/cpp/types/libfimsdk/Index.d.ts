
// 认证相关 (1个)
export const fimLogin: (params: string) => Promise<string>;

// 消息查询相关 (7个)
export const fimListPreviousMsgs: (params: string) => Promise<string>;
export const fimListNextMsgs: (params: string) => Promise<string>;
export const fimListPreviousLocalMsgs: (params: string) => Promise<string>;
export const fimListNextLocalMsgs: (params: string) => Promise<string>;
export const fimGetMessage: (params: string) => Promise<string>;
export const fimGetLocalMessage: (params: string) => Promise<string>;
export const fimListMessagesReadStatus: (params: string) => Promise<string>;

// 消息操作相关 (6个)
export const fimDeleteMessage: (params: string) => Promise<string>;
export const fimDeleteLocalMessage: (params: string) => Promise<string>;
export const fimClearMessages: (params: string) => Promise<string>;
export const fimRecallMessage: (params: string) => Promise<string>;
export const fimUpdateMessageToRead: (params: string) => Promise<string>;
export const fimResendMessage: (params: string) => Promise<string>;

// 事件监听相关 (3个)
export const fimOnMessageEvent: (params: string) => Promise<string>;
export const fimOnConversationEvent: (params: string) => Promise<string>;
export const fimOnGroupEvent: (params: string) => Promise<string>;

// 消息发送相关 (7个)
export const fimSendMessageText: (params: string) => Promise<string>;
export const fimSendMessageCustom: (params: string) => Promise<string>;
export const fimSendMessageImage: (params: string) => Promise<string>;
export const fimSendMessageAt: (params: string) => Promise<string>;
export const fimSendMessageStruct: (params: string) => Promise<string>;
export const fimSendMessageVideo: (params: string) => Promise<string>;
export const fimReplyMessage: (params: string) => Promise<string>;

// 会话管理相关 (5个)
export const fimActiveConversation: (params: string) => Promise<string>;
export const fimGetMuteStatus: (params: string) => Promise<string>;
export const fimGetConversations: (params: string) => Promise<string>;
export const fimCreateCustomSingleConversation: (params: string) => Promise<string>;
export const fimLeaveGroup: (params: string) => Promise<string>;

// 搜索和工具相关 (3个)
export const fimGetMyFuid: (params: string) => Promise<string>;
export const fimSearchChatContent: (params: string) => Promise<string>;
export const fimGetMediaLocalPath: (params: string) => Promise<string>;

// FIM Engine 配置方法
export const fimEngineStart: () => Promise<string>;

// FIM SDK 配置方法
export const setAppId: (appId: string) => Promise<string>;
export const setAppName: (appName: string) => string;
export const setAppKey: (appKey: string) => string;
export const setDeviceType: (deviceType: string) => string;
export const setDeviceName: (deviceName: string) => string;
export const setDeviceId: (deviceId: string) => string;
export const setOsName: (osName: string) => string;
export const setOsVersion: (osVersion: string) => string;
export const setAppVersion: (appVersion: string) => string;
export const setDataPath: (dataPath: string) => string;

// 测试
export const add: (a: number, b: number) => number;
