// Copyright (c) 2025 Fliggy Team. All rights reserved.
// Created by guyan on 2025-08-21.
// FIM Message Handler Implementation for fliggy_im module

#include "fim_message_handler.h"
#include "../utils/napi_context_manager.h"
#include "../utils/fim_common_utils.h"
#include <cstdio>
#include <cstddef>
#include <sstream>
#include <map>
#include <string>
#include <exception>
#include <iostream>

// 需要包含FIM SDK相关头文件
#include "sdk/src/ffi/message/fim_message_c_api.h"
#include "sdk/src/ffi/conversation/fim_conversation_service_c_api.h"
#include "sdk/src/method_invoker/fim_method_invoker.h"
#include "json.hpp"

// 使用nlohmann json库
using json = nlohmann::json;
using namespace alibaba::fim;
using namespace fliggy::fim::napi;

FIMMessageHandler& FIMMessageHandler::GetInstance() {
    static FIMMessageHandler instance;
    return instance;
}

std::string FIMMessageHandler::ExtractJsonValue(const std::string& json, const std::string& key) {
    std::string searchKey = "\"" + key + "\":";
    size_t pos = json.find(searchKey);
    if (pos == std::string::npos) {
        return "";
    }

    pos += searchKey.length();
    // 跳过空格
    while (pos < json.length() && (json[pos] == ' ' || json[pos] == '\t')) {
        pos++;
    }

    if (pos >= json.length()) {
        return "";
    }

    // 如果是字符串值（以"开头）
    if (json[pos] == '"') {
        pos++; // 跳过开始的"
        size_t endPos = json.find('"', pos);
        if (endPos != std::string::npos) {
            return json.substr(pos, endPos - pos);
        }
    }

    return "";
}

void FIMMessageHandler::SendMessageAsyncAdapter(const std::string& messageJson, LocalFIMCallback callback) {
    printf("[DEBUG] SendMessageAsyncAdapter called with: %s\n", messageJson.c_str());

    try {
        printf("[DEBUG] sendMessageText (REAL SDK) - calling FIMMessageService::SendMessage\n");

        // 1. 解析JSON参数
        std::string conversationId = FIMCommonUtils::getStringParam(messageJson, "conversationId", "default_conv");
        std::string text = FIMCommonUtils::getStringParam(messageJson, "text", "");
        std::string receiverList = FIMCommonUtils::getStringParam(messageJson, "receivers", "[]");

        printf("[DEBUG] Parsed - conversationId: %s, text: %s\n", conversationId.c_str(), text.c_str());

        // 2. 验证必要参数
        if (text.empty()) {
            callback(FIMCommonUtils::createErrorResult("sendMessage", "缺少必要参数：text"));
            return;
        }

        // 3. 构造 FIMSendMessageText 对象
        common_types::FIMSendMessageText message;
        message.appCid = conversationId;
        message.text = text;

        // 4. 解析接收者列表
        message.receivers = FIMCommonUtils::parseReceivers(receiverList);

        // 如果没有接收者，添加默认接收者
        if (message.receivers.empty()) {
            message.receivers.push_back("default_receiver");
            printf("[DEBUG] No receivers found, using default_receiver\n");
        }

        // 5. 创建用户数据
        std::map<std::string, std::string> userData;
        userData["source"] = "fliggy_im_napi";
        userData["method"] = "sendMessage";
        userData["timestamp"] = std::to_string(FIMCommonUtils::getCurrentTimestamp());

        // 6. 创建同步回调机制
        std::string sdkResult;
        bool sdkCallbackExecuted = false;

        // 7. 调用真实的SDK API
        printf("[DEBUG] Calling real SDK API for send message\n");

        // 构造SDK期望的JSON参数
        json messageObj;
        messageObj["conversationId"] = conversationId;
        messageObj["text"] = text;
        messageObj["messageType"] = "text";

        json args = json::array({messageObj, json::object()});
        printf("[DEBUG] SDK args for send: %s\n", args.dump().c_str());

        // 调用真实的SDK API
        alibaba::fim::SendMessage(args, [callback](const std::string& result) {
            printf("[DEBUG] SDK API callback received: %s\n", result.c_str());
            callback(result);
        });

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in SendMessageAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("sendMessage", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in SendMessageAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("sendMessage", "Unknown exception occurred"));
    }
}

template<typename BusinessFunc>
napi_value FIMMessageHandler::HandleFIMMethodAsync(napi_env env, napi_callback_info info,
                                                  const char* methodName, BusinessFunc businessFunc) {
    printf("[DEBUG] HandleFIMMethodAsync ENTRY: method=%s\n", methodName);

    // 1. 获取并校验参数个数
    size_t argc = 1;
    napi_value args[1] = {nullptr};
    napi_status status = napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    if (status != napi_ok || argc != 1) {
        napi_throw_error(env, nullptr, "Expected exactly 1 argument (JSON string)");
        return nullptr;
    }

    // 2. 检查参数类型
    napi_valuetype valuetype;
    status = napi_typeof(env, args[0], &valuetype);
    if (status != napi_ok || valuetype != napi_string) {
        napi_throw_error(env, nullptr, "Argument must be a string (JSON)");
        return nullptr;
    }

    // 3. 提取字符串参数
    size_t str_size = 0;
    status = napi_get_value_string_utf8(env, args[0], nullptr, 0, &str_size);
    if (status != napi_ok) {
        napi_throw_error(env, nullptr, "Failed to get string size");
        return nullptr;
    }

    std::string paramsJson(str_size, '\0');
    status = napi_get_value_string_utf8(env, args[0], &paramsJson[0], str_size + 1, &str_size);
    if (status != napi_ok) {
        napi_throw_error(env, nullptr, "Failed to get string value");
        return nullptr;
    }

    printf("[DEBUG] Extracted params: %s\n", paramsJson.c_str());

    // 4. 创建Promise
    napi_deferred deferred;
    napi_value promise;
    status = napi_create_promise(env, &deferred, &promise);
    if (status != napi_ok) {
        napi_throw_error(env, nullptr, "Failed to create promise");
        return nullptr;
    }

    // 5. 先创建Promise上下文（暂时不设置tsfn）
    NAPIPromiseContext* context = new NAPIPromiseContext(env, deferred, nullptr);

    // 6. 创建线程安全函数，将context作为上下文数据传递
    napi_threadsafe_function tsfn;
    napi_value resourceName;
    status = napi_create_string_utf8(env, methodName, NAPI_AUTO_LENGTH, &resourceName);
    if (status != napi_ok) {
        delete context;
        napi_throw_error(env, nullptr, "Failed to create resource name");
        return nullptr;
    }

    status = napi_create_threadsafe_function(
        env,
        nullptr,  // 不需要JavaScript回调函数
        nullptr,  // 不需要async resource
        resourceName,
        0,        // 最大队列大小，0表示无限制
        1,        // 初始线程计数
        nullptr,  // 线程结束回调数据
        nullptr,  // 线程结束回调函数
        context,  // 传递Promise上下文作为context参数
        ThreadSafePromiseHandler::ThreadSafeCallback,
        &tsfn
    );

    if (status != napi_ok) {
        delete context;
        napi_throw_error(env, nullptr, "Failed to create threadsafe function");
        return nullptr;
    }

    // 7. 更新上下文中的线程安全函数
    context->tsfn = tsfn;

    // 8. 存储Promise上下文到映射表中
    int contextId = NAPIContextManager::GetInstance().StorePromiseContext(context);

    // 9. 创建使用contextId机制的回调函数
    LocalFIMCallback callback = ThreadSafePromiseHandler::GetInstance().CreatePromiseCallback(contextId);

    // 10. 调用业务逻辑函数
    businessFunc(paramsJson, callback);

    return promise;
}

void FIMMessageHandler::ResendMessageAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] ResendMessageAsyncAdapter called with: %s\n", paramsJson.c_str());

    try {
        printf("[DEBUG] resendMessage (REAL SDK) - calling FIMMessageService::ResendMessage\n");

        // 1. 解析JSON参数
        std::string messageId = FIMCommonUtils::getStringParam(paramsJson, "messageId", "");
        std::string conversationId = FIMCommonUtils::getStringParam(paramsJson, "conversationId", "");

        printf("[DEBUG] Parsed - messageId: %s, conversationId: %s\n", messageId.c_str(), conversationId.c_str());

        // 2. 验证必要参数
        if (messageId.empty()) {
            callback(FIMCommonUtils::createErrorResult("resendMessage", "缺少必要参数：messageId"));
            return;
        }

        // 3. 创建用户数据
        std::map<std::string, std::string> userData;
        userData["source"] = "fliggy_im_napi";
        userData["method"] = "resendMessage";
        userData["timestamp"] = std::to_string(FIMCommonUtils::getCurrentTimestamp());

        // 4. 创建同步回调机制
        std::string sdkResult;
        bool sdkCallbackExecuted = false;

        // 5. 调用真实的SDK API
        printf("[DEBUG] Calling real SDK API for resend message\n");

        // 构造SDK期望的JSON参数
        json messageObj;
        messageObj["messageId"] = messageId;
        messageObj["conversationId"] = conversationId;

        json args = json::array({messageObj, json::object()});
        printf("[DEBUG] SDK args for resend: %s\n", args.dump().c_str());

        // 调用真实的SDK API
        alibaba::fim::ResendMessage(args, [callback](const std::string& result) {
            printf("[DEBUG] SDK API callback received: %s\n", result.c_str());
            callback(result);
        });

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in ResendMessageAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("resendMessage", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in ResendMessageAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("resendMessage", "Unknown exception occurred"));
    }
}

void FIMMessageHandler::ReplyMessageAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] ReplyMessageAsyncAdapter called with: %s\n", paramsJson.c_str());

    try {
        printf("[DEBUG] replyMessage (REAL SDK) - calling FIMMessageService::ReplyMessage\n");

        // 1. 解析JSON参数
        std::string originalMessageId = FIMCommonUtils::getStringParam(paramsJson, "originalMessageId", "");
        std::string replyText = FIMCommonUtils::getStringParam(paramsJson, "text", "");
        std::string conversationId = FIMCommonUtils::getStringParam(paramsJson, "conversationId", "");

        printf("[DEBUG] Parsed - originalMessageId: %s, text: %s, conversationId: %s\n",
               originalMessageId.c_str(), replyText.c_str(), conversationId.c_str());

        // 2. 验证必要参数
        if (originalMessageId.empty() || replyText.empty()) {
            callback(FIMCommonUtils::createErrorResult("replyMessage", "缺少必要参数：originalMessageId 或 text"));
            return;
        }

        // 3. 创建用户数据
        std::map<std::string, std::string> userData;
        userData["source"] = "fliggy_im_napi";
        userData["method"] = "replyMessage";
        userData["timestamp"] = std::to_string(FIMCommonUtils::getCurrentTimestamp());

        // 4. 创建同步回调机制
        std::string sdkResult;
        bool sdkCallbackExecuted = false;

        // 5. 调用真实的SDK API
        printf("[DEBUG] Calling real SDK API for reply message\n");

        // 构造SDK期望的JSON参数
        json messageObj;
        messageObj["originalMessageId"] = originalMessageId;
        messageObj["conversationId"] = conversationId;
        messageObj["text"] = replyText;

        json args = json::array({messageObj, json::object()});
        printf("[DEBUG] SDK args for reply: %s\n", args.dump().c_str());

        // 调用真实的SDK API
        alibaba::fim::ReplyMessage(args, [callback](const std::string& result) {
            printf("[DEBUG] SDK API callback received: %s\n", result.c_str());
            callback(result);
        });

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in ReplyMessageAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("replyMessage", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in ReplyMessageAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("replyMessage", "Unknown exception occurred"));
    }
}

napi_value FIMMessageHandler::SendMessage(napi_env env, napi_callback_info info) {
    // 使用统一的线程安全处理函数
    return HandleFIMMethodAsync(env, info, "fimSendMessage", SendMessageAsyncAdapter);
}

napi_value FIMMessageHandler::ResendMessage(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimResendMessage", ResendMessageAsyncAdapter);
}

napi_value FIMMessageHandler::ReplyMessage(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimReplyMessage", ReplyMessageAsyncAdapter);
}

// ============================================================================
// 新增的消息方法实现
// ============================================================================

void FIMMessageHandler::GetMessageAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] GetMessageAsyncAdapter called with: %s\n", paramsJson.c_str());

    try {
        printf("[DEBUG] getMessage (REAL SDK) - calling FIMMessageService::GetMessage\n");

        // 1. 解析JSON参数
        std::string conversationId = FIMCommonUtils::getStringParam(paramsJson, "conversationId", "");
        std::string messageId = FIMCommonUtils::getStringParam(paramsJson, "messageId", "");

        printf("[DEBUG] Parsed - conversationId: %s, messageId: %s\n", conversationId.c_str(), messageId.c_str());

        // 2. 验证必要参数
        if (conversationId.empty() || messageId.empty()) {
            callback(FIMCommonUtils::createErrorResult("getMessage", "缺少必要参数：conversationId 或 messageId"));
            return;
        }

        // 3. 构造SDK期望的JSON参数
        json messageObj;
        messageObj["conversationId"] = conversationId;
        messageObj["messageId"] = messageId;

        json args = json::array({messageObj, json::object()});
        printf("[DEBUG] SDK args for getMessage: %s\n", args.dump().c_str());

        // 4. 调用真实的SDK API
        alibaba::fim::GetMessage(args, [callback](const std::string& result) {
            printf("[DEBUG] SDK API callback received: %s\n", result.c_str());
            callback(result);
        });

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in GetMessageAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("getMessage", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in GetMessageAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("getMessage", "Unknown exception occurred"));
    }
}

void FIMMessageHandler::GetLocalMessageAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] GetLocalMessageAsyncAdapter called with: %s\n", paramsJson.c_str());

    try {
        printf("[DEBUG] getLocalMessage (REAL SDK) - calling FIMMessageService::GetLocalMessage\n");

        // 1. 解析JSON参数
        std::string conversationId = FIMCommonUtils::getStringParam(paramsJson, "conversationId", "");
        std::string localId = FIMCommonUtils::getStringParam(paramsJson, "localId", "");

        printf("[DEBUG] Parsed - conversationId: %s, localId: %s\n", conversationId.c_str(), localId.c_str());

        // 2. 验证必要参数
        if (conversationId.empty() || localId.empty()) {
            callback(FIMCommonUtils::createErrorResult("getLocalMessage", "缺少必要参数：conversationId 或 localId"));
            return;
        }

        // 3. 构造SDK期望的JSON参数
        json messageObj;
        messageObj["conversationId"] = conversationId;
        messageObj["localId"] = localId;

        json args = json::array({messageObj, json::object()});
        printf("[DEBUG] SDK args for getLocalMessage: %s\n", args.dump().c_str());

        // 4. 调用真实的SDK API
        alibaba::fim::GetLocalMessage(args, [callback](const std::string& result) {
            printf("[DEBUG] SDK API callback received: %s\n", result.c_str());
            callback(result);
        });

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in GetLocalMessageAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("getLocalMessage", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in GetLocalMessageAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("getLocalMessage", "Unknown exception occurred"));
    }
}

void FIMMessageHandler::DeleteMessageAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] DeleteMessageAsyncAdapter called with: %s\n", paramsJson.c_str());

    try {
        printf("[DEBUG] deleteMessage (REAL SDK) - calling FIMMessageService::DeleteMessage\n");

        // 1. 解析JSON参数
        std::string conversationId = FIMCommonUtils::getStringParam(paramsJson, "conversationId", "");
        std::string messageIds = FIMCommonUtils::getStringParam(paramsJson, "messageIds", "[]");

        printf("[DEBUG] Parsed - conversationId: %s, messageIds: %s\n", conversationId.c_str(), messageIds.c_str());

        // 2. 验证必要参数
        if (conversationId.empty()) {
            callback(FIMCommonUtils::createErrorResult("deleteMessage", "缺少必要参数：conversationId"));
            return;
        }

        // 3. 构造SDK期望的JSON参数
        json messageObj;
        messageObj["conversationId"] = conversationId;

        // 解析messageIds数组
        try {
            json messageIdsArray = json::parse(messageIds);
            messageObj["messageIds"] = messageIdsArray;
        } catch (const json::parse_error& e) {
            callback(FIMCommonUtils::createErrorResult("deleteMessage", "messageIds格式错误"));
            return;
        }

        json args = json::array({messageObj, json::object()});
        printf("[DEBUG] SDK args for deleteMessage: %s\n", args.dump().c_str());

        // 4. 调用真实的SDK API
        alibaba::fim::DeleteMessage(args, [callback](const std::string& result) {
            printf("[DEBUG] SDK API callback received: %s\n", result.c_str());
            callback(result);
        });

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in DeleteMessageAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("deleteMessage", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in DeleteMessageAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("deleteMessage", "Unknown exception occurred"));
    }
}

void FIMMessageHandler::RecallMessageAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] RecallMessageAsyncAdapter called with: %s\n", paramsJson.c_str());

    try {
        printf("[DEBUG] recallMessage (REAL SDK) - calling FIMMessageService::RecallMessage\n");

        // 1. 解析JSON参数
        std::string conversationId = FIMCommonUtils::getStringParam(paramsJson, "conversationId", "");
        std::string messageId = FIMCommonUtils::getStringParam(paramsJson, "messageId", "");

        printf("[DEBUG] Parsed - conversationId: %s, messageId: %s\n", conversationId.c_str(), messageId.c_str());

        // 2. 验证必要参数
        if (conversationId.empty() || messageId.empty()) {
            callback(FIMCommonUtils::createErrorResult("recallMessage", "缺少必要参数：conversationId 或 messageId"));
            return;
        }

        // 3. 构造SDK期望的JSON参数
        json messageObj;
        messageObj["conversationId"] = conversationId;
        messageObj["messageId"] = messageId;

        json args = json::array({messageObj, json::object()});
        printf("[DEBUG] SDK args for recallMessage: %s\n", args.dump().c_str());

        // 4. 调用真实的SDK API
        alibaba::fim::RecallMessage(args, [callback](const std::string& result) {
            printf("[DEBUG] SDK API callback received: %s\n", result.c_str());
            callback(result);
        });

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in RecallMessageAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("recallMessage", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in RecallMessageAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("recallMessage", "Unknown exception occurred"));
    }
}

napi_value FIMMessageHandler::GetMessage(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimGetMessage", GetMessageAsyncAdapter);
}

napi_value FIMMessageHandler::GetLocalMessage(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimGetLocalMessage", GetLocalMessageAsyncAdapter);
}

napi_value FIMMessageHandler::DeleteMessage(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimDeleteMessage", DeleteMessageAsyncAdapter);
}

napi_value FIMMessageHandler::RecallMessage(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimRecallMessage", RecallMessageAsyncAdapter);
}

// ============================================================================
// 消息列表相关方法实现
// ============================================================================

void FIMMessageHandler::ListPreviousLocalMsgsAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] ListPreviousLocalMsgsAsyncAdapter called with: %s\n", paramsJson.c_str());

    try {
        printf("[DEBUG] listPreviousLocalMsgs (REAL SDK) - calling FIMMessageService::ListPreviousLocalMsgs\n");

        // 1. 解析JSON参数
        std::string conversationId = FIMCommonUtils::getStringParam(paramsJson, "conversationId", "");
        std::string cursor = FIMCommonUtils::getStringParam(paramsJson, "cursor", "0");
        std::string count = FIMCommonUtils::getStringParam(paramsJson, "count", "20");

        printf("[DEBUG] Parsed - conversationId: %s, cursor: %s, count: %s\n",
               conversationId.c_str(), cursor.c_str(), count.c_str());

        // 2. 验证必要参数
        if (conversationId.empty()) {
            callback(FIMCommonUtils::createErrorResult("listPreviousLocalMsgs", "缺少必要参数：conversationId"));
            return;
        }

        // 3. 构造SDK期望的JSON参数
        json messageObj;
        messageObj["conversationId"] = conversationId;
        messageObj["cursor"] = std::stoll(cursor);
        messageObj["count"] = std::stoi(count);

        json args = json::array({messageObj, json::object()});
        printf("[DEBUG] SDK args for listPreviousLocalMsgs: %s\n", args.dump().c_str());

        // 4. 调用真实的SDK API
        alibaba::fim::ListPreviousLocalMsgs(args, [callback](const std::string& result) {
            printf("[DEBUG] SDK API callback received: %s\n", result.c_str());
            callback(result);
        });

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in ListPreviousLocalMsgsAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("listPreviousLocalMsgs", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in ListPreviousLocalMsgsAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("listPreviousLocalMsgs", "Unknown exception occurred"));
    }
}

void FIMMessageHandler::ListNextLocalMsgsAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] ListNextLocalMsgsAsyncAdapter called with: %s\n", paramsJson.c_str());

    try {
        printf("[DEBUG] listNextLocalMsgs (REAL SDK) - calling FIMMessageService::ListNextLocalMsgs\n");

        // 1. 解析JSON参数
        std::string conversationId = FIMCommonUtils::getStringParam(paramsJson, "conversationId", "");
        std::string cursor = FIMCommonUtils::getStringParam(paramsJson, "cursor", "0");
        std::string count = FIMCommonUtils::getStringParam(paramsJson, "count", "20");

        printf("[DEBUG] Parsed - conversationId: %s, cursor: %s, count: %s\n",
               conversationId.c_str(), cursor.c_str(), count.c_str());

        // 2. 验证必要参数
        if (conversationId.empty()) {
            callback(FIMCommonUtils::createErrorResult("listNextLocalMsgs", "缺少必要参数：conversationId"));
            return;
        }

        // 3. 构造SDK期望的JSON参数
        json messageObj;
        messageObj["conversationId"] = conversationId;
        messageObj["cursor"] = std::stoll(cursor);
        messageObj["count"] = std::stoi(count);

        json args = json::array({messageObj, json::object()});
        printf("[DEBUG] SDK args for listNextLocalMsgs: %s\n", args.dump().c_str());

        // 4. 调用真实的SDK API
        alibaba::fim::ListNextLocalMsgs(args, [callback](const std::string& result) {
            printf("[DEBUG] SDK API callback received: %s\n", result.c_str());
            callback(result);
        });

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in ListNextLocalMsgsAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("listNextLocalMsgs", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in ListNextLocalMsgsAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("listNextLocalMsgs", "Unknown exception occurred"));
    }
}

void FIMMessageHandler::ListPreviousMsgsAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] ListPreviousMsgsAsyncAdapter called with: %s\n", paramsJson.c_str());

    try {
        printf("[DEBUG] listPreviousMsgs (REAL SDK) - calling FIMMessageService::ListPreviousMsgs\n");

        // 1. 解析JSON参数
        std::string conversationId = FIMCommonUtils::getStringParam(paramsJson, "conversationId", "");
        std::string cursor = FIMCommonUtils::getStringParam(paramsJson, "cursor", "0");
        std::string count = FIMCommonUtils::getStringParam(paramsJson, "count", "20");

        printf("[DEBUG] Parsed - conversationId: %s, cursor: %s, count: %s\n",
               conversationId.c_str(), cursor.c_str(), count.c_str());

        // 2. 验证必要参数
        if (conversationId.empty()) {
            callback(FIMCommonUtils::createErrorResult("listPreviousMsgs", "缺少必要参数：conversationId"));
            return;
        }

        // 3. 构造SDK期望的JSON参数
        json messageObj;
        messageObj["conversationId"] = conversationId;
        messageObj["cursor"] = std::stoll(cursor);
        messageObj["count"] = std::stoi(count);

        json args = json::array({messageObj, json::object()});
        printf("[DEBUG] SDK args for listPreviousMsgs: %s\n", args.dump().c_str());

        // 4. 调用真实的SDK API
        alibaba::fim::ListPreviousMsgs(args, [callback](const std::string& result) {
            printf("[DEBUG] SDK API callback received: %s\n", result.c_str());
            callback(result);
        });

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in ListPreviousMsgsAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("listPreviousMsgs", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in ListPreviousMsgsAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("listPreviousMsgs", "Unknown exception occurred"));
    }
}

void FIMMessageHandler::ListNextMsgsAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] ListNextMsgsAsyncAdapter called with: %s\n", paramsJson.c_str());

    try {
        printf("[DEBUG] listNextMsgs (REAL SDK) - calling FIMMessageService::ListNextMsgs\n");

        // 1. 解析JSON参数
        std::string conversationId = FIMCommonUtils::getStringParam(paramsJson, "conversationId", "");
        std::string cursor = FIMCommonUtils::getStringParam(paramsJson, "cursor", "0");
        std::string count = FIMCommonUtils::getStringParam(paramsJson, "count", "20");

        printf("[DEBUG] Parsed - conversationId: %s, cursor: %s, count: %s\n",
               conversationId.c_str(), cursor.c_str(), count.c_str());

        // 2. 验证必要参数
        if (conversationId.empty()) {
            callback(FIMCommonUtils::createErrorResult("listNextMsgs", "缺少必要参数：conversationId"));
            return;
        }

        // 3. 构造SDK期望的JSON参数
        json messageObj;
        messageObj["conversationId"] = conversationId;
        messageObj["cursor"] = std::stoll(cursor);
        messageObj["count"] = std::stoi(count);

        json args = json::array({messageObj, json::object()});
        printf("[DEBUG] SDK args for listNextMsgs: %s\n", args.dump().c_str());

        // 4. 调用真实的SDK API
        alibaba::fim::ListNextMsgs(args, [callback](const std::string& result) {
            printf("[DEBUG] SDK API callback received: %s\n", result.c_str());
            callback(result);
        });

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in ListNextMsgsAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("listNextMsgs", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in ListNextMsgsAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("listNextMsgs", "Unknown exception occurred"));
    }
}

napi_value FIMMessageHandler::ListPreviousLocalMsgs(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimListPreviousLocalMsgs", ListPreviousLocalMsgsAsyncAdapter);
}

napi_value FIMMessageHandler::ListNextLocalMsgs(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimListNextLocalMsgs", ListNextLocalMsgsAsyncAdapter);
}

napi_value FIMMessageHandler::ListPreviousMsgs(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimListPreviousMsgs", ListPreviousMsgsAsyncAdapter);
}

napi_value FIMMessageHandler::ListNextMsgs(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimListNextMsgs", ListNextMsgsAsyncAdapter);
}

// ============================================================================
// 已读状态和本地消息相关方法实现
// ============================================================================

void FIMMessageHandler::ListMessagesReadStatusAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] ListMessagesReadStatusAsyncAdapter called with: %s\n", paramsJson.c_str());
    try {
        printf("[DEBUG] listMessagesReadStatus (REAL SDK) - calling FIMMessageService::ListMessagesReadStatus\n");

        // 1. 解析JSON参数
        std::string conversationId = FIMCommonUtils::getStringParam(paramsJson, "conversationId", "");
        std::string messageId = FIMCommonUtils::getStringParam(paramsJson, "messageId", "");

        printf("[DEBUG] Parsed - conversationId: %s, messageId: %s\n", conversationId.c_str(), messageId.c_str());

        // 2. 验证必要参数
        if (conversationId.empty() || messageId.empty()) {
            callback(FIMCommonUtils::createErrorResult("listMessagesReadStatus", "缺少必要参数：conversationId 或 messageId"));
            return;
        }

        // 3. 构造SDK期望的JSON参数
        json messageObj;
        messageObj["conversationId"] = conversationId;
        messageObj["messageId"] = messageId;

        json args = json::array({messageObj, json::object()});
        printf("[DEBUG] SDK args for listMessagesReadStatus: %s\n", args.dump().c_str());

        // 4. 调用真实的SDK API
        alibaba::fim::ListMessagesReadStatus(args, [callback](const std::string& result) {
            printf("[DEBUG] SDK API callback received: %s\n", result.c_str());
            callback(result);
        });

     } catch (const std::exception& e) {
        printf("[ERROR] Exception in ListMessagesReadStatusAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("listMessagesReadStatus", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in ListMessagesReadStatusAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("listMessagesReadStatus", "Unknown exception occurred"));
    }
}

void FIMMessageHandler::UpdateMessageToReadAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] UpdateMessageToReadAsyncAdapter called with: %s\n", paramsJson.c_str());

    try {
        printf("[DEBUG] updateMessageToRead (REAL SDK) - calling FIMMessageService::UpdateMessageToRead\n");

        // 1. 解析JSON参数
        std::string conversationId = FIMCommonUtils::getStringParam(paramsJson, "conversationId", "");
        std::string messageIds = FIMCommonUtils::getStringParam(paramsJson, "messageIds", "[]");

        printf("[DEBUG] Parsed - conversationId: %s, messageIds: %s\n", conversationId.c_str(), messageIds.c_str());

        // 2. 验证必要参数
        if (conversationId.empty()) {
            callback(FIMCommonUtils::createErrorResult("updateMessageToRead", "缺少必要参数：conversationId"));
            return;
        }

        // 3. 构造SDK期望的JSON参数
        json messageObj;
        messageObj["conversationId"] = conversationId;

        // 解析messageIds数组
        try {
            json messageIdsArray = json::parse(messageIds);
            messageObj["messageIds"] = messageIdsArray;
        } catch (const json::parse_error& e) {
            callback(FIMCommonUtils::createErrorResult("updateMessageToRead", "messageIds格式错误"));
            return;
        }

        json args = json::array({messageObj, json::object()});
        printf("[DEBUG] SDK args for updateMessageToRead: %s\n", args.dump().c_str());

        // 4. 调用真实的SDK API
        alibaba::fim::UpdateMessageToRead(args, [callback](const std::string& result) {
            printf("[DEBUG] SDK API callback received: %s\n", result.c_str());
            callback(result);
        });

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in UpdateMessageToReadAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("updateMessageToRead", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in UpdateMessageToReadAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("updateMessageToRead", "Unknown exception occurred"));
    }
   
}

void FIMMessageHandler::DeleteLocalMessageAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] DeleteLocalMessageAsyncAdapter called with: %s\n", paramsJson.c_str());

    try {
        printf("[DEBUG] deleteLocalMessage (REAL SDK) - calling FIMMessageService::DeleteLocalMessage\n");

        // 1. 解析JSON参数
        std::string conversationId = FIMCommonUtils::getStringParam(paramsJson, "conversationId", "");
        std::string localIds = FIMCommonUtils::getStringParam(paramsJson, "localIds", "[]");

        printf("[DEBUG] Parsed - conversationId: %s, localIds: %s\n", conversationId.c_str(), localIds.c_str());

        // 2. 验证必要参数
        if (conversationId.empty()) {
            callback(FIMCommonUtils::createErrorResult("deleteLocalMessage", "缺少必要参数：conversationId"));
            return;
        }

        // 3. 构造SDK期望的JSON参数
        json messageObj;
        messageObj["conversationId"] = conversationId;

        // 解析localIds数组
        try {
            json localIdsArray = json::parse(localIds);
            messageObj["localIds"] = localIdsArray;
        } catch (const json::parse_error& e) {
            callback(FIMCommonUtils::createErrorResult("deleteLocalMessage", "localIds格式错误"));
            return;
        }

        json args = json::array({messageObj, json::object()});
        printf("[DEBUG] SDK args for deleteLocalMessage: %s\n", args.dump().c_str());

        // 4. 调用真实的SDK API
        alibaba::fim::DeleteLocalMessage(args, [callback](const std::string& result) {
            printf("[DEBUG] SDK API callback received: %s\n", result.c_str());
            callback(result);
        });

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in DeleteLocalMessageAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("deleteLocalMessage", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in DeleteLocalMessageAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("deleteLocalMessage", "Unknown exception occurred"));
    }
}

napi_value FIMMessageHandler::ListMessagesReadStatus(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimListMessagesReadStatus", ListMessagesReadStatusAsyncAdapter);
}

napi_value FIMMessageHandler::UpdateMessageToRead(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimUpdateMessageToRead", UpdateMessageToReadAsyncAdapter);
}

napi_value FIMMessageHandler::DeleteLocalMessage(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimDeleteLocalMessage", DeleteLocalMessageAsyncAdapter);
}

// ============================================================================
// 会话相关方法实现
// ============================================================================

void FIMMessageHandler::GetConversationsAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] GetConversationsAsyncAdapter called with: %s\n", paramsJson.c_str());
    
    try {
        printf("[DEBUG] getConversations (REAL SDK) - calling FIMConversationService::GetConversations\n");

        // 1. 解析JSON参数
        std::string conversationIds = FIMCommonUtils::getStringParam(paramsJson, "conversationIds", "[]");

        printf("[DEBUG] Parsed - conversationIds: %s\n", conversationIds.c_str());

        // 2. 构造SDK期望的JSON参数
        json messageObj;

        // 解析conversationIds数组
        try {
            json conversationIdsArray = json::parse(conversationIds);
            messageObj["conversationIds"] = conversationIdsArray;
        } catch (const json::parse_error& e) {
            callback(FIMCommonUtils::createErrorResult("getConversations", "conversationIds格式错误"));
            return;
        }

        json args = json::array({messageObj, json::object()});
        printf("[DEBUG] SDK args for getConversations: %s\n", args.dump().c_str());

        // 3. 调用真实的SDK API
        alibaba::fim::GetConversations(args, [callback](const std::string& result) {
            printf("[DEBUG] SDK API callback received: %s\n", result.c_str());
            callback(result);
        });

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in GetConversationsAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("getConversations", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in GetConversationsAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("getConversations", "Unknown exception occurred"));
    }
}

napi_value FIMMessageHandler::GetConversations(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimGetConversations", GetConversationsAsyncAdapter);
}

// ============================================================================
// Phase 2: 新增认证和事件监听API实现
// ============================================================================

void FIMMessageHandler::LoginAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] LoginAsyncAdapter called with: %s\n", paramsJson.c_str());

    try {
        printf("[DEBUG] login (TODO: SDK API NOT FOUND) - 需要在SDK中实现Login API\n");

        // 1. 解析JSON参数
        std::string userId = FIMCommonUtils::getStringParam(paramsJson, "userId", "");
        std::string token = FIMCommonUtils::getStringParam(paramsJson, "token", "");

        printf("[DEBUG] Parsed - userId: %s, token: %s\n", userId.c_str(), token.c_str());

        // 2. 验证必要参数
        if (userId.empty() || token.empty()) {
            callback(FIMCommonUtils::createErrorResult("login", "缺少必要参数：userId或token"));
            return;
        }

        // TODO: SDK中没有找到对应的Login API，需要后续实现
        // 临时返回成功结果
        json result;
        result["success"] = true;
        result["message"] = "登录功能待实现 - SDK API未找到";
        result["userId"] = userId;
        result["status"] = "TODO_IMPLEMENTATION";

        printf("[DEBUG] Login API 待实现，返回临时结果: %s\n", result.dump().c_str());
        callback(result.dump());

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in LoginAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("login", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in LoginAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("login", "Unknown exception occurred"));
    }
}

void FIMMessageHandler::OnMessageEventAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] OnMessageEventAsyncAdapter called with: %s\n", paramsJson.c_str());

    try {
        printf("[DEBUG] onMessageEvent (TODO: SDK API NOT FOUND) - 需要在SDK中实现OnMessageEvent API\n");

        // 1. 解析JSON参数
        std::string eventType = FIMCommonUtils::getStringParam(paramsJson, "eventType", "");

        printf("[DEBUG] Parsed - eventType: %s\n", eventType.c_str());

        // TODO: SDK中没有找到对应的OnMessageEvent API，需要后续实现
        // 临时返回成功结果
        json result;
        result["success"] = true;
        result["message"] = "消息事件监听功能待实现 - SDK API未找到";
        result["eventType"] = eventType.empty() ? "all" : eventType;
        result["status"] = "TODO_IMPLEMENTATION";

        printf("[DEBUG] OnMessageEvent API 待实现，返回临时结果: %s\n", result.dump().c_str());
        callback(result.dump());

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in OnMessageEventAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("onMessageEvent", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in OnMessageEventAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("onMessageEvent", "Unknown exception occurred"));
    }
}

void FIMMessageHandler::OnConversationEventAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] OnConversationEventAsyncAdapter called with: %s\n", paramsJson.c_str());

    try {
        printf("[DEBUG] onConversationEvent (TODO: SDK API NOT FOUND) - 需要在SDK中实现OnConversationEvent API\n");

        // 1. 解析JSON参数
        std::string eventType = FIMCommonUtils::getStringParam(paramsJson, "eventType", "");

        printf("[DEBUG] Parsed - eventType: %s\n", eventType.c_str());

        // TODO: SDK中没有找到对应的OnConversationEvent API，需要后续实现
        // 临时返回成功结果
        json result;
        result["success"] = true;
        result["message"] = "会话事件监听功能待实现 - SDK API未找到";
        result["eventType"] = eventType.empty() ? "all" : eventType;
        result["status"] = "TODO_IMPLEMENTATION";

        printf("[DEBUG] OnConversationEvent API 待实现，返回临时结果: %s\n", result.dump().c_str());
        callback(result.dump());

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in OnConversationEventAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("onConversationEvent", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in OnConversationEventAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("onConversationEvent", "Unknown exception occurred"));
    }
}

void FIMMessageHandler::OnGroupEventAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] OnGroupEventAsyncAdapter called with: %s\n", paramsJson.c_str());

    try {
        printf("[DEBUG] onGroupEvent (TODO: SDK API NOT FOUND) - 需要在SDK中实现OnGroupEvent API\n");

        // 1. 解析JSON参数
        std::string eventType = FIMCommonUtils::getStringParam(paramsJson, "eventType", "");

        printf("[DEBUG] Parsed - eventType: %s\n", eventType.c_str());

        // TODO: SDK中没有找到对应的OnGroupEvent API，需要后续实现
        // 临时返回成功结果
        json result;
        result["success"] = true;
        result["message"] = "群组事件监听功能待实现 - SDK API未找到";
        result["eventType"] = eventType.empty() ? "all" : eventType;
        result["status"] = "TODO_IMPLEMENTATION";

        printf("[DEBUG] OnGroupEvent API 待实现，返回临时结果: %s\n", result.dump().c_str());
        callback(result.dump());

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in OnGroupEventAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("onGroupEvent", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in OnGroupEventAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("onGroupEvent", "Unknown exception occurred"));
    }
}

// NAPI封装方法实现
napi_value FIMMessageHandler::Login(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimLogin", LoginAsyncAdapter);
}

napi_value FIMMessageHandler::OnMessageEvent(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimOnMessageEvent", OnMessageEventAsyncAdapter);
}

napi_value FIMMessageHandler::OnConversationEvent(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimOnConversationEvent", OnConversationEventAsyncAdapter);
}

napi_value FIMMessageHandler::OnGroupEvent(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimOnGroupEvent", OnGroupEventAsyncAdapter);
}

// ============================================================================
// Phase 3: 新增消息发送功能实现
// ============================================================================

void FIMMessageHandler::SendMessageTextAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] SendMessageTextAsyncAdapter called with: %s\n", paramsJson.c_str());

    try {
        printf("[DEBUG] sendMessageText (REAL SDK) - calling FIMMessageService::SendMessage\n");

        // 1. 解析JSON参数
        std::string conversationId = FIMCommonUtils::getStringParam(paramsJson, "conversationId", "");
        std::string text = FIMCommonUtils::getStringParam(paramsJson, "text", "");
        std::string messageType = FIMCommonUtils::getStringParam(paramsJson, "messageType", "text");
        std::string receiverId = FIMCommonUtils::getStringParam(paramsJson, "receiverId", "");

        printf("[DEBUG] Parsed - conversationId: %s, text: %s, messageType: %s, receiverId: %s\n",
               conversationId.c_str(), text.c_str(), messageType.c_str(), receiverId.c_str());

        // 2. 验证必要参数
        if (conversationId.empty() || text.empty()) {
            callback(FIMCommonUtils::createErrorResult("sendMessageText", "缺少必要参数：conversationId或text"));
            return;
        }

        // 3. 构造SDK期望的消息对象
        json messageObj;
        messageObj["appCid"] = conversationId;
        messageObj["content"]["content_type"] = 1; // TEXT类型
        messageObj["content"]["text_content"]["text"] = text;

        // 设置接收者
        if (!receiverId.empty()) {
            messageObj["receivers"] = json::array({receiverId});
        } else {
            messageObj["receivers"] = json::array();
        }

        json args = json::array({messageObj, json::object()});
        printf("[DEBUG] SDK args for sendMessageText: %s\n", args.dump().c_str());

        // 4. 调用真实的SDK API
        alibaba::fim::SendMessage(args, [callback](const std::string& result) {
            printf("[DEBUG] SDK API callback received: %s\n", result.c_str());
            callback(result);
        });

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in SendMessageTextAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("sendMessageText", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in SendMessageTextAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("sendMessageText", "Unknown exception occurred"));
    }
}

void FIMMessageHandler::SendMessageCustomAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] SendMessageCustomAsyncAdapter called with: %s\n", paramsJson.c_str());

    try {
        printf("[DEBUG] sendMessageCustom (REAL SDK) - calling FIMMessageService::SendMessage\n");

        // 1. 解析JSON参数
        std::string conversationId = FIMCommonUtils::getStringParam(paramsJson, "conversationId", "");

        printf("[DEBUG] Parsed - conversationId: %s\n", conversationId.c_str());

        // 2. 验证必要参数
        if (conversationId.empty()) {
            callback(FIMCommonUtils::createErrorResult("sendMessageCustom", "缺少必要参数：conversationId"));
            return;
        }

        // 3. 解析content对象
        json parsedParams = json::parse(paramsJson);
        if (!parsedParams.contains("content")) {
            callback(FIMCommonUtils::createErrorResult("sendMessageCustom", "缺少必要参数：content"));
            return;
        }

        // 4. 构造SDK期望的消息对象
        json messageObj;
        messageObj["appCid"] = conversationId;
        messageObj["content"] = parsedParams["content"]; // 直接使用传入的content
        messageObj["receivers"] = json::array();

        json args = json::array({messageObj, json::object()});
        printf("[DEBUG] SDK args for sendMessageCustom: %s\n", args.dump().c_str());

        // 5. 调用真实的SDK API
        alibaba::fim::SendMessage(args, [callback](const std::string& result) {
            printf("[DEBUG] SDK API callback received: %s\n", result.c_str());
            callback(result);
        });

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in SendMessageCustomAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("sendMessageCustom", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in SendMessageCustomAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("sendMessageCustom", "Unknown exception occurred"));
    }
}

void FIMMessageHandler::SendMessageImageAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] SendMessageImageAsyncAdapter called with: %s\n", paramsJson.c_str());

    try {
        printf("[DEBUG] sendMessageImage (REAL SDK) - calling FIMMessageService::SendMessage\n");

        // 1. 解析JSON参数
        std::string conversationId = FIMCommonUtils::getStringParam(paramsJson, "conversationId", "");
        std::string imagePath = FIMCommonUtils::getStringParam(paramsJson, "imagePath", "");

        printf("[DEBUG] Parsed - conversationId: %s, imagePath: %s\n", conversationId.c_str(), imagePath.c_str());

        // 2. 验证必要参数
        if (conversationId.empty() || imagePath.empty()) {
            callback(FIMCommonUtils::createErrorResult("sendMessageImage", "缺少必要参数：conversationId或imagePath"));
            return;
        }

        // 3. 构造SDK期望的消息对象
        json messageObj;
        messageObj["appCid"] = conversationId;
        messageObj["content"]["content_type"] = 2; // IMAGE类型
        messageObj["content"]["image_content"]["path"] = imagePath;
        messageObj["receivers"] = json::array();

        json args = json::array({messageObj, json::object()});
        printf("[DEBUG] SDK args for sendMessageImage: %s\n", args.dump().c_str());

        // 4. 调用真实的SDK API
        alibaba::fim::SendMessage(args, [callback](const std::string& result) {
            printf("[DEBUG] SDK API callback received: %s\n", result.c_str());
            callback(result);
        });

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in SendMessageImageAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("sendMessageImage", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in SendMessageImageAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("sendMessageImage", "Unknown exception occurred"));
    }
}

void FIMMessageHandler::SendMessageAtAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] SendMessageAtAsyncAdapter called with: %s\n", paramsJson.c_str());

    try {
        printf("[DEBUG] sendMessageAt (REAL SDK) - calling FIMMessageService::SendMessage\n");

        // 1. 解析JSON参数
        std::string conversationId = FIMCommonUtils::getStringParam(paramsJson, "conversationId", "");
        std::string text = FIMCommonUtils::getStringParam(paramsJson, "text", "");

        printf("[DEBUG] Parsed - conversationId: %s, text: %s\n", conversationId.c_str(), text.c_str());

        // 2. 验证必要参数
        if (conversationId.empty() || text.empty()) {
            callback(FIMCommonUtils::createErrorResult("sendMessageAt", "缺少必要参数：conversationId或text"));
            return;
        }

        // 3. 解析atUserIds数组
        json parsedParams = json::parse(paramsJson);
        json atUserIds = json::array();
        if (parsedParams.contains("atUserIds") && parsedParams["atUserIds"].is_array()) {
            atUserIds = parsedParams["atUserIds"];
        }

        // 4. 构造SDK期望的消息对象
        json messageObj;
        messageObj["appCid"] = conversationId;
        messageObj["content"]["content_type"] = 6; // AT类型
        messageObj["content"]["struct_content"]["text"] = text;
        messageObj["content"]["struct_content"]["atUserIds"] = atUserIds;
        messageObj["receivers"] = json::array();

        json args = json::array({messageObj, json::object()});
        printf("[DEBUG] SDK args for sendMessageAt: %s\n", args.dump().c_str());

        // 5. 调用真实的SDK API
        alibaba::fim::SendMessage(args, [callback](const std::string& result) {
            printf("[DEBUG] SDK API callback received: %s\n", result.c_str());
            callback(result);
        });

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in SendMessageAtAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("sendMessageAt", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in SendMessageAtAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("sendMessageAt", "Unknown exception occurred"));
    }
}

void FIMMessageHandler::SendMessageStructAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] SendMessageStructAsyncAdapter called with: %s\n", paramsJson.c_str());

    try {
        printf("[DEBUG] sendMessageStruct (REAL SDK) - calling FIMMessageService::SendMessage\n");

        // 1. 解析JSON参数
        std::string conversationId = FIMCommonUtils::getStringParam(paramsJson, "conversationId", "");

        printf("[DEBUG] Parsed - conversationId: %s\n", conversationId.c_str());

        // 2. 验证必要参数
        if (conversationId.empty()) {
            callback(FIMCommonUtils::createErrorResult("sendMessageStruct", "缺少必要参数：conversationId"));
            return;
        }

        // 3. 解析structData对象
        json parsedParams = json::parse(paramsJson);
        if (!parsedParams.contains("structData")) {
            callback(FIMCommonUtils::createErrorResult("sendMessageStruct", "缺少必要参数：structData"));
            return;
        }

        // 4. 构造SDK期望的消息对象
        json messageObj;
        messageObj["appCid"] = conversationId;
        messageObj["content"]["content_type"] = 5; // STRUCT类型
        messageObj["content"]["struct_content"] = parsedParams["structData"];
        messageObj["receivers"] = json::array();

        json args = json::array({messageObj, json::object()});
        printf("[DEBUG] SDK args for sendMessageStruct: %s\n", args.dump().c_str());

        // 5. 调用真实的SDK API
        alibaba::fim::SendMessage(args, [callback](const std::string& result) {
            printf("[DEBUG] SDK API callback received: %s\n", result.c_str());
            callback(result);
        });

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in SendMessageStructAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("sendMessageStruct", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in SendMessageStructAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("sendMessageStruct", "Unknown exception occurred"));
    }
}

void FIMMessageHandler::SendMessageVideoAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback) {
    printf("[DEBUG] SendMessageVideoAsyncAdapter called with: %s\n", paramsJson.c_str());

    try {
        printf("[DEBUG] sendMessageVideo (REAL SDK) - calling FIMMessageService::SendMessage\n");

        // 1. 解析JSON参数
        std::string conversationId = FIMCommonUtils::getStringParam(paramsJson, "conversationId", "");
        std::string videoPath = FIMCommonUtils::getStringParam(paramsJson, "videoPath", "");

        printf("[DEBUG] Parsed - conversationId: %s, videoPath: %s\n", conversationId.c_str(), videoPath.c_str());

        // 2. 验证必要参数
        if (conversationId.empty() || videoPath.empty()) {
            callback(FIMCommonUtils::createErrorResult("sendMessageVideo", "缺少必要参数：conversationId或videoPath"));
            return;
        }

        // 3. 构造SDK期望的消息对象
        json messageObj;
        messageObj["appCid"] = conversationId;
        messageObj["content"]["content_type"] = 4; // VIDEO类型
        messageObj["content"]["video_content"]["path"] = videoPath;
        messageObj["receivers"] = json::array();

        json args = json::array({messageObj, json::object()});
        printf("[DEBUG] SDK args for sendMessageVideo: %s\n", args.dump().c_str());

        // 4. 调用真实的SDK API
        alibaba::fim::SendMessage(args, [callback](const std::string& result) {
            printf("[DEBUG] SDK API callback received: %s\n", result.c_str());
            callback(result);
        });

    } catch (const std::exception& e) {
        printf("[ERROR] Exception in SendMessageVideoAsyncAdapter: %s\n", e.what());
        callback(FIMCommonUtils::createErrorResult("sendMessageVideo", e.what()));
    } catch (...) {
        printf("[ERROR] Unknown exception in SendMessageVideoAsyncAdapter\n");
        callback(FIMCommonUtils::createErrorResult("sendMessageVideo", "Unknown exception occurred"));
    }
}

// NAPI封装方法实现
napi_value FIMMessageHandler::SendMessageText(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimSendMessageText", SendMessageTextAsyncAdapter);
}

napi_value FIMMessageHandler::SendMessageCustom(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimSendMessageCustom", SendMessageCustomAsyncAdapter);
}

napi_value FIMMessageHandler::SendMessageImage(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimSendMessageImage", SendMessageImageAsyncAdapter);
}

napi_value FIMMessageHandler::SendMessageAt(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimSendMessageAt", SendMessageAtAsyncAdapter);
}

napi_value FIMMessageHandler::SendMessageStruct(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimSendMessageStruct", SendMessageStructAsyncAdapter);
}

napi_value FIMMessageHandler::SendMessageVideo(napi_env env, napi_callback_info info) {
    return HandleFIMMethodAsync(env, info, "fimSendMessageVideo", SendMessageVideoAsyncAdapter);
}
