// Copyright (c) 2025 Fliggy Team. All rights reserved.
// Created by guyan on 2025-08-21.
// FIM Setting Handler Implementation for fliggy_im module

#include "fim_setting_handler.h"
#include <cstdio>
#include <cstring>

// 需要包含FIM SDK相关头文件
#include "sdk/src/Engine/fim_engine.h"
#include "sdk/src/Engine/fim_setting_service.h"

using namespace alibaba::fim;

FIMSettingHandler& FIMSettingHandler::GetInstance() {
    static FIMSettingHandler instance;
    return instance;
}

template<typename SetterFunc>
napi_value FIMSettingHandler::HandleSetting(napi_env env, napi_callback_info info, 
                                           const char* settingName, SetterFunc setterFunc, 
                                           size_t bufferSize, bool returnPromise) {
    size_t argc = 1;
    napi_value args[1];

    napi_status status = napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    if (argc < 1) {
        napi_throw_error(env, nullptr, "Expected one argument");
        return nullptr;
    }

    // 动态分配缓冲区
    char* buffer = new char[bufferSize];
    size_t len;

    status = napi_get_value_string_utf8(env, args[0], buffer, bufferSize, &len);
    if (status != napi_ok) {
        delete[] buffer;
        napi_throw_error(env, nullptr, "Failed to get string value");
        return nullptr;
    }

    try {
        // 调用设置函数
        setterFunc(std::string(buffer));
        
        printf("[DEBUG] FIMSettingHandler: %s set to: %s\n", settingName, buffer);
        
        delete[] buffer;

        if (returnPromise) {
            // 创建并返回成功结果的 Promise
            napi_deferred deferred;
            napi_value promise;

            status = napi_create_promise(env, &deferred, &promise);
            if (status != napi_ok) {
                napi_throw_error(env, nullptr, "Failed to create promise");
                return nullptr;
            }

            // 创建成功消息
            std::string successMsg = std::string(settingName) + " set successfully";
            napi_value result;
            status = napi_create_string_utf8(env, successMsg.c_str(), NAPI_AUTO_LENGTH, &result);
            if (status != napi_ok) {
                napi_throw_error(env, nullptr, "Failed to create result string");
                return nullptr;
            }

            // 解析 Promise
            status = napi_resolve_deferred(env, deferred, result);
            if (status != napi_ok) {
                napi_throw_error(env, nullptr, "Failed to resolve promise");
                return nullptr;
            }

            return promise;
        } else {
            // 返回同步结果
            std::string successMsg = std::string(settingName) + " set successfully";
            napi_value result;
            napi_create_string_utf8(env, successMsg.c_str(), NAPI_AUTO_LENGTH, &result);
            return result;
        }

    } catch (const std::exception& e) {
        delete[] buffer;
        printf("[ERROR] FIMSettingHandler: Exception in %s: %s\n", settingName, e.what());
        napi_throw_error(env, nullptr, e.what());
        return nullptr;
    }
}

napi_value FIMSettingHandler::SetAppID(napi_env env, napi_callback_info info) {
    auto setter = [](const std::string& value) {
        auto service = FIMEngine::GetInstance().GetSettingService();
        service->SetAppID(value);
    };
    return HandleSetting(env, info, "AppID", setter, 256, true); // 返回Promise
}

napi_value FIMSettingHandler::SetAppName(napi_env env, napi_callback_info info) {
    auto setter = [](const std::string& value) {
        auto service = FIMEngine::GetInstance().GetSettingService();
        service->SetAppName(value);
    };
    return HandleSetting(env, info, "AppName", setter);
}

napi_value FIMSettingHandler::SetAppKey(napi_env env, napi_callback_info info) {
    auto setter = [](const std::string& value) {
        auto service = FIMEngine::GetInstance().GetSettingService();
        service->SetAppKey(value);
    };
    return HandleSetting(env, info, "AppKey", setter, 512);
}

napi_value FIMSettingHandler::SetDeviceType(napi_env env, napi_callback_info info) {
    auto setter = [](const std::string& value) {
        auto service = FIMEngine::GetInstance().GetSettingService();
        service->SetDeviceType(value);
    };
    return HandleSetting(env, info, "DeviceType", setter);
}

napi_value FIMSettingHandler::SetDeviceName(napi_env env, napi_callback_info info) {
    auto setter = [](const std::string& value) {
        auto service = FIMEngine::GetInstance().GetSettingService();
        service->SetDeviceName(value);
    };
    return HandleSetting(env, info, "DeviceName", setter);
}

napi_value FIMSettingHandler::SetDeviceId(napi_env env, napi_callback_info info) {
    auto setter = [](const std::string& value) {
        auto service = FIMEngine::GetInstance().GetSettingService();
        service->SetDeviceId(value);
    };
    return HandleSetting(env, info, "DeviceId", setter);
}

napi_value FIMSettingHandler::SetDataPath(napi_env env, napi_callback_info info) {
    auto setter = [](const std::string& value) {
        auto service = FIMEngine::GetInstance().GetSettingService();
        service->SetDataPath(value);
    };
    return HandleSetting(env, info, "DataPath", setter, 512);
}

napi_value FIMSettingHandler::SetOsName(napi_env env, napi_callback_info info) {
    auto setter = [](const std::string& value) {
        auto service = FIMEngine::GetInstance().GetSettingService();
        service->SetOSName(value);
    };
    return HandleSetting(env, info, "OsName", setter);
}

napi_value FIMSettingHandler::SetOsVersion(napi_env env, napi_callback_info info) {
    auto setter = [](const std::string& value) {
        auto service = FIMEngine::GetInstance().GetSettingService();
        service->SetOSVersion(value);
    };
    return HandleSetting(env, info, "OsVersion", setter);
}

napi_value FIMSettingHandler::SetAppVersion(napi_env env, napi_callback_info info) {
    auto setter = [](const std::string& value) {
        auto service = FIMEngine::GetInstance().GetSettingService();
        service->SetAppVersion(value);
    };
    return HandleSetting(env, info, "AppVersion", setter);
}
