// Copyright (c) 2025 Fliggy Team. All rights reserved.
// Created by guyan on 2025-08-21.
// FIM Setting Handler for fliggy_im module

#ifndef FIM_SETTING_HANDLER_H
#define FIM_SETTING_HANDLER_H

#include "napi/native_api.h"
#include <string>

/**
 * @brief FIM设置处理器
 * 负责处理各种设置服务相关的业务逻辑
 */
class FIMSettingHandler {
public:
    /**
     * @brief 获取单例实例
     */
    static FIMSettingHandler& GetInstance();

    // 设置相关的NAPI函数
    napi_value SetAppID(napi_env env, napi_callback_info info);
    napi_value SetAppName(napi_env env, napi_callback_info info);
    napi_value SetAppKey(napi_env env, napi_callback_info info);
    napi_value SetDeviceType(napi_env env, napi_callback_info info);
    napi_value SetDeviceName(napi_env env, napi_callback_info info);
    napi_value SetDeviceId(napi_env env, napi_callback_info info);
    napi_value SetDataPath(napi_env env, napi_callback_info info);
    napi_value SetOsName(napi_env env, napi_callback_info info);
    napi_value SetOsVersion(napi_env env, napi_callback_info info);
    napi_value SetAppVersion(napi_env env, napi_callback_info info);

private:
    FIMSettingHandler() = default;
    ~FIMSettingHandler() = default;
    FIMSettingHandler(const FIMSettingHandler&) = delete;
    FIMSettingHandler& operator=(const FIMSettingHandler&) = delete;

    /**
     * @brief 通用的设置函数模板
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @param settingName 设置名称（用于日志和错误信息）
     * @param setterFunc 具体的设置函数
     * @param bufferSize 字符串缓冲区大小
     * @param returnPromise 是否返回Promise（默认false，返回同步结果）
     * @return napi_value 结果值
     */
    template<typename SetterFunc>
    napi_value HandleSetting(napi_env env, napi_callback_info info, 
                            const char* settingName, SetterFunc setterFunc, 
                            size_t bufferSize = 256, bool returnPromise = false);
};

#endif // FIM_SETTING_HANDLER_H
