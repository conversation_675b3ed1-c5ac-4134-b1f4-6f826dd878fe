// Copyright (c) 2025 Fliggy Team. All rights reserved.
// Created by guyan on 2025-08-21.
// FIM Message Handler for fliggy_im module

#ifndef FIM_MESSAGE_HANDLER_H
#define FIM_MESSAGE_HANDLER_H

#include "napi/native_api.h"
#include "../utils/thread_safe_promise_handler.h"
#include "../utils/fim_common_utils.h"
#include <string>

/**
 * @brief FIM消息处理器
 * 负责处理消息发送等相关业务逻辑
 */
class FIMMessageHandler {
public:
    /**
     * @brief 获取单例实例
     */
    static FIMMessageHandler& GetInstance();

    /**
     * @brief 发送消息的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value SendMessage(napi_env env, napi_callback_info info);

    /**
     * @brief 重发消息的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value ResendMessage(napi_env env, napi_callback_info info);

    /**
     * @brief 回复消息的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value ReplyMessage(napi_env env, napi_callback_info info);

    /**
     * @brief 获取消息的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value GetMessage(napi_env env, napi_callback_info info);

    /**
     * @brief 获取本地消息的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value GetLocalMessage(napi_env env, napi_callback_info info);

    /**
     * @brief 删除消息的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value DeleteMessage(napi_env env, napi_callback_info info);

    /**
     * @brief 撤回消息的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value RecallMessage(napi_env env, napi_callback_info info);

    /**
     * @brief 获取上一页本地消息的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value ListPreviousLocalMsgs(napi_env env, napi_callback_info info);

    /**
     * @brief 获取下一页本地消息的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value ListNextLocalMsgs(napi_env env, napi_callback_info info);

    /**
     * @brief 获取上一页消息的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value ListPreviousMsgs(napi_env env, napi_callback_info info);

    /**
     * @brief 获取下一页消息的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value ListNextMsgs(napi_env env, napi_callback_info info);

    /**
     * @brief 获取消息已读状态的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value ListMessagesReadStatus(napi_env env, napi_callback_info info);

    /**
     * @brief 更新消息为已读的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value UpdateMessageToRead(napi_env env, napi_callback_info info);

    /**
     * @brief 删除本地消息的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value DeleteLocalMessage(napi_env env, napi_callback_info info);

    /**
     * @brief 获取会话列表的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value GetConversations(napi_env env, napi_callback_info info);

    // ============================================================================
    // Phase 2: 新增认证和事件监听API
    // ============================================================================

    /**
     * @brief 登录认证的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value Login(napi_env env, napi_callback_info info);

    /**
     * @brief 消息事件监听的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value OnMessageEvent(napi_env env, napi_callback_info info);

    /**
     * @brief 会话事件监听的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value OnConversationEvent(napi_env env, napi_callback_info info);

    /**
     * @brief 群组事件监听的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value OnGroupEvent(napi_env env, napi_callback_info info);

    // ============================================================================
    // Phase 3: 新增消息发送API
    // ============================================================================

    /**
     * @brief 发送文本消息的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value SendMessageText(napi_env env, napi_callback_info info);

    /**
     * @brief 发送自定义消息的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value SendMessageCustom(napi_env env, napi_callback_info info);

    /**
     * @brief 发送图片消息的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value SendMessageImage(napi_env env, napi_callback_info info);

    /**
     * @brief 发送@消息的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value SendMessageAt(napi_env env, napi_callback_info info);

    /**
     * @brief 发送结构化消息的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value SendMessageStruct(napi_env env, napi_callback_info info);

    /**
     * @brief 发送视频消息的NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @return Promise对象
     */
    napi_value SendMessageVideo(napi_env env, napi_callback_info info);

private:
    FIMMessageHandler() = default;
    ~FIMMessageHandler() = default;
    FIMMessageHandler(const FIMMessageHandler&) = delete;
    FIMMessageHandler& operator=(const FIMMessageHandler&) = delete;

    /**
     * @brief 发送消息的异步适配器
     * @param messageJson 消息JSON字符串
     * @param callback 回调函数
     */
    static void SendMessageAsyncAdapter(const std::string& messageJson, LocalFIMCallback callback);

    /**
     * @brief 重发消息的异步适配器
     * @param paramsJson 重发参数JSON字符串
     * @param callback 回调函数
     */
    static void ResendMessageAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    /**
     * @brief 回复消息的异步适配器
     * @param paramsJson 回复参数JSON字符串
     * @param callback 回调函数
     */
    static void ReplyMessageAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    /**
     * @brief 获取消息的异步适配器
     * @param paramsJson 获取消息参数JSON字符串
     * @param callback 回调函数
     */
    static void GetMessageAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    /**
     * @brief 获取本地消息的异步适配器
     * @param paramsJson 获取本地消息参数JSON字符串
     * @param callback 回调函数
     */
    static void GetLocalMessageAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    /**
     * @brief 删除消息的异步适配器
     * @param paramsJson 删除消息参数JSON字符串
     * @param callback 回调函数
     */
    static void DeleteMessageAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    /**
     * @brief 撤回消息的异步适配器
     * @param paramsJson 撤回消息参数JSON字符串
     * @param callback 回调函数
     */
    static void RecallMessageAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    /**
     * @brief 获取上一页本地消息的异步适配器
     * @param paramsJson 获取消息参数JSON字符串
     * @param callback 回调函数
     */
    static void ListPreviousLocalMsgsAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    /**
     * @brief 获取下一页本地消息的异步适配器
     * @param paramsJson 获取消息参数JSON字符串
     * @param callback 回调函数
     */
    static void ListNextLocalMsgsAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    /**
     * @brief 获取上一页消息的异步适配器
     * @param paramsJson 获取消息参数JSON字符串
     * @param callback 回调函数
     */
    static void ListPreviousMsgsAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    /**
     * @brief 获取下一页消息的异步适配器
     * @param paramsJson 获取消息参数JSON字符串
     * @param callback 回调函数
     */
    static void ListNextMsgsAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    /**
     * @brief 获取消息已读状态的异步适配器
     * @param paramsJson 获取已读状态参数JSON字符串
     * @param callback 回调函数
     */
    static void ListMessagesReadStatusAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    /**
     * @brief 更新消息为已读的异步适配器
     * @param paramsJson 更新已读参数JSON字符串
     * @param callback 回调函数
     */
    static void UpdateMessageToReadAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    /**
     * @brief 删除本地消息的异步适配器
     * @param paramsJson 删除本地消息参数JSON字符串
     * @param callback 回调函数
     */
    static void DeleteLocalMessageAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    /**
     * @brief 获取会话列表的异步适配器
     * @param paramsJson 获取会话列表参数JSON字符串
     * @param callback 回调函数
     */
    static void GetConversationsAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    // ============================================================================
    // Phase 2: 新增认证和事件监听API的异步适配器
    // ============================================================================

    /**
     * @brief 登录认证的异步适配器
     * @param paramsJson 登录参数JSON字符串
     * @param callback 回调函数
     */
    static void LoginAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    /**
     * @brief 消息事件监听的异步适配器
     * @param paramsJson 事件监听参数JSON字符串
     * @param callback 回调函数
     */
    static void OnMessageEventAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    /**
     * @brief 会话事件监听的异步适配器
     * @param paramsJson 事件监听参数JSON字符串
     * @param callback 回调函数
     */
    static void OnConversationEventAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    /**
     * @brief 群组事件监听的异步适配器
     * @param paramsJson 事件监听参数JSON字符串
     * @param callback 回调函数
     */
    static void OnGroupEventAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    // ============================================================================
    // Phase 3: 新增消息发送API的异步适配器
    // ============================================================================

    /**
     * @brief 发送文本消息的异步适配器
     * @param paramsJson 消息参数JSON字符串
     * @param callback 回调函数
     */
    static void SendMessageTextAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    /**
     * @brief 发送自定义消息的异步适配器
     * @param paramsJson 消息参数JSON字符串
     * @param callback 回调函数
     */
    static void SendMessageCustomAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    /**
     * @brief 发送图片消息的异步适配器
     * @param paramsJson 消息参数JSON字符串
     * @param callback 回调函数
     */
    static void SendMessageImageAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    /**
     * @brief 发送@消息的异步适配器
     * @param paramsJson 消息参数JSON字符串
     * @param callback 回调函数
     */
    static void SendMessageAtAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    /**
     * @brief 发送结构化消息的异步适配器
     * @param paramsJson 消息参数JSON字符串
     * @param callback 回调函数
     */
    static void SendMessageStructAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    /**
     * @brief 发送视频消息的异步适配器
     * @param paramsJson 消息参数JSON字符串
     * @param callback 回调函数
     */
    static void SendMessageVideoAsyncAdapter(const std::string& paramsJson, LocalFIMCallback callback);

    /**
     * @brief 简单的JSON解析辅助函数
     * @param json JSON字符串
     * @param key 要提取的键
     * @return 提取的值
     */
    static std::string ExtractJsonValue(const std::string& json, const std::string& key);

    /**
     * @brief 统一的FIM方法NAPI封装函数
     * @param env NAPI环境句柄
     * @param info NAPI回调信息
     * @param methodName 方法名称
     * @param businessFunc 业务逻辑函数指针
     * @return Promise对象
     */
    template<typename BusinessFunc>
    napi_value HandleFIMMethodAsync(napi_env env, napi_callback_info info,
                                   const char* methodName, BusinessFunc businessFunc);
};

#endif // FIM_MESSAGE_HANDLER_H
