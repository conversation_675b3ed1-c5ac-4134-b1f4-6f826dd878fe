// Copyright (c) 2025 Fliggy Team. All rights reserved.
// Created by guyan on 2025-08-21.
// FIM Common Utils for fliggy_im module - Migrated from Bridge

#ifndef FIM_COMMON_UTILS_H
#define FIM_COMMON_UTILS_H

#include <string>
#include <map>
#include <vector>
#include <sstream>
#include <ctime>
#include <memory>

namespace fliggy {
namespace fim {
namespace napi {

// 桥接层类型定义（避免与真实SDK命名空间冲突）
namespace common_types {
    struct FIMError {
        std::string reason;
        int code;
        
        FIMError(const std::string& r = "", int c = -1) : reason(r), code(c) {}
    };
    
    struct FIMMessage {
        std::string mid;
        std::string appCid;
        std::string localId;
        int64_t timestamp;
        int64_t createTime;
        
        FIMMessage() : timestamp(0), createTime(0) {}
    };
    
    struct FIMSendMessageText {
        std::string appCid;
        std::string text;
        std::vector<std::string> receivers;
    };
} // namespace common_types



/**
 * SDK模式枚举
 */
enum class SDKMode {
    MOCK = 0,    // 模拟模式
    REAL = 1     // 真实SDK模式
};

/**
 * 统一的结果结构
 */
struct FIMResult {
    bool success;
    std::string data;
    std::string error;
    std::string method;
    std::string sdk_mode;
    
    FIMResult(bool success = true, const std::string& data = "",
              const std::string& error = "", const std::string& method = "")
        : success(success), data(data), error(error), method(method) {
        sdk_mode = "real"; // 始终使用真实SDK
    }
    
    // 转换为JSON字符串
    std::string toJson() const;
};

/**
 * 公共工具类
 * 整合了Bridge中的工具函数，适配NAPI架构
 */
class FIMCommonUtils {
public:
    /**
     * JSON参数解析工具
     * 从JSON字符串中提取指定键的字符串值
     */
    static std::string getStringParam(const std::string& json, const std::string& key, const std::string& defaultValue = "");
    
    /**
     * 从JSON中提取整数值
     */
    static int getIntParam(const std::string& json, const std::string& key, int defaultValue = 0);
    
    /**
     * 从JSON中提取布尔值
     */
    static bool getBoolParam(const std::string& json, const std::string& key, bool defaultValue = false);
    
    /**
     * 生成唯一ID
     */
    static std::string generateId(const std::string& prefix = "id");
    
    /**
     * 创建错误结果JSON
     */
    static std::string createErrorResult(const std::string& method, const std::string& error, int code = -1);
    
    /**
     * 创建成功结果JSON
     */
    static std::string createSuccessResult(const std::string& method, const std::string& data);
    
    /**
     * 解析接收者列表
     * 从JSON字符串中解析出接收者数组
     */
    static std::vector<std::string> parseReceivers(const std::string& receiverList);
    
    /**
     * 生成模拟消息响应（用于测试）
     */
    static std::string generateMockMessage(const std::string& method, const std::string& messageId);
    
    /**
     * 验证JSON格式的基本有效性
     */
    static bool isValidJson(const std::string& json);
    
    /**
     * 转义JSON字符串中的特殊字符
     */
    static std::string escapeJsonString(const std::string& input);
    
    /**
     * 获取当前时间戳（毫秒）
     */
    static int64_t getCurrentTimestamp();
    
    /**
     * 格式化时间戳为可读字符串
     */
    static std::string formatTimestamp(int64_t timestamp);

private:
    // 私有构造函数，工具类不允许实例化
    FIMCommonUtils() = delete;
    ~FIMCommonUtils() = delete;
    FIMCommonUtils(const FIMCommonUtils&) = delete;
    FIMCommonUtils& operator=(const FIMCommonUtils&) = delete;
};

/**
 * SDK回调同步等待机制
 * 适配NAPI的异步架构
 */
class SDKCallbackSync {
private:
    std::string result_;
    bool executed_;
    const int maxWaitCount_;
    const int sleepInterval_;
    
public:
    SDKCallbackSync(int maxWaitMs = 1000);
    
    // 设置结果并标记完成
    void setResult(const std::string& result);
    
    // 等待回调完成并返回结果
    std::string waitForResult();
    
    // 检查是否已执行
    bool isExecuted() const { return executed_; }
    
    // 重置状态（用于复用）
    void reset();
};

} // namespace napi
} // namespace fim
} // namespace fliggy

#endif // FIM_COMMON_UTILS_H
