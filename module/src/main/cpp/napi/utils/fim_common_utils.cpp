// Copyright (c) 2025 Fliggy Team. All rights reserved.
// Created by guyan on 2025-08-21.
// FIM Common Utils Implementation - Migrated from Bridge

#include "fim_common_utils.h"
#include <cstdio>
#include <chrono>
#include <iomanip>

namespace fliggy {
namespace fim {
namespace napi {



std::string FIMResult::toJson() const {
    std::ostringstream oss;
    oss << "{";
    oss << "\"success\":" << (success ? "true" : "false") << ",";
    oss << "\"data\":" << (data.empty() ? "null" : data) << ",";
    oss << "\"error\":\"" << FIMCommonUtils::escapeJsonString(error) << "\",";
    oss << "\"method\":\"" << FIMCommonUtils::escapeJsonString(method) << "\",";
    oss << "\"sdk_mode\":\"" << sdk_mode << "\"";
    oss << "}";
    return oss.str();
}

std::string FIMCommonUtils::getStringParam(const std::string& json, const std::string& key, const std::string& defaultValue) {
    // 简单的字符串查找，实际项目中应该使用正式的JSON解析库
    std::string searchKey = "\"" + key + "\"";
    size_t keyPos = json.find(searchKey);
    if (keyPos == std::string::npos) {
        return defaultValue;
    }
    
    size_t colonPos = json.find(":", keyPos);
    if (colonPos == std::string::npos) {
        return defaultValue;
    }
    
    size_t valueStart = json.find("\"", colonPos);
    if (valueStart == std::string::npos) {
        return defaultValue;
    }
    valueStart++; // 跳过开始引号
    
    size_t valueEnd = json.find("\"", valueStart);
    if (valueEnd == std::string::npos) {
        return defaultValue;
    }
    
    return json.substr(valueStart, valueEnd - valueStart);
}

int FIMCommonUtils::getIntParam(const std::string& json, const std::string& key, int defaultValue) {
    std::string searchKey = "\"" + key + "\":";
    size_t keyPos = json.find(searchKey);
    if (keyPos == std::string::npos) {
        return defaultValue;
    }
    
    size_t valueStart = keyPos + searchKey.length();
    // 跳过空格
    while (valueStart < json.length() && (json[valueStart] == ' ' || json[valueStart] == '\t')) {
        valueStart++;
    }
    
    if (valueStart >= json.length()) {
        return defaultValue;
    }
    
    // 查找数字结束位置
    size_t valueEnd = valueStart;
    while (valueEnd < json.length() && 
           (std::isdigit(json[valueEnd]) || json[valueEnd] == '-' || json[valueEnd] == '+')) {
        valueEnd++;
    }
    
    if (valueEnd > valueStart) {
        try {
            return std::stoi(json.substr(valueStart, valueEnd - valueStart));
        } catch (...) {
            return defaultValue;
        }
    }
    
    return defaultValue;
}

bool FIMCommonUtils::getBoolParam(const std::string& json, const std::string& key, bool defaultValue) {
    std::string searchKey = "\"" + key + "\":";
    size_t keyPos = json.find(searchKey);
    if (keyPos == std::string::npos) {
        return defaultValue;
    }
    
    size_t valueStart = keyPos + searchKey.length();
    // 跳过空格
    while (valueStart < json.length() && (json[valueStart] == ' ' || json[valueStart] == '\t')) {
        valueStart++;
    }
    
    if (valueStart >= json.length()) {
        return defaultValue;
    }
    
    // 检查true/false
    if (json.substr(valueStart, 4) == "true") {
        return true;
    } else if (json.substr(valueStart, 5) == "false") {
        return false;
    }
    
    return defaultValue;
}

std::string FIMCommonUtils::generateId(const std::string& prefix) {
    auto now = std::chrono::system_clock::now();
    auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    return prefix + "_" + std::to_string(timestamp);
}

std::string FIMCommonUtils::createErrorResult(const std::string& method, const std::string& error, int code) {
    std::ostringstream oss;
    oss << "{";
    oss << "\"success\":false,";
    oss << "\"data\":null,";
    oss << "\"message\":\"" << escapeJsonString(error) << "\",";
    oss << "\"code\":" << code << ",";
    oss << "\"error\":\"" << escapeJsonString(error) << "\",";
    oss << "\"method\":\"" << escapeJsonString(method) << "\"";
    oss << "}";
    return oss.str();
}

std::string FIMCommonUtils::createSuccessResult(const std::string& method, const std::string& data) {
    std::ostringstream oss;
    oss << "{";
    oss << "\"success\":true,";
    oss << "\"data\":" << data << ",";
    oss << "\"message\":\"操作成功\",";
    oss << "\"code\":0,";
    oss << "\"method\":\"" << escapeJsonString(method) << "\"";
    oss << "}";
    return oss.str();
}

std::vector<std::string> FIMCommonUtils::parseReceivers(const std::string& receiverList) {
    std::vector<std::string> receivers;
    
    if (receiverList.find("[") != std::string::npos) {
        size_t start = 0;
        while ((start = receiverList.find('"', start)) != std::string::npos) {
            start++; // 跳过开始引号
            size_t end = receiverList.find('"', start);
            if (end != std::string::npos) {
                std::string receiver = receiverList.substr(start, end - start);
                if (!receiver.empty() && receiver != "," && receiver != " ") {
                    receivers.push_back(receiver);
                }
                start = end + 1;
            } else {
                break;
            }
        }
    }
    
    return receivers;
}

std::string FIMCommonUtils::generateMockMessage(const std::string& method, const std::string& messageId) {
    std::ostringstream oss;
    oss << "{";
    oss << "\"success\":true,";
    oss << "\"data\":{";
    oss << "\"messageId\":\"" << messageId << "\",";
    oss << "\"timestamp\":" << getCurrentTimestamp() << ",";
    oss << "\"status\":\"sent\",";
    oss << "\"mode\":\"mock\"";
    oss << "},";
    oss << "\"message\":\"模拟消息发送成功\",";
    oss << "\"code\":0,";
    oss << "\"method\":\"" << escapeJsonString(method) << "\"";
    oss << "}";
    return oss.str();
}

bool FIMCommonUtils::isValidJson(const std::string& json) {
    // 简单的JSON格式检查
    if (json.empty()) return false;
    
    // 检查是否以{开头，}结尾
    size_t start = json.find_first_not_of(" \t\n\r");
    size_t end = json.find_last_not_of(" \t\n\r");
    
    if (start == std::string::npos || end == std::string::npos) return false;
    
    return (json[start] == '{' && json[end] == '}') || 
           (json[start] == '[' && json[end] == ']');
}

std::string FIMCommonUtils::escapeJsonString(const std::string& input) {
    std::string result;
    result.reserve(input.length() * 2); // 预分配空间
    
    for (char c : input) {
        switch (c) {
            case '"':  result += "\\\""; break;
            case '\\': result += "\\\\"; break;
            case '\b': result += "\\b"; break;
            case '\f': result += "\\f"; break;
            case '\n': result += "\\n"; break;
            case '\r': result += "\\r"; break;
            case '\t': result += "\\t"; break;
            default:   result += c; break;
        }
    }
    
    return result;
}

int64_t FIMCommonUtils::getCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    return std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
}

std::string FIMCommonUtils::formatTimestamp(int64_t timestamp) {
    auto time_point = std::chrono::system_clock::from_time_t(timestamp / 1000);
    auto time_t = std::chrono::system_clock::to_time_t(time_point);
    
    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    oss << "." << std::setfill('0') << std::setw(3) << (timestamp % 1000);
    
    return oss.str();
}

// SDKCallbackSync 实现
SDKCallbackSync::SDKCallbackSync(int maxWaitMs) 
    : executed_(false), 
      maxWaitCount_(maxWaitMs / 10), 
      sleepInterval_(10000000) {} // 10ms

void SDKCallbackSync::setResult(const std::string& result) {
    result_ = result;
    executed_ = true;
}

std::string SDKCallbackSync::waitForResult() {
    int waitCount = 0;
    while (!executed_ && waitCount < maxWaitCount_) {
        // 简单的忙等待
        for (int i = 0; i < sleepInterval_; i++) { 
            volatile int dummy = i; /* 防止编译器优化 */
            (void)dummy; /* 避免未使用变量警告 */
            /* 忙等待 */ 
        }   
        waitCount++;
    }
    
    if (executed_) {
        return result_;
    } else {
        return FIMCommonUtils::createErrorResult("unknown", "SDK调用超时");
    }
}

void SDKCallbackSync::reset() {
    result_.clear();
    executed_ = false;
}

} // namespace napi
} // namespace fim
} // namespace fliggy
