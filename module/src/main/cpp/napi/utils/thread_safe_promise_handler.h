// Copyright (c) 2025 Fliggy Team. All rights reserved.
// Created by guyan on 2025-08-21.
// Thread Safe Promise Handler for fliggy_im module

#ifndef THREAD_SAFE_PROMISE_HANDLER_H
#define THREAD_SAFE_PROMISE_HANDLER_H

#include "napi/native_api.h"
#include "napi_context_manager.h"
#include <string>
#include <functional>

// 定义本地回调函数类型
typedef void (*LocalFIMCallback)(const std::string& result);

/**
 * @brief 线程安全Promise处理器
 * 负责处理跨线程的Promise解析和拒绝操作
 */
class ThreadSafePromiseHandler {
public:
    /**
     * @brief 获取单例实例
     */
    static ThreadSafePromiseHandler& GetInstance();

    /**
     * @brief 线程安全的Promise解析函数
     * @param contextId 上下文ID
     * @param result 结果字符串
     */
    void ResolvePromiseWithResult(int contextId, const std::string& result);

    /**
     * @brief 线程安全的Promise拒绝函数
     * @param contextId 上下文ID
     * @param error 错误信息
     */
    void RejectPromiseWithError(int contextId, const std::string& error);

    /**
     * @brief 创建Promise回调函数
     * @param contextId 上下文ID
     * @return 回调函数指针
     */
    LocalFIMCallback CreatePromiseCallback(int contextId);

    /**
     * @brief 线程安全回调函数，在主线程中执行
     * 这个函数会被napi_threadsafe_function调用，确保在主线程中执行
     */
    static void ThreadSafeCallback(napi_env env, napi_value js_callback, void* context, void* data);

private:
    ThreadSafePromiseHandler() = default;
    ~ThreadSafePromiseHandler() = default;
    ThreadSafePromiseHandler(const ThreadSafePromiseHandler&) = delete;
    ThreadSafePromiseHandler& operator=(const ThreadSafePromiseHandler&) = delete;

    /**
     * @brief C风格回调函数适配器
     * @param result 结果字符串
     */
    static void CStyleCallbackAdapter(const std::string& result);

    // 线程局部存储的当前上下文ID
    static thread_local int currentContextId_;
};

#endif // THREAD_SAFE_PROMISE_HANDLER_H
