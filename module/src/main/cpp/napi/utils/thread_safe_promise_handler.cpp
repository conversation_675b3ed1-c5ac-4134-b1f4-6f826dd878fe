// Copyright (c) 2025 Fliggy Team. All rights reserved.
// Created by guyan on 2025-08-21.
// Thread Safe Promise Handler Implementation for fliggy_im module

#include "thread_safe_promise_handler.h"
#include <cstdio>

// 线程局部存储的当前上下文ID
thread_local int ThreadSafePromiseHandler::currentContextId_ = 0;

ThreadSafePromiseHandler& ThreadSafePromiseHandler::GetInstance() {
    static ThreadSafePromiseHandler instance;
    return instance;
}

void ThreadSafePromiseHandler::ThreadSafeCallback(napi_env env, napi_value js_callback, void* context, void* data) {
    // 避免未使用参数警告
    (void)js_callback;

    ThreadSafeCallbackData* callbackData = static_cast<ThreadSafeCallbackData*>(data);
    NAPIPromiseContext* promiseContext = static_cast<NAPIPromiseContext*>(context);

    if (!callbackData || !promiseContext) {
        printf("[ERROR] ThreadSafeCallback: callbackData=%p, promiseContext=%p\n", callbackData, promiseContext);
        return;
    }

    // 在主线程中解析Promise
    napi_value napiResult;
    napi_status status = napi_create_string_utf8(env, callbackData->result.c_str(), NAPI_AUTO_LENGTH, &napiResult);
    if (status == napi_ok) {
        napi_resolve_deferred(env, promiseContext->deferred, napiResult);
        printf("[DEBUG] Promise resolved successfully with result: %s\n", callbackData->result.c_str());
    } else {
        printf("[ERROR] Failed to create result string, rejecting Promise\n");
        napi_value error;
        napi_create_string_utf8(env, "Failed to create result value", NAPI_AUTO_LENGTH, &error);
        napi_reject_deferred(env, promiseContext->deferred, error);
    }

    // 清理数据
    delete callbackData;

    // 释放线程安全函数
    if (promiseContext->tsfn) {
        napi_release_threadsafe_function(promiseContext->tsfn, napi_tsfn_release);
    }
    delete promiseContext;
}

void ThreadSafePromiseHandler::ResolvePromiseWithResult(int contextId, const std::string& result) {
    // 获取Promise上下文
    NAPIPromiseContext* context = NAPIContextManager::GetInstance().RetrievePromiseContext(contextId);
    if (!context) {
        printf("[WARN] ThreadSafePromiseHandler: Context %d not found, cannot resolve promise\n", contextId);
        return;
    }

    // 使用线程安全函数调用Promise解析
    if (context->tsfn) {
        // 创建回调数据
        ThreadSafeCallbackData* callbackData = new ThreadSafeCallbackData(result, contextId);

        // 使用线程安全函数将结果传递回主线程
        napi_status status = napi_call_threadsafe_function(context->tsfn, callbackData, napi_tsfn_blocking);

        if (status != napi_ok) {
            printf("[ERROR] napi_call_threadsafe_function failed with status: %d\n", status);
            // 如果调用失败，需要清理资源
            delete callbackData;
            if (context->tsfn) {
                napi_release_threadsafe_function(context->tsfn, napi_tsfn_release);
            }
            delete context;
        }
    } else {
        printf("[ERROR] context->tsfn is null, cannot use threadsafe function\n");
        delete context;
    }
}

void ThreadSafePromiseHandler::RejectPromiseWithError(int contextId, const std::string& error) {
    // 获取Promise上下文
    NAPIPromiseContext* context = NAPIContextManager::GetInstance().RetrievePromiseContext(contextId);
    if (!context) {
        printf("[WARN] ThreadSafePromiseHandler: Context %d not found, cannot reject promise\n", contextId);
        return;
    }

    // 直接在当前线程拒绝Promise（简化处理）
    napi_value errorValue;
    napi_status status = napi_create_string_utf8(context->env, error.c_str(), NAPI_AUTO_LENGTH, &errorValue);
    if (status == napi_ok) {
        napi_reject_deferred(context->env, context->deferred, errorValue);
    }

    // 清理资源
    if (context->tsfn) {
        napi_release_threadsafe_function(context->tsfn, napi_tsfn_release);
    }
    delete context;
}

void ThreadSafePromiseHandler::CStyleCallbackAdapter(const std::string& result) {
    GetInstance().ResolvePromiseWithResult(currentContextId_, result);
}

LocalFIMCallback ThreadSafePromiseHandler::CreatePromiseCallback(int contextId) {
    // 存储contextId到线程局部存储中
    currentContextId_ = contextId;
    
    // 返回C风格的回调函数
    return CStyleCallbackAdapter;
}
