// Copyright (c) 2025 Fliggy Team. All rights reserved.
// Created by guyan on 2025-08-21.
// NAPI Context Manager for fliggy_im module

#ifndef NAPI_CONTEXT_MANAGER_H
#define NAPI_CONTEXT_MANAGER_H

#include "napi/native_api.h"
#include <memory>
#include <map>
#include <mutex>
#include <atomic>

/**
 * @brief NAPI Promise上下文结构
 * 用于在业务层回调中resolve Promise，使用线程安全函数来跨线程通信
 */
struct NAPIPromiseContext {
    napi_env env;
    napi_deferred deferred;
    napi_threadsafe_function tsfn;  // 线程安全函数

    NAPIPromiseContext(napi_env e, napi_deferred d, napi_threadsafe_function t)
        : env(e), deferred(d), tsfn(t) {}
};

/**
 * @brief 线程安全回调数据结构
 */
struct ThreadSafeCallbackData {
    std::string result;
    int contextId;

    ThreadSafeCallbackData(const std::string& r, int id) : result(r), contextId(id) {}
};

/**
 * @brief NAPI上下文管理器
 * 负责管理Promise上下文的生命周期，包括ID生成、存储、获取和清理
 */
class NAPIContextManager {
public:
    /**
     * @brief 获取单例实例
     */
    static NAPIContextManager& GetInstance();

    /**
     * @brief 生成唯一的上下文ID
     * @return 新的上下文ID
     */
    int GenerateContextId();

    /**
     * @brief 存储Promise上下文
     * @param context Promise上下文指针
     * @return 分配的上下文ID
     */
    int StorePromiseContext(NAPIPromiseContext* context);

    /**
     * @brief 获取并移除Promise上下文
     * @param contextId 上下文ID
     * @return Promise上下文指针，如果不存在则返回nullptr
     */
    NAPIPromiseContext* RetrievePromiseContext(int contextId);

    /**
     * @brief 清理所有上下文（用于模块卸载时）
     */
    void ClearAllContexts();

    /**
     * @brief 获取当前上下文数量（用于调试）
     */
    size_t GetContextCount() const;

private:
    NAPIContextManager() = default;
    ~NAPIContextManager() = default;
    NAPIContextManager(const NAPIContextManager&) = delete;
    NAPIContextManager& operator=(const NAPIContextManager&) = delete;

    // Promise上下文映射表，用于关联回调ID和Promise上下文
    std::map<int, NAPIPromiseContext*> promiseContextMap_;
    mutable std::mutex contextMapMutex_;
    std::atomic<int> nextContextId_{1};
};

#endif // NAPI_CONTEXT_MANAGER_H
