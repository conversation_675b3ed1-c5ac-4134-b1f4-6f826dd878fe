// Copyright (c) 2025 Fliggy Team. All rights reserved.
// Created by guyan on 2025-08-21.
// NAPI Context Manager Implementation for fliggy_im module

#include "napi_context_manager.h"
#include <cstdio>

NAPIContextManager& NAPIContextManager::GetInstance() {
    static NAPIContextManager instance;
    return instance;
}

int NAPIContextManager::GenerateContextId() {
    return nextContextId_.fetch_add(1);
}

int NAPIContextManager::StorePromiseContext(NAPIPromiseContext* context) {
    if (!context) {
        printf("[ERROR] NAPIContextManager::StorePromiseContext: context is null\n");
        return -1;
    }

    int contextId = GenerateContextId();
    std::lock_guard<std::mutex> lock(contextMapMutex_);
    promiseContextMap_[contextId] = context;
    
    printf("[DEBUG] NAPIContextManager: Stored context %d, total contexts: %zu\n", 
           contextId, promiseContextMap_.size());
    
    return contextId;
}

NAPIPromiseContext* NAPIContextManager::RetrievePromiseContext(int contextId) {
    std::lock_guard<std::mutex> lock(contextMapMutex_);
    auto it = promiseContextMap_.find(contextId);
    if (it != promiseContextMap_.end()) {
        NAPIPromiseContext* context = it->second;
        promiseContextMap_.erase(it);
        
        printf("[DEBUG] NAPIContextManager: Retrieved and removed context %d, remaining: %zu\n", 
               contextId, promiseContextMap_.size());
        
        return context;
    }
    
    printf("[WARN] NAPIContextManager: Context %d not found\n", contextId);
    return nullptr;
}

void NAPIContextManager::ClearAllContexts() {
    std::lock_guard<std::mutex> lock(contextMapMutex_);
    
    // 清理所有未处理的上下文
    for (auto& pair : promiseContextMap_) {
        NAPIPromiseContext* context = pair.second;
        if (context) {
            // 释放线程安全函数
            if (context->tsfn) {
                napi_release_threadsafe_function(context->tsfn, napi_tsfn_release);
            }
            delete context;
        }
    }
    
    size_t clearedCount = promiseContextMap_.size();
    promiseContextMap_.clear();
    
    printf("[INFO] NAPIContextManager: Cleared %zu contexts\n", clearedCount);
}

size_t NAPIContextManager::GetContextCount() const {
    std::lock_guard<std::mutex> lock(contextMapMutex_);
    return promiseContextMap_.size();
}
