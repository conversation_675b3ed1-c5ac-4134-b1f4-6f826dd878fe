# the minimum version of CMake.
cmake_minimum_required(VERSION 3.5.0)
project(fliggy_im)

set(NATIVERENDER_ROOT_PATH ${CMAKE_CURRENT_SOURCE_DIR})

set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_STANDARD 20)

if(DEFINED PACKAGE_FIND_FILE)
    include(${PACKAGE_FIND_FILE})
endif()

# 添加基础包含目录
include_directories(
    ${NATIVERENDER_ROOT_PATH}
    ${NATIVERENDER_ROOT_PATH}/im_core_paas/include/libaim
    ${NATIVERENDER_ROOT_PATH}/im_core_paas/include/libdps
    # 添加SDK公共头文件目录 - 解决 fliggy_im_sdk.h 找不到的问题
    ${NATIVERENDER_ROOT_PATH}/sdk/public
    # 添加SDK源码根目录
    ${NATIVERENDER_ROOT_PATH}/sdk/src
    # 添加监听器目录
    ${NATIVERENDER_ROOT_PATH}/sdk/lisenter
    # 添加NAPI重构后的头文件目录（已整合Bridge功能）
    ${NATIVERENDER_ROOT_PATH}/napi
)


message("CMAKE_SOURCE_DIR= ${CMAKE_SOURCE_DIR}")
message("CMAKE_CURRENT_SOURCE_DIR= ${CMAKE_CURRENT_SOURCE_DIR}")


# 在这里可以更换tcpkg版本
set(TCPKG_DOWNLOAD_URL "https://dev.g.alicdn.com/taobao-cxx/tcpkg/3.0.137/tcpkg.cmake.obj")

# 这里不要修改
set(TCPKG_INSTALL_PATH "${CMAKE_BINARY_DIR}/.tcpkg_cache" CACHE STRING "在这里设置tcpkg的安装路径")
set(TCPKG_PATH ${TCPKG_INSTALL_PATH}/tcpkg.cmake)
message("CMAKE_BINARY_DIR: ${CMAKE_BINARY_DIR}")

if(NOT EXISTS ${TCPKG_PATH})
    message("TCPKG_PATH: NOT EXISTS DOWNLOAD")
    file(DOWNLOAD "${TCPKG_DOWNLOAD_URL}" "${TCPKG_PATH}" SHOW_PROGRESS)
endif ()
message("TCPKG_PATH: ${TCPKG_PATH}")

include(${TCPKG_PATH})


# 递归添加所有SDK子目录
message(STATUS "Adding FliggyIMSDK internal headers...")
file(GLOB_RECURSE SDK_DIRS LIST_DIRECTORIES true "${NATIVERENDER_ROOT_PATH}/sdk/src/*")
foreach(dir ${SDK_DIRS})
    if(IS_DIRECTORY ${dir})
        include_directories(${dir})
        get_filename_component(dir_name ${dir} NAME)
        message(STATUS "  Added SDK subdir: ${dir_name}")
    endif()
endforeach()

# 添加第三方库头文件路径
if(EXISTS "${NATIVERENDER_ROOT_PATH}/im_core_paas/include")
    include_directories(${NATIVERENDER_ROOT_PATH}/im_core_paas/include/libdps)
    include_directories(${NATIVERENDER_ROOT_PATH}/im_core_paas/include/libaim)
    message(STATUS "  Added DingTalk DPS headers")
    message(STATUS "  Added DingTalk AIM headers")
endif()

# 验证关键头文件是否存在
set(KEY_HEADERS
    "${NATIVERENDER_ROOT_PATH}/sdk/public/fliggy_im_sdk.h"
    "${NATIVERENDER_ROOT_PATH}/sdk/public/fim_error.h"
    "${NATIVERENDER_ROOT_PATH}/sdk/src/auth/fim_auth_listener.h"
)

foreach(header ${KEY_HEADERS})
    if(EXISTS ${header})
        message(STATUS "  ✓ Found key header: ${header}")
    else()
        message(WARNING "  ✗ Missing key header: ${header}")
    endif()
endforeach()

# 收集FIM SDK源文件
file(GLOB_RECURSE FIM_SDK_SOURCES
    "${NATIVERENDER_ROOT_PATH}/sdk/src/*.cpp"
)

# 收集NAPI重构后的源文件（已整合Bridge功能）
file(GLOB_RECURSE NAPI_REFACTOR_SOURCES
    "${NATIVERENDER_ROOT_PATH}/napi/*.cpp"
)

# 过滤掉不需要的源文件（如果有的话）
list(FILTER FIM_SDK_SOURCES EXCLUDE REGEX ".*test.*")
list(FILTER FIM_SDK_SOURCES EXCLUDE REGEX ".*main\\.cpp$")

# 显示将要编译的源文件
message(STATUS "FIM SDK source files:")
foreach(source ${FIM_SDK_SOURCES})
    get_filename_component(source_name ${source} NAME)
    message(STATUS "  - ${source_name}")
endforeach()

message(STATUS "NAPI Refactor source files (with integrated Bridge functionality):")
foreach(source ${NAPI_REFACTOR_SOURCES})
    get_filename_component(source_name ${source} NAME)
    message(STATUS "  - ${source_name}")
endforeach()

# 添加预编译库路径
set(PREBUILT_LIBS_PATH ${CMAKE_CURRENT_SOURCE_DIR}/../../../libs/arm64-v8a)


# 创建entry库，包含NAPI代码、FIM SDK源码和NAPI重构源码
add_library(fimsdk SHARED
    napi_init.cpp
    ${FIM_SDK_SOURCES}
    ${NAPI_REFACTOR_SOURCES}
)

# 编译警告
#-Werror: 这个标志会将所有警告转化为错误，这样在编译时任何警告都会导致编译失败。这是一种确保代码质量和正确性的方法，因为它迫使开发者解决所有警告。
#-Wshorten-64-to-32: 当编译器检测到可能导致数据丢失的 64 位到 32 位的隐式转换时，它会发出警告。
#-Wno-error=reorder: 尽管成员初始化顺序不正确会发出警告，但这个标志确保不会因为这个警告而导致编译错误。
#-Wno-error=unused-variable: 不将未使用变量的警告当作错误，这样可以防止编译因未使用变量而失败，尽管警告本身仍然会显示。
#-Wno-error=unused-private-field: 提供类中存在未使用的私有字段时，编译器会发出警告，但这个标志会阻止这种警告成为编译错误。
#-Wno-error=unknown-warning-option: 如果编译器不识别某个警告选项，通常会发出警告，但该标志会防止这种警告升级为错误。
#-Wno-error=unused-command-line-argument: 这个标志确保如果命令行中提供了未使用的参数，编译器会发出警告，但不会导致编译错误。
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wno-unused-command-line-argument") # 去除gcc-toolchain垃圾警告
target_compile_options(fimsdk PRIVATE
        -Werror
        -Wno-error=reorder
        -Wno-error=unused-variable
        -Wno-error=unused-private-field
        -Wno-error=unknown-warning-option
        -Wno-error=unused-command-line-argument
        -Wno-error=undef
        -Wno-error=deprecated-declarations
        -Wno-error=unused-result
        -Wno-error=c++2b-extensions
)

if(CMAKE_SIZEOF_VOID_P EQUAL 8)
    # 64位系统增加一个警告
    target_compile_options(fimsdk PRIVATE -Wshorten-64-to-32)
endif()

# 编译选项
if (HIDDEN_SYMBOL AND NOT ${CMAKE_BUILD_TYPE} STREQUAL "Debug")
    target_compile_options(fimsdk PRIVATE -fvisibility=hidden)
endif ()

# 链接库
target_link_libraries(fimsdk PUBLIC
    libace_napi.z.so
    ${PREBUILT_LIBS_PATH}/libdps.so
    ${PREBUILT_LIBS_PATH}/libaim.so
    ${PREBUILT_LIBS_PATH}/libfml.so
    ${PREBUILT_LIBS_PATH}/libgaea.so
)