#include "napi/native_api.h"
#include <memory>
#include <functional>
#include <string>
#include <cstddef>
#include <cstdio>
#include <cstdlib>

// FIM SDK相关头文件
#include "sdk/src/Engine/fim_engine.h"
#include "sdk/src/Engine/fim_setting_service.h"
#include "sdk/public/fim_error.h"

// 重构后的类头文件
#include "napi/utils/napi_context_manager.h"
#include "napi/utils/thread_safe_promise_handler.h"
#include "napi/handlers/fim_message_handler.h"
#include "napi/handlers/fim_setting_handler.h"

// HarmonyOS 日志
#include "hilog/log.h"

// HarmonyOS日志配置
#undef LOG_DOMAIN
#undef LOG_TAG
#define LOG_DOMAIN 0x3200  // 自定义domain
#define LOG_TAG "FIM_NAPI"

// 使用命名空间
using namespace alibaba::fim;

// ============================================================================
// 简化的NAPI封装函数 - 使用重构后的类
// ============================================================================

/**
 * @brief 重发消息的NAPI封装函数（重构版本）
 */
static napi_value NAPI_FIMResendMessage(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().ResendMessage(env, info);
}

/**
 * @brief 回复消息的NAPI封装函数（重构版本）
 */
static napi_value NAPI_FIMReplyMessage(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().ReplyMessage(env, info);
}

/**
 * @brief 获取消息的NAPI封装函数
 */
static napi_value NAPI_FIMGetMessage(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().GetMessage(env, info);
}

/**
 * @brief 获取本地消息的NAPI封装函数
 */
static napi_value NAPI_FIMGetLocalMessage(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().GetLocalMessage(env, info);
}

/**
 * @brief 删除消息的NAPI封装函数
 */
static napi_value NAPI_FIMDeleteMessage(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().DeleteMessage(env, info);
}

/**
 * @brief 撤回消息的NAPI封装函数
 */
static napi_value NAPI_FIMRecallMessage(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().RecallMessage(env, info);
}

/**
 * @brief 获取上一页本地消息的NAPI封装函数
 */
static napi_value NAPI_FIMListPreviousLocalMsgs(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().ListPreviousLocalMsgs(env, info);
}

/**
 * @brief 获取下一页本地消息的NAPI封装函数
 */
static napi_value NAPI_FIMListNextLocalMsgs(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().ListNextLocalMsgs(env, info);
}

/**
 * @brief 获取上一页消息的NAPI封装函数
 */
static napi_value NAPI_FIMListPreviousMsgs(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().ListPreviousMsgs(env, info);
}

/**
 * @brief 获取下一页消息的NAPI封装函数
 */
static napi_value NAPI_FIMListNextMsgs(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().ListNextMsgs(env, info);
}

/**
 * @brief 获取消息已读状态的NAPI封装函数
 */
static napi_value NAPI_FIMListMessagesReadStatus(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().ListMessagesReadStatus(env, info);
}

/**
 * @brief 更新消息为已读的NAPI封装函数
 */
static napi_value NAPI_FIMUpdateMessageToRead(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().UpdateMessageToRead(env, info);
}

/**
 * @brief 删除本地消息的NAPI封装函数
 */
static napi_value NAPI_FIMDeleteLocalMessage(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().DeleteLocalMessage(env, info);
}

/**
 * @brief 获取会话列表的NAPI封装函数
 */
static napi_value NAPI_FIMGetConversations(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().GetConversations(env, info);
}

// ============================================================================
// 新增的真实API封装函数 - 消息发送相关
// ============================================================================

/**
 * @brief 发送文本消息的NAPI封装函数
 */
static napi_value NAPI_FIMSendMessageText(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().SendMessageText(env, info);
}

/**
 * @brief 发送自定义消息的NAPI封装函数
 */
static napi_value NAPI_FIMSendMessageCustom(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().SendMessageCustom(env, info);
}

/**
 * @brief 发送图片消息的NAPI封装函数
 */
static napi_value NAPI_FIMSendMessageImage(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().SendMessageImage(env, info);
}

/**
 * @brief 发送@消息的NAPI封装函数
 */
static napi_value NAPI_FIMSendMessageAt(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().SendMessageAt(env, info);
}

/**
 * @brief 发送结构化消息的NAPI封装函数
 */
static napi_value NAPI_FIMSendMessageStruct(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().SendMessageStruct(env, info);
}

/**
 * @brief 发送视频消息的NAPI封装函数
 */
static napi_value NAPI_FIMSendMessageVideo(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().SendMessageVideo(env, info);
}

// ============================================================================
// 新增的真实API封装函数 - 登录认证相关
// ============================================================================

/**
 * @brief 登录认证的NAPI封装函数
 */
static napi_value NAPI_FIMLogin(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().Login(env, info);
}

// ============================================================================
// 新增的真实API封装函数 - 事件监听相关
// ============================================================================

/**
 * @brief 消息事件监听的NAPI封装函数
 */
static napi_value NAPI_FIMOnMessageEvent(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().OnMessageEvent(env, info);
}

/**
 * @brief 会话事件监听的NAPI封装函数
 */
static napi_value NAPI_FIMOnConversationEvent(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().OnConversationEvent(env, info);
}

/**
 * @brief 群组事件监听的NAPI封装函数
 */
static napi_value NAPI_FIMOnGroupEvent(napi_env env, napi_callback_info info) {
    return FIMMessageHandler::GetInstance().OnGroupEvent(env, info);
}

// ============================================================================
// 新增的真实API封装函数 - 会话管理相关
// ============================================================================

/**
 * @brief 激活会话的NAPI封装函数
 */
static napi_value NAPI_FIMActiveConversation(napi_env env, napi_callback_info info) {
    // TODO: 实现激活会话
    return FIMMessageHandler::GetInstance().SendMessage(env, info); // 临时使用
}

/**
 * @brief 获取静音状态的NAPI封装函数
 */
static napi_value NAPI_FIMGetMuteStatus(napi_env env, napi_callback_info info) {
    // TODO: 实现获取静音状态
    return FIMMessageHandler::GetInstance().SendMessage(env, info); // 临时使用
}

/**
 * @brief 创建自定义单人会话的NAPI封装函数
 */
static napi_value NAPI_FIMCreateCustomSingleConversation(napi_env env, napi_callback_info info) {
    // TODO: 实现创建自定义单人会话
    return FIMMessageHandler::GetInstance().SendMessage(env, info); // 临时使用
}

/**
 * @brief 退群的NAPI封装函数
 */
static napi_value NAPI_FIMLeaveGroup(napi_env env, napi_callback_info info) {
    // TODO: 实现退群功能
    return FIMMessageHandler::GetInstance().SendMessage(env, info); // 临时使用
}

// ============================================================================
// 新增的真实API封装函数 - 搜索和工具相关
// ============================================================================

/**
 * @brief 获取fuid的NAPI封装函数
 */
static napi_value NAPI_FIMGetMyFuid(napi_env env, napi_callback_info info) {
    // TODO: 实现获取fuid
    return FIMMessageHandler::GetInstance().SendMessage(env, info); // 临时使用
}

/**
 * @brief 搜索聊天内容的NAPI封装函数
 */
static napi_value NAPI_FIMSearchChatContent(napi_env env, napi_callback_info info) {
    // TODO: 实现搜索聊天内容
    return FIMMessageHandler::GetInstance().SendMessage(env, info); // 临时使用
}

/**
 * @brief 获取媒体本地路径的NAPI封装函数
 */
static napi_value NAPI_FIMGetMediaLocalPath(napi_env env, napi_callback_info info) {
    // TODO: 实现获取媒体本地路径
    return FIMMessageHandler::GetInstance().SendMessage(env, info); // 临时使用
}

/**
 * @brief 清空聊天记录的NAPI封装函数
 */
static napi_value NAPI_FIMClearMessages(napi_env env, napi_callback_info info) {
    // TODO: 实现清空聊天记录
    return FIMMessageHandler::GetInstance().SendMessage(env, info); // 临时使用
}

/**
 * @brief 设置AppID的NAPI封装函数（重构版本）
 */
static napi_value FIMSettingService_SetAppID(napi_env env, napi_callback_info info) {
    return FIMSettingHandler::GetInstance().SetAppID(env, info);
}

/**
 * @brief 设置AppName的NAPI封装函数（重构版本）
 */
static napi_value FIMSettingService_SetAppName(napi_env env, napi_callback_info info) {
    return FIMSettingHandler::GetInstance().SetAppName(env, info);
}

/**
 * @brief 设置AppKey的NAPI封装函数（重构版本）
 */
static napi_value FIMSettingService_SetAppKey(napi_env env, napi_callback_info info) {
    return FIMSettingHandler::GetInstance().SetAppKey(env, info);
}

/**
 * @brief 设置DeviceType的NAPI封装函数（重构版本）
 */
static napi_value FIMSettingService_SetDeviceType(napi_env env, napi_callback_info info) {
    return FIMSettingHandler::GetInstance().SetDeviceType(env, info);
}

/**
 * @brief 设置DeviceName的NAPI封装函数（重构版本）
 */
static napi_value FIMSettingService_SetDeviceName(napi_env env, napi_callback_info info) {
    return FIMSettingHandler::GetInstance().SetDeviceName(env, info);
}

/**
 * @brief 设置DeviceId的NAPI封装函数（重构版本）
 */
static napi_value FIMSettingService_SetDeviceId(napi_env env, napi_callback_info info) {
    return FIMSettingHandler::GetInstance().SetDeviceId(env, info);
}

/**
 * @brief 设置DataPath的NAPI封装函数（重构版本）
 */
static napi_value FIMSettingService_SetDataPath(napi_env env, napi_callback_info info) {
    return FIMSettingHandler::GetInstance().SetDataPath(env, info);
}

/**
 * @brief 设置OsName的NAPI封装函数（重构版本）
 */
static napi_value FIMSettingService_SetOsName(napi_env env, napi_callback_info info) {
    return FIMSettingHandler::GetInstance().SetOsName(env, info);
}

/**
 * @brief 设置OsVersion的NAPI封装函数（重构版本）
 */
static napi_value FIMSettingService_SetOsVersion(napi_env env, napi_callback_info info) {
    return FIMSettingHandler::GetInstance().SetOsVersion(env, info);
}

/**
 * @brief 设置AppVersion的NAPI封装函数（重构版本）
 */
static napi_value FIMSettingService_SetAppVersion(napi_env env, napi_callback_info info) {
    return FIMSettingHandler::GetInstance().SetAppVersion(env, info);
}

// ============================================================================
// FIMEngine相关函数 - 保留原有实现（暂时）
// ============================================================================

// FIMEngine::Start的Promise上下文结构体
struct FIMEngineStartContext {
    napi_env env;
    napi_deferred deferred;
    napi_threadsafe_function tsfn;
    std::atomic<bool> resolved;

    FIMEngineStartContext(napi_env e, napi_deferred d, napi_threadsafe_function t)
        : env(e), deferred(d), tsfn(t), resolved(false) {}

    ~FIMEngineStartContext() {
        if (tsfn) {
            napi_release_threadsafe_function(tsfn, napi_tsfn_release);
        }
    }
};

// Promise结果数据结构
struct PromiseResult {
    std::string result;
    bool isSuccess;
    FIMError error;
    FIMEngineStartContext* context;

    PromiseResult(FIMEngineStartContext* ctx, const std::string& res)
        : context(ctx), result(res), isSuccess(true) {}
    PromiseResult(FIMEngineStartContext* ctx, const FIMError& err)
        : context(ctx), error(err), isSuccess(false) {}
};

// 将FIMError转换为JavaScript对象
static napi_value CreateErrorObject(napi_env env, const FIMError& error) {
    napi_value errorObj;
    napi_create_object(env, &errorObj);

    napi_value domainValue;
    napi_create_int32(env, static_cast<int32_t>(error.domain), &domainValue);
    napi_set_named_property(env, errorObj, "domain", domainValue);

    napi_value codeValue;
    napi_create_int32(env, static_cast<int32_t>(error.code), &codeValue);
    napi_set_named_property(env, errorObj, "code", codeValue);

    napi_value messageValue;
    napi_create_string_utf8(env, error.developer_message.c_str(), NAPI_AUTO_LENGTH, &messageValue);
    napi_set_named_property(env, errorObj, "developer_message", messageValue);

    napi_value reasonValue;
    napi_create_string_utf8(env, error.reason.c_str(), NAPI_AUTO_LENGTH, &reasonValue);
    napi_set_named_property(env, errorObj, "reason", reasonValue);

    napi_value extraInfoValue;
    napi_create_string_utf8(env, error.extra_info.c_str(), NAPI_AUTO_LENGTH, &extraInfoValue);
    napi_set_named_property(env, errorObj, "extra_info", extraInfoValue);

    napi_value scopeValue;
    napi_create_string_utf8(env, error.scope.c_str(), NAPI_AUTO_LENGTH, &scopeValue);
    napi_set_named_property(env, errorObj, "scope", scopeValue);

    return errorObj;
}

// 线程安全函数的回调，在主线程中执行
static void ThreadSafeFunctionCallback(napi_env env, napi_value js_callback, void* context, void* data) {
    (void)js_callback;
    
    PromiseResult* result = static_cast<PromiseResult*>(data);
    
    if (result && result->context) {
        FIMEngineStartContext* ctx = result->context;
        bool expected = false;
        if (ctx->resolved.compare_exchange_strong(expected, true)) {
            if (result->isSuccess) {
                napi_value napiResult;
                napi_status status = napi_create_string_utf8(env, result->result.c_str(), NAPI_AUTO_LENGTH, &napiResult);
                status = napi_resolve_deferred(env, ctx->deferred, napiResult);
            } else {
                napi_value errorObj = CreateErrorObject(env, result->error);
                napi_reject_deferred(env, ctx->deferred, errorObj);
            }
        }
    }

    delete result;
}

// 线程安全的Promise解析函数
static void resolvePromiseThreadSafe(FIMEngineStartContext* context, const std::string& result) {
    if (context && context->tsfn) {
        PromiseResult* data = new PromiseResult(context, result);
        napi_status status = napi_call_threadsafe_function(context->tsfn, data, napi_tsfn_blocking);
        if (status != napi_ok) {
            delete data;
        }
    }
}

// 线程安全的Promise拒绝函数
static void rejectPromiseThreadSafe(FIMEngineStartContext* context, const FIMError& error) {
    if (context && context->tsfn) {
        PromiseResult* data = new PromiseResult(context, error);
        napi_status status = napi_call_threadsafe_function(context->tsfn, data, napi_tsfn_blocking);
        if (status != napi_ok) {
            delete data;
        }
    }
}

// FIMEngine::Start的NAPI封装函数
static napi_value FIMEngineStart(napi_env env, napi_callback_info info) {
    napi_deferred deferred;
    napi_value promise;
    napi_status status = napi_create_promise(env, &deferred, &promise);
    if (status != napi_ok) {
        napi_throw_error(env, nullptr, "Failed to create promise");
        return nullptr;
    }

    napi_threadsafe_function tsfn;
    napi_value resource_name;
    napi_create_string_utf8(env, "FIMEngineStartCallback", NAPI_AUTO_LENGTH, &resource_name);

    status = napi_create_threadsafe_function(
        env, nullptr, nullptr, resource_name, 0, 1, nullptr, nullptr, nullptr,
        ThreadSafeFunctionCallback, &tsfn
    );

    if (status != napi_ok) {
        napi_throw_error(env, nullptr, "Failed to create threadsafe function");
        return nullptr;
    }

    auto context = std::make_shared<FIMEngineStartContext>(env, deferred, tsfn);

    auto onSuccess = [context]() {
        resolvePromiseThreadSafe(context.get(), "FIM Engine started successfully");
    };

    auto onFailure = [context](const FIMError& error) {
        rejectPromiseThreadSafe(context.get(), error);
    };

    try {
        FIMEngine::GetInstance().Start(onSuccess, onFailure);
    } catch (const std::exception& e) {
        napi_value errorMsg;
        napi_create_string_utf8(env, e.what(), NAPI_AUTO_LENGTH, &errorMsg);
        napi_reject_deferred(env, deferred, errorMsg);
    }

    return promise;
}

// 测试函数
static napi_value Add(napi_env env, napi_callback_info info) {
    size_t argc = 2;
    napi_value args[2] = {nullptr};

    napi_get_cb_info(env, info, &argc, args , nullptr, nullptr);

    double value0, value1;
    napi_get_value_double(env, args[0], &value0);
    napi_get_value_double(env, args[1], &value1);

    napi_value sum;
    napi_create_double(env, value0 + value1, &sum);

    return sum;
}

// ============================================================================
// 模块初始化和注册
// ============================================================================

EXTERN_C_START
static napi_value Init(napi_env env, napi_value exports) {
    napi_property_descriptor desc[] = {
        {"add", nullptr, Add, nullptr, nullptr, nullptr, napi_default, nullptr},
        
        // 认证相关 (1个)
        {"fimLogin", nullptr, NAPI_FIMLogin, nullptr, nullptr, nullptr, napi_default, nullptr},
        
        // 消息查询相关 (7个)
        {"fimListPreviousMsgs", nullptr, NAPI_FIMListPreviousMsgs, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimListNextMsgs", nullptr, NAPI_FIMListNextMsgs, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimListPreviousLocalMsgs", nullptr, NAPI_FIMListPreviousLocalMsgs, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimListNextLocalMsgs", nullptr, NAPI_FIMListNextLocalMsgs, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimGetMessage", nullptr, NAPI_FIMGetMessage, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimGetLocalMessage", nullptr, NAPI_FIMGetLocalMessage, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimListMessagesReadStatus", nullptr, NAPI_FIMListMessagesReadStatus, nullptr, nullptr, nullptr, napi_default, nullptr},
        
        // 消息操作相关 (6个)
        {"fimDeleteMessage", nullptr, NAPI_FIMDeleteMessage, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimDeleteLocalMessage", nullptr, NAPI_FIMDeleteLocalMessage, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimClearMessages", nullptr, NAPI_FIMClearMessages, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimRecallMessage", nullptr, NAPI_FIMRecallMessage, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimUpdateMessageToRead", nullptr, NAPI_FIMUpdateMessageToRead, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimResendMessage", nullptr, NAPI_FIMResendMessage, nullptr, nullptr, nullptr, napi_default, nullptr},
        
        // 事件监听相关 (3个)
        {"fimOnMessageEvent", nullptr, NAPI_FIMOnMessageEvent, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimOnConversationEvent", nullptr, NAPI_FIMOnConversationEvent, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimOnGroupEvent", nullptr, NAPI_FIMOnGroupEvent, nullptr, nullptr, nullptr, napi_default, nullptr},
        
        // 消息发送相关 (7个)
        {"fimSendMessageText", nullptr, NAPI_FIMSendMessageText, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimSendMessageCustom", nullptr, NAPI_FIMSendMessageCustom, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimSendMessageImage", nullptr, NAPI_FIMSendMessageImage, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimSendMessageAt", nullptr, NAPI_FIMSendMessageAt, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimSendMessageStruct", nullptr, NAPI_FIMSendMessageStruct, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimSendMessageVideo", nullptr, NAPI_FIMSendMessageVideo, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimReplyMessage", nullptr, NAPI_FIMReplyMessage, nullptr, nullptr, nullptr, napi_default, nullptr},
        
        // 会话管理相关 (5个)
        {"fimActiveConversation", nullptr, NAPI_FIMActiveConversation, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimGetMuteStatus", nullptr, NAPI_FIMGetMuteStatus, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimGetConversations", nullptr, NAPI_FIMGetConversations, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimCreateCustomSingleConversation", nullptr, NAPI_FIMCreateCustomSingleConversation, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimLeaveGroup", nullptr, NAPI_FIMLeaveGroup, nullptr, nullptr, nullptr, napi_default, nullptr},
        
        // 搜索和工具相关 (3个)
        {"fimGetMyFuid", nullptr, NAPI_FIMGetMyFuid, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimSearchChatContent", nullptr, NAPI_FIMSearchChatContent, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"fimGetMediaLocalPath", nullptr, NAPI_FIMGetMediaLocalPath, nullptr, nullptr, nullptr, napi_default, nullptr},
        
        // 引擎和设置功能
        {"fimEngineStart", nullptr, FIMEngineStart, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setAppId", nullptr, FIMSettingService_SetAppID, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setAppName", nullptr, FIMSettingService_SetAppName, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setAppKey", nullptr, FIMSettingService_SetAppKey, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setDeviceType", nullptr, FIMSettingService_SetDeviceType, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setDeviceName", nullptr, FIMSettingService_SetDeviceName, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setDeviceId", nullptr, FIMSettingService_SetDeviceId, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setOsName", nullptr, FIMSettingService_SetOsName, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setOsVersion", nullptr, FIMSettingService_SetOsVersion, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setAppVersion", nullptr, FIMSettingService_SetAppVersion, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setDataPath", nullptr, FIMSettingService_SetDataPath, nullptr, nullptr, nullptr, napi_default, nullptr},
    };
    napi_define_properties(env, exports, sizeof(desc) / sizeof(desc[0]), desc);
    return exports;
}
EXTERN_C_END

static napi_module demoModule = {
    .nm_version = 1,
    .nm_flags = 0,
    .nm_filename = nullptr,
    .nm_register_func = Init,
    .nm_modname = "fimsdk",
    .nm_priv = ((void*)0),
    .reserved = { 0 },
};

extern "C" __attribute__((constructor)) void RegisterEntryModule(void) {
    napi_module_register(&demoModule);
}
