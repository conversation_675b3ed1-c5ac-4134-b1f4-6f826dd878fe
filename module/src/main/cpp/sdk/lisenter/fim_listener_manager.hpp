// // FIMListenerManager.h

// #ifndef FIMLISTENERMANAGER_H
// #define FIMLISTENERMANAGER_H

// #include <memory>
// #include <unordered_map>
// #include <mutex>

// namespace alibaba {
// namespace fim {

// class FIMListenerManager {
// public:
//   static FIMListenerManager& Instance() {
//     static FIMListenerManager instance; // Meyers' Singleton
//     return instance;
//   }

//   // 删除拷贝构造函数和赋值操作符确保它们不会被错误地使用
//   FIMListenerManager(const FIMListenerManager&) = delete;
//   FIMListenerManager& operator=(const FIMListenerManager&) = delete;

//   bool AddFIMMessageChangeListener(std::shared_ptr<FIMMessageChangeListener> listener) {

//   }
//   bool RemoveFIMMessageChangeListener(std::shared_ptr<FIMMessageChangeListener> listener);
//   void RemoveAllFIMMessageChangeListeners();

// private:
//   FIMListenerManager(); // 私有构造函数
//   ~FIMListenerManager();

//   std::shared_ptr<AIMManager> aimManager_;
//   std::unordered_map<FIMMessageChangeListener*, std::shared_ptr<dps::AIMPubMsgChangeListener>> aimListenerMap_;
//   std::mutex mapMutex_;
// };

// } // namespace fim
// } // namespace alibaba

// #endif // FIMLISTENERMANAGER_H
