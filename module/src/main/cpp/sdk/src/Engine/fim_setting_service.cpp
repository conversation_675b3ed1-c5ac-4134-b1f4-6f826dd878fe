//
//  FIMSettingService.cpp
//  iOSDemo
//
//  Created by 蒙晨 on 2024/2/6.
//


#include "fim_setting_service.h"
//DPSAuthTokenGotCallback
#include "dps_auth_token_got_callback.h"
//DPSPubAuthTokenCallback
#include "dps_pub_auth_token_callback.h"
//DPSAuthTokenExpiredReason
#include "dps_auth_token_expired_reason.h"
//DPSEnvType
#include "dps_env_type.h"
//DPSMediaHost
#include "dps_media_host.h"
//DPSSyncProtocolInfo
#include "dps_sync_protocol_info.h"
//FIMEngine
#include "fim_engine.h"
//FIMSettingService
#include "dps_pub_setting_service.h"
#include "dps_pub_engine.h"


namespace alibaba {
namespace fim {
using namespace alibaba::dps;
/**
 * DPSSettingService
 */

/**
 * 设置多媒体Media Host, 若不设置则采用内置默认Host
 */
void FIMSettingService::SetMediaHost(
    const std::vector<DPSMediaHost> &media_host) {
  std::shared_ptr<DPSPubSettingService> settingService =
      DPSPubEngine::GetDPSEngine()->GetSettingService();
  if (settingService) {
    settingService->SetMediaHost(media_host);
  }
}

/**
 * 设置应用appid
 * @param app_id DingPaaS分配给应用的appID
 */
void FIMSettingService::SetAppID(const std::string &app_id) {
  std::shared_ptr<DPSPubSettingService> settingService =
      DPSPubEngine::GetDPSEngine()->GetSettingService();
  if (settingService) {
    settingService->SetAppID(app_id);
  }
}

/**
 * 设置应用key
 * @param app_key IM_PAAS分配给应用的key
 */
void FIMSettingService::SetAppKey(const std::string &app_key) {
  appKey = app_key;
  std::shared_ptr<DPSPubSettingService> settingService =
      DPSPubEngine::GetDPSEngine()->GetSettingService();
  if (settingService) {
    settingService->SetAppKey(app_key);
  }
}

/**
 * 设置数据存储路径
 * @param data_path
 * 数据存取路径(utf-8),app需提前创建好此目录并对此目录有读写权限
 */
void FIMSettingService::SetDataPath(const std::string &data_path) {
  std::shared_ptr<DPSPubSettingService> settingService =
      DPSPubEngine::GetDPSEngine()->GetSettingService();
  if (settingService) {
    settingService->SetDataPath(data_path);
  }
}

/**
 * 设置应用名称
 * @param app_name 应用名称
 */
void FIMSettingService::SetAppName(const std::string &app_name) {
  std::shared_ptr<DPSPubSettingService> settingService =
      DPSPubEngine::GetDPSEngine()->GetSettingService();
  if (settingService) {
    settingService->SetAppName(app_name);
  }
}

/**
 * 设置应用版本
 * @param app_version 应用版本号
 */
void FIMSettingService::SetAppVersion(const std::string &version) {
  std::shared_ptr<DPSPubSettingService> settingService =
      DPSPubEngine::GetDPSEngine()->GetSettingService();
  if (settingService) {
    settingService->SetAppVersion(version);
  }
}

/**
 * 设置App语言区域
 * @param app_locale 语言区域（默认zh_CN）
 */
void FIMSettingService::SetAppLocale(const std::string &app_local) {
  std::shared_ptr<DPSPubSettingService> settingService =
      DPSPubEngine::GetDPSEngine()->GetSettingService();
  if (settingService) {
    settingService->SetAppLocale(app_local);
  }
}

/**
 * 设置操作系统名称
 * @param os_name 操作系统名称，如Win、macOS、iOS、Android等
 */
void FIMSettingService::SetOSName(const std::string &os_name) {
  this->os_name = os_name;
  std::shared_ptr<DPSPubSettingService> settingService =
      DPSPubEngine::GetDPSEngine()->GetSettingService();
  if (settingService) {
    settingService->SetOSName(os_name);
  }
}

/**
 * 设置操作系统版本
 * @param os_version 操作系统版本
 */
void FIMSettingService::SetOSVersion(const std::string &os_version) {
  std::shared_ptr<DPSPubSettingService> settingService =
      DPSPubEngine::GetDPSEngine()->GetSettingService();
  if (settingService) {
    settingService->SetOSVersion(os_version);
  }
}

/**
 * 设置设备名称
 * @param device_name 设备名称
 */
void FIMSettingService::SetDeviceName(const std::string &device_name) {
  std::shared_ptr<DPSPubSettingService> settingService =
      DPSPubEngine::GetDPSEngine()->GetSettingService();
  if (settingService) {
    settingService->SetDeviceName(device_name);
  }
}

/**
 * 设置设备型号
 * @param device_type 设备型号
 */
void FIMSettingService::SetDeviceType(const std::string &device_type) {
  std::shared_ptr<DPSPubSettingService> settingService =
      DPSPubEngine::GetDPSEngine()->GetSettingService();
  if (settingService) {
    settingService->SetDeviceType(device_type);
  }
}

/**
 * 设置设备语言
 * @param device_local 设备语言（默认zh_CN）
 */
void FIMSettingService::SetDeviceLocale(const std::string &device_local) {
  std::shared_ptr<DPSPubSettingService> settingService =
      DPSPubEngine::GetDPSEngine()->GetSettingService();
  if (settingService) {
    settingService->SetDeviceLocale(device_local);
  }
}

/**
 * 设置设备唯一id
 * @param device_id 设备唯一id
 *  device_id生成算法不合适（容易冲突）可能会影响正常使用
 */
void FIMSettingService::SetDeviceId(const std::string &device_id) {
  deviceId = device_id;
  std::shared_ptr<DPSPubSettingService> settingService =
      DPSPubEngine::GetDPSEngine()->GetSettingService();
  if (settingService) {
    settingService->SetDeviceId(device_id);
  }
}

/**
 * 设置时区
 * @param time_zone 时区（默认Asia/Shanghai）
 */
void FIMSettingService::SetTimeZone(const std::string &time_zone) {
  std::shared_ptr<DPSPubSettingService> settingService =
      DPSPubEngine::GetDPSEngine()->GetSettingService();
  if (settingService) {
    settingService->SetTimeZone(time_zone);
  }
}

std::string LongLinkServerAddressFor(DPSEnvType type) {
  if (type == DPSEnvType::ENV_TYPE_PRE_RELEASE) {
    return "tls://**************:443";
  } else {
    return "tls://tls-fliggy.dingtalk.com";
  }
}

std::string MediaHostFor(DPSEnvType type) {
  if (type == DPSEnvType::ENV_TYPE_PRE_RELEASE) {
    return "https://pre-down-impaas.dingtalk.com:443";
  } else {
    return "https://impaas-static.dingtalk.com";
  }
}

std::string FileUploadUrlFor(DPSEnvType type) {
  if (type == DPSEnvType::ENV_TYPE_PRE_RELEASE) {
    return "lws://lws-short-pre-impaas.dingtalk.com:443";
  } else {
    return "lws://lws-short-impaas.dingtalk.com:443";
  }
}

/**
 * 设置长链接地址, 若不设置则采用内置默认地址
 * @param url 长链接地址
 */
void FIMSettingService::SetLonglinkServerAddress(const std::string &url) {
  std::shared_ptr<DPSPubSettingService> settingService =
      DPSPubEngine::GetDPSEngine()->GetSettingService();
  if (settingService) {
    settingService->SetLonglinkServerAddress(url);
  }
}

/**
 * 文件上传服务器地址, 若不设置则采用内置默认地址
 * @param url 服务器地址
 */
void FIMSettingService::SetFileUploadServerAddress(const std::string &url) {
  std::shared_ptr<DPSPubSettingService> settingService =
      DPSPubEngine::GetDPSEngine()->GetSettingService();
  if (settingService) {
    settingService->SetFileUploadServerAddress(url);
  }
}

/**
 * 设置Sync协议相关设置
 * @param info Sync协议设置
 */
void FIMSettingService::AddSyncProtocolSetting(
    const std::vector<DPSSyncProtocolInfo> &info) {
  std::shared_ptr<DPSPubSettingService> settingService =
      DPSPubEngine::GetDPSEngine()->GetSettingService();
  if (settingService) {
    settingService->AddSyncProtocolSetting(info);
  }
}

/**
 * 设置自定义UserAgent：可选配置，如不设定，则自动生成UserAgent，设置则使用该UA
 */
void FIMSettingService::SetCustomUserAgent(const std::string &ua) {
  std::shared_ptr<DPSPubSettingService> settingService =
      DPSPubEngine::GetDPSEngine()->GetSettingService();
  if (settingService) {
    settingService->SetCustomUserAgent(ua);
  }
}

/**
 * 设置是否开启ipv6(默认不开启)
 */
void FIMSettingService::SetEnableIpv6(bool enable) {
  std::shared_ptr<DPSPubSettingService> settingService =
      DPSPubEngine::GetDPSEngine()->GetSettingService();
  if (settingService) {
    settingService->SetEnableIpv6(enable);
  }
}

/**
 * 设置关闭ssl校验(默认不关闭，日常环境请设置为true)
 */
void FIMSettingService::SetDisableSslVerify(bool disable) {
  std::shared_ptr<DPSPubSettingService> settingService =
      DPSPubEngine::GetDPSEngine()->GetSettingService();
  if (settingService) {
    settingService->SetDisableSslVerify(disable);
  }
}

/**
 * 设置当前连接的服务端环境(日常，预发或线上)
 * 需要与长连接地址匹配，不然无法登陆成功
 */
void FIMSettingService::SetEnvType(DPSEnvType type) {
  env_type = type;
  std::shared_ptr<DPSPubSettingService> settingService =
      DPSPubEngine::GetDPSEngine()->GetSettingService();
  if (settingService) {
    settingService->SetEnvType(type);

    settingService->SetLonglinkServerAddress(LongLinkServerAddressFor(type));
    
    alibaba::dps::DPSMediaHost mediaHost;
    mediaHost.host = MediaHostFor(type);
    std::vector<DPSMediaHost> media_host = {mediaHost};
    settingService->SetMediaHost(media_host);

    settingService->SetFileUploadServerAddress(FileUploadUrlFor(type));
  }
}

using FIMSettingServicePtr = std::shared_ptr<FIMSettingService>;
using FIMSettingServiceWeakPtr = std::weak_ptr<FIMSettingService>;

} // namespace fim
} // namespace alibaba
