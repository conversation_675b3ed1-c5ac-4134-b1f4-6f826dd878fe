//
//  FIMEngine.cpp
//  FIMSDKFramework
//
//  Created by on 2024/1/24.
//

#include "dps_auth_service.h"
#include "dps_auth_token.h"
#include "dps_auth_token_got_callback.h"
#include "dps_env_type.h"
#include "dps_error.h"
#include "dps_module_info.h"
#include "dps_pub_engine.h"
#include "dps_pub_manager.h"
#include "dps_pub_setting_service.h"
#include "engine_listener.h"
#include "fim_error.hpp"
#include "json.hpp"
#include <functional>
#include <iostream>
#include <map>
#include <memory>
#include <string>
#include <vector>

#include "aim_pub_c_api.h"
#include "aim_pub_module.h"
#include "aim_pub_msg_send_message.h"
#include "fim_engine.h"
#include "fim_network_service.h"
#include "fim_setting_service.h"
#include "log_service.h"


alibaba::dps::DPSModuleInfo *rawPtr = GetAIMPubModuleInfo();
// fim_engine.cpp
namespace alibaba {
namespace fim {

using namespace dps;

FIMEngine &FIMEngine::GetInstance() {
  static FIMEngine instance;
  return instance;
}

FIMEngine::FIMEngine() {
  DPSPubEngine::CreateDPSEngine();
  std::shared_ptr<alibaba::dps::DPSModuleInfo> moduleInfoPtr(rawPtr);
  DPSPubEngine::GetDPSEngine()->RegisterModule(moduleInfoPtr);
}

FIMEngine::~FIMEngine() {
  // 析构函数的清理代码
}

/**
 *
 */
void FIMEngine::Start(
    const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  if (!DPSPubEngine::GetDPSEngine()) {
    OnFailure(FIMError(FIMErrorDomain::FIM_ERR_DOMAIN_FIMSDK,
                       FIMErrorCode::FIM_ERR_NO_ENGINE, "fim engine not start",
                       "FIMErrorCode::FIM_ERR_NO_ENGINE"));
    return;
  }
  // check settingServiceInstance
  if (!settingServiceInstance) {
    OnFailure(FIMError(FIMErrorDomain::FIM_ERR_DOMAIN_FIMSDK,
                       FIMErrorCode::FIM_ERR_SETTING_SERVICE,
                       "fim setting service not crate",
                       "FIMErrorCode::FIM_ERR_NO_SETTING_SERVICE"));
    return;
  }
  // check std::string deviceId;
  if (settingServiceInstance->deviceId.empty()) {
    OnFailure(FIMError(FIMErrorDomain::FIM_ERR_DOMAIN_FIMSDK,
                       FIMErrorCode::FIM_ERR_SETTING_SERVICE,
                       "fim device id not set",
                       "FIMErrorCode::FIM_ERR_NO_DEVICE_ID"));
    return;
  }
  // check std::string appKey;
  if (settingServiceInstance->appKey.empty()) {
    OnFailure(FIMError(FIMErrorDomain::FIM_ERR_DOMAIN_FIMSDK,
                       FIMErrorCode::FIM_ERR_SETTING_SERVICE,
                       "fim app key not set",
                       "FIMErrorCode::FIM_ERR_NO_APP_KEY"));
    return;
  }

  std::shared_ptr<DPSPubSettingService> settingService =
      DPSPubEngine::GetDPSEngine()->GetSettingService();
  settingService->SetAuthTokenCallback(
      [this, OnFailure](const std::string &userId,
                        const std::shared_ptr<DPSAuthTokenGotCallback> &on_got,
                        DPSAuthTokenExpiredReason reason) {
        FetchAuthToken(
            [on_got, this](const std::string &accessToken,
                           const std::string &refreshToken,
                           const std::string &fuid) {
              this->fuid = fuid;
              on_got->OnSuccess(DPSAuthToken(accessToken, refreshToken));
            },
            [on_got, OnFailure](const std::string &errorDescription) {
              on_got->OnFailure(500011, errorDescription);
              OnFailure(FIMError(FIMErrorDomain::FIM_ERR_DOMAIN_FIMSDK,
                                 FIMErrorCode::FIM_ERR_NETWORK_ERROR,
                                 "Fetch token failure", errorDescription));
            });
      });

  DPSPubEngine::GetDPSEngine()->Start(
      [OnSuccess]() { OnSuccess(); },
      [OnFailure](const DPSError &error) { OnFailure(FIMErrorFrom(error)); });
}

/**
 *
 */
std::shared_ptr<FIMSettingService> FIMEngine::GetSettingService() {
  return GetInternalSettingService();
}

bool FIMEngine::IsStarted() {
  return DPSPubEngine::GetDPSEngine()->IsStarted();
}

/**
 *
 * @param listener 监听器
 */
void FIMEngine::SetListener(
    const std::shared_ptr<FIMEngineListener> &listener) {
  EngineListener::GetInstance().SetEngineListener(listener);
}

/**
 *
 */
std::vector<std::string> FIMEngine::GetUserIds() {
  return DPSPubEngine::GetDPSEngine()->GetUserIds();
}

void CreateDPSManager(const std::string &fuid,
                      std::shared_ptr<FIMAuthListener> listener) {
  LogService::DebugLog([&listener] {
    return std::string("CreateDPSManager start") +
           "listener:" + (listener ? "true" : "false");
  });

  std::string aladdin_base_url = "https://aladdin-impaas.dingtalk.com/aidns";
  if (FIMEngine::GetInstance().GetSettingService()->env_type ==
      dps::DPSEnvType::ENV_TYPE_PRE_RELEASE) {
    aladdin_base_url = "https://pre-aladdin-impaas.dingtalk.com/aidns";
  }

  std::map<std::string, std::string> biz_params = {};
  if (!FIMEngine::GetInstance().biz_params.empty()) {
    biz_params = FIMEngine::GetInstance().biz_params;
  }

  DPSPubEngine::GetDPSEngine()->CreateDPSManager(
      fuid, biz_params,
      [fuid, listener](const std::shared_ptr<DPSPubManager> &manager) {
        FIMEngine::GetInstance().fuid = fuid;
        DPSAuthServicePtr authService = manager->GetAuthService();
        if (listener && authService) {
          LogService::DebugLog([] { return "CreateDPSManager has listener_"; });
          authService->RemoveAllListeners();
          authService->AddListener(listener);
        } else {
          LogService::ErrorLog([] { return "CreateDPSManager no listener_"; });
        }
        LogService::DebugLog([] { return "authService->Login()"; });
        authService->Login();
      },
      [](const DPSError &error) {
        LogService::ErrorLog([error] {
          return "CreateDPSManager error:" + error.developer_message;
        });
      });
}

void FIMEngine::Login(std::shared_ptr<FIMAuthListener> listener) {
  LogService::DebugLog([listener] {
    return std::string("FIMEngine::Login start") +
           "listener:" + (listener ? "true" : "false");
  });
  GetFuid(
      [listener](const std::string &fuid) {
        LogService::DebugLog(
            [fuid] { return "CreateDPSManager success, fuid:" + fuid; });
        CreateDPSManager(fuid, listener);
      },
      [listener](const std::string &errorDescription) {
        if (listener) {
          listener->OnGetAuthCodeFailed(
              static_cast<int>(FIMErrorCode::FIM_ERR_NETWORK_ERROR),
              errorDescription);
        }
        LogService::ErrorLog(
            [] { return "FIMEngine::Login getfuid failure"; });
      });
  // FetchAuthToken(
  //     [this, listener](const std::string &accessToken,
  //                      const std::string &refreshToken,
  //                      const std::string &fuid) {
  //       LogService::DebugLog([fuid] { return "CreateDPSManager success, fuid:"
  //       + fuid; }); CreateDPSManager(fuid, listener);
  //     },
  //     [listener](const std::string &errorDescription) {
  //       if (listener) {
  //         listener->OnGetAuthCodeFailed(static_cast<int>(FIMErrorCode::FIM_ERR_NETWORK_ERROR),
  //         errorDescription);
  //       }
  //       LogService::ErrorLog([] {
  //         return "FIMEngine::Login FetchAuthToken failure";
  //       });
  //     });
}

void FIMEngine::Login(std::string fuid,
                      std::shared_ptr<FIMAuthListener> listener) {
  LogService::DebugLog([listener] {
    return std::string("FIMEngine::Login start") +
           "listener:" + (listener ? "true" : "false");
  });
  FIMEngine::GetInstance().fuid = fuid;
  std::shared_ptr<DPSPubManager> dpsManager =
      DPSPubEngine::GetDPSEngine()->GetDPSManager(fuid);
  if (dpsManager) {
    std::shared_ptr<DPSAuthService> authService = dpsManager->GetAuthService();
    if (authService) {
      authService->RemoveAllListeners();
    }
  }
  CreateDPSManager(fuid, listener);
}

void FIMEngine::Logout(const std::string &user_id,
                       const std::function<void()> &OnSuccess,
                       const std::function<void(const FIMError &)> &OnFailure) {
  if (FIMEngine::GetInstance().fuid.empty()) {
    return;
  }
  std::shared_ptr<DPSPubEngine> engine = DPSPubEngine::GetDPSEngine();
  if (engine == nullptr) {
    return;
  }
  std::shared_ptr<DPSPubManager> manager = engine->GetDPSManager(user_id);
  if (manager == nullptr) {
    return;
  }

  std::shared_ptr<DPSAuthService> authService = manager->GetAuthService();
  if (authService == nullptr) {
    return;
  }
  authService->Logout(
      [OnSuccess, OnFailure] {
        //    const std::function<void()>& OnSuccess,
        // const std::function<void(const DPSError& error)>& OnFailure)
        DPSPubEngine::GetDPSEngine()->ReleaseDPSManager(
            FIMEngine::GetInstance().fuid, [OnSuccess]() { OnSuccess(); },
            [OnFailure](const DPSError &error) {
              LogService::ErrorLog(
                  [] { return "FIMEngine::Logout DPSManager release error"; });
            });
        FIMEngine::GetInstance().fuid = "";
      },
      [OnFailure](const DPSError &error) { OnFailure(FIMErrorFrom(error)); });
}

void FIMEngine::Logout(const std::function<void()> &OnSuccess,
                       const std::function<void(const FIMError &)> &OnFailure) {
  std::shared_ptr<DPSPubEngine> engine = DPSPubEngine::GetDPSEngine();
  if (FIMEngine::GetInstance().fuid.empty()) {
    return;
  }
  if (engine == nullptr) {
    LogService::ErrorLog([] { return "FIMEngine::Logout DPSEngine is null"; });
    return;
  }
  std::shared_ptr<DPSPubManager> manager =
      engine->GetDPSManager(FIMEngine::GetInstance().fuid);
  if (manager == nullptr) {
    LogService::ErrorLog([] { return "FIMEngine::Logout DPSManager is null"; });
    return;
  }

  std::shared_ptr<DPSAuthService> authService = manager->GetAuthService();
  if (authService == nullptr) {
    LogService::ErrorLog(
        [] { return "FIMEngine::Logout AuthService is null"; });
    return;
  }
  authService->Logout(
      [OnSuccess, OnFailure, authService] {
        authService->RemoveAllListeners();
        DPSPubEngine::GetDPSEngine()->ReleaseDPSManager(
            FIMEngine::GetInstance().fuid, [OnSuccess]() { OnSuccess(); },
            [OnFailure](const DPSError &error) {
              LogService::ErrorLog(
                  [] { return "FIMEngine::Logout DPSManager release error"; });
            });
        FIMEngine::GetInstance().fuid = "";
      },
      [OnFailure](const DPSError &error) { OnFailure(FIMErrorFrom(error)); });
}

void FIMEngine::SetLogHandler(FIMLogLevel min_log_level,
                              const std::shared_ptr<FIMLogHandler> &handler) {
  LogService::GetInstance()->SetLogHandler(min_log_level, handler);
}

void FIMEngine::ReleaseDPSEngine() {
  alibaba::dps::DPSPubEngine::ReleaseDPSEngine();
}

void FIMEngine::ResetUserData(
    const std::string &data_path, const std::string &user_id,
    const std::string &app_id, const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  alibaba::dps::DPSPubEngine::ResetUserData(
      data_path, user_id, app_id, OnSuccess,
      [OnFailure](const DPSError &error) { OnFailure(FIMErrorFrom(error)); });
}

void FIMEngine::OnAppDidEnterBackground() {
  if (DPSPubEngine::GetDPSEngine()) {
    DPSPubEngine::GetDPSEngine()->OnAppDidEnterBackground();
  }
}

void FIMEngine::OnAppWillEnterForeground() {
  if (DPSPubEngine::GetDPSEngine()) {
    DPSPubEngine::GetDPSEngine()->OnAppWillEnterForeground();
  }
}

void FIMEngine::RegisterNetworkService(
    std::unique_ptr<FIMNetworkService> NetworkService) {
  network_service = std::move(NetworkService);
}

void FIMEngine::GetFuid(
    const std::function<void(const std::string &fuid)> &OnTokenSuccess,
    const std::function<void(const std::string &errorDescription)>
        &OnTokenFailure) {
  std::cout << "FIM_LOG:"
            << "GetFuid" << std::endl;
  // 验证network_service
  if (!network_service) {
    std::cout << "FIM_LOG:"
              << "GetFuid no network_service" << std::endl;
    OnTokenFailure("Network service is empty");
    return;
  }
  // 验证settingServiceInstance
  if (!settingServiceInstance || settingServiceInstance->appKey.empty() ||
      settingServiceInstance->deviceId.empty()) {
    std::cout << "FIM_LOG:"
              << "GetFuid no deviceId" << std::endl;
    OnTokenFailure("Setting service instance, App key or Device ID is empty");
    return;
  }

  nlohmann::json json;
  json["deviceId"] =
      settingServiceInstance->os_name + "|" + settingServiceInstance->deviceId;
  // 发送网络请求
  FIMEngine::GetInstance().network_service->Mtop(
      "mtop.fliggy.fim.user.init", "1.0", json,
      [OnTokenSuccess, OnTokenFailure](const FIMHttpResponse &response) {
        LogService::DebugLog([] { return "FIMEngine::GetFuid get response"; });
        try {
          nlohmann::json jsonObj = nlohmann::json::parse(response.body);

          if (jsonObj["fuid"].is_null()) {
            std::cout << "FIM_LOG:"
                      << "fuid is null" << std::endl;
            OnTokenFailure("Response data is null or success flag is false");
            return;
          }
          std::string fuid;
          if (!jsonObj["fuid"].is_null()) {
            fuid = jsonObj["fuid"].get<std::string>();
          }
          if (fuid.empty()) {
            OnTokenFailure("FUID is empty");
            std::cout << "FIM_LOG:"
                      << "FUID is empty" << std::endl;
            return;
          }
          OnTokenSuccess(fuid);
        } catch (nlohmann::json::parse_error &e) {
          std::cout << "FIM_LOG:"
                    << "getfuid JSON parse error: " + std::string(e.what())
                    << std::endl;
          OnTokenFailure("getfuid JSON parse error: " + std::string(e.what()));
        }
      });
}

void FIMEngine::FetchAuthToken(
    const std::function<void(const std::string &accessToken,
                             const std::string &refreshToken,
                             const std::string &fuid)> &OnTokenSuccess,
    const std::function<void(const std::string &errorDescription)>
        &OnTokenFailure) {
  std::cout << "FIM_LOG:"
            << "FetchAuthToken" << std::endl;
  // 验证network_service
  if (!network_service) {
    std::cout << "FIM_LOG:"
              << "FetchAuthToken no network_service" << std::endl;
    OnTokenFailure("Network service is empty");
    return;
  }
  // 验证settingServiceInstance
  if (!settingServiceInstance || settingServiceInstance->appKey.empty() ||
      settingServiceInstance->deviceId.empty()) {
    std::cout << "FIM_LOG:"
              << "FetchAuthToken no deviceId" << std::endl;
    OnTokenFailure("Setting service instance, App key or Device ID is empty");
    return;
  }

  nlohmann::json json;
  json["deviceId"] =
      settingServiceInstance->os_name + "|" + settingServiceInstance->deviceId;
  // 发送网络请求
  FIMEngine::GetInstance().network_service->Mtop(
      "mtop.fliggy.fim.user.login", "1.0", json,
      [OnTokenSuccess, OnTokenFailure](const FIMHttpResponse &response) {
        LogService::DebugLog(
            [] { return "FIMEngine::FetchAuthToken get response"; });
        try {
          nlohmann::json jsonObj = nlohmann::json::parse(response.body);

          if (jsonObj["fuid"].is_null()) {
            std::cout << "FIM_LOG:"
                      << "fuid is null" << std::endl;
            OnTokenFailure("Response data is null or success flag is false");
            return;
          }
          // accessToken
          std::string accessToken;
          if (!jsonObj["imToken"]["accessToken"].is_null()) {
            accessToken = jsonObj["imToken"]["accessToken"].get<std::string>();
          }

          // refreshToken
          std::string refreshToken;
          if (!jsonObj["imToken"]["refreshToken"].is_null()) {
            refreshToken =
                jsonObj["imToken"]["refreshToken"].get<std::string>();
          }
          std::string fuid;
          if (!jsonObj["fuid"].is_null()) {
            fuid = jsonObj["fuid"].get<std::string>();
          }

          if (accessToken.empty() || refreshToken.empty()) {
            std::cout << "FIM_LOG:"
                      << "Access token or refresh token is empty" << std::endl;

            OnTokenFailure("Access token or refresh token is empty");
            return;
          }

          if (fuid.empty()) {
            OnTokenFailure("FUID is empty");
            std::cout << "FIM_LOG:"
                      << "FUID is empty" << std::endl;
            return;
          }
          std::cout << "FIM_LOG:"
                    << "accessToken->" << accessToken << "refreshToken->"
                    << refreshToken << "fuid->" << fuid << std::endl;
          OnTokenSuccess(accessToken, refreshToken, fuid);
        } catch (nlohmann::json::parse_error &e) {
          std::cout << "FIM_LOG:"
                    << "JSON parse error: " + std::string(e.what())
                    << std::endl;
          OnTokenFailure("JSON parse error: " + std::string(e.what()));
        }
      });
}

} // namespace fim
} // namespace alibaba
