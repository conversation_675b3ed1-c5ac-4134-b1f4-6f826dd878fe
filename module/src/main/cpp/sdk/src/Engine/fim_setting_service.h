//
//  FIMSettingService.hpp
//  iOSDemo
//
//  Created by 蒙晨 on 2024/2/6.
//


#pragma once

#include <functional>
#include <memory>
#include <string>
#include <vector>
#include "fliggy_im_sdk.h"

namespace alibaba {
namespace dps {

class DPSAuthTokenGotCallback;
class DPSPubAuthTokenCallback;
enum class DPSAuthTokenExpiredReason;
enum class DPSEnvType;
struct DPSMediaHost;
struct DPSSyncProtocolInfo;

}
}

namespace alibaba {
namespace fim {
using namespace alibaba::dps;;


/**
 * DPSSettingService
 */
class FIMSDK_API FIMSettingService {
 public:
    std::string deviceId;
    std::string appKey;
    std::string authId;
    std::string fuid;
    std::string os_name;
    DPSEnvType env_type;
  virtual ~FIMSettingService() {}

  /**
   * 设置多媒体Media Host, 若不设置则采用内置默认Host
   */
  void SetMediaHost(const std::vector<DPSMediaHost>& media_host);

  /**
   * 设置应用appid
   * @param app_id DingPaaS分配给应用的appID
   */
  void SetAppID(const std::string& app_id);

  /**
   * 设置应用key
   * @param app_key IM_PAAS分配给应用的key
   */
  void SetAppKey(const std::string& app_key);

  /**
   * 设置数据存储路径
   * @param data_path
   * 数据存取路径(utf-8),app需提前创建好此目录并对此目录有读写权限
   */
  void SetDataPath(const std::string& data_path);

  /**
   * 设置应用名称
   * @param app_name 应用名称
   */
  void SetAppName(const std::string& app_name);

  /**
   * 设置应用版本
   * @param app_version 应用版本号
   */
  void SetAppVersion(const std::string& version);

  /**
   * 设置App语言区域
   * @param app_locale 语言区域（默认zh_CN）
   */
  void SetAppLocale(const std::string& app_local);

  /**
   * 设置操作系统名称
   * @param os_name 操作系统名称，如Win、macOS、iOS、Android等
   */
  void SetOSName(const std::string& os_name);

  /**
   * 设置操作系统版本
   * @param os_version 操作系统版本
   */
  void SetOSVersion(const std::string& os_version);

  /**
   * 设置设备名称
   * @param device_name 设备名称
   */
  void SetDeviceName(const std::string& device_name);

  /**
   * 设置设备型号
   * @param device_type 设备型号
   */
  void SetDeviceType(const std::string& device_type);

  /**
   * 设置设备语言
   * @param device_local 设备语言（默认zh_CN）
   */
  void SetDeviceLocale(const std::string& device_local);

  /**
   * 设置设备唯一id
   * @param device_id 设备唯一id
   *  device_id生成算法不合适（容易冲突）可能会影响正常使用
   */
  void SetDeviceId(const std::string& device_id);

  /**
   * 设置时区
   * @param time_zone 时区（默认Asia/Shanghai）
   */
  void SetTimeZone(const std::string& time_zone);

  /**
   * !!!默认不需要设置，会根据你设置的env自动匹配，这个接口调试时候可以使用
   * 设置长链接地址, 若不设置则采用内置默认地址
   * @param url 长链接地址
   */
  void SetLonglinkServerAddress(const std::string& url);

  /**
   * !!!默认不需要设置，会根据你设置的env自动匹配，这个接口调试时候可以使用
   * 文件上传服务器地址, 若不设置则采用内置默认地址
   * @param url 服务器地址
   */
  void SetFileUploadServerAddress(const std::string& url);

  /**
   * 设置Sync协议相关设置
   * @param info Sync协议设置
   */
  void AddSyncProtocolSetting(
      const std::vector<DPSSyncProtocolInfo>& info);

  /**
   * 设置自定义UserAgent：可选配置，如不设定，则自动生成UserAgent，设置则使用该UA
   */
  void SetCustomUserAgent(const std::string& ua);

  /**
   * 设置是否开启ipv6(默认不开启)
   */
  void SetEnableIpv6(bool enable);

  /**
   * 设置关闭ssl校验(默认不关闭，日常环境请设置为true)
   */
  void SetDisableSslVerify(bool disable);

  /**
   * !!!默认不需要设置，会根据你设置的env自动匹配，这个接口调试时候可以使用
   * 设置当前连接的服务端环境(日常，预发或线上)
   * 需要与长连接地址匹配，不然无法登陆成功
   */
  void SetEnvType(DPSEnvType type);
};

using FIMSettingServicePtr = std::shared_ptr<FIMSettingService>;
using FIMSettingServiceWeakPtr = std::weak_ptr<FIMSettingService>;

}  // namespace dps
}  // namespace alibaba


