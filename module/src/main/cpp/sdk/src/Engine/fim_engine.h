//
//  FIMEngine.h
//  FIMSDKFramework
//
//  Created by 蒙晨 on 2024/1/24.
//

#pragma once
#include "fliggy_im_sdk.h"
#include <functional>

#include "fim_auth_listener.h"
#include "fim_engine_listener.h"
#include "fim_log_handler.h"
#include "fim_log_level.h"
#include "fim_network_service.h"
#include "fim_setting_service.h"

namespace alibaba {
namespace fim {
struct FIMError;
using namespace alibaba::dps;

class FIMSettingService;
class FIMSDK_API FIMEngine {
public:
  // 获取单例对象
  static FIMEngine &GetInstance();
  // 确保不可拷贝和不可移动
  FIMEngine(const FIMEngine &) = delete;
  FIMEngine &operator=(const FIMEngine &) = delete;
  FIMEngine(FIMEngine &&) = delete;
  FIMEngine &operator=(FIMEngine &&) = delete;

  // FIMNetworkService指针，用于发起网络请求
  std::unique_ptr<FIMNetworkService> network_service;

  std::string fuid = "";
  std::string authId;
    std::map<std::string, std::string> biz_params = {};

  void Start(const std::function<void()> &OnSuccess,
             const std::function<void(const FIMError &error)> &OnFailure);

  void OnAppDidEnterBackground();
  void OnAppWillEnterForeground();

  // 确保不可拷贝和不可移动
  // FIMEngine(const FIMEngine&) = delete;
  // FIMEngine& operator=(const FIMEngine&) = delete;
  // FIMEngine(FIMEngine&&) = delete;
  // FIMEngine& operator=(FIMEngine&&) = delete;

  // 注册外部提供的网络客户端实现
  void
  RegisterNetworkService(std::unique_ptr<FIMNetworkService> NetworkService);

  /**
   * 获取设置服务
   */
  std::shared_ptr<FIMSettingService> GetSettingService();

  /**
   * 获取引擎是否启动
   */
  bool IsStarted();

  /**
   * 设置事件监听器
   * @param listener 监听器
   */
  void SetListener(const std::shared_ptr<FIMEngineListener> &listener);
  /**
   * 没有fuid登录。sdk内部会请求fuid
   * @param listener 监听器
   */
  void Login(std::shared_ptr<FIMAuthListener> listener);
  
  /**
   * 有fuid登录
   * @param listener 监听器
   */
  void Login(std::string fuid, std::shared_ptr<FIMAuthListener> listener);

  void Logout(const std::function<void()> &OnSuccess,
              const std::function<void(const FIMError &)> &OnFailure);

  void Logout(const std::string &user_id,
              const std::function<void()> &OnSuccess,
              const std::function<void(const FIMError &)> &OnFailure);

  void
  GetMessageService(const std::string &user_id,
                    const std::function<void()> &OnSuccess,
                    const std::function<void(const FIMError &)> &OnFailure);

  /**
   * 获取所有当前存在的DPSUserId
   */
  std::vector<std::string> GetUserIds();
  /**
   * 设置log处理函数,处理函数可能会在多个线程调用,处理函数不能长时间阻塞
   * @param min_log_level
   * 需要输出的最低日志级别（日志级别大于等于这个值才会输出）
   * @param handler 日志处理函数
   */
  static void SetLogHandler(FIMLogLevel min_log_level,
                            const std::shared_ptr<FIMLogHandler> &handler);

  /**
   * 释放DPSEngine
   */
  static void ReleaseDPSEngine();

  // static void ResetUserData(
  //     const std::string& data_path,
  //     const std::string& user_id,
  //     const std::string& app_id,
  //     const std::shared_ptr<DPSResetUserDataListener>& listener);

  /**
   * 清理用户数据接口，可通过该接口清理用户数据，本地存储的消息/会话等数据将被清空
   * @param data_path
   * 数据存储路径，与之前设置到DPSSettingService内的DataPath一致
   * @param user_id 需清理的账号
   * @param app_id 账号关联的appID
   * @param OnSuccess 重置成功
   * @param OnFailure 重置失败，部分文件无法删除
   * 注意事项：
   * 1. 需在user_id 对应DPSManager启动之前进行该操作
   * 2. 部分文件由于io错误，或被其他应用使用，可能导致无法删除
   */
  static void
  ResetUserData(const std::string &data_path, const std::string &user_id,
                const std::string &app_id,
                const std::function<void()> &OnSuccess,
                const std::function<void(const FIMError &error)> &OnFailure);

private:
  // 私有构造和析构函数以防止外部创建实例
  FIMEngine();
  ~FIMEngine();

  std::shared_ptr<FIMSettingService> settingServiceInstance;

  std::shared_ptr<FIMSettingService> GetInternalSettingService() {
    if (!settingServiceInstance) {
      settingServiceInstance = std::make_shared<FIMSettingService>();
    }
    return settingServiceInstance;
  }

  void FetchAuthToken(
      const std::function<void(const std::string &accessToken,
                               const std::string &refreshToken,
                               const std::string &fuid)> &OnTokenSuccess,
      const std::function<void(const std::string &errorDescription)>
          &OnTokenFailure);
  void GetFuid(
      const std::function<void(const std::string &fuid)> &OnTokenSuccess,
      const std::function<void(const std::string &errorDescription)> &OnTokenFailure);
};

} // namespace fim
} // namespace alibaba
