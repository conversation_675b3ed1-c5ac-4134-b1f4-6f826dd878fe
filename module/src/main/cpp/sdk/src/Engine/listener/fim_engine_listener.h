#pragma once
#include "fim_error.h"
#include "fliggy_im_sdk.h"
#include <memory>

namespace alibaba {
namespace fim {
/**
 * engine事件监听器
 */
class FIMSDK_API FIMEngineListener {
public:
  virtual ~FIMEngineListener() {}

  /**
   * DB错误回调, 在数据库无法继续工作时发生此回调
   * @param uid 用户id
   * @param error 错误类型
   * error.code类型:
   * DB_FULL: 磁盘满，需提示用户进行磁盘清理操作，否则功能异常
   * DB_MAILFORMED:
   * 数据库异常，需在下次启动前调用engine.ResetUserData接口进行数据清理重置
   * DB_NO_MEMORY: 内存不足
   */
  virtual void OnDBError(const std::string &uid, const FIMError &error) = 0;

  /**
   * 用户目录重置成功
   */
  virtual void OnResetUserDataSuccess() = 0;

  /**
   * 用户目录重置失败
   */
  virtual void OnResetUserDataFailure(const FIMError &error) = 0;
};

using FIMEngineListenerPtr = std::shared_ptr<FIMEngineListener>;
using FIMEngineListenerWeakPtr = std::weak_ptr<FIMEngineListener>;

} // namespace fim
} // namespace alibaba
