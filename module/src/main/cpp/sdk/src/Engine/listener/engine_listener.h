#pragma once
#include "fim_engine_listener.h"


namespace alibaba {
namespace fim {

class EngineListener {
private:
  FIMEngineListenerPtr fim_engine_listener;
  EngineListener() = default;

public:
  static EngineListener &GetInstance();

  ~EngineListener() {}
  void SetEngineListener(const FIMEngineListenerPtr& listener);

    void OnDBError(const std::string& uid, const FIMError& error);
      /**
   * 用户目录重置成功
   */
void OnResetUserDataSuccess();

  /**
   * 用户目录重置失败
   */
void OnResetUserDataFailure(const FIMError &error);
    
    
};


} // namespace fim
} // namespace alibaba
