#pragma once
#include "dps_log_handler.h"
#include "dps_log_level.h"
#include "dps_pub_engine_listener.h"
#include "dps_reset_user_data_listener.h"
#include "engine_listener.h"
#include "fim_error.h"
#include "fim_error.hpp"
#include "fim_log_level.h"
#include "log_service.h"

namespace alibaba {
namespace fim {

class DPSEngineListenerProxy : public dps::DPSPubEngineListener,
                               public DPSResetUserDataListener {
public:
  /**
   * DB错误回调, 在数据库无法继续工作时发生此回调
   * @param uid 用户id
   * @param error 错误类型
   * error.code类型:
   * DB_FULL: 磁盘满，需提示用户进行磁盘清理操作，否则功能异常
   * DB_MAILFORMED:
   * 数据库异常，需在下次启动前调用engine.ResetUserData接口进行数据清理重置
   * DB_NO_MEMORY: 内存不足
   */
  virtual void OnDBError(const std::string &uid, const DPSError &error) {
    FIMError fim_error = FIMErrorFrom(error);
    EngineListener::GetInstance().OnDBError(uid, fim_error);
  }

  /**
   * 用户目录重置成功
   */
  virtual void OnSuccess() {
    EngineListener::GetInstance().OnResetUserDataSuccess();
  }

  /**
   * 用户目录重置失败
   */
  virtual void OnFailure(const DPSError &error) {
    FIMError fim_error = FIMErrorFrom(error);
    EngineListener::GetInstance().OnResetUserDataFailure(fim_error);
  }
};

} // namespace fim
} // namespace alibaba
