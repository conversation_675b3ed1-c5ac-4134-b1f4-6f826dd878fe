#include "engine_listener.h"
#include "dps_engine_listener_proxy.h"
#include "dps_pub_engine.h"
#include "fim_error.h"

namespace alibaba {
namespace fim {
  
EngineListener& EngineListener::GetInstance() {
    static EngineListener instance;
    return instance;
}

void EngineListener::SetEngineListener(const FIMEngineListenerPtr &listener) {
  fim_engine_listener = listener;
  std::shared_ptr<alibaba::fim::DPSEngineListenerProxy> proxy = std::make_shared<alibaba::fim::DPSEngineListenerProxy>();
  dps::DPSPubEngine::GetDPSEngine()->SetListener(proxy);
}

void EngineListener::OnDBError(const std::string &uid, const FIMError &error) {
  if (fim_engine_listener) {
    fim_engine_listener->OnDBError(uid, error);
  }
}

      /**
   * 用户目录重置成功
   */
void EngineListener::OnResetUserDataSuccess() {
  if (fim_engine_listener) {
    fim_engine_listener->OnResetUserDataSuccess();
  }
}

  /**
   * 用户目录重置失败
   */
void EngineListener::OnResetUserDataFailure(const FIMError &error) {
  if (fim_engine_listener) {
    fim_engine_listener->OnResetUserDataFailure(error);
  }
}

} // namespace fim
} // namespace alibaba
