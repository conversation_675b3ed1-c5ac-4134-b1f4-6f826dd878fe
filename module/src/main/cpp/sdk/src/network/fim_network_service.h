//
//  fim_network_service.hpp
//  FIMSDKFramework
//
//  Created by �ɳ� on 2024/1/26.
//

#ifndef fim_network_service_h
#define fim_network_service_h
// FIMNetworkService.h

//#include "fim_message_custom_content_binary_data.h"
#include <string>
#include <map>
#include "json.hpp"
#include <functional>


namespace alibaba {
    namespace fim {

        struct FIMHttpResponse {
            std::string body;
            std::string error_message;
        };

    using ResponseCallback = std::function<void(const FIMHttpResponse &)>;
    
    class FIMNetworkService {
    public:
        virtual ~FIMNetworkService() {}
    
        virtual void Mtop(const std::string& api,
                          const std::string& apiVersion,
                          const nlohmann::json &params,
                          const ResponseCallback& callback) = 0;
    };
} // namespace network
} // namespace alibaba

#endif /* fim_network_service_hpp */
