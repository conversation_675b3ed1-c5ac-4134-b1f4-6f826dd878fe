//
//  fim_invoker_macro.h
//  FliggyIM
//
//  Created by 蒙晨 on 2024/9/5.
//

#ifndef fim_invoker_macro_h
#define fim_invoker_macro_h
#include <functional>
#include <string>
#include "json.hpp"
#include "fim_error.h"
#include "fim_error_2_json.h"



using json = nlohmann::json;

namespace alibaba {
namespace fim {


#define CALLBACK_ERROR_MESSAGE_AND_RETURN(errorMessage) \
    do { \
        try { \
            json j; \
            j["error"] = errorMessage; \
            if (OnResult) OnResult(j.dump()); \
            else { /* 日志记录：回调函数为空 */ } \
        } catch (...) { \
            /* 日志记录：错误回调失败 */ \
        } \
        return; \
    } while(0)

#define CHECK_ARGS_ARRAY_AND_SIZE(expected_size) \
    do { \
        if (!args.is_array()) { \
            CALLBACK_ERROR_MESSAGE_AND_RETURN("Arguments must be an array"); \
        } \
        if (args.size() != expected_size) { \
            CALLBACK_ERROR_MESSAGE_AND_RETURN( \
                "Arguments length must be " #expected_size); \
        } \
    } while(0)

#define CHECK_AND_GET_ARG(index, paramName, checkFunc, typeName) \
    if (index < 0 || static_cast<size_t>(index) >= args.size() || args[index].is_null()) { \
        CALLBACK_ERROR_MESSAGE_AND_RETURN( \
            "Argument " #paramName " (index " #index ") is invalid or null"); \
    } \
    if (!args[index].checkFunc()) { \
        CALLBACK_ERROR_MESSAGE_AND_RETURN( \
            "Argument " #paramName " (index " #index ") must be a " typeName); \
    } \
    auto paramName = args[index]

#define GET_STRING_ARG(index, paramName) \
    CHECK_AND_GET_ARG(index, paramName, is_string, "string")

#define GET_INT_ARG(index, paramName) \
    CHECK_AND_GET_ARG(index, paramName, is_number_integer, "integer")

#define GET_INT32_ARG(index, paramName) \
    if (index < 0 || static_cast<size_t>(index) >= args.size()) { \
        CALLBACK_ERROR_MESSAGE_AND_RETURN( \
            "Argument " #paramName " (index " #index ") is out of range"); \
    } \
    if (!args[index].is_number_integer()) { \
        CALLBACK_ERROR_MESSAGE_AND_RETURN( \
            "Argument " #paramName " (index " #index ") must be an integer"); \
    } \
    int32_t paramName; \
    try { \
        paramName = args[index].get<int32_t>(); \
    } catch (...) { \
        CALLBACK_ERROR_MESSAGE_AND_RETURN( \
            "Argument " #paramName " (index " #index ") is out of range for int32_t"); \
    }

#define GET_INT64_ARG(index, paramName) \
    if (index < 0 || static_cast<size_t>(index) >= args.size()) { \
        CALLBACK_ERROR_MESSAGE_AND_RETURN( \
            "Argument " #paramName " (index " #index ") is out of range"); \
    } \
    if (!args[index].is_number_integer()) { \
        CALLBACK_ERROR_MESSAGE_AND_RETURN( \
            "Argument " #paramName " (index " #index ") must be an integer"); \
    } \
    int64_t paramName; \
    try { \
        paramName = args[index].get<int64_t>(); \
    } catch (...) { \
        CALLBACK_ERROR_MESSAGE_AND_RETURN( \
            "Argument " #paramName " (index " #index ") is out of range for int64_t"); \
    }

#define GET_DOUBLE_ARG(index, paramName) \
    CHECK_AND_GET_ARG(index, paramName, is_number, "number")

#define GET_BOOL_ARG(index, paramName) \
    CHECK_AND_GET_ARG(index, paramName, is_boolean, "boolean")

#define GET_OBJECT_ARG(index, paramName) \
    if (index < 0 || static_cast<size_t>(index) >= args.size() || args[index].is_null()) { \
        CALLBACK_ERROR_MESSAGE_AND_RETURN( \
            "Argument " #paramName " (index " #index ") is invalid or null"); \
    } \
    json paramName; \
    do { \
        if (args[index].is_object()) { \
            paramName = args[index]; \
        } else if (args[index].is_string()) { \
            try { \
                paramName = json::parse(args[index].get<std::string>()); \
                if (!paramName.is_object()) { \
                    CALLBACK_ERROR_MESSAGE_AND_RETURN( \
                        "Argument " #paramName " (index " #index ") string does not represent a valid JSON object"); \
                } \
            } catch (const json::parse_error& e) { \
                CALLBACK_ERROR_MESSAGE_AND_RETURN( \
                    "Argument " #paramName " (index " #index ") is not a valid JSON string: " + std::string(e.what())); \
            } catch (...) { \
                CALLBACK_ERROR_MESSAGE_AND_RETURN( \
                    "Argument " #paramName " (index " #index ") caused an unexpected error during parsing"); \
            } \
        } else { \
            CALLBACK_ERROR_MESSAGE_AND_RETURN( \
                "Argument " #paramName " (index " #index ") must be an object or a string representing a JSON object"); \
        } \
    } while(0)


#define GET_ARRAY_ARG(index, paramName) \
    CHECK_AND_GET_ARG(index, paramName, is_array, "array")

#define GET_MAP_ARG_WITH_DEFAULT(index, paramName) \
    std::map<std::string, std::string> paramName; \
    do { \
        if (index >= 0 && static_cast<size_t>(index) < args.size() && \
            args[index].is_object()) { \
            try { \
                for (const auto& [key, value] : args[index].items()) { \
                    if (value.is_string()) { \
                        paramName[key] = value.get<std::string>(); \
                    } \
                } \
            } catch (...) { \
                /* 转换失败时，保持 map 为空 */ \
            } \
        } \
    } while(0)

#define GET_STRING_VECTOR_ARG(index, name) \
    std::vector<std::string> name; \
    do { \
        if (args.size() <= index || !args[index].is_array()) { \
            OnResult("Invalid argument at index " #index ": expected string array"); \
            return; \
        } \
        const auto& array = args[index]; \
        name.reserve(array.size()); \
        for (const auto& item : array) { \
            if (!item.is_string()) { \
                OnResult("Invalid item in array at index " #index ": expected string"); \
                return; \
            } \
            name.push_back(item.get<std::string>()); \
        } \
    } while(0)


}
}

#endif /* fim_invoker_macro_h */
