//
//  fim_method_invoker.cpp
//  FliggyIM
//
//  Created by 蒙晨 on 2024/9/5.
//

#include "fim_method_invoker.h"
#include "fim_error.h"
#include "fim_error_2_json.h"
#include "fim_engine.h"
#include "json.hpp"
#include "fim_fbridge.h"
#include "fim_message_service.h"
#include "fim_message_c_api.h"
#include "fim_search_c_api.h"
#include "fim_conversation_service_c_api.h"

using json = nlohmann::json;

using namespace alibaba::fim;

namespace alibaba {
namespace fim {

void FIMMethodInvoker::InvokeMethod(const std::string& method,
                                    const std::string& paramsString,
                                    const FIMInvokerResultCallback& OnResult) {
    if (method.empty()) {
        FIMError error = FIMError(FIMErrorCode::FIM_ERR_FFI, "methodName is null", "methodName is null");
        std::string result = fimError2Json(error).dump();
        OnResult(result);
        return;
    }
    json args;
    if (!paramsString.empty()) {
        try {
            args = json::parse(paramsString);
        } catch (std::exception& e) {
            CallbackErrorMessage(OnResult, e.what());
            return;
        }
    }
    
    if (method == "GetMyFuid") {
        GetMyFuid(OnResult);
    } else if (method == "AddMsgListener") {
        AddMsgListener(OnResult);
    } else if (method == "RemoveMsgListener") {
        RemoveMsgListener(OnResult);
    } else if (method == "AddMsgChangeListener") {
        AddMsgChangeListener(OnResult);
    } else if (method == "RemoveMsgChangeListener") {
        RemoveMsgChangeListener(OnResult);
    }
    //fim_ListPreviousLocalMsgs
    else if (method == "ListPreviousLocalMsgs") {
        ListPreviousLocalMsgs(args, OnResult);
    }
    //fim_ListNextLocalMsgs
    else if (method == "ListNextLocalMsgs") {
        ListNextLocalMsgs(args, OnResult);
    }
    //fim_ListNextMsgs
    else if (method == "ListNextMsgs") {
        ListNextMsgs(args, OnResult);
    }
    //fim_ListPreviousMsgs
    else if (method == "ListPreviousMsgs") {
        ListPreviousMsgs(args, OnResult);
    }
    //SendMessage
    else if (method == "SendMessage") {
        SendMessage(args, OnResult);
    }
    //fim_ResendMessage
    else if (method == "ResendMessage") {
        ResendMessage(args, OnResult);
    }
    //fim_SendMessageTolocal
    else if (method == "SendMessageTolocal") {
        SendMessageTolocal(args, OnResult);
    }
    //fim_GetMessage
    else if (method == "GetMessage") {
        GetMessage(args, OnResult);
    }
    //fim_GetLocalMessage
    else if (method == "GetLocalMessage") {
        GetLocalMessage(args, OnResult);
    }
    //fim_GetLocalMessages
    else if (method == "GetLocalMessages") {
        GetLocalMessages(args, OnResult);
    }
    //fim_ForwardMessage
    else if (method == "ForwardMessage") {
        ForwardMessage(args, OnResult);
    }
    //fim_CombineForwardMessage
    else if (method == "CombineForwardMessage") {
        CombineForwardMessage(args, OnResult);
    }
    //fim_SetNeedReceivers
    else if (method == "SetNeedReceivers") {
        SetNeedReceivers(args, OnResult);
    }
    else if (method == "DeleteLocalMessage") {
        DeleteLocalMessage(args, OnResult);
    } else if (method == "DeleteMessage") {
        DeleteMessage(args, OnResult);
    } else if (method == "RecallMessage") {
        RecallMessage(args, OnResult);
    } else if (method == "SearchChatContent") {
        SearchChatContent(args, OnResult);
    }
    //fim_UpdateLocalExtension
    else if (method == "UpdateLocalExtension") {
        UpdateLocalExtension(args, OnResult);
    }
    //fim_UpdateLocalExtensionByKey
    else if (method == "UpdateLocalExtensionByKey") {
        UpdateLocalExtensionByKey(args, OnResult);
    }
    //fim_UpdateLocalMessagesBizInfo
    else if (method == "UpdateLocalMessagesBizInfo") {
        UpdateLocalMessagesBizInfo(args, OnResult);
    }
    //fim_UpdateLocalExtensionByKey
    else if (method == "UpdateLocalMessagesBizInfoByKey") {
        UpdateLocalExtensionByKey(args, OnResult);
    }
    //fim_ListMessagesReadStatus
    else if (method == "ListMessagesReadStatus") {
        ListMessagesReadStatus(args, OnResult);
    }
    //fim_UpdateMessageToRead
    else if (method == "UpdateMessageToRead") {
        UpdateMessageToRead(args, OnResult);
    }
    //fim_ReplyMessage
    else if (method == "ReplyMessage") {
        ReplyMessage(args, OnResult);
    }
    //fim_GetConversation
    else if (method == "GetConversation") {
        GetConversation(args, OnResult);
    }
    //fim_GetConversations
    else if (method == "GetConversations") {
        GetConversations(args, OnResult);
    }
    //fim_CreateCustomSingleConversation
    else if (method == "CreateCustomSingleConversation") {
        CreateCustomSingleConversation(args, OnResult);
    }
    else if (method == "ClearConversation") {
        ClearConversation(args, OnResult);
    }
    else if (method == "MuteConversation") {
        MuteConversation(args, OnResult);
    }
    else if (method == "CreateStandardSingleConversation") {
        CreateStandardSingleConversation(args, OnResult);
    }
    else if (method == "TestCreateCustomSingleConversation") {
        TestCreateCustomSingleConversation(args, OnResult);
    }
    else if (method == "ListLocalConversationsWithOffset") {
        ListLocalConversationsWithOffset(args, OnResult);
    }
    else if (method == "ListLocalConversationsWithCid") {
        ListLocalConversationsWithCid(args, OnResult);
    }
    else if (method == "ListAllStatusLocalConvs") {
        ListAllStatusLocalConvs(args, OnResult);
    }
    else if (method == "GetLocalConversations") {
        GetLocalConversations(args, OnResult);
    }
    else if (method == "GetSingleConversations") {
        GetSingleConversations(args, OnResult);
    }
    else if (method == "GetSingleConversationsWithUserIds") {
        GetSingleConversationsWithUserIds(args, OnResult);
    }
    else if (method == "RemoveLocalConversation") {
        RemoveLocalConversation(args, OnResult);
    }
    else if (method == "UpdateTypingStatus") {
        UpdateTypingStatus(args, OnResult);
    }
    else if (method == "UpdateDraftMessage") {
        UpdateDraftMessage(args, OnResult);
    }
    else if (method == "Conversation_UpdateLocalExtension") {
        Conversation_UpdateLocalExtension(args, OnResult);
    }
    else if (method == "CreateAimCustomSingleConversation") { // 支持版本： 9.10.3
        v_9_10_3_CreateAimCustomSingleConversation(args, OnResult);
    }
    else {
        CallbackErrorMessage(OnResult, "Unknown method name");
    }
}


// 全局函数来处理结果和错误
void HandleResultOrError(
    const FIMInvokerResultCallback& OnResult,
    const std::function<std::string()>& action
) {
    try {
        std::string result = action(); // 调用传入的函数
        OnResult(result); // 直接传递结果字符串
    }
    catch (const std::exception& e) {
        json errorJson;
        errorJson["error"] = std::string("Error processing result: ") + e.what();
        OnResult(errorJson.dump());
    }
    catch (...) {
        json errorJson;
        errorJson["error"] = "Unknown error occurred";
        OnResult(errorJson.dump());
    }
}

void CallbackFIMError(FIMInvokerResultCallback OnResult, const FIMError& error) {
    try {
        json errorJson = fimError2Json(error);
        json result;
        result["error"] = errorJson;
        std::string resultString = result.dump();
        OnResult(resultString);
    }
    catch (const std::exception& e) {
        // 捕获其他标准异常
        OnResult(e.what());
    }
    catch (...) {
        // 捕获所有其他异常
        OnResult("Unknown error occurred");
    }
}

void CallbackBool(FIMInvokerResultCallback OnResult, bool value) {
    json j;
    j["result"] = value;
    std::string result = j.dump();
    OnResult(result);
}

void CallbackErrorMessage(FIMInvokerResultCallback OnResult, const std::string& errorMessage) {
    json j;
    j["error"] = errorMessage;
    std::string result = j.dump();
    OnResult(result);
    return;
}

}
}



