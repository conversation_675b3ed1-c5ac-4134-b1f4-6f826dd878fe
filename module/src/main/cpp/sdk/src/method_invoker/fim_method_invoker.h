//
//  fim_method_invoker.hpp
//  FliggyIM
//
//  Created by 蒙晨 on 2024/9/5.
//

#ifndef fim_method_invoker_h
#define fim_method_invoker_h
#include "fliggy_im_sdk.h"
#include "fim_invoker_macro.h"
#include <stdio.h>
#include <string>
#include <functional>
#include "fim_error.h"
#include "json.hpp"

using json = nlohmann::json;

namespace alibaba {
namespace fim {

using FIMInvokerResultCallback = std::function<void(const std::string& result)>;


class FIMSDK_API FIMMethodInvoker {
public:
    static void InvokeMethod(const std::string& method, const std::string& paramsString,
                             const FIMInvokerResultCallback& OnResult);
};


// 全局函数来处理结果和错误
void HandleResultOrError(
    const FIMInvokerResultCallback& OnResult,
    const std::function<std::string()>& action
                         );

void CallbackFIMError(FIMInvokerResultCallback OnResult, const FIMError& error);

void CallbackBool(FIMInvokerResultCallback OnResult, bool value);

void CallbackErrorMessage(FIMInvokerResultCallback OnResult, const std::string& errorMessage);

}
}



#endif /* fim_method_invoker_hpp */
