#include "fim_message_listener.h"
#include "aim_pub_msg_listener.h"
#include "fim_message.hpp"
#include "fim_new_message.hpp"
#include "log_service.h"

namespace alibaba {
namespace fim {
      /**
   * 消息新增
   * 发送消息或收到推送消息时，触发该回调
   * 当从服务端拉取历史消息时，不会触发该回调
   * @param msgs 新增消息
   */
void FIMMessageListener::OnAddedMessages(const std::vector<dps::AIMPubNewMessage> &msgs) {
    LogService::DebugLog( [msgs]() {
        std::string logString = "cpp:OnAddedMessages: ";
        for (const auto &msg : msgs) {
            logString += msg.msg.content.text_content.text;
        }
        return logString;
    });
    std::vector<FIMNewMessage> fim_msgs = FIMNewMessageListFrom(msgs);
    OnAddedMessages(fim_msgs);
}

  /**
   * 消息删除
   * @param msgs 变更消息
   */
void FIMMessageListener::OnRemovedMessages(const std::vector<dps::AIMPubMessage> &msgs) {
    std::vector<FIMMessage> fim_msgs = FIMMessageListFrom(msgs);
    OnRemovedMessages(fim_msgs);
}

  /**
   * 当消息数据库内有消息添加时，触发该回调
   * 包括发送，推送及拉取历史消息
   * 注意：
   * 1. 不保证传入消息 msgs 的顺序
   * 2. onStored 回调的顺序也不保证消息组间的顺序
   * @param msgs 变更消息
   */
void FIMMessageListener::OnStoredMessages(const std::vector<dps::AIMPubMessage> &msgs) {
    LogService::DebugLog( [msgs]() {
        std::string logString = "cpp:OnStoredMessages: ";
        for (const auto &msg : msgs) {
            logString += msg.content.text_content.text;
        }
        return logString;
    });
    std::vector<FIMMessage> fim_msgs = FIMMessageListFrom(msgs);
    OnStoredMessages(fim_msgs);
}

} // namespace fim
} // namespace alibaba
