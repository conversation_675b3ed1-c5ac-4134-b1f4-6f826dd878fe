// FIMMessageListener
#pragma once

#include <functional>
#include <memory>
#include <vector>
#include "fim_message.h"
#include "fim_new_message.h"
#include "aim_pub_msg_listener.h"

namespace alibaba {
namespace fim {
class FIMSDK_API FIMMessageListener : public dps::AIMPubMsgListener {
public:
  virtual ~FIMMessageListener() {}

  /**
   * 消息新增
   * 发送消息或收到推送消息时，触发该回调
   * 当从服务端拉取历史消息时，不会触发该回调
   * @param msgs 新增消息
   */
  virtual void OnAddedMessages(const std::vector<FIMNewMessage> &msgs) = 0;

  /**
   * 消息删除
   * @param msgs 变更消息
   */
  virtual void OnRemovedMessages(const std::vector<FIMMessage> &msgs) = 0;

  /**
   * 当消息数据库内有消息添加时，触发该回调
   * 包括发送，推送及拉取历史消息
   * 注意：
   * 1. 不保证传入消息 msgs 的顺序
   * 2. onStored 回调的顺序也不保证消息组间的顺序
   * @param msgs 变更消息
   */
  virtual void OnStoredMessages(const std::vector<FIMMessage> &msgs) = 0;
private:

  /**
   * 消息新增
   * 发送消息或收到推送消息时，触发该回调
   * 当从服务端拉取历史消息时，不会触发该回调
   * @param msgs 新增消息
   */
void OnAddedMessages(const std::vector<AIMPubNewMessage> &msgs);

  /**
   * 消息删除
   * @param msgs 变更消息
   */
void OnRemovedMessages(const std::vector<AIMPubMessage> &msgs);

  /**
   * 当消息数据库内有消息添加时，触发该回调
   * 包括发送，推送及拉取历史消息
   * 注意：
   * 1. 不保证传入消息 msgs 的顺序
   * 2. onStored 回调的顺序也不保证消息组间的顺序
   * @param msgs 变更消息
   */
void OnStoredMessages(const std::vector<AIMPubMessage> &msgs);

};

using FIMMessageListenerPtr = std::shared_ptr<FIMMessageListener>;
using FIMMessageListenerWeakPtr = std::weak_ptr<FIMMessageListener>;

} // namespace fim
} // namespace alibaba