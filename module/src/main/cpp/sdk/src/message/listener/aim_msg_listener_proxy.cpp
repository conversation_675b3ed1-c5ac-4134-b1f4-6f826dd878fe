// AIMMsgListenerProxy.cpp

#include "aim_msg_listener_proxy.h"
#include "message_listener_manager.h"

namespace alibaba {
namespace fim {

AIMMsgListenerProxy::~AIMMsgListenerProxy() {
  // Destructor implementation if needed
}

/**
 * 消息新增
 * 发送消息或收到推送消息时，触发该回调
 * 当从服务端拉取历史消息时，不会触发该回调
 * @param msgs 新增消息
 */
void AIMMsgListenerProxy::OnAddedMessages(
    const std::vector<AIMPubNewMessage> &msgs) {
  MessageListenerManager::GetInstance()->OnAddedMessages(msgs);
}

/**
 * 消息删除
 * @param msgs 变更消息
 */
void AIMMsgListenerProxy::OnRemovedMessages(
    const std::vector<AIMPubMessage> &msgs) {
    MessageListenerManager::GetInstance()->OnRemovedMessages(msgs);
}

/**
 * 当消息数据库内有消息添加时，触发该回调
 * 包括发送，推送及拉取历史消息
 * 注意：
 * 1. 不保证传入消息 msgs 的顺序
 * 2. onStored 回调的顺序也不保证消息组间的顺序
 * @param msgs 变更消息
 */
void AIMMsgListenerProxy::OnStoredMessages(
    const std::vector<AIMPubMessage> &msgs) {
    MessageListenerManager::GetInstance()->OnStoredMessages(msgs);
}

} // namespace fim
} // namespace alibaba
