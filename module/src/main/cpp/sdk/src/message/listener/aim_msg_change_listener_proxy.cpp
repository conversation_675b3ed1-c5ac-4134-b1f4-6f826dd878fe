// AIMMsgChangeListenerProxy.cpp

#include "aim_msg_change_listener_proxy.h"
#include "message_listener_manager.h"

namespace alibaba {
namespace fim {

AIMMsgChangeListenerProxy::~AIMMsgChangeListenerProxy() {
  // Destructor implementation if needed
}

/**
 * 消息未读数变更，作为消息的发送者，表示单聊对方或者群聊群内其他成员
 * 没有读取该条消息的人数，如果未读数是0，表示所有人已读
 * @param msgs 发生变化的消息(有效字段appCid/mid/unread_count）
 */
void AIMMsgChangeListenerProxy::OnMsgUnreadCountChanged(
    const std::vector<AIMPubMessage> &msgs) {
  MessageListenerManager::GetInstance()->OnMsgUnreadCountChanged(msgs);
}

/**
 * 消息未读数变更，作为消息的接收者，多端同步消息已读状态
 * @param msgs 发生变化的消息(有效字段appCid/mid/is_read）
 */
void AIMMsgChangeListenerProxy::OnMsgReadStatusChanged(
    const std::vector<AIMPubMessage> &msgs) {
  MessageListenerManager::GetInstance()->OnMsgReadStatusChanged(msgs);
}

/**
 * 消息扩展信息变更
 * @param msgs 发生变化的消息(有效字段appCid/mid/extension)
 */
void AIMMsgChangeListenerProxy::OnMsgExtensionChanged(
    const std::vector<AIMPubMessage> &msgs) {
  MessageListenerManager::GetInstance()->OnMsgExtensionChanged(msgs);
}

/**
 * 消息本地扩展信息变更
 * @param msgs 发生变化的消息(有效字段appCid/localid/local_extension)
 */
void AIMMsgChangeListenerProxy::OnMsgLocalExtensionChanged(
    const std::vector<AIMPubMessage> &msgs) {
  MessageListenerManager::GetInstance()->OnMsgLocalExtensionChanged(msgs);
}

/**
 * 业务方自定义消息扩展信息变更
 * @param msgs 发生变化的消息(有效字段appCid/mid/user_extension字段)
 */
void AIMMsgChangeListenerProxy::OnMsgUserExtensionChanged(
    const std::vector<AIMPubMessage> &msgs) {
  MessageListenerManager::GetInstance()->OnMsgUserExtensionChanged(msgs);
}

/**
 * 消息被撤回
 * @param msgs 发生变化的消息(有效字段appCid/mid/is_recall字段)
 */
void AIMMsgChangeListenerProxy::OnMsgRecalled(
    const std::vector<AIMPubMessage> &msgs) {
  MessageListenerManager::GetInstance()->OnMsgRecalled(msgs);
}

/**
 * 消息状态变更，比如：消息状态从发送中变成了发送失败
 * @param msgs
 * 发生变化的消息(有效字段status/mid/created_at/unread_count/receiver_count/content)
 */
void AIMMsgChangeListenerProxy::OnMsgStatusChanged(
    const std::vector<AIMPubMessage> &msgs) {
  MessageListenerManager::GetInstance()->OnMsgStatusChanged(msgs);
}

/**
 * 消息发送进度变更
 * @param progress 发送进度
 */
void AIMMsgChangeListenerProxy::OnMsgSendMediaProgressChanged(
    const AIMMsgSendMediaProgress &progress) {
  MessageListenerManager::GetInstance()->OnMsgSendMediaProgressChanged(
      progress);
}

} // namespace fim
} // namespace alibaba
