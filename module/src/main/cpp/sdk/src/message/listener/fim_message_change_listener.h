#ifndef FIM_MESSAGE_CHANGE_LISTENER_H
#define FIM_MESSAGE_CHANGE_LISTENER_H

#include "fim_message.h"
#include <memory>
#include <vector>
//AIMMsgSendMediaProgress
#include "aim_msg_send_media_progress.h"
//AIMPubMsgChangeListener
#include "aim_pub_msg_change_listener.h"

namespace alibaba {
  namespace dps {
    struct AIMPubMsgChangeListener;
  }
namespace fim {


// 定义您的SDK用户将要使用的接口
class FIMSDK_API FIMMessageChangeListener: public AIMPubMsgChangeListener {
public:
  virtual ~FIMMessageChangeListener() {}

/**
   * 消息未读数变更，作为消息的发送者，表示单聊对方或者群聊群内其他成员
   * 没有读取该条消息的人数，如果未读数是0，表示所有人已读
   * @param msgs 发生变化的消息(有效字段appCid/mid/unread_count）
   */
  virtual void
  OnMsgUnreadCountChanged(const std::vector<FIMMessage> &msgs) = 0;

  /**
   * 消息未读数变更，作为消息的接收者，多端同步消息已读状态
   * @param msgs 发生变化的消息(有效字段appCid/mid/is_read）
   */
  virtual void
  OnMsgReadStatusChanged(const std::vector<FIMMessage> &msgs) = 0;

  /**
   * 消息扩展信息变更
   * @param msgs 发生变化的消息(有效字段appCid/mid/extension)
   */
  virtual void
  OnMsgExtensionChanged(const std::vector<FIMMessage> &msgs) = 0;

  /**
   * 消息本地扩展信息变更
   * @param msgs 发生变化的消息(有效字段appCid/localid/local_extension)
   */
  virtual void
  OnMsgLocalExtensionChanged(const std::vector<FIMMessage> &msgs) = 0;

  /**
   * 业务方自定义消息扩展信息变更
   * @param msgs 发生变化的消息(有效字段appCid/mid/user_extension字段)
   */
  virtual void
  OnMsgUserExtensionChanged(const std::vector<FIMMessage> &msgs) = 0;

  /**
   * 消息被撤回
   * @param msgs 发生变化的消息(有效字段appCid/mid/is_recall字段)
   */
  virtual void OnMsgRecalled(const std::vector<FIMMessage> &msgs) = 0;

  /**
   * 消息状态变更，比如：消息状态从发送中变成了发送失败
   * @param msgs
   * 发生变化的消息(有效字段status/mid/created_at/unread_count/receiver_count/content)
   */
  virtual void OnMsgStatusChanged(const std::vector<FIMMessage> &msgs) = 0;

  /**
   * 消息发送进度变更
   * @param progress 发送进度
   */
  virtual void
  OnMsgSendMediaProgressChanged(const AIMMsgSendMediaProgress &progress) = 0;

private:
  /**
   * 消息未读数变更，作为消息的发送者，表示单聊对方或者群聊群内其他成员
   * 没有读取该条消息的人数，如果未读数是0，表示所有人已读
   * @param msgs 发生变化的消息(有效字段appCid/mid/unread_count）
   */
void OnMsgUnreadCountChanged(const std::vector<AIMPubMessage> &msgs);

  /**
   * 消息未读数变更，作为消息的接收者，多端同步消息已读状态
   * @param msgs 发生变化的消息(有效字段appCid/mid/is_read）
   */
void OnMsgReadStatusChanged(const std::vector<AIMPubMessage> &msgs);

  /**
   * 消息扩展信息变更
   * @param msgs 发生变化的消息(有效字段appCid/mid/extension)
   */
void OnMsgExtensionChanged(const std::vector<AIMPubMessage> &msgs);

  /**
   * 消息本地扩展信息变更
   * @param msgs 发生变化的消息(有效字段appCid/localid/local_extension)
   */
void OnMsgLocalExtensionChanged(const std::vector<AIMPubMessage> &msgs);

  /**
   * 业务方自定义消息扩展信息变更
   * @param msgs 发生变化的消息(有效字段appCid/mid/user_extension字段)
   */
void OnMsgUserExtensionChanged(const std::vector<AIMPubMessage> &msgs);

  /**
   * 消息被撤回
   * @param msgs 发生变化的消息(有效字段appCid/mid/is_recall字段)
   */
void OnMsgRecalled(const std::vector<AIMPubMessage> &msgs);

  /**
   * 消息状态变更，比如：消息状态从发送中变成了发送失败
   * @param msgs
   * 发生变化的消息(有效字段status/mid/created_at/unread_count/receiver_count/content)
   */
void OnMsgStatusChanged(const std::vector<AIMPubMessage> &msgs);

};


} // namespace fim
} // namespace alibaba

#endif // FIM_MESSAGE_CHANGE_LISTENER_H