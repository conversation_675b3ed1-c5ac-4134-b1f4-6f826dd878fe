#pragma once
#include "aim_pub_msg_change_listener.h"
#include "aim_pub_msg_listener.h"
#include "fim_message_change_listener.h"
#include "fim_message_listener.h"
#include <mutex>
#include <type_traits>

namespace alibaba {
namespace fim {
class MessageListenerManager : public dps::AIMPubMsgListener,
                               public dps::AIMPubMsgChangeListener {
private:
  static MessageListenerManager *instance;
  std::mutex listeners_mutex;
  std::mutex change_listeners_mutex;
  std::vector<std::shared_ptr<FIMMessageListener>> message_listeners;
  std::vector<std::shared_ptr<FIMMessageChangeListener>>
      message_change_listeners;

public:
  static MessageListenerManager* GetInstance();

  ~MessageListenerManager() {}
    // Thread-safe method to add a message listener
  // void AddMessageListener(std::shared_ptr<FIMMessageListener> listener);

  // // Thread-safe method to remove a message listener
  // void RemoveMessageListener(std::shared_ptr<FIMMessageListener> listener);

  // // Thread-safe method to add a message change listener
  // void
  // AddMessageChangeListener(std::shared_ptr<FIMMessageChangeListener> listener);

  // // Thread-safe method to remove a message change listener
  // void RemoveMessageChangeListener(
  //     std::shared_ptr<FIMMessageChangeListener> listener);

  // void RemoveAllMessageListener();

  // void RemoveAllMessageChangeListener();

  void OnAddedMessages(const std::vector<AIMPubNewMessage> &msgs);

  /**
   * 消息删除
   * @param msgs 变更消息
   */
  void OnRemovedMessages(const std::vector<AIMPubMessage> &msgs);

  /**
   * 当消息数据库内有消息添加时，触发该回调
   * 包括发送，推送及拉取历史消息
   * 注意：
   * 1. 不保证传入消息 msgs 的顺序
   * 2. onStored 回调的顺序也不保证消息组间的顺序
   * @param msgs 变更消息
   */
  void OnStoredMessages(const std::vector<AIMPubMessage> &msgs);


  /**
   * 消息未读数变更，作为消息的发送者，表示单聊对方或者群聊群内其他成员
   * 没有读取该条消息的人数，如果未读数是0，表示所有人已读
   * @param msgs 发生变化的消息(有效字段appCid/mid/unread_count）
   */
  void OnMsgUnreadCountChanged(const std::vector<AIMPubMessage> &msgs);

  /**
   * 消息未读数变更，作为消息的接收者，多端同步消息已读状态
   * @param msgs 发生变化的消息(有效字段appCid/mid/is_read）
   */
  void OnMsgReadStatusChanged(const std::vector<AIMPubMessage> &msgs);

  /**
   * 消息扩展信息变更
   * @param msgs 发生变化的消息(有效字段appCid/mid/extension)
   */
  void OnMsgExtensionChanged(const std::vector<AIMPubMessage> &msgs);

  /**
   * 消息本地扩展信息变更
   * @param msgs 发生变化的消息(有效字段appCid/localid/local_extension)
   */
  void OnMsgLocalExtensionChanged(const std::vector<AIMPubMessage> &msgs);

  /**
   * 业务方自定义消息扩展信息变更
   * @param msgs 发生变化的消息(有效字段appCid/mid/user_extension字段)
   */
  void OnMsgUserExtensionChanged(const std::vector<AIMPubMessage> &msgs);

  /**
   * 消息被撤回
   * @param msgs 发生变化的消息(有效字段appCid/mid/is_recall字段)
   */
  void OnMsgRecalled(const std::vector<AIMPubMessage> &msgs);

  /**
   * 消息状态变更，比如：消息状态从发送中变成了发送失败
   * @param msgs
   * 发生变化的消息(有效字段status/mid/created_at/unread_count/receiver_count/content)
   */
  void OnMsgStatusChanged(const std::vector<AIMPubMessage> &msgs);

  /**
   * 消息发送进度变更
   * @param progress 发送进度
   */
  void OnMsgSendMediaProgressChanged(const AIMMsgSendMediaProgress &progress);
};

} // namespace fim
} // namespace alibaba