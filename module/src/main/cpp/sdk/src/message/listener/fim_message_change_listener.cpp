#include "fim_message_change_listener.h"
#include "aim_pub_msg_listener.h"
#include "fim_new_message.hpp"

namespace alibaba {
namespace fim {
  /**
   * 消息未读数变更，作为消息的发送者，表示单聊对方或者群聊群内其他成员
   * 没有读取该条消息的人数，如果未读数是0，表示所有人已读
   * @param msgs 发生变化的消息(有效字段appCid/mid/unread_count）
   */
void FIMMessageChangeListener::OnMsgUnreadCountChanged(const std::vector<AIMPubMessage> &msgs) {
    std::vector<FIMMessage> fim_msgs = FIMMessageListFrom(msgs);
    OnMsgUnreadCountChanged(fim_msgs);
}


  /**
   * 消息未读数变更，作为消息的接收者，多端同步消息已读状态
   * @param msgs 发生变化的消息(有效字段appCid/mid/is_read）
   */
void FIMMessageChangeListener::OnMsgReadStatusChanged(const std::vector<AIMPubMessage> &msgs) {
    std::vector<FIMMessage> fim_msgs = FIMMessageListFrom(msgs);
    OnMsgReadStatusChanged(fim_msgs);
}

  /**
   * 消息扩展信息变更
   * @param msgs 发生变化的消息(有效字段appCid/mid/extension)
   */
void FIMMessageChangeListener::OnMsgExtensionChanged(const std::vector<AIMPubMessage> &msgs) {
    std::vector<FIMMessage> fim_msgs = FIMMessageListFrom(msgs);
    OnMsgExtensionChanged(fim_msgs);
}

  /**
   * 消息本地扩展信息变更
   * @param msgs 发生变化的消息(有效字段appCid/localid/local_extension)
   */
void FIMMessageChangeListener::OnMsgLocalExtensionChanged(const std::vector<AIMPubMessage> &msgs) {
    std::vector<FIMMessage> fim_msgs = FIMMessageListFrom(msgs);
    OnMsgLocalExtensionChanged(fim_msgs);
}

  /**
   * 业务方自定义消息扩展信息变更
   * @param msgs 发生变化的消息(有效字段appCid/mid/user_extension字段)
   */
void FIMMessageChangeListener::OnMsgUserExtensionChanged(const std::vector<AIMPubMessage> &msgs) {
    std::vector<FIMMessage> fim_msgs = FIMMessageListFrom(msgs);
    OnMsgUserExtensionChanged(fim_msgs);
}

  /**
   * 消息被撤回
   * @param msgs 发生变化的消息(有效字段appCid/mid/is_recall字段)
   */
void FIMMessageChangeListener::OnMsgRecalled(const std::vector<AIMPubMessage> &msgs) {
    std::vector<FIMMessage> fim_msgs = FIMMessageListFrom(msgs);
    OnMsgRecalled(fim_msgs);
}

  /**
   * 消息状态变更，比如：消息状态从发送中变成了发送失败
   * @param msgs
   * 发生变化的消息(有效字段status/mid/created_at/unread_count/receiver_count/content)
   */
void FIMMessageChangeListener::OnMsgStatusChanged(const std::vector<AIMPubMessage> &msgs) {
    std::vector<FIMMessage> fim_msgs = FIMMessageListFrom(msgs);
    OnMsgStatusChanged(fim_msgs);
}
} // namespace fim
} // namespace alibaba
