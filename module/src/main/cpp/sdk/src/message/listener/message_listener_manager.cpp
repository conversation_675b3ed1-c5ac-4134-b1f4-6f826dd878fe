
#include "message_listener_manager.h"
#include "aim_pub_c_api.h"
#include "aim_pub_module.h"
#include "fim_engine.h"
#include "fim_message.h"
#include "fim_message.hpp"
#include "fim_message_change_listener.h"
#include "fim_message_listener.h"
#include "fim_new_message.hpp"
#include <memory>
#include <mutex>
#include <log_service.h>
// AIMPubMsgService
#include "aim_msg_change_listener_proxy.h"
#include "aim_msg_listener_proxy.h"
#include "aim_pub_msg_service.h"

namespace alibaba {
#pragma mark - listeners 管理
namespace fim {

MessageListenerManager *MessageListenerManager::GetInstance() {
  static MessageListenerManager instance;
  return &instance;
}

// // Thread-safe method to add a message listener
// void MessageListenerManager::AddMessageListener(
//     std::shared_ptr<FIMMessageListener> listener) {
//   std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
//   if (messageService == nullptr) {
//     return;
//   }
//   std::lock_guard<std::mutex> lock(listeners_mutex);
//   message_listeners.push_back(listener);
//   static std::once_flag flag;
//   std::call_once(flag, [messageService]() {
//     std::shared_ptr<AIMMsgListenerProxy> listenerProxy =
//         std::make_shared<AIMMsgListenerProxy>(AIMMsgListenerProxy());
//     messageService->AddMsgListener(listenerProxy);
//   });
// }

// // Thread-safe method to remove a message listener
// void MessageListenerManager::RemoveMessageListener(
//     std::shared_ptr<FIMMessageListener> listener) {
//   std::lock_guard<std::mutex> lock(listeners_mutex);
//   auto it =
//       std::find(message_listeners.begin(), message_listeners.end(), listener);
//   if (it != message_listeners.end()) {
//     message_listeners.erase(it);
//   }
// }

// Thread-safe method to add a message change listener
// void MessageListenerManager::AddMessageChangeListener(
//     std::shared_ptr<FIMMessageChangeListener> listener) {
//   std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
//   if (messageService == nullptr) {
//     return;
//   }
//   std::lock_guard<std::mutex> lock(change_listeners_mutex);
//   message_change_listeners.push_back(listener);
//   static std::once_flag flag;
//   std::call_once(flag, [messageService]() {
//     std::shared_ptr<AIMMsgChangeListenerProxy> listenerProxy =
//         std::make_shared<AIMMsgChangeListenerProxy>(
//             AIMMsgChangeListenerProxy());
//     messageService->AddMsgChangeListener(listenerProxy);
//   });
// }

// // Thread-safe method to remove a message change listener
// void MessageListenerManager::RemoveMessageChangeListener(
//     std::shared_ptr<FIMMessageChangeListener> listener) {
//   std::lock_guard<std::mutex> lock(change_listeners_mutex);
//   auto it = std::find(message_change_listeners.begin(),
//                       message_change_listeners.end(), listener);
//   if (it != message_change_listeners.end()) {
//     message_change_listeners.erase(it);
//   }
// }

// void MessageListenerManager::RemoveAllMessageListener() {
//   std::lock_guard<std::mutex> lock(listeners_mutex);
//   message_listeners.clear();
// }

// void MessageListenerManager::RemoveAllMessageChangeListener() {
//   std::lock_guard<std::mutex> lock(change_listeners_mutex);
//   message_change_listeners.clear();
// }

} // namespace fim

#pragma mark - 实现AIMPubMsgListener
namespace fim {

void MessageListenerManager::OnAddedMessages(
    const std::vector<AIMPubNewMessage> &msgs) {
    LogService::DebugLog([]() { return "OnAddedMessages"; });
  std::vector<FIMNewMessage> fimMessageList = FIMNewMessageListFrom(msgs);
  std::vector<std::shared_ptr<FIMMessageListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(listeners_mutex);
  listeners_copy = message_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      listener->OnAddedMessages(fimMessageList);
    }
  }
}

/**
 * 消息删除
 * @param msgs 变更消息
 */
void MessageListenerManager::OnRemovedMessages(
    const std::vector<AIMPubMessage> &msgs) {
  std::vector<FIMMessage> fimMessageList = FIMMessageListFrom(msgs);
  std::vector<std::shared_ptr<FIMMessageListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(listeners_mutex);
  listeners_copy = message_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      listener->OnRemovedMessages(fimMessageList);
    }
  }
}

/**
 * 当消息数据库内有消息添加时，触发该回调
 * 包括发送，推送及拉取历史消息
 * 注意：
 * 1. 不保证传入消息 msgs 的顺序
 * 2. onStored 回调的顺序也不保证消息组间的顺序
 * @param msgs 变更消息
 */
void MessageListenerManager::OnStoredMessages(
    const std::vector<AIMPubMessage> &msgs) {
  std::vector<FIMMessage> fimMessageList = FIMMessageListFrom(msgs);
  std::vector<std::shared_ptr<FIMMessageListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(listeners_mutex);
  listeners_copy = message_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      listener->OnStoredMessages(fimMessageList);
    }
  }
}

} // namespace fim
#pragma mark AIMPubMsgChangeListener
namespace fim {
/**
 * 消息未读数变更，作为消息的发送者，表示单聊对方或者群聊群内其他成员
 * 没有读取该条消息的人数，如果未读数是0，表示所有人已读
 * @param msgs 发生变化的消息(有效字段appCid/mid/unread_count）
 */
void MessageListenerManager::OnMsgUnreadCountChanged(
    const std::vector<AIMPubMessage> &msgs) {
  std::vector<std::shared_ptr<FIMMessageChangeListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(change_listeners_mutex);
  listeners_copy = message_change_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      std::vector<FIMMessage> fimMessageList = FIMMessageListFrom(msgs);
      listener->OnMsgUnreadCountChanged(fimMessageList);
    }
  }
}

/**
 * 消息未读数变更，作为消息的接收者，多端同步消息已读状态
 * @param msgs 发生变化的消息(有效字段appCid/mid/is_read）
 */
void MessageListenerManager::OnMsgReadStatusChanged(
    const std::vector<AIMPubMessage> &msgs) {
  std::vector<std::shared_ptr<FIMMessageChangeListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(change_listeners_mutex);
  listeners_copy = message_change_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      std::vector<FIMMessage> fimMessageList = FIMMessageListFrom(msgs);
      listener->OnMsgReadStatusChanged(fimMessageList);
    }
  }
}

/**
 * 消息扩展信息变更
 * @param msgs 发生变化的消息(有效字段appCid/mid/extension)
 */
void MessageListenerManager::OnMsgExtensionChanged(
    const std::vector<AIMPubMessage> &msgs) {
  std::vector<std::shared_ptr<FIMMessageChangeListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(change_listeners_mutex);
  listeners_copy = message_change_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      std::vector<FIMMessage> fimMessageList = FIMMessageListFrom(msgs);
      listener->OnMsgExtensionChanged(fimMessageList);
    }
  }
}

/**
 * 消息本地扩展信息变更
 * @param msgs 发生变化的消息(有效字段appCid/localid/local_extension)
 */
void MessageListenerManager::OnMsgLocalExtensionChanged(
    const std::vector<AIMPubMessage> &msgs) {
  std::vector<std::shared_ptr<FIMMessageChangeListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(change_listeners_mutex);
  listeners_copy = message_change_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      std::vector<FIMMessage> fimMessageList = FIMMessageListFrom(msgs);
      listener->OnMsgLocalExtensionChanged(fimMessageList);
    }
  }
}

/**
 * 业务方自定义消息扩展信息变更
 * @param msgs 发生变化的消息(有效字段appCid/mid/user_extension字段)
 */
void MessageListenerManager::OnMsgUserExtensionChanged(
    const std::vector<AIMPubMessage> &msgs) {
  std::vector<std::shared_ptr<FIMMessageChangeListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(change_listeners_mutex);
  listeners_copy = message_change_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      std::vector<FIMMessage> fimMessageList = FIMMessageListFrom(msgs);
      listener->OnMsgUserExtensionChanged(fimMessageList);
    }
  }
}

/**
 * 消息被撤回
 * @param msgs 发生变化的消息(有效字段appCid/mid/is_recall字段)
 */
void MessageListenerManager::OnMsgRecalled(
    const std::vector<AIMPubMessage> &msgs) {
  std::vector<std::shared_ptr<FIMMessageChangeListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(change_listeners_mutex);
  listeners_copy = message_change_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      std::vector<FIMMessage> fimMessageList = FIMMessageListFrom(msgs);
      listener->OnMsgRecalled(fimMessageList);
    }
  }
}

/**
 * 消息状态变更，比如：消息状态从发送中变成了发送失败
 * @param msgs
 * 发生变化的消息(有效字段status/mid/created_at/unread_count/receiver_count/content)
 */
void MessageListenerManager::OnMsgStatusChanged(
    const std::vector<AIMPubMessage> &msgs) {
  std::vector<std::shared_ptr<FIMMessageChangeListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(change_listeners_mutex);
  listeners_copy = message_change_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      std::vector<FIMMessage> fimMessageList = FIMMessageListFrom(msgs);
      listener->OnMsgStatusChanged(fimMessageList);
    }
  }
}

/**
 * 消息发送进度变更
 * @param progress 发送进度
 */
void MessageListenerManager::OnMsgSendMediaProgressChanged(
    const AIMMsgSendMediaProgress &progress) {
  std::vector<std::shared_ptr<FIMMessageChangeListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(change_listeners_mutex);
  listeners_copy = message_change_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      listener->OnMsgSendMediaProgressChanged(progress);
    }
  }
}

} // namespace fim
} // namespace alibaba
