// AIMMsgListenerProxy.h
#ifndef AIMMSGLISTENERPROXY_H_
#define AIMMSGLISTENERPROXY_H_

#include "aim_pub_msg_listener.h"
#include <memory>
#include <vector>

namespace alibaba {
namespace fim {
using namespace dps;

class AIMMsgListenerProxy : public AIMPubMsgListener {
public:
  AIMMsgListenerProxy() = default;
  virtual ~AIMMsgListenerProxy();

  /**
   * 消息新增
   * 发送消息或收到推送消息时，触发该回调
   * 当从服务端拉取历史消息时，不会触发该回调
   * @param msgs 新增消息
   */
  virtual void
  OnAddedMessages(const std::vector<AIMPubNewMessage> &msgs) override;

  /**
   * 消息删除
   * @param msgs 变更消息
   */
  virtual void
  OnRemovedMessages(const std::vector<AIMPubMessage> &msgs) override;

  /**
   * 当消息数据库内有消息添加时，触发该回调
   * 包括发送，推送及拉取历史消息
   * 注意：
   * 1. 不保证传入消息 msgs 的顺序
   * 2. onStored 回调的顺序也不保证消息组间的顺序
   * @param msgs 变更消息
   */
  virtual void
  OnStoredMessages(const std::vector<AIMPubMessage> &msgs) override;

}; // class AIMMsgListenerProxy

} // namespace fim
} // namespace alibaba

#endif // AIMMSGLISTENERPROXY_H_
