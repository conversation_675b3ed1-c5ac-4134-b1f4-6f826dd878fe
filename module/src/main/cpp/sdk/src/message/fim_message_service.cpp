//
//  fim_test.cpp
//  FliggyIMSDK
//
//  Created by 蒙晨 on 2024/2/5.
//

#include "fim_message_service.h"
#include "aim_msg_custom_content.h"
#include "aim_pub_c_api.h"
#include "aim_pub_conv_service.h"
#include "aim_pub_module.h"
#include "aim_pub_msg_content.h"
#include "aim_pub_msg_list_local_msgs_listener.h"
#include "aim_pub_msg_re_send_message.h"
#include "aim_pub_msg_send_message.h"
#include "aim_pub_msg_send_msg_listener.h"
#include "aim_pub_msg_service.h"
#include "dps_error.h"
#include "fim_engine.h"
#include "fim_error.h"
#include "fim_error.hpp"
#include "fim_message.h"
#include "fim_message.hpp"
#include "fim_message_content_base.h"
#include "fim_message_custom_content.h"
#include "fim_message_custom_content.hpp"
#include "fim_message_listener.h"
#include "fim_message_resend_message.hpp"
#include "fim_message_send_forward_message.hpp"
#include "fim_message_send_reply_message.hpp"
#include "fim_message_video_content.hpp"
#include "fim_send_message_models.h"
#include "log_service.h"
#include "message_listener_manager.h"
#include <memory>
#include <string>
//AIMPubMsgSendReplyMessage
#include "aim_pub_msg_send_reply_message.h"
// AIMPubMsgReadStatus
#include "aim_pub_msg_read_status.h"

/// 发送消息
namespace alibaba {
namespace fim {
using namespace alibaba::dps;

std::shared_ptr<AIMPubMsgService> getCurrentMessageService() {
  alibaba::dps::AIMPubModule *rawModulePtr =
      GetAIMPubModuleInstance((FIMEngine::GetInstance().fuid));
  if (rawModulePtr == nullptr) {
    return nullptr;
  }
  return rawModulePtr->GetMsgService();
}

void sendMessage(std::string appCid, AIMPubMsgContent content,
                 std::vector<std::string> receivers,
                 const std::function<void(double progress)> &OnProgress,
                 const std::function<void(const FIMMessage &msg)> &OnSuccess,
                 const std::function<void(const FIMError &error)> &OnFailure);

// void FIMMessageService::SendMessage(
//     const FIMSendMessageBase &msg,
//     const std::shared_ptr<dps::AIMPubMsgSendMsgListener> &listener,
//     const std::map<std::string, std::string> &user_data) {
//   std::shared_ptr<AIMPubMsgService> messageService =
//   getCurrentMessageService(); if (messageService == nullptr) {
//     return;
//   }
//   dps::AIMPubMsgSendMessage aimMessage = msg.toAIMPubMsgSendMessage();
//   messageService->SendMessage(aimMessage, listener, user_data);
// }

void FIMMessageService::SendMessage(
    const FIMSendMessageBase &msg,
    const std::function<void(double progress)> &OnProgress,
    const std::function<void(const FIMMessage &msg)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure,
    const std::map<std::string, std::string> &user_data) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  dps::AIMPubMsgSendMessage aimMessage = msg.toAIMPubMsgSendMessage();
  messageService->SendMessage(
      aimMessage, OnProgress,
      [OnSuccess](const AIMPubMessage &msg) {
        LogService::DebugLog([msg]() {
          std::string result = "SendMessage OnSuccess: ";
          result += "<msg.appCid:" + msg.appCid + " msg.mid:" + msg.mid + ">";
          return result;
        });
        OnSuccess(FIMMessageFrom(msg));
      },
      [OnFailure](const DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "SendMessage OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      },
      user_data);
}

void sendMessage(std::string appCid, AIMPubMsgContent content,
                 std::vector<std::string> receivers,
                 const std::function<void(double progress)> &OnProgress,
                 const std::function<void(const FIMMessage &msg)> &OnSuccess,
                 const std::function<void(const FIMError &error)> &OnFailure) {

  AIMPubMsgSendMessage msg = AIMPubMsgSendMessage();
  msg.appCid = appCid;
  msg.content = content;
  msg.receivers = receivers;
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }

  messageService->SendMessage(
      msg, [OnProgress](double progress) { OnProgress(progress); },
      [OnSuccess](const AIMPubMessage &msg) { OnSuccess(FIMMessageFrom(msg)); },
      [OnFailure](const DPSError &error) { OnFailure(FIMErrorFrom(error)); },
      {});
}

void FIMMessageService::SendAimMessage(AIMPubMsgSendMessage msg,
                 const std::function<void(double progress)> &OnProgress,
                 const std::function<void(const FIMMessage &msg)> &OnSuccess,
                 const std::function<void(const FIMError &error)> &OnFailure,
                 const std::map<std::string, std::string> &user_data) {

  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }

  messageService->SendMessage(
      msg, [OnProgress](double progress) { OnProgress(progress); },
      [OnSuccess](const AIMPubMessage &msg) { OnSuccess(FIMMessageFrom(msg)); },
      [OnFailure](const DPSError &error) { OnFailure(FIMErrorFrom(error)); },
                              user_data);
}

} // namespace fim
} // namespace alibaba

/// 查询消息
namespace alibaba {
namespace fim {
using namespace alibaba::dps;
/*
 * 获取上一页特定类型消息，按时间升序排列
 * 注意：该接口只返回本地连续数据
 * @param appCid 会话唯一id
 * @param cursor 消息事件游标,消息的创建时间，如果是最新的开始
 * AIM_MAX_MSG_CURSOR
 * @param count  返回的结果数量，最大100
 * @param listener 监听器
 */
// void FIMMessageService::ListPreviousLocalMsgs(
//     const std::string &appCid, int64_t cursor, int32_t count,
//     const std::shared_ptr<AIMPubMsgListLocalMsgsListener> &listener) {
//   std::shared_ptr<AIMPubMsgService> messageService =
//   getCurrentMessageService(); if (messageService == nullptr) {
//     return;
//   }
//   messageService->ListPreviousLocalMsgs(appCid, cursor, count, listener);
// }

void FIMMessageService::ListPreviousLocalMsgs(
    const std::string &appCid, int64_t cursor, int32_t count,
    const std::function<void(const std::vector<FIMMessage> &msgs,
                             bool has_more)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  messageService->ListPreviousLocalMsgs(
      appCid, cursor, count,
      [OnSuccess](const std::vector<AIMPubMessage> &msgs, bool has_more) {
        std::vector<FIMMessage> fimMsgs;
        for (auto msg : msgs) {
          fimMsgs.push_back(FIMMessageFrom(msg));
        }
        OnSuccess(fimMsgs, has_more);
      },
      [OnFailure](const DPSError &error) { OnFailure(FIMErrorFrom(error)); });
}

/**
 * 获取下一页特定类型消息，按时间升序排列
 * 注意：该接口只返回本地连续数据
 * @param appCid 会话唯一id
 * @param cursor 消息事件游标,消息的创建时间，如果是最新的开始
 * AIM_MAX_MSG_CURSOR
 * @param count  返回的结果数量，最大100
 * @param listener 监听器
 */
// void FIMMessageService::ListNextLocalMsgs(
//     const std::string &appCid, int64_t cursor, int32_t count,
//     const std::shared_ptr<AIMPubMsgListLocalMsgsListener> &listener) {
//   std::shared_ptr<AIMPubMsgService> messageService =
//   getCurrentMessageService(); if (messageService == nullptr) {
//     return;
//   }
//   messageService->ListNextLocalMsgs(appCid, cursor, count, listener);
// }

void FIMMessageService::ListNextLocalMsgs(
    const std::string &appCid, int64_t cursor, int32_t count,
    const std::function<void(const std::vector<FIMMessage> &msgs,
                             bool has_more)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  messageService->ListNextLocalMsgs(
      appCid, cursor, count,
      [OnSuccess](const std::vector<AIMPubMessage> &msgs, bool has_more) {
        OnSuccess(FIMMessageListFrom(msgs), has_more);
      },
      [OnFailure](const DPSError &error) { OnFailure(FIMErrorFrom(error)); });
}

/**
 * 获取下一页消息，按时间升序排列
 * @param appCid 会话唯一id
 * @param cursor 消息事件游标,消息的创建时间，如果是最新的开始
 * AIM_MAX_MSG_CURSOR
 * @param count 返回的结果数量，最大100
 * @param listener 监听器
 */
// void FIMMessageService::ListNextMsgs(
//     const std::string &appCid, int64_t cursor, int32_t count,
//     const std::shared_ptr<AIMPubMsgListNextMsgsListener> &listener) {
//   std::shared_ptr<AIMPubMsgService> messageService =
//   getCurrentMessageService(); if (messageService == nullptr) {
//     return;
//   }
//   messageService->ListNextMsgs(appCid, cursor, count, listener);
// }

void FIMMessageService::ListNextMsgs(
    const std::string &appCid, int64_t cursor, int32_t count,
    const std::function<void(const std::vector<FIMMessage> &msgs,
                             bool has_more)> &OnSuccess,
    const std::function<void(const std::vector<std::vector<FIMMessage>> &msgs,
                             const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure({}, error);
    return;
  }
  messageService->ListNextMsgs(
      appCid, cursor, count,
      [OnSuccess](const std::vector<AIMPubMessage> &msgs, bool has_more) {
        std::vector<FIMMessage> fimMsgs = FIMMessageListFrom(msgs);

        OnSuccess(fimMsgs, has_more);
      },
      [OnFailure](const std::vector<std::vector<AIMPubMessage>> &msgs,
                  const dps::DPSError &error) {
        std::vector<std::vector<FIMMessage>> lists = {};
        for (auto msgList : msgs) {
          lists.push_back(FIMMessageListFrom(msgList));
        }
        OnFailure(lists, FIMErrorFrom(error));
      });
}

/**
 * 获取上一页消息，按时间升序排列
 * @param appCid 会话唯一id
 * @param cursor 消息事件游标,消息的创建时间，如果是最新的开始
 * AIM_MAX_MSG_CURSOR
 * @param count 返回的结果数量，最大100
 * @param listener 监听器
 */
// void FIMMessageService::ListPreviousMsgs(
//     const std::string &appCid, int64_t cursor, int32_t count,
//     const std::shared_ptr<AIMPubMsgListPreviousMsgsListener> &listener) {
//   std::shared_ptr<AIMPubMsgService> messageService =
//   getCurrentMessageService(); if (messageService == nullptr) {
//     return;
//   }
//   messageService->ListPreviousMsgs(appCid, cursor, count, listener);
// }
void FIMMessageService::ListPreviousMsgs(
    const std::string &appCid, int64_t cursor, int32_t count,
    const std::function<void(const std::vector<FIMMessage> &msgs,
                             bool has_more)> &OnSuccess,
    const std::function<void(const std::vector<std::vector<FIMMessage>> &msgs,
                             const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog([]() { return "get messageService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure({}, error);
    return;
  }
  messageService->ListPreviousMsgs(
      appCid, cursor, count,
      [OnSuccess](const std::vector<AIMPubMessage> &msgs, bool has_more) {
        LogService::DebugLog([msgs]() {
          std::string result = "ListPreviousMsgs OnSuccess: ";
          for (auto msg : msgs) {
            result += "<msg.appCid:" + msg.appCid + " msg.mid:" + msg.mid + ">";
          }
          return result;
        });
        OnSuccess(FIMMessageListFrom(msgs), has_more);
      },
      [OnFailure, appCid, cursor,
       count](const std::vector<std::vector<AIMPubMessage>> &msgs,
              const DPSError &error) {
        LogService::DebugLog([appCid, cursor, count, error]() {
          std::string result = "ListPreviousMsgs OnFailure: ";
          result += "<appCid:" + appCid + " cursor:" + std::to_string(cursor) +
                    " count:" + std::to_string(count) + ">" +
                    " error develoer_message:" + error.developer_message;
          return result;
        });
        std::vector<std::vector<FIMMessage>> lists = {};
        for (auto msgList : msgs) {
          lists.push_back(FIMMessageListFrom(msgList));
        }
        FIMError fimError = FIMErrorFrom(error);
        OnFailure(lists, fimError);
      });
}

/**
 * 重发消息
 * @param resend_msg 重发消息结构
 * @param listener 监听器
 * @param user_data 用户数据
 */
// void FIMMessageService::ResendMessage(
//     const FIMMessageReSendMessage &resend_msg,
//     const std::shared_ptr<dps::AIMPubMsgReSendMsgListener> &listener,
//     const std::map<std::string, std::string> &user_data) {
//   std::shared_ptr<AIMPubMsgService> messageService =
//   getCurrentMessageService(); if (messageService == nullptr) {
//     return;
//   }
//   dps::AIMPubMsgReSendMessage aimMessage =
//       ConvertToAIMPubMsgReSendMessage(resend_msg);
//   messageService->ResendMessage(aimMessage, listener, user_data);
// }

void FIMMessageService::ResendMessage(
    const FIMMessageReSendMessage &resend_msg,
    const std::function<void(double progress)> &OnProgress,
    const std::function<void(const FIMMessage &msg)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure,
    const std::map<std::string, std::string> &user_data) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog([]() { return "get messageService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  dps::AIMPubMsgReSendMessage aimMessage =
      ConvertToAIMPubMsgReSendMessage(resend_msg);
  messageService->ResendMessage(
      aimMessage, OnProgress,
      [OnSuccess](const AIMPubMessage &msg) {
        LogService::DebugLog([msg]() {
          std::string result = "ResendMessage OnSuccess: ";
          result += "<msg.appCid:" + msg.appCid + " msg.mid:" + msg.mid + ">";
          return result;
        });
        OnSuccess(FIMMessageFrom(msg));
      },
      [OnFailure](const DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "ResendMessage OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      },
      user_data);
}
/**
 * 发送消息到本地不发送
 * @param msg 发送的消息对象
 * @param listener 监听器
 */
// void FIMMessageService::SendMessageTolocal(
//     const FIMSendMessageBase &msg,
//     const std::shared_ptr<AIMPubMsgSendMsgToLocalListener> &listener) {
//   std::shared_ptr<AIMPubMsgService> messageService =
//   getCurrentMessageService(); if (messageService == nullptr) {
//     return;
//   }
//     dps::AIMPubMsgSendMessage aimMessage = msg.toAIMPubMsgSendMessage();
//   messageService->SendMessageTolocal(aimMessage, listener);
// }

void FIMMessageService::SendMessageTolocal(
    const FIMSendMessageBase &msg,
    const std::function<void(const FIMMessage &msg)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog([]() { return "get messageService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  dps::AIMPubMsgSendMessage aimMessage = msg.toAIMPubMsgSendMessage();
  messageService->SendMessageTolocal(
      aimMessage,
      [OnSuccess](const AIMPubMessage &msg) {
        LogService::DebugLog([msg]() {
          std::string result = "SendMessageTolocal OnSuccess: ";
          result += "<msg.appCid:" + msg.appCid + " msg.mid:" + msg.mid + ">";
          return result;
        });
        OnSuccess(FIMMessageFrom(msg));
      },
      [OnFailure](const DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "SendMessageTolocal OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      });
}

void FIMMessageService::SendAimMessageTolocal(
    const AIMPubMsgSendMessage &msg,
    const std::function<void(const FIMMessage &msg)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog([]() { return "get messageService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  
  messageService->SendMessageTolocal(
                                     msg,
      [OnSuccess](const AIMPubMessage &msg) {
        LogService::DebugLog([msg]() {
          std::string result = "SendMessageTolocal OnSuccess: ";
          result += "<msg.appCid:" + msg.appCid + " msg.mid:" + msg.mid + ">";
          return result;
        });
        OnSuccess(FIMMessageFrom(msg));
      },
      [OnFailure](const DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "SendMessageTolocal OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      });
}

/**
 * 通过mid获取消息,本地不存在到服务端拉取
 * @param appCid 会话唯一id
 * @param mid 消息唯一id
 * @param listener 监听器
 */
// void FIMMessageService::GetMessage(
//     const std::string &appCid, const std::string &mid,
//     const std::shared_ptr<AIMPubMsgGetMsgListener> &listener) {
//   std::shared_ptr<AIMPubMsgService> messageService =
//   getCurrentMessageService(); if (messageService == nullptr) {
//     return;
//   }
//   messageService->GetMessage(appCid, mid, listener);
// }
void FIMMessageService::GetMessage(
    const std::string &appCid, const std::string &mid,
    const std::function<void(const FIMMessage &msg)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog([]() { return "get messageService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  messageService->GetMessage(
      appCid, mid,
      [OnSuccess](const AIMPubMessage &msg) {
        LogService::DebugLog([msg]() {
          std::string result = "GetMessage OnSuccess: ";
          result += "<msg.appCid:" + msg.appCid + " msg.mid:" + msg.mid + ">";
          return result;
        });
        FIMMessage fimMsg = FIMMessageFrom(msg);
        OnSuccess(fimMsg);
      },
      [OnFailure](const dps::DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "GetMessage OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      });
}

/**
 * 通过mid获取本地消息
 * @param appCid 会话唯一id
 * @param local_id 消息本地唯一id
 * @param listener 监听器
 */
// void FIMMessageService::GetLocalMessage(
//     const std::string &appCid, const std::string &local_id,
//     const std::shared_ptr<AIMPubMsgGetLocalMsgListener> &listener) {
//   std::shared_ptr<AIMPubMsgService> messageService =
//   getCurrentMessageService(); if (messageService == nullptr) {
//     return;
//   }
//   messageService->GetLocalMessage(appCid, local_id, listener);
// }

void FIMMessageService::GetLocalMessage(
    const std::string &appCid, const std::string &local_id,
    const std::function<void(const FIMMessage &msg)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog([]() { return "get messageService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  messageService->GetLocalMessage(
      appCid, local_id,
      [OnSuccess](const AIMPubMessage &msg) {
        LogService::DebugLog([msg]() {
          std::string result = "GetLocalMessage OnSuccess: ";
          result += "<msg.appCid:" + msg.appCid + " msg.mid:" + msg.mid + ">";
          return result;
        });
        OnSuccess(FIMMessageFrom(msg));
      },
      [OnFailure](const dps::DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "GetLocalMessage OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      });
}

/**
 * 根据条件获取本地消息
 * 注意：该接口只返回本地数据，且不保证连续
 * @param appCid 会话唯一id
 * @param cursor 消息事件游标,消息的创建时间，如果是最新的开始
 * AIM_MAX_MSG_CURSOR
 * @param count  返回的结果数量，最大100
 * @param forward true: cursor 时间更大的数据， false： cursor时间更小的数据
 * @param filter 过滤条件
 * @param listener 监听器
 */
// void FIMMessageService::GetLocalMessages(
//     const std::string &appCid, int64_t cursor, int32_t count, bool forward,
//     const AIMMsgFilter &filter,
//     const std::shared_ptr<AIMPubMsgGetLocalMsgsListener> &listener) {
//   std::shared_ptr<AIMPubMsgService> messageService =
//   getCurrentMessageService(); if (messageService == nullptr) {
//     return;
//   }
//   messageService->GetLocalMessages(appCid, cursor, count, forward, filter,
//                                    listener);
// }
void FIMMessageService::GetLocalMessages(
    const std::string &appCid, int64_t cursor, int32_t count, bool forward,
    const AIMMsgFilter &filter,
    const std::function<void(const std::vector<FIMMessage> &msgs)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog([]() { return "get messageService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  messageService->GetLocalMessages(
      appCid, cursor, count, forward, filter,
      [OnSuccess](const std::vector<AIMPubMessage> &msgs) {
        LogService::DebugLog([msgs]() {
          std::string result = "GetLocalMessages OnSuccess: ";
          for (auto msg : msgs) {
            result += "<msg.appCid:" + msg.appCid + " msg.mid:" + msg.mid + ">";
          }
          return result;
        });
        OnSuccess(FIMMessageListFrom(msgs));
      },
      [OnFailure](const dps::DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "GetLocalMessages OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      });
}

/**
 * 删除消息，消息将从列表中移除，同步入云端
 * @param appCid 会话唯一id
 * @param mids 消息id列表
 * @param listener 监听器
 */
// void FIMMessageService::DeleteMessage(
//     const std::string &appCid, const std::vector<std::string> &mids,
//     const std::shared_ptr<AIMMsgDeleteMsgListener> &listener) {
//   std::shared_ptr<AIMPubMsgService> messageService =
//   getCurrentMessageService(); if (messageService == nullptr) {
//     return;
//   }
//   messageService->DeleteMessage(appCid, mids, listener);
// }

void FIMMessageService::DeleteMessage(
    const std::string &appCid, const std::vector<std::string> &mids,
    const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog([]() { return "get messageService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  messageService->DeleteMessage(
      appCid, mids,
      [OnSuccess]() {
        LogService::DebugLog([]() { return "DeleteMessage OnSuccess"; });
        OnSuccess();
      },
      [OnFailure](const dps::DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "DeleteMessage OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      });
}

/**
 * 删除本地消息，云端不同步
 * @param appCid 会话唯一id
 * @param local_ids 消息id
 * @param listener 监听器
 */
// void FIMMessageService::DeleteLocalMessage(
//     const std::string &appCid, const std::vector<std::string> &local_ids,
//     const std::shared_ptr<AIMMsgDeleteLocalMsgListener> &listener) {
//   std::shared_ptr<AIMPubMsgService> messageService =
//   getCurrentMessageService(); if (messageService == nullptr) {
//     return;
//   }
//   messageService->DeleteLocalMessage(appCid, local_ids, listener);
// }

void FIMMessageService::DeleteLocalMessage(
    const std::string &appCid, const std::vector<std::string> &local_ids,
    const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog([]() { return "get messageService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  messageService->DeleteLocalMessage(
      appCid, local_ids,
      [OnSuccess]() {
        LogService::DebugLog([]() { return "DeleteLocalMessage OnSuccess"; });
        OnSuccess();
      },
      [OnFailure](const dps::DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "DeleteLocalMessage OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      });
}

/**
 * 撤回一条已发送的消息
 * @param appCid 会话唯一id
 * @param mid 消息id
 * @param listener 监听器
 */
// void FIMMessageService::RecallMessage(
//     const std::string &appCid, const std::string &mid,
//     const std::shared_ptr<AIMMsgRecallMsgListener> &listener) {
//   std::shared_ptr<AIMPubMsgService> messageService =
//   getCurrentMessageService(); if (messageService == nullptr) {
//     return;
//   }
//   messageService->RecallMessage(appCid, mid, listener);
// }

void FIMMessageService::RecallMessage(
    const std::string &appCid, const std::string &mid,
    const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog([]() { return "get messageService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  messageService->RecallMessage(
      appCid, mid,
      [OnSuccess]() {
        LogService::DebugLog([]() { return "RecallMessage OnSuccess"; });
        OnSuccess();
      },
      [OnFailure](const dps::DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "RecallMessage OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      });
}

/**
 * 全量更新本地消息 local extension，不同步到云端
 * @param update_infos 更新消息Extension信息列表
 * @param listener 监听器
 */
// void FIMMessageService::UpdateLocalExtension(
//     const std::vector<AIMPubMsgLocalExtensionUpdateInfo> &update_infos,
//     const std::shared_ptr<AIMMsgUpdateLocalExtensionListener> &listener) {
//   std::shared_ptr<AIMPubMsgService> messageService =
//   getCurrentMessageService(); if (messageService == nullptr) {
//     return;
//   }
//   messageService->UpdateLocalExtension(update_infos, listener);
// }
void FIMMessageService::UpdateLocalExtension(
    const std::vector<AIMPubMsgLocalExtensionUpdateInfo> &update_infos,
    const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog([]() { return "get messageService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  messageService->UpdateLocalExtension(
      update_infos,
      [OnSuccess]() {
        LogService::DebugLog([]() { return "UpdateLocalExtension OnSuccess"; });
        OnSuccess();
      },
      [OnFailure](const dps::DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "UpdateLocalExtension OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      });
}

/**
 * 根据 key 局部更新本地消息 local extension, 不同步到云端
 * @param update_infos 更新消息Extension信息列表
 * @param listener 监听器
 */
// void FIMMessageService::UpdateLocalExtensionByKey(
//     const std::vector<AIMPubMsgLocalExtensionUpdateInfo> &update_infos,
//     const std::shared_ptr<AIMMsgUpdateLocalExtensionListener> &listener) {
//   std::shared_ptr<AIMPubMsgService> messageService =
//   getCurrentMessageService(); if (messageService == nullptr) {
//     return;
//   }
//   messageService->UpdateLocalExtensionByKey(update_infos, listener);
// }
void FIMMessageService::UpdateLocalExtensionByKey(
    const std::vector<AIMPubMsgLocalExtensionUpdateInfo> &update_infos,
    const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog([]() { return "get messageService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  messageService->UpdateLocalExtensionByKey(
      update_infos,
      [OnSuccess]() {
        LogService::DebugLog(
            []() { return "UpdateLocalExtensionByKey OnSuccess"; });
        OnSuccess();
      },
      [OnFailure](const dps::DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "UpdateLocalExtensionByKey OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      });
}

/**
 * 更新本地消息 biz 相关字段
 * @param update_infos 更新消息Biz信息列表
 * @param listener 监听器
 */
// void FIMMessageService::UpdateLocalMessagesBizInfo(
//     const std::vector<AIMPubMsgBizUpdateInfo> &update_infos,
//     const std::shared_ptr<AIMMsgUpdateLocalMsgsBizInfoListener> &listener) {
//   std::shared_ptr<AIMPubMsgService> messageService =
//   getCurrentMessageService(); if (messageService == nullptr) {
//     return;
//   }
//   messageService->UpdateLocalMessagesBizInfo(update_infos, listener);
// }

void FIMMessageService::UpdateLocalMessagesBizInfo(
    const std::vector<AIMPubMsgBizUpdateInfo> &update_infos,
    const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog([]() { return "get messageService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  messageService->UpdateLocalMessagesBizInfo(
      update_infos,
      [OnSuccess]() {
        LogService::DebugLog(
            []() { return "UpdateLocalMessagesBizInfo OnSuccess"; });
        OnSuccess();
      },
      [OnFailure](const dps::DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "UpdateLocalMessagesBizInfo OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      });
}

/**
 * 获取消息已读状态
 * @param appCid 会话唯一id
 * @param mid 消息唯一id
 * @param listener 监听器
 */
// void FIMMessageService::ListMessagesReadStatus(
//     const std::string &appCid, const std::string &mid,
//     const std::shared_ptr<AIMPubMsgListMsgsReadStatus> &listener) {
//   std::shared_ptr<AIMPubMsgService> messageService =
//   getCurrentMessageService(); if (messageService == nullptr) {
//     return;
//   }
//   messageService->ListMessagesReadStatus(appCid, mid, listener);
// }

void FIMMessageService::ListMessagesReadStatus(
    const std::string &appCid, const std::string &mid,
    const std::function<void(const AIMPubMsgReadStatus &status)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog([]() { return "get messageService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  messageService->ListMessagesReadStatus(
      appCid, mid,
      [OnSuccess, appCid, mid](const AIMPubMsgReadStatus &status) {
        LogService::DebugLog([appCid, mid, status]() {
          std::string result = "ListMessagesReadStatus OnSuccess: ";
          result += "<appCid:" + appCid + " mid:" + mid + ">";
          result += "<read_uids:";
          for (auto uid : status.read_uids) {
            result += uid + ",";
          }
          result += ">";
          result += "<unread_uids:";
          for (auto uid : status.unread_uids) {
            result += uid + ",";
          }
          result += ">";
          return result;
        });
        OnSuccess(status);
      },
      [OnFailure](const dps::DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "ListMessagesReadStatus OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      });
}

/**
 * 读取接收到消息，消息进入已读状态，同步入云端
 * 人数。
 * @param appCid 会话唯一id
 * @param mids 批量消息mid
 */
void FIMMessageService::UpdateMessageToRead(
    const std::string &appCid, const std::vector<std::string> &mids) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog([]() { return "get messageService failure"; });
    return;
  }
  LogService::DebugLog([appCid, mids]() {
    std::string result = "Update Message To Read: ";
    result += "<appCid:" + appCid + ">";
    result += "<mids:";
    for (auto mid : mids) {
      result += mid + ",";
    }
    result += ">";
    return result;
  });
  messageService->UpdateMessageToRead(appCid, mids);
}

/**
 * 生成消息localId,本地唯一
 */
int64_t FIMMessageService::GenerateMsgLocalId() {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    return 0;
  }
  return messageService->GenerateMsgLocalId();
}

/**
 * 注册消息变动的监听器，如，消息新增、删除、更新
 * @param listener 消息监听器
 */
bool FIMMessageService::AddMsgListener(
    const std::shared_ptr<FIMMessageListener> &listener) {
  LogService::DebugLog([]() { return "AddMsgListener"; });
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog(
        []() { return "AddMsgListener get messageService failure"; });
    return false;
  }
  return messageService->AddMsgListener(listener);
}

/**
 * 删除消息的监听器
 * @param listener 消息监听器
 */
bool FIMMessageService::RemoveMsgListener(
    const std::shared_ptr<FIMMessageListener> &listener) {
  LogService::DebugLog([]() { return "RemoveMsgListener"; });
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog(
        []() { return "RemoveMsgListener get messageService failure"; });
    return false;
  }
  LogService::DebugLog([]() { return "RemoveMsgListener"; });
  return messageService->RemoveMsgListener(listener);
}

/**
 * 删除所有消息的监听器
 */
void FIMMessageService::RemoveAllMsgListener() {
  LogService::DebugLog([]() { return "RemoveAllMsgListener"; });
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog(
        []() { return "RemoveAllMsgListener get messageService failure"; });
    return;
  }
  LogService::DebugLog([]() { return "RemoveAllMsgListener"; });
  messageService->RemoveAllMsgListener();
}

/**
 * 注册消息属性变更的监听器
 * @param listener 变更监听器
 */
bool FIMMessageService::AddMsgChangeListener(
    const std::shared_ptr<FIMMessageChangeListener> &listener) {
  LogService::DebugLog([]() { return "AddMsgChangeListener"; });
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog(
        []() { return "AddMsgChangeListener get messageService failure"; });
    return false;
  }
  LogService::DebugLog([]() { return "AddMsgChangeListener"; });
  return messageService->AddMsgChangeListener(listener);
}

/**
 * 删除消息的属性监听器
 * @param listener 变更监听器
 */
bool FIMMessageService::RemoveMsgChangeListener(
    const std::shared_ptr<FIMMessageChangeListener> &listener) {
  LogService::DebugLog([]() { return "RemoveMsgChangeListener"; });
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog(
        []() { return "RemoveMsgChangeListener get messageService failure"; });
    return false;
  }
  LogService::DebugLog([]() { return "RemoveMsgChangeListener"; });
  return messageService->RemoveMsgChangeListener(listener);
}

/**
 * 删除所有消息的属性监听器
 */
void FIMMessageService::RemoveAllMsgChangeListener() {
  LogService::DebugLog([]() { return "RemoveAllMsgChangeListener"; });
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog([]() {
      return "RemoveAllMsgChangeListener get messageService failure";
    });
    return;
  }
  LogService::DebugLog([]() { return "RemoveAllMsgChangeListener"; });
  messageService->RemoveAllMsgChangeListener();
}

/**
 * 回复消息
 * @param msg 发送的回复消息对象
 * @param listener 监听器
 */
// void FIMMessageService::ReplyMessage(
//     const FIMMessageSendReplyMessage &msg,
//     const std::shared_ptr<AIMPubMsgReSendMsgListener> &listener,
//     const std::map<std::string, std::string> &user_data) {
//   std::shared_ptr<AIMPubMsgService> messageService =
//   getCurrentMessageService(); if (messageService == nullptr) {
//     return;
//   }
//   AIMPubMsgSendReplyMessage aimMessage =
//       ConvertToAIMPubMsgSendReplyMessage(msg);
//   messageService->ReplyMessage(aimMessage, listener, user_data);
// }

void FIMMessageService::ReplyMessage(
    const FIMMessageSendReplyMessage &msg,
    const std::function<void(double progress)> &OnProgress,
    const std::function<void(const FIMMessage &msg)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure,
    const std::map<std::string, std::string> &user_data) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog([]() { return "get messageService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  AIMPubMsgSendReplyMessage aimMessage =
      ConvertToAIMPubMsgSendReplyMessage(msg);
  messageService->ReplyMessage(
      aimMessage, OnProgress,
      [OnSuccess](const AIMPubMessage &msg) {
        LogService::DebugLog([msg]() {
          std::string result = "ReplyMessage OnSuccess: ";
          result += "<msg.appCid:" + msg.appCid + " msg.mid:" + msg.mid + ">";
          return result;
        });
        OnSuccess(FIMMessageFrom(msg));
      },
      [OnFailure](const DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "ReplyMessage OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      },
      user_data);
}

void FIMMessageService::ReplyAimMessage(
    const alibaba::dps::AIMPubMsgSendReplyMessage &aimMessage,
    const std::function<void(double progress)> &OnProgress,
    const std::function<void(const FIMMessage &msg)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure,
    const std::map<std::string, std::string> &user_data) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog([]() { return "get messageService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  messageService->ReplyMessage(
      aimMessage, OnProgress,
      [OnSuccess](const AIMPubMessage &msg) {
        LogService::DebugLog([msg]() {
          std::string result = "ReplyMessage OnSuccess: ";
          result += "<msg.appCid:" + msg.appCid + " msg.mid:" + msg.mid + ">";
          return result;
        });
        OnSuccess(FIMMessageFrom(msg));
      },
      [OnFailure](const DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "ReplyMessage OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      },
      user_data);
}

/**
 * 转发消息
 * @param msg 发送的消息对象
 * @param listener 监听器
 * @param user_data 用户数据（"trace_id"："_trace_id_example"）
 */

// void FIMMessageService::ForwardMessage(
//     const FIMMessageSendForwardMessage &mgs,
//     const std::shared_ptr<AIMMsgForwardMsgListener> &listener,
//     const std::map<std::string, std::string> &user_data) {
//   std::shared_ptr<AIMPubMsgService> messageService =
//   getCurrentMessageService(); if (messageService == nullptr) {
//     return;
//   }
//   AIMPubMsgSendForwardMessage msg = FIMMessageFromSendForwardMessage(mgs);
//   messageService->ForwardMessage(msg, listener, user_data);
// }

void FIMMessageService::ForwardMessage(
    const FIMMessageSendForwardMessage &mgs,
    const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure,
    const std::map<std::string, std::string> &user_data) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog([]() { return "get messageService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  AIMPubMsgSendForwardMessage aimMessage = AIMPubMsgSendForwardMessageFrom(mgs);
  messageService->ForwardMessage(
      aimMessage,
      [OnSuccess]() {
        LogService::DebugLog([]() { return "ForwardMessage OnSuccess"; });
        OnSuccess();
      },
      [OnFailure](const DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "ForwardMessage OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      },
      user_data);
}

void FIMMessageService::ForwardAimMessage(
    const AIMPubMsgSendForwardMessage &aimMessage,
    const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure,
    const std::map<std::string, std::string> &user_data) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog([]() { return "get messageService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  messageService->ForwardMessage(
      aimMessage,
      [OnSuccess]() {
        LogService::DebugLog([]() { return "ForwardMessage OnSuccess"; });
        OnSuccess();
      },
      [OnFailure](const DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "ForwardMessage OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      },
      user_data);
}

/**
 * 合并转发消息
 * @param msg 发送的消息对象
 * @param listener 监听器
 * @param user_data 用户数据（"trace_id"："_trace_id_example"）
 */
// void FIMMessageService::CombineForwardMessage(
//     const FIMMessageSendForwardMessage &mgs,
//     const std::shared_ptr<AIMPubMsgCombineForwardMsgListener> &listener,
//     const std::map<std::string, std::string> &user_data) {
//   std::shared_ptr<AIMPubMsgService> messageService =
//   getCurrentMessageService(); if (messageService == nullptr) {
//     return;
//   }
//   AIMPubMsgSendForwardMessage aimMessage =
//   AIMPubMsgSendForwardMessageFrom(mgs);
//   messageService->CombineForwardMessage(aimMessage, listener, user_data);
// }

void FIMMessageService::CombineForwardMessage(
    const FIMMessageSendForwardMessage &mgs,
    const std::function<void(const FIMMessage &msg)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure,
    const std::map<std::string, std::string> &user_data) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog([]() { return "get messageService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  AIMPubMsgSendForwardMessage aimMessage = AIMPubMsgSendForwardMessageFrom(mgs);
  messageService->CombineForwardMessage(
      aimMessage,
      [OnSuccess](const AIMPubMessage &msg) {
        LogService::DebugLog([msg]() {
          std::string result = "CombineForwardMessage OnSuccess: ";
          result += "<msg.appCid:" + msg.appCid + " msg.mid:" + msg.mid + ">";
          return result;
        });
        OnSuccess(FIMMessageFrom(msg));
      },
      [OnFailure](const DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "CombineForwardMessage OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      },
      user_data);
}


void FIMMessageService::CombineForwardAimMessage(
    const AIMPubMsgSendForwardMessage &aimMessage,
    const std::function<void(const FIMMessage &msg)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure,
    const std::map<std::string, std::string> &user_data) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    LogService::ErrorLog([]() { return "get messageService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get messageService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  messageService->CombineForwardMessage(
      aimMessage,
      [OnSuccess](const AIMPubMessage &msg) {
        LogService::DebugLog([msg]() {
          std::string result = "CombineForwardMessage OnSuccess: ";
          result += "<msg.appCid:" + msg.appCid + " msg.mid:" + msg.mid + ">";
          return result;
        });
        OnSuccess(FIMMessageFrom(msg));
      },
      [OnFailure](const DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "CombineForwardMessage OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      },
      user_data);
}
/**
 * 设置拉消息时是否需要receivers字段，默认为true
 * @param need 是否需要
 */
void FIMMessageService::SetNeedReceivers(bool need) {
  std::shared_ptr<AIMPubMsgService> messageService = getCurrentMessageService();
  if (messageService == nullptr) {
    return;
  }
  messageService->SetNeedReceivers(need);
}

} // namespace fim
} // namespace alibaba
