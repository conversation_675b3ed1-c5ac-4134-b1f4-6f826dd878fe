#pragma once

#include "fim_message_content_type.h"
#include "aim_msg_content_type.h"

namespace alibaba {
namespace fim {
    inline FIMMessageContentType FIMMessageContentTypeFrom(const dps::AIMMsgContentType& aimContentType) {
        return static_cast<FIMMessageContentType>(aimContentType);
    }

    inline dps::AIMMsgContentType AIMMsgContentTypeFrom(const FIMMessageContentType& fimContentType) {
        return static_cast<dps::AIMMsgContentType>(fimContentType);
    }

    inline std::vector<FIMMessageContentType> FIMMessageContentTypeListFrom(const std::vector<dps::AIMMsgContentType>& aimContentTypes) {
        std::vector<FIMMessageContentType> fimContentTypes;
        fimContentTypes.reserve(aimContentTypes.size());
        for (const auto& aimContentType : aimContentTypes) {
            fimContentTypes.push_back(FIMMessageContentTypeFrom(aimContentType));
        }
        return fimContentTypes;
    }

    inline std::vector<dps::AIMMsgContentType> AIMMsgContentTypeListFrom(const std::vector<FIMMessageContentType>& fimContentTypes) {
        std::vector<dps::AIMMsgContentType> aimContentTypes;
        aimContentTypes.reserve(fimContentTypes.size());
        for (const auto& fimContentType : fimContentTypes) {
            aimContentTypes.push_back(AIMMsgContentTypeFrom(fimContentType));
        }
        return aimContentTypes;
    }
} // namespace fim
} // namespace alibaba
