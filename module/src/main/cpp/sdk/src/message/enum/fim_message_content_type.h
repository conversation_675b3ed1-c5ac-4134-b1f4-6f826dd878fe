
#pragma once

#include <functional>

namespace alibaba {
namespace fim {

enum class FIMMessageContentType : int {
  CONTENT_TYPE_UNKNOW = -1,
  CONTENT_TYPE_TEXT = 1,
  CONTENT_TYPE_IMAGE = 2,
  CONTENT_TYPE_AUDIO = 3,
  CONTENT_TYPE_VIDEO = 4,
  CONTENT_TYPE_GEO = 5,
  CONTENT_TYPE_STRUCT = 6,
  CONTENT_TYPE_LINK = 7,
  CONTENT_TYPE_AT = 8,
  CONTENT_TYPE_FILE = 9,
  CONTENT_TYPE_REPLY = 10,
  CONTENT_TYPE_COMBINE_FORWARD = 11,
  CONTENT_TYPE_CUSTOM = 101,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::fim::FIMMessageContentType> {
  size_t operator()(::alibaba::fim::FIMMessageContentType type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
