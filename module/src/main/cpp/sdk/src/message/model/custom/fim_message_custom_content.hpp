#pragma once
#include "aim_msg_custom_content.h"
#include "fim_message_custom_content.h"
#include "fim_message_custom_content_binary_data.h"
#include <string>

namespace alibaba {
namespace fim {

// 将 AIMMsgCustomContent 转换为 FIMMessageCustomContent
inline FIMMessageCustomContent FIMMessageFromCustomContentFrom(
    const dps::AIMMsgCustomContent &aimCustomContent) {
      // std::vector<uint8_t> binary_data; ->  std::string data;
  std::string str(aimCustomContent.binary_data.begin(), aimCustomContent.binary_data.end());

  FIMMessageCustomContent fimCustomContent;
  fimCustomContent.type = aimCustomContent.type;
  fimCustomContent.data = str;
  fimCustomContent.title = aimCustomContent.title;
  fimCustomContent.summary = aimCustomContent.summary;
  fimCustomContent.degrade = aimCustomContent.degrade;
  return fimCustomContent;
}

// 将 FIMMessageCustomContent 转换为 AIMMsgCustomContent
inline dps::AIMMsgCustomContent
AIMMsgCustomContentFrom(const FIMMessageCustomContent &fimCustomContent) {
  std::vector<uint8_t> binary_data = std::vector<uint8_t>(fimCustomContent.data.begin(), fimCustomContent.data.end());
  return dps::AIMMsgCustomContent(
        fimCustomContent.type, binary_data,
        fimCustomContent.title, fimCustomContent.summary,
        fimCustomContent.degrade);
}

} // namespace fim
} // namespace alibaba
