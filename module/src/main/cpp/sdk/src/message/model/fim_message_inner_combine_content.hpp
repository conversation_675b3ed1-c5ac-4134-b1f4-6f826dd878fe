#pragma once
#include "fim_message_inner_combine_content.h"
// Include the corresponding DPS header if it's in a separate file
#include "aim_pub_msg_inner_combine_content.h" 
#include "fim_message_simple_content.hpp"
#include "fim_message_reply_content.hpp"


namespace alibaba {
namespace fim {

// Convert DPS' AIMPubMsgInnerCombineContent to FIM's version
inline FIMMessageInnerCombineContent FIMMessageFromInnerCombineContent(const dps::AIMPubMsgInnerCombineContent& aimInnerCombineContent) {
  FIMMessageInnerCombineContent fimInnerCombineContent;
  fimInnerCombineContent.content_type = static_cast<FIMMessageContentType>(aimInnerCombineContent.content_type); 
  fimInnerCombineContent.simple_content = FIMMessageSimpleContentFrom(aimInnerCombineContent.simple_content); // Assuming this function exists
  fimInnerCombineContent.reply_content = FIMMessageFromReplyContent(aimInnerCombineContent.reply_content); // Assuming this function exists
  return fimInnerCombineContent;
}

// Convert FIM's FIMMessageInnerCombineContent to DPS' version
inline dps::AIMPubMsgInnerCombineContent ConvertToAIMPubMsgInnerCombineContent(const FIMMessageInnerCombineContent& fimInnerCombineContent) {
  dps::AIMPubMsgInnerCombineContent aimInnerCombineContent;
  aimInnerCombineContent.content_type = static_cast<AIMMsgContentType>(fimInnerCombineContent.content_type); 
  aimInnerCombineContent.simple_content = AIMPubMsgSimpleContentFrom(fimInnerCombineContent.simple_content); // Assuming this function exists
  aimInnerCombineContent.reply_content = AIMPubMsgReplyContentFrom(fimInnerCombineContent.reply_content); // Assuming this function exists
  return aimInnerCombineContent;
}

} // namespace fim
} // namespace alibaba
