#pragma once
#include "fim_message_text_content.h"
#include "aim_pub_msg_text_content.h"

namespace alibaba {
namespace fim {
/**
 * 将 AIMPubMsgTextContent 转换为 FIMMessageTextContent
 */
inline FIMMessageTextContent FIMMessageTextContentFrom(const dps::AIMPubMsgTextContent& aimTextContent) {
  FIMMessageTextContent fimTextContent;
  fimTextContent.text = aimTextContent.text;
  fimTextContent.encrypted_text = aimTextContent.encrypted_text;
  fimTextContent.extension = aimTextContent.extension;
  return fimTextContent;
}

/**
 * 将 FIMMessageTextContent 转换为 AIMPubMsgTextContent
 */
inline dps::AIMPubMsgTextContent AIMPubMsgTextContentFrom(const FIMMessageTextContent& fimTextContent) {
  dps::AIMPubMsgTextContent aimTextContent;
  aimTextContent.text = fimTextContent.text;
  aimTextContent.encrypted_text = fimTextContent.encrypted_text;
  aimTextContent.extension = fimTextContent.extension;
  return aimTextContent;
}
} // namespace fim
} // namespace alibaba
