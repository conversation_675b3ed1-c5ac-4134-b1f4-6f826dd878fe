#pragma once
#include "fim_message.h"
#include "fim_message_audio_content.h"
// FIMMessageTextContent
#include "fim_message_custom_content.h"
#include "fim_message_goods_content.h"
#include "fim_message_order_content.h"
#include "fim_message_text_content.h"
// FIMMessageCombineForwardContent
#include "fim_message_combine_forward.h"
#include "fim_message_combine_forward_content.h"
#include "fim_message_custom_content_binary_data.h"
#include "fliggy_im_sdk.h"
#include <map>
#include <string>
#include <vector>

namespace alibaba {
namespace dps {
class AIMPubMsgSendMessage;
}

namespace fim {
enum class FIMMessageCardType : int32_t {
  Goods = 21,
  Order = 22,
};

struct FIMSDK_API FIMSendMessageBase {
  std::string appCid;
  std::vector<std::string> receivers;
  std::map<std::string, std::string> send_msg_extension;
  std::map<std::string, std::string> send_msg_local_extension;
  std::map<std::string, std::string> send_msg_callback_ctx;
  std::string custom_localid;
  virtual dps::AIMPubMsgSendMessage toAIMPubMsgSendMessage() const = 0;
};

// text_content
struct FIMSDK_API FIMSendMessageText : public FIMSendMessageBase,
                                       public FIMMessageTextContent {
  dps::AIMPubMsgSendMessage toAIMPubMsgSendMessage() const override;
};

// image_content
struct FIMSDK_API FIMSendMessageImage : public FIMSendMessageBase,
                                        public FIMMessageImageContent {
  dps::AIMPubMsgSendMessage toAIMPubMsgSendMessage() const override;
};

// audio_content
struct FIMSDK_API FIMSendMessageAudio : public FIMSendMessageBase,
                                        public FIMMessageAudioContent {
  dps::AIMPubMsgSendMessage toAIMPubMsgSendMessage() const override;
};

// video_content
struct FIMSDK_API FIMSendMessageVideo : public FIMSendMessageBase,
                                        public FIMMessageVideoContent {
  dps::AIMPubMsgSendMessage toAIMPubMsgSendMessage() const override;
};

// geo_content
struct FIMSDK_API FIMSendMessageGeo : public FIMSendMessageBase,
                                      public FIMMessageGeoContent {
  dps::AIMPubMsgSendMessage toAIMPubMsgSendMessage() const override;
};

// custom_content
struct FIMSDK_API FIMSendMessageCustom : public FIMSendMessageBase,
                                         public FIMMessageCustomContent {
                                          
  dps::AIMPubMsgSendMessage toAIMPubMsgSendMessage() const override;
};

// struct_content
struct FIMSDK_API FIMSendMessageStruct : public FIMSendMessageBase,
                                         public FIMMessageStructContent {
  dps::AIMPubMsgSendMessage toAIMPubMsgSendMessage() const override;
};

// at_content
struct FIMSDK_API FIMSendMessageAt : public FIMSendMessageBase,
                                         public FIMMessageStructContent {
  dps::AIMPubMsgSendMessage toAIMPubMsgSendMessage() const override;
};

// file_content
struct FIMSDK_API FIMSendMessageFile : public FIMSendMessageBase,
                                       public FIMMessageFileContent {
  dps::AIMPubMsgSendMessage toAIMPubMsgSendMessage() const override;
};

// reply_content
struct FIMSDK_API FIMSendMessageReply : public FIMSendMessageBase,
                                        public FIMMessageReplyContent {
  dps::AIMPubMsgSendMessage toAIMPubMsgSendMessage() const override;
};

// combine_forward_content
struct FIMSDK_API FIMSendMessageCombineForwardContent
    : public FIMSendMessageBase,
      public FIMMessageCombineForwardContent {
  std::string appCid;
  std::vector<std::string> receivers;
  dps::AIMPubMsgSendMessage toAIMPubMsgSendMessage() const override;
};

// goods_content
// struct FIMSDK_API FIMSendMessageGoods : public FIMSendMessageBase,
//                                         public FIMMessageCustomContent {
//   /*
//    * 卡片数据类型，是否是动态卡片，3.31全静态
//    */
//   FIMMessageDataType dataType = FIMMessageDataType::Static;
//   /*
//    * 卡片状态 1正常，2失效
//    */
//   int32_t status = 1;

//   dps::AIMPubMsgSendMessage toAIMPubMsgSendMessage() const override;


// private:
//   FIMMessageCardType type = FIMMessageCardType::Goods;
// };

// order_content
// struct FIMSDK_API FIMSendMessageOrder : public FIMSendMessageBase,
//                                         public FIMMessageCustomContent {
//   /*
//    * 卡片数据类型，是否是动态卡片，3.31全静态
//    */
//   FIMMessageDataType dataType = FIMMessageDataType::Static;
//   /*
//    * 卡片状态 1正常，2失效
//    */
//   int32_t status = 1;
//   dps::AIMPubMsgSendMessage toAIMPubMsgSendMessage() const override;

// private:
//   FIMMessageCardType type = FIMMessageCardType::Order;
// };

// share_content
struct FIMSDK_API FIMSendMessageShare : public FIMSendMessageBase {
  std::string title;
  std::string summary;
//  FIMMessageCustomContentBinaryData binaryDataStruct;
  dps::AIMPubMsgSendMessage toAIMPubMsgSendMessage() const override;
};

} // namespace fim
} // namespace alibaba
