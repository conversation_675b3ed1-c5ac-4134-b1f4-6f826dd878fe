#include "fim_send_message_models.h"
// AIMPubMsgSendMessage
#include "aim_pub_msg_send_message.h"
// AIMPubMsgTextContent
#include "aim_pub_msg_combine_forward_content.h"
#include "aim_pub_msg_text_content.h"
#include "fim_message_audio_content.hpp"
#include "fim_message_combine_forward.hpp"
#include "fim_message_combine_forward_content.hpp"
#include "fim_message_custom_content.hpp"
#include "fim_message_file_content.hpp"
#include "fim_message_geo_content.hpp"
#include "fim_message_image_content.hpp"
#include "fim_message_reply_content.hpp"
#include "fim_message_struct_content.hpp"
#include "fim_message_text_content.hpp"
#include "fim_message_video_content.hpp"
// AIMMsgContentType
#include "aim_msg_content_type.h"
#include "aim_msg_custom_content.h"
#include "aim_pub_msg_text_content.h"

namespace alibaba {
namespace fim {
using namespace dps;
// text_content
AIMPubMsgSendMessage FIMSendMessageText::toAIMPubMsgSendMessage() const {

  AIMPubMsgContent content;
  content.text_content = AIMPubMsgTextContentFrom(*this);
  content.content_type = AIMMsgContentType::CONTENT_TYPE_TEXT;

  AIMPubMsgSendMessage aimPubMsgSendMessage;
  aimPubMsgSendMessage.appCid = appCid;
  aimPubMsgSendMessage.receivers = receivers;
  aimPubMsgSendMessage.extension = send_msg_extension;
  aimPubMsgSendMessage.local_extension = send_msg_local_extension;
  aimPubMsgSendMessage.callback_ctx = send_msg_callback_ctx;
  aimPubMsgSendMessage.custom_localid = custom_localid;
  aimPubMsgSendMessage.content = content;

  return aimPubMsgSendMessage;
}

// image_content
AIMPubMsgSendMessage FIMSendMessageImage::toAIMPubMsgSendMessage() const {

  AIMPubMsgContent content;
  content.image_content = AIMMsgImageContentFrom(*this);
  content.content_type = AIMMsgContentType::CONTENT_TYPE_IMAGE;

  AIMPubMsgSendMessage aimPubMsgSendMessage;
  aimPubMsgSendMessage.appCid = appCid;
  aimPubMsgSendMessage.receivers = receivers;
  aimPubMsgSendMessage.extension = send_msg_extension;
  aimPubMsgSendMessage.local_extension = send_msg_local_extension;
  aimPubMsgSendMessage.callback_ctx = send_msg_callback_ctx;
  aimPubMsgSendMessage.custom_localid = custom_localid;
  aimPubMsgSendMessage.content = content;

  return aimPubMsgSendMessage;
}

// audio_content
AIMPubMsgSendMessage FIMSendMessageAudio::toAIMPubMsgSendMessage() const {

  AIMPubMsgContent content;
  content.audio_content = AIMPubMsgAudioContentFrom(*this);
  content.content_type = AIMMsgContentType::CONTENT_TYPE_AUDIO;

  AIMPubMsgSendMessage aimPubMsgSendMessage;
  aimPubMsgSendMessage.appCid = appCid;
  aimPubMsgSendMessage.receivers = receivers;
  aimPubMsgSendMessage.extension = send_msg_extension;
  aimPubMsgSendMessage.local_extension = send_msg_local_extension;
  aimPubMsgSendMessage.callback_ctx = send_msg_callback_ctx;
  aimPubMsgSendMessage.custom_localid = custom_localid;
  aimPubMsgSendMessage.content = content;

  return aimPubMsgSendMessage;
}

// video_content
AIMPubMsgSendMessage FIMSendMessageVideo::toAIMPubMsgSendMessage() const {

  AIMPubMsgContent content;
  content.video_content = AIMMsgVideoContentFrom(*this);
  content.content_type = AIMMsgContentType::CONTENT_TYPE_VIDEO;

  AIMPubMsgSendMessage aimPubMsgSendMessage;
  aimPubMsgSendMessage.appCid = appCid;
  aimPubMsgSendMessage.receivers = receivers;
  aimPubMsgSendMessage.extension = send_msg_extension;
  aimPubMsgSendMessage.local_extension = send_msg_local_extension;
  aimPubMsgSendMessage.callback_ctx = send_msg_callback_ctx;
  aimPubMsgSendMessage.custom_localid = custom_localid;
  aimPubMsgSendMessage.content = content;

  return aimPubMsgSendMessage;
}

// geo_content
AIMPubMsgSendMessage FIMSendMessageGeo::toAIMPubMsgSendMessage() const {
  AIMPubMsgContent content;
  content.geo_content = AIMMsgGeoContentFrom(*this);
  content.content_type = AIMMsgContentType::CONTENT_TYPE_GEO;

  AIMPubMsgSendMessage aimPubMsgSendMessage;
  aimPubMsgSendMessage.appCid = appCid;
  aimPubMsgSendMessage.receivers = receivers;
  aimPubMsgSendMessage.extension = send_msg_extension;
  aimPubMsgSendMessage.local_extension = send_msg_local_extension;
  aimPubMsgSendMessage.callback_ctx = send_msg_callback_ctx;
  aimPubMsgSendMessage.custom_localid = custom_localid;
  aimPubMsgSendMessage.content = content;

  return aimPubMsgSendMessage;
}

// custom_content
AIMPubMsgSendMessage FIMSendMessageCustom::toAIMPubMsgSendMessage() const {
  AIMMsgCustomContent custom_content;

  std::vector<uint8_t> binary_data;
  if (!data.empty()) {
    binary_data = std::vector<uint8_t>(data.begin(), data.end());
  }
  custom_content = AIMMsgCustomContent(0, binary_data, title, summary, degrade);
  AIMPubMsgContent content;
  content.custom_content = AIMMsgCustomContentFrom(*this);
  content.content_type = AIMMsgContentType::CONTENT_TYPE_CUSTOM;

  AIMPubMsgSendMessage aimPubMsgSendMessage;
  aimPubMsgSendMessage.appCid = appCid;
  aimPubMsgSendMessage.receivers = receivers;
  aimPubMsgSendMessage.extension = send_msg_extension;
  aimPubMsgSendMessage.local_extension = send_msg_local_extension;
  aimPubMsgSendMessage.callback_ctx = send_msg_callback_ctx;
  aimPubMsgSendMessage.custom_localid = custom_localid;
  aimPubMsgSendMessage.content = content;

  return aimPubMsgSendMessage;
}

// struct_content
AIMPubMsgSendMessage FIMSendMessageStruct::toAIMPubMsgSendMessage() const {
  AIMPubMsgContent content;
  content.struct_content = AIMPubMsgStructContentFrom(*this);
  content.content_type = AIMMsgContentType::CONTENT_TYPE_STRUCT;

  AIMPubMsgSendMessage aimPubMsgSendMessage;
  aimPubMsgSendMessage.appCid = appCid;
  aimPubMsgSendMessage.receivers = receivers;
  aimPubMsgSendMessage.extension = send_msg_extension;
  aimPubMsgSendMessage.local_extension = send_msg_local_extension;
  aimPubMsgSendMessage.callback_ctx = send_msg_callback_ctx;
  aimPubMsgSendMessage.custom_localid = custom_localid;
  aimPubMsgSendMessage.content = content;

  return aimPubMsgSendMessage;
}

// at_content
AIMPubMsgSendMessage FIMSendMessageAt::toAIMPubMsgSendMessage() const {
  AIMPubMsgContent content;
  content.struct_content = AIMPubMsgStructContentFrom(*this);
  content.content_type = AIMMsgContentType::CONTENT_TYPE_AT;

  AIMPubMsgSendMessage aimPubMsgSendMessage;
  aimPubMsgSendMessage.appCid = appCid;
  aimPubMsgSendMessage.receivers = receivers;
  aimPubMsgSendMessage.extension = send_msg_extension;
  aimPubMsgSendMessage.local_extension = send_msg_local_extension;
  aimPubMsgSendMessage.callback_ctx = send_msg_callback_ctx;
  aimPubMsgSendMessage.custom_localid = custom_localid;
  aimPubMsgSendMessage.content = content;

  return aimPubMsgSendMessage;
}

// file_content
AIMPubMsgSendMessage FIMSendMessageFile::toAIMPubMsgSendMessage() const {
  AIMPubMsgContent content;
  content.file_content = AIMMsgFileContentFrom(*this);
  content.content_type = AIMMsgContentType::CONTENT_TYPE_FILE;

  AIMPubMsgSendMessage aimPubMsgSendMessage;
  aimPubMsgSendMessage.appCid = appCid;
  aimPubMsgSendMessage.receivers = receivers;
  aimPubMsgSendMessage.extension = send_msg_extension;
  aimPubMsgSendMessage.local_extension = send_msg_local_extension;
  aimPubMsgSendMessage.callback_ctx = send_msg_callback_ctx;
  aimPubMsgSendMessage.custom_localid = custom_localid;
  aimPubMsgSendMessage.content = content;

  return aimPubMsgSendMessage;
}

// reply_content
AIMPubMsgSendMessage FIMSendMessageReply::toAIMPubMsgSendMessage() const {
  AIMPubMsgContent content;
  content.reply_content = AIMPubMsgReplyContentFrom(*this);
  content.content_type = AIMMsgContentType::CONTENT_TYPE_REPLY;

  AIMPubMsgSendMessage aimPubMsgSendMessage;
  aimPubMsgSendMessage.appCid = appCid;
  aimPubMsgSendMessage.receivers = receivers;
  aimPubMsgSendMessage.extension = send_msg_extension;
  aimPubMsgSendMessage.local_extension = send_msg_local_extension;
  aimPubMsgSendMessage.callback_ctx = send_msg_callback_ctx;
  aimPubMsgSendMessage.custom_localid = custom_localid;
  aimPubMsgSendMessage.content = content;

  return aimPubMsgSendMessage;
}

// fim_message_combine_forward_content
AIMPubMsgSendMessage
FIMSendMessageCombineForwardContent::toAIMPubMsgSendMessage() const {
  AIMPubMsgContent content;
  content.combine_forward_content = AIMPubMsgCombineForwardContentFrom(*this);
  content.content_type = AIMMsgContentType::CONTENT_TYPE_COMBINE_FORWARD;

  AIMPubMsgSendMessage aimPubMsgSendMessage;
  aimPubMsgSendMessage.appCid = appCid;
  aimPubMsgSendMessage.receivers = receivers;
  aimPubMsgSendMessage.extension = send_msg_extension;
  aimPubMsgSendMessage.local_extension = send_msg_local_extension;
  aimPubMsgSendMessage.callback_ctx = send_msg_callback_ctx;
  aimPubMsgSendMessage.custom_localid = custom_localid;
  aimPubMsgSendMessage.content = content;

  return aimPubMsgSendMessage;
}

// goods_content
// AIMPubMsgSendMessage FIMSendMessageGoods::toAIMPubMsgSendMessage() const {
//   AIMMsgCustomContent custom_content = AIMMsgCustomContent();
//   custom_content.type = static_cast<int32_t>(type);

//   std::vector<uint8_t> binary_data;
//   // binary_data = std::vector<uint8_t>(jsonString.begin(), jsonString.end());
//   custom_content.binary_data = binary_data;

//   AIMPubMsgContent content;
//   content.custom_content = custom_content;
//   content.content_type = AIMMsgContentType::CONTENT_TYPE_CUSTOM;

//   AIMPubMsgSendMessage aimPubMsgSendMessage;
//   aimPubMsgSendMessage.appCid = appCid;
//   aimPubMsgSendMessage.receivers = receivers;
//   aimPubMsgSendMessage.extension = send_msg_extension;
//   aimPubMsgSendMessage.local_extension = send_msg_local_extension;
//   aimPubMsgSendMessage.callback_ctx = send_msg_callback_ctx;
//   aimPubMsgSendMessage.custom_localid = custom_localid;
//   aimPubMsgSendMessage.content = content;

//   return aimPubMsgSendMessage;
// }

// // order_content
// AIMPubMsgSendMessage FIMSendMessageOrder::toAIMPubMsgSendMessage() const {
//   AIMMsgCustomContent custom_content = AIMMsgCustomContent();
//     custom_content.type = static_cast<int32_t>(type);

//   std::vector<uint8_t> binary_data;
//   binary_data = std::vector<uint8_t>(data.begin(), data.end());
//   custom_content.binary_data = binary_data;

//   AIMPubMsgContent content;
//   content.custom_content = custom_content;
//   content.content_type = AIMMsgContentType::CONTENT_TYPE_CUSTOM;

//   AIMPubMsgSendMessage aimPubMsgSendMessage;
//   aimPubMsgSendMessage.appCid = appCid;
//   aimPubMsgSendMessage.receivers = receivers;
//   aimPubMsgSendMessage.extension = send_msg_extension;
//   aimPubMsgSendMessage.local_extension = send_msg_local_extension;
//   aimPubMsgSendMessage.callback_ctx = send_msg_callback_ctx;
//   aimPubMsgSendMessage.custom_localid = custom_localid;
//   aimPubMsgSendMessage.content = content;
//   return aimPubMsgSendMessage;
// }

// share_content
AIMPubMsgSendMessage FIMSendMessageShare::toAIMPubMsgSendMessage() const {
  AIMMsgCustomContent custom_content = AIMMsgCustomContent();
  custom_content.title = title;
  custom_content.summary = summary;
  custom_content.type = 101;
//  custom_content.binary_data = binaryDataStruct.toBinaryData();

  AIMPubMsgContent content;
  content.custom_content = custom_content;
  content.content_type = AIMMsgContentType::CONTENT_TYPE_CUSTOM;

  AIMPubMsgSendMessage aimPubMsgSendMessage;
  aimPubMsgSendMessage.appCid = appCid;
  aimPubMsgSendMessage.receivers = receivers;
  aimPubMsgSendMessage.extension = send_msg_extension;
  aimPubMsgSendMessage.local_extension = send_msg_local_extension;
  aimPubMsgSendMessage.callback_ctx = send_msg_callback_ctx;
  aimPubMsgSendMessage.custom_localid = custom_localid;
  aimPubMsgSendMessage.content = content;

  return aimPubMsgSendMessage;
}

} // namespace fim
} // namespace alibaba
