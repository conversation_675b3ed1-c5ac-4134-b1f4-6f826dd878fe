#pragma once

#include <string>
#include <vector>
#include <map>
#include "fim_message_content.h" // Include the corresponding FIM message content header

namespace alibaba {
namespace fim {

/**
 * 回复消息
 */
struct FIMMessageSendReplyMessage {
  std::string appCid;
  std::string reference_mid;
  FIMMessageContent reply_content;
  std::vector<std::string> receivers;
  std::map<std::string, std::string> extension;
  std::map<std::string, std::string> local_extension;
  std::map<std::string, std::string> callback_ctx;

  FIMMessageSendReplyMessage() {}

  FIMMessageSendReplyMessage(std::string appCid_, std::string reference_mid_,
                             FIMMessageContent reply_content_,
                             std::vector<std::string> receivers_,
                             std::map<std::string, std::string> extension_,
                             std::map<std::string, std::string> local_extension_,
                             std::map<std::string, std::string> callback_ctx_)
      : appCid(std::move(appCid_)), reference_mid(std::move(reference_mid_)),
        reply_content(std::move(reply_content_)),
        receivers(std::move(receivers_)), extension(std::move(extension_)),
        local_extension(std::move(local_extension_)),
        callback_ctx(std::move(callback_ctx_)) {}
};

} // namespace fim
} // namespace alibaba
