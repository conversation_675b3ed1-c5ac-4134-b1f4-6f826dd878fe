#pragma once
#include "fim_message_reference_content.hpp" // Include reference content header
#include "fim_message_reference.h" // Include the definition of FIMMessageReference
#include "aim_pub_msg_reference.h" // Include the original structure definition from dps namespace

namespace alibaba {
namespace fim {

inline FIMMessageReference FIMMessageReferenceFrom(const dps::AIMPubMsgReference& aimRef) {
  return FIMMessageReference(
    aimRef.sender,
    aimRef.appCid,
    aimRef.mid,
    aimRef.created_at,
    FIMMessageReferenceContentFrom(aimRef.reference_content), // Assuming a conversion function exists
    aimRef.extension
  );
}

inline dps::AIMPubMsgReference AIMPubMsgReferenceFrom(const FIMMessageReference& fimRef) {
  return dps::AIMPubMsgReference(
    fimRef.sender,
    fimRef.appCid,
    fimRef.mid,
    fimRef.created_at,
    AIMPubMsgReferenceContentFrom(fimRef.reference_content), // Assuming a conversion function exists
    fimRef.extension
  );
}

} // namespace fim
} // namespace alibaba
