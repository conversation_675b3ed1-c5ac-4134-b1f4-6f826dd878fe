#pragma once
#include "fim_message_text_content.h" 
#include "fim_message_struct_element_type.h" 
#include "fim_message_struct_element_uid.h"
#include "fim_message_struct_element_at.h" 

namespace alibaba {
namespace fim {

/**
 * 结构化消息元素
 */
struct FIMMessageStructElement final {
  FIMMessageStructElementType element_type =
      FIMMessageStructElementType::ELEMENT_TYPE_UNKNOWN;
  FIMMessageTextContent text_content;
  FIMMessageStructElementUid uid_element;
  FIMMessageStructElementAt at_element;

  FIMMessageStructElement(FIMMessageStructElementType element_type_,
                          FIMMessageTextContent text_content_,
                          FIMMessageStructElementUid uid_element_,
                          FIMMessageStructElementAt at_element_)
      : element_type(std::move(element_type_)),
        text_content(std::move(text_content_)),
        uid_element(std::move(uid_element_)),
        at_element(std::move(at_element_)) {}

  FIMMessageStructElement() {}
};

} // namespace fim
} // namespace alibaba
