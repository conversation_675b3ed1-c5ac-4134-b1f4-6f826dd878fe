#pragma once
#include <string>
#include <map>

namespace alibaba {
namespace fim {
/**
 * 文本消息内容
 */
struct FIMMessageTextContent {
  /**
   * 内容
   */
  std::string text;
  /**
   * 加密内容
   */
  std::string encrypted_text;
  /**
   * 扩展内容
   */
  std::map<std::string, std::string> extension;

  FIMMessageTextContent() {}
  
  FIMMessageTextContent(std::string text_, std::string encrypted_text_,
                        std::map<std::string, std::string> extension_)
      : text(std::move(text_)), encrypted_text(std::move(encrypted_text_)),
        extension(std::move(extension_)) {}
};
} // namespace fim
} // namespace alibaba
