#pragma once
#include "fim_message_audio_content.h"
#include "aim_msg_audio_content.h"

namespace alibaba {
namespace fim {

/**
 * 将 AIMMsgAudioContent 转换为 FIMMessageAudioContent
 */
inline FIMMessageAudioContent FIMMessageFromAudioContent(const AIMMsgAudioContent& aimContent) {
  return FIMMessageAudioContent(aimContent.local_path,
                                aimContent.upload_path,
                                aimContent.mime_type,
                                aimContent.url,
                                aimContent.media_id,
                                aimContent.binary_data,
                                aimContent.audio_type,
                                aimContent.duration);
}

/**
 * 将 FIMMessageAudioContent 转换为 AIMMsgAudioContent
 */
inline AIMMsgAudioContent AIMPubMsgAudioContentFrom(const FIMMessageAudioContent& fimContent) {
  return AIMMsgAudioContent(fimContent.local_path,
                            fimContent.upload_path,
                            fimContent.mime_type,
                            fimContent.url,
                            fimContent.media_id,
                            fimContent.binary_data,
                            fimContent.audio_type,
                            fimContent.duration);
}

} // namespace fim
} // namespace alibaba
