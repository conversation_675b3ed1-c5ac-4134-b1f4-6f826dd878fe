#pragma once
#include "fim_message_combine_forward_content.h"
// Include the corresponding DPS header
#include "aim_pub_msg_combine_forward_content.h"
#include "fim_message_combine_forward.hpp"

namespace alibaba {
namespace fim {

// Convert DPS' AIMPubMsgCombineForwardContent to FIM's version
inline FIMMessageCombineForwardContent FIMMessageFromCombineForwardContent(const dps::AIMPubMsgCombineForwardContent& aimCombineForwardContent) {
  FIMMessageCombineForwardContent fimCombineForwardContent;
  for (const auto& aimCombineForward : aimCombineForwardContent.combine_forward) {
    fimCombineForwardContent.combine_forward.push_back(FIMMessageFromCombineForward(aimCombineForward)); // Assuming this function exists
  }
  return fimCombineForwardContent;
}

// Convert FIM's FIMMessageCombineForwardContent to DPS' version
inline dps::AIMPubMsgCombineForwardContent AIMPubMsgCombineForwardContentFrom(const FIMMessageCombineForwardContent& fimCombineForwardContent) {
  dps::AIMPubMsgCombineForwardContent aimCombineForwardContent;
  for (const auto& fimCombineForward : fimCombineForwardContent.combine_forward) {
    aimCombineForwardContent.combine_forward.push_back(AIMPubMsgCombineForwardFrom(fimCombineForward)); // Assuming this function exists
  }
  return aimCombineForwardContent;
}

} // namespace fim
} // namespace alibaba
