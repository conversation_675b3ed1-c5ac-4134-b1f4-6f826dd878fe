#pragma once
#include "fim_message_simple_content.h" 
#include "fim_message_content_type.h"
namespace alibaba {
namespace fim {

struct FIMMessageReferenceContent final {
  FIMMessageContentType content_type = FIMMessageContentType::CONTENT_TYPE_UNKNOW;
  FIMMessageSimpleContent content;

  FIMMessageReferenceContent(FIMMessageContentType content_type_,
                             FIMMessageSimpleContent content_)
      : content_type(std::move(content_type_)), content(std::move(content_)) {}

  FIMMessageReferenceContent() {}
};

} // namespace fim
} // namespace alibaba
