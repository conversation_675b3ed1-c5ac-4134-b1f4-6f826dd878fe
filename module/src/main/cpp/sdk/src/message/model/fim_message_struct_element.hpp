#pragma once
#include "fim_message_struct_element.h"
#include "aim_pub_msg_struct_element.h"
#include "fim_message_struct_element_type.h"
#include "fim_message_struct_element_uid.hpp"
#include "fim_message_text_content.hpp"
#include "fim_message_struct_element_at.hpp"
#include <vector>

namespace alibaba {
namespace fim {

inline FIMMessageStructElement FIMMessageStructElementFrom(const dps::AIMPubMsgStructElement& aimElement) {
  return FIMMessageStructElement(
    static_cast<FIMMessageStructElementType>(aimElement.element_type),
    FIMMessageTextContentFrom(aimElement.text_content),
    FIMMessageStructElementUidFrom(aimElement.uid_element),
    FIMMessageStructElementAtFrom(aimElement.at_element)
  );
}

inline dps::AIMPubMsgStructElement AIMPubMsgStructElementFrom(const FIMMessageStructElement& fimElement) {
  return dps::AIMPubMsgStructElement(
    static_cast<dps::AIMMsgStructElementType>(fimElement.element_type),
    AIMPubMsgTextContentFrom(fimElement.text_content),
    AIMPubMsgStructElementUidFrom(fimElement.uid_element),
    AIMPubMsgStructElementAtFrom(fimElement.at_element)
  );
}

inline std::vector<FIMMessageStructElement> FIMMessageStructElementListFrom(const std::vector<dps::AIMPubMsgStructElement>& aimElements) {
    std::vector<FIMMessageStructElement> fimElements;
    fimElements.reserve(aimElements.size());
    for (const auto& aimElement : aimElements) {
        fimElements.push_back(FIMMessageStructElementFrom(aimElement));
    }
    return fimElements;
}

inline std::vector<dps::AIMPubMsgStructElement> AIMPubMsgStructElementListFrom(const std::vector<FIMMessageStructElement>& fimElements) {
    std::vector<dps::AIMPubMsgStructElement> aimElements;
    aimElements.reserve(fimElements.size());
    for (const auto& fimElement : fimElements) {
        aimElements.push_back(AIMPubMsgStructElementFrom(fimElement));
    }
    return aimElements;
}

} // namespace fim
} // namespace alibaba
