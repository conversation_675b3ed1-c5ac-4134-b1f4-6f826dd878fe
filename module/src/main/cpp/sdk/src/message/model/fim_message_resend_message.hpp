#pragma once
#include "fim_message_resend_message.h"
// Include the corresponding DPS header
#include "aim_pub_msg_re_send_message.h"

namespace alibaba {
namespace fim {

// Convert FIM's FIMMessageReSendMessage to DPS' AIM version
inline dps::AIMPubMsgReSendMessage ConvertToAIMPubMsgReSendMessage(const FIMMessageReSendMessage& fimReSendMessage) {
  return dps::AIMPubMsgReSendMessage(
      fimReSendMessage.appCid,
      fimReSendMessage.localid,
      fimReSendMessage.callback_ctx);
}

// Convert DPS' AIMPubMsgReSendMessage to FIM's version
inline FIMMessageReSendMessage ConvertToFIMReSendMessage(const dps::AIMPubMsgReSendMessage& aimReSendMessage) {
  return FIMMessageReSendMessage(
      aimReSendMessage.appCid,
      aimReSendMessage.localid,
      aimReSendMessage.callback_ctx);
}

} // namespace fim
} // namespace alibaba
