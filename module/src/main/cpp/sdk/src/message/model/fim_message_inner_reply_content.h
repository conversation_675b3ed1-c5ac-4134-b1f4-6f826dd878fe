#pragma once
#include "fim_message_content_type.h" // FIM version of AIMMsgContentType
#include "fim_message_simple_content.h" // FIM version of AIMPubMsgSimpleContent

namespace alibaba {
namespace fim {

struct FIMMessageInnerReplyContent final {
  FIMMessageContentType content_type = FIMMessageContentType::CONTENT_TYPE_UNKNOW;
  FIMMessageSimpleContent content;
  FIMMessageInnerReplyContent(FIMMessageContentType content_type_,
                             FIMMessageSimpleContent content_)
      : content_type(content_type_), content(std::move(content_)) {}
  FIMMessageInnerReplyContent() {}
};

} // namespace fim
} // namespace alibaba
