#pragma once
#include "aim_pub_msg_reply_content.h"
#include "fim_message_reference.hpp"
#include "fim_message_reply_content.h"
#include "fim_message_inner_reply_content.hpp"
namespace alibaba {
namespace fim {

inline FIMMessageReplyContent
FIMMessageFromReplyContent(const dps::AIMPubMsgReplyContent &aimReplyContent) {
  return FIMMessageReplyContent(
    FIMMessageReferenceFrom(aimReplyContent.reference_msg),
    FIMMessageInnerReplyContentFrom(aimReplyContent.reply_content)
  );
}

// 将 FIMMessageReplyContent 转换为 AIMPubMsgReplyContent
inline dps::AIMPubMsgReplyContent
AIMPubMsgReplyContentFrom(const FIMMessageReplyContent &fimReplyContent) {
  return dps::AIMPubMsgReplyContent(
    AIMPubMsgReferenceFrom(fimReplyContent.reference_msg),
    AIMPubMsgInnerReplyContentFrom(fimReplyContent.reply_content)
  );
}

} // namespace fim
} // namespace alibaba
