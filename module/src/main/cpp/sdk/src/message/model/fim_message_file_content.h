#pragma once
#include <string>

namespace alibaba {
namespace fim {

/**
 * 文件消息内容
 */
struct FIMMessageFileContent {
  /**
   * 本地路径
   */
  std::string local_path;
  /**
   * 文件名称
   */
  std::string file_name;
  /**
   * 文件类型
   */
  std::string file_type;
  /**
   * media id
   */
  std::string media_id;
  /**
   * 上传媒体类型（发送方）
   */
  std::string mime_type;
  /**
   * 文件url
   */
  std::string url;
  /**
   * 文件大小
   */
  int64_t file_size;

  FIMMessageFileContent() : file_size(0) {}

  FIMMessageFileContent(std::string local_path_, std::string file_name_,
                        std::string file_type_, std::string media_id_,
                        std::string mime_type_, std::string url_,
                        int64_t file_size_)
      : local_path(std::move(local_path_)), file_name(std::move(file_name_)),
        file_type(std::move(file_type_)), media_id(std::move(media_id_)),
        mime_type(std::move(mime_type_)), url(std::move(url_)),
        file_size(file_size_) {}
};

} // namespace fim
} // namespace alibaba
