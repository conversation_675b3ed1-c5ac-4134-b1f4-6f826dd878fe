#pragma once
#include "fim_message_struct_element_uid.h"
#include "aim_pub_msg_struct_element_uid.h"

namespace alibaba {
namespace fim {

inline FIMMessageStructElementUid FIMMessageStructElementUidFrom(const dps::AIMPubMsgStructElementUid& aimElementUid) {
  return FIMMessageStructElementUid(aimElementUid.uid, aimElementUid.default_nick, aimElementUid.prefix);
}

inline dps::AIMPubMsgStructElementUid AIMPubMsgStructElementUidFrom(const FIMMessageStructElementUid& fimElementUid) {
  return dps::AIMPubMsgStructElementUid(fimElementUid.uid, fimElementUid.default_nick, fimElementUid.prefix);
}

} // namespace fim
} // namespace alibaba
