#pragma once
#include "fim_message_image_content.h"
//AIMMsgImageContent
#include "aim_msg_image_content.h"

namespace alibaba {
namespace fim {
/**
 * 将 AIMPubMsgTextContent 转换为 FIMMessageTextContent
 */
inline FIMMessageImageContent FIMMessageFromImageContent(const dps::AIMMsgImageContent& aimTextContent) {
  FIMMessageImageContent fimImageContent;
  fimImageContent.local_path = aimTextContent.local_path;
  fimImageContent.upload_path = aimTextContent.upload_path;
  fimImageContent.mime_type = aimTextContent.mime_type;
  fimImageContent.original_url = aimTextContent.original_url;
  fimImageContent.thumbnail_url = aimTextContent.thumbnail_url;
  fimImageContent.blurred_data = aimTextContent.blurred_data;
  fimImageContent.media_id = aimTextContent.media_id;
  fimImageContent.file_name = aimTextContent.file_name;
  fimImageContent.height = aimTextContent.height;
  fimImageContent.width = aimTextContent.width;
  fimImageContent.size = aimTextContent.size;
  fimImageContent.type = aimTextContent.type;
  fimImageContent.file_type = aimTextContent.file_type;
  fimImageContent.orientation = aimTextContent.orientation;
  fimImageContent.extension = aimTextContent.extension;

  return fimImageContent;
}

/**
 * 将 FIMMessageTextContent 转换为 AIMPubMsgTextContent
 */
inline dps::AIMMsgImageContent AIMMsgImageContentFrom(const FIMMessageImageContent& fimTextContent) {
  dps::AIMMsgImageContent aimImageContent;
  aimImageContent.local_path = fimTextContent.local_path;
  aimImageContent.upload_path = fimTextContent.upload_path;
  aimImageContent.mime_type = fimTextContent.mime_type;
  aimImageContent.original_url = fimTextContent.original_url;
  aimImageContent.thumbnail_url = fimTextContent.thumbnail_url;
  aimImageContent.blurred_data = fimTextContent.blurred_data;
  aimImageContent.media_id = fimTextContent.media_id;
  aimImageContent.file_name = fimTextContent.file_name;
  aimImageContent.height = fimTextContent.height;
  aimImageContent.width = fimTextContent.width;
  aimImageContent.size = fimTextContent.size;
  aimImageContent.type = fimTextContent.type;
  aimImageContent.file_type = fimTextContent.file_type;
  aimImageContent.orientation = fimTextContent.orientation;
  aimImageContent.extension = fimTextContent.extension;
  return aimImageContent;
}
} // namespace fim
} // namespace alibaba
