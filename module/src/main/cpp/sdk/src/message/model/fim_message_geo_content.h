#pragma once
#include <string>
#include <map>

namespace alibaba {
namespace dps {
    // 枚举的前向声明
    enum class AIMMsgImageFileType;
}

namespace fim {
using namespace dps; // 使用 dps 命名空间中定义的枚举类型

/**
 * 位置消息内容
 */
struct FIMMessageGeoContent {
  /**
   * 位置图片本地路径（发送方）
   */
  std::string pic_local_path;
  /**
   * 上传本地路径（发送方）
   */
  std::string pic_upload_path;
  /**
   * 上传媒体类型（发送方）
   */
  std::string mime_type;
  /**
   * 位置图片
   */
  std::string pic_url;
  /**
   * 位置图片 media id
   */
  std::string pic_media_id;
  /**
   * 位置图片类型 AIMMsgImageFileType
   */
  AIMMsgImageFileType pic_file_type;
  /**
   * 宽度
   */
  int32_t pic_width;
  /**
   * 高度
   */
  int32_t pic_height;
  /**
   * 维度
   */
  double latitude;
  /**
   * 经度
   */
  double longitude;
  /**
   * 地理位置名称
   */
  std::string location_name;

  FIMMessageGeoContent() : pic_width(0), pic_height(0), latitude(0), longitude(0), 
                           pic_file_type(AIMMsgImageFileType::IMAGE_FILE_TYPE_UNKNOWN) {}

  FIMMessageGeoContent(std::string pic_local_path_, std::string pic_upload_path_,
                       std::string mime_type_, std::string pic_url_,
                       std::string pic_media_id_,
                       AIMMsgImageFileType pic_file_type_, int32_t pic_width_,
                       int32_t pic_height_, double latitude_, double longitude_,
                       std::string location_name_)
      : pic_local_path(std::move(pic_local_path_)),
        pic_upload_path(std::move(pic_upload_path_)),
        mime_type(std::move(mime_type_)), pic_url(std::move(pic_url_)),
        pic_media_id(std::move(pic_media_id_)),
        pic_file_type(pic_file_type_), pic_width(pic_width_),
        pic_height(pic_height_), latitude(latitude_), longitude(longitude_),
        location_name(std::move(location_name_)) {}
};
} // namespace fim
} // namespace alibaba
