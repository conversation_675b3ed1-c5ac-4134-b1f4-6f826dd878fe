#pragma once

#include <string>
#include <map>
#include "fim_message_inner_combine_content.h"

namespace alibaba {
namespace fim {

struct FIMMessageCombineForward {
  std::string sender;
  std::string appCid;
  std::string mid;
  int64_t created_at;
  FIMMessageInnerCombineContent combine_content;
  std::map<std::string, std::string> extension;

  FIMMessageCombineForward() : created_at(0) {}

  FIMMessageCombineForward(std::string sender_, std::string appCid_,
                           std::string mid_, int64_t created_at_,
                           FIMMessageInnerCombineContent combine_content_,
                           std::map<std::string, std::string> extension_)
      : sender(std::move(sender_)), appCid(std::move(appCid_)),
        mid(std::move(mid_)), created_at(created_at_),
        combine_content(std::move(combine_content_)),
        extension(std::move(extension_)) {}
};

} // namespace fim
} // namespace alibaba
