#pragma once
#include <vector>
#include "fim_message_struct_element.h"

namespace alibaba {
namespace fim {

/**
 * 结构化消息内容
 */
struct FIMMessageStructContent {
  std::vector<FIMMessageStructElement> elements;

  FIMMessageStructContent() {}

  FIMMessageStructContent(std::vector<FIMMessageStructElement> elements_)
      : elements(std::move(elements_)) {}
};

} // namespace fim
} // namespace alibaba
