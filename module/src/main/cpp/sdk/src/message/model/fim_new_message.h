// aim_pub_new_message.h

#pragma once

//AIMMsgSourceType
#include "aim_msg_source_type.h"
#include "fim_message.h"

namespace alibaba {
namespace fim {


/**
 * 添加的消息
 */
struct FIMNewMessage final {
    FIMMessage msg;
    AIMMsgSourceType type = dps::AIMMsgSourceType::SOURCE_TYPE_UNKNOWN;

    FIMNewMessage() {}

    FIMNewMessage(FIMMessage msg_, dps::AIMMsgSourceType type_)
        : msg(std::move(msg_)), type(std::move(type_)) {}
};

} // namespace dps
} // namespace alibaba
