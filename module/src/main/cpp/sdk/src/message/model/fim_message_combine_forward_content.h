#pragma once

#include <vector>
// Include the header file for FIMMessageCombineForward
#include "fim_message_combine_forward.h"

namespace alibaba {
namespace fim {

struct FIMMessageCombineForwardContent {
  std::vector<FIMMessageCombineForward> combine_forward;

  FIMMessageCombineForwardContent() {}

  FIMMessageCombineForwardContent(std::vector<FIMMessageCombineForward> combine_forward_)
      : combine_forward(std::move(combine_forward_)) {}
};

} // namespace fim
} // namespace alibaba
