//
//  fim_message.h
//  FliggyIMSDK
//
//  Created by 蒙晨 on 2024/2/18.
//

#ifndef fim_message_h
#define fim_message_h
#include <string>
//AIMMsgSendStatus
#include "aim_msg_send_status.h"
//AIMMsgBizInfo
#include "aim_msg_biz_info.h"
//AIMMsgDisplayStyle
#include "aim_msg_display_style.h"
//AIMPubMsgRecallFeature
#include "aim_pub_msg_recall_feature.h"
#include "fliggy_im_sdk.h"
#include "fim_message_content.h"

namespace alibaba {
namespace fim {
using namespace alibaba::dps;
struct FIMSDK_API FIMMessage final {
    /*
    * 是否是自己发的消息
    */
    bool is_self_message() const;
    /*
    * 会话ID
    */
    std::string appCid;
    /*
    * 消息ID
    */
    std::string mid;
    /*
    * 本地消息ID
    */
    std::string localid;
    /*
    * 发送者 fuid
    */
    std::string sender;
    int64_t sender_tag = 0;
    /*
    * 消息创建时间
    */
    int64_t created_at = -1;
    /*
    * 未读消息数
    */
    int32_t unread_count = -1;
    /*
    * 接收者数量
    */
    int32_t receiver_count = -1;
    /*
    * 接收者列表
    */
    std::vector<std::string> receivers;
    /*
    * 是否已读
    */
    bool is_read = false;
    /*
    * 扩展字段
    */
    std::map<std::string, std::string> extension;
    /*
    * 本地扩展字段
    */
    std::map<std::string, std::string> local_extension;
    /*
    * 用户扩展字段
    */
    std::map<std::string, std::string> user_extension;
    /*
    * 消息内容
    */
    FIMMessageContent content;
    /*
    * 消息发送状态
    */
    AIMMsgSendStatus status = AIMMsgSendStatus::SEND_STATUS_UNKNOWN;
    /*
    * 是否删除
    */
    bool is_delete = false;
    /*
    * 是否撤回
    */
    bool is_recall = false;
    /*
    * 是否禁止读
    */
    bool is_disable_read = false;
    /*
    * 是否本地消息
    */
    bool is_local = false;
    /*
    * 业务信息
    */
    AIMMsgBizInfo biz_info;
    /*
    * 消息展示样式
    */
    AIMMsgDisplayStyle display_style = AIMMsgDisplayStyle::DISPLAY_STYLE_USER;
    /*
    * 撤回消息相关字段
    */
    AIMPubMsgRecallFeature recall_feature;

    FIMMessage(std::string appCid_, std::string mid_, std::string localid_,
                std::string sender_, int64_t sender_tag_, int64_t created_at_,
                int32_t unread_count_, int32_t receiver_count_,
                std::vector<std::string> receivers_, bool is_read_,
                std::map<std::string, std::string> extension_,
                std::map<std::string, std::string> local_extension_,
                std::map<std::string, std::string> user_extension_,
                FIMMessageContent content_, AIMMsgSendStatus status_,
                bool is_delete_, bool is_recall_, bool is_disable_read_,
                bool is_local_, AIMMsgBizInfo biz_info_,
                AIMMsgDisplayStyle display_style_,
                AIMPubMsgRecallFeature recall_feature_)
      : appCid(std::move(appCid_)), mid(std::move(mid_)),
        localid(std::move(localid_)), sender(std::move(sender_)),
        sender_tag(std::move(sender_tag_)), created_at(std::move(created_at_)),
        unread_count(std::move(unread_count_)),
        receiver_count(std::move(receiver_count_)),
        receivers(std::move(receivers_)), is_read(std::move(is_read_)),
        extension(std::move(extension_)),
        local_extension(std::move(local_extension_)),
        user_extension(std::move(user_extension_)),
        content(std::move(content_)), status(std::move(status_)),
        is_delete(std::move(is_delete_)), is_recall(std::move(is_recall_)),
        is_disable_read(std::move(is_disable_read_)),
        is_local(std::move(is_local_)), biz_info(std::move(biz_info_)),
        display_style(std::move(display_style_)),
        recall_feature(std::move(recall_feature_)) {}

    FIMMessage() {}
};

}
}
#endif /* fim_message_h */
