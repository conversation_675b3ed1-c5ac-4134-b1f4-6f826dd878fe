#pragma once

// Include headers for the various content types
#include "fim_message_text_content.h" // For FIMMessageTextContent
#include "fim_message_image_content.h" // For FIMMessageImageContent
#include "fim_message_audio_content.h" // For FIMMessageAudioContent
#include "fim_message_video_content.h" // For FIMMessageVideoContent
#include "fim_message_geo_content.h" // For FIMMessageGeoContent
#include "fim_message_custom_content.h" // For FIMMessageCustomContent
#include "fim_message_struct_content.h" // For FIMMessageStructContent
#include "fim_message_file_content.h" // For FIMMessageFileContent

namespace alibaba {
namespace fim {

struct FIMMessageSimpleContent {
  FIMMessageTextContent text_content;
  FIMMessageImageContent image_content;
  FIMMessageAudioContent audio_content;
  FIMMessageVideoContent video_content;
  FIMMessageGeoContent geo_content;
  FIMMessageCustomContent custom_content;
  FIMMessageStructContent struct_content;
  FIMMessageFileContent file_content;

  FIMMessageSimpleContent() {}

  FIMMessageSimpleContent(
      FIMMessageTextContent text_content_, FIMMessageImageContent image_content_,
      FIMMessageAudioContent audio_content_, FIMMessageVideoContent video_content_,
      FIMMessageGeoContent geo_content_, FIMMessageCustomContent custom_content_,
      FIMMessageStructContent struct_content_, FIMMessageFileContent file_content_)
      : text_content(std::move(text_content_)),
        image_content(std::move(image_content_)),
        audio_content(std::move(audio_content_)),
        video_content(std::move(video_content_)),
        geo_content(std::move(geo_content_)),
        custom_content(std::move(custom_content_)),
        struct_content(std::move(struct_content_)),
        file_content(std::move(file_content_)) {}
};

} // namespace fim
} // namespace alibaba
