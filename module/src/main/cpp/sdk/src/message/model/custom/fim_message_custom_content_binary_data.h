#pragma once
#include "fim_message_card_type.h"
#include "fliggy_im_sdk.h"
//#include "json.hpp"
#include <string>
#include <vector>

namespace alibaba {
namespace fim {

//using json = nlohmann::json;
//
//struct FIMSDK_API FIMMessageCustomContentBinaryData {
//  /*
//   * 卡片模版类型，用来控制展示卡片的样式
//   */
//  FIMTemplateType templateCode = FIMTemplateType::Unknown;
//  /*
//   * 预留：卡片所属业务，只有服务端关心，请求数据时需要透传此数据
//   */
//  int32_t businessType;
//  /*
//   * 卡片数据类型，是否是动态卡片，3.31全静态
//   */
//  FIMMessageDataType dataType = FIMMessageDataType::Static;
//  /*
//   * 卡片状态 0.失效 1.正常
//   */
//  int32_t status = 1;
//
//  /// 卡片数据结构 ： json string
//  std::string schema;
//
//      // 序列化为二进制数据
//    std::vector<uint8_t> toBinaryData() const {
//        json j;
//        j["templateCode"] = static_cast<int32_t>(templateCode);
//        j["businessType"] = businessType;
//        j["dataType"] = static_cast<int32_t>(dataType);
//        j["status"] = status;
//        j["schema"] = schema;
//
//        std::string jsonString = j.dump();
//        return std::vector<uint8_t>(jsonString.begin(), jsonString.end());
//    }
//
//    // 从二进制数据反序列化
//    static FIMMessageCustomContentBinaryData fromBinaryData(const std::vector<uint8_t>& binaryData) {
//        if (binaryData.empty()) {
//            return FIMMessageCustomContentBinaryData();
//        }
//        std::string jsonString(binaryData.begin(), binaryData.end());
//        json j = json::parse(jsonString);
//
//        FIMMessageCustomContentBinaryData data;
//        data.templateCode = static_cast<FIMTemplateType>(j.value("templateCode", static_cast<int32_t>(FIMTemplateType::Unknown)));
//        data.businessType = j.value("businessType", 0);
//        data.dataType = static_cast<FIMMessageDataType>(j.value("dataType", static_cast<int32_t>(FIMMessageDataType::Static)));
//        data.status = j.value("status", 0);
//        data.schema = j.value("schema", std::string{});
//        return data;
//    }
//};

} // namespace fim
} // namespace alibaba
