#pragma once
#include <string>
#include <vector>

namespace alibaba {
namespace dps {
    enum class AIMMsgAudioType;
}
}

namespace alibaba {
namespace fim {
    using namespace dps;

/**
 * 音频消息内容
 */
struct FIMMessageAudioContent {
  /**
   * 语音本地路径（发送方），本地原始语音数据路径
   */
  std::string local_path;
  /**
   * 上传本地路径（发送方）
   * 需要上传到服务端的语音文件路径，如加密，压缩的数据
   */
  std::string upload_path;
  /**
   * 上传媒体类型（发送方）
   */
  std::string mime_type;
  /**
   * 语音文件url，服务端上传成功后返回
   */
  std::string url;
  /**
   * media id，SDK 内部将语音文件上传成功后得到的 media_id
   * 发送文件时，如果 media_id 不为空，则使用该media_id发送
   */
  std::string media_id;
  /**
   * 语音数据,只是下行使用
   */
  std::vector<uint8_t> binary_data;
  /**
   * 语音数据类型  AIMMsgAudioType
   */
  AIMMsgAudioType audio_type;  // 根据原始定义，需要定义对应的枚举或直接使用 int
  /**
   * 语音时长
   */
  int64_t duration;
  
  FIMMessageAudioContent() {}
  
  FIMMessageAudioContent(std::string local_path_, std::string upload_path_,
                     std::string mime_type_, std::string url_,
                     std::string media_id_, std::vector<uint8_t> binary_data_,
                     AIMMsgAudioType audio_type_, int64_t duration_)
      : local_path(std::move(local_path_)),
        upload_path(std::move(upload_path_)), mime_type(std::move(mime_type_)),
        url(std::move(url_)), media_id(std::move(media_id_)),
        binary_data(std::move(binary_data_)),
        audio_type(audio_type_), duration(duration_) {}
};

} // namespace fim
} // namespace alibaba
