#pragma once
#include "fim_message_struct_content.h"
#include "aim_pub_msg_struct_content.h"
#include "fim_message_struct_element.hpp"

namespace alibaba {
namespace fim {

// 将 AIMPubMsgStructContent 转换为 FIMMessageStructContent
inline FIMMessageStructContent FIMMessageFromStructContent(const dps::AIMPubMsgStructContent& aimStructContent) {
  std::vector<FIMMessageStructElement> elements = FIMMessageStructElementListFrom(aimStructContent.elements);
  return FIMMessageStructContent(elements);
}

// 将 FIMMessageStructContent 转换为 AIMPubMsgStructContent
inline dps::AIMPubMsgStructContent AIMPubMsgStructContentFrom(const FIMMessageStructContent& fimStructContent) {
  std::vector<dps::AIMPubMsgStructElement> elements = AIMPubMsgStructElementListFrom(fimStructContent.elements);
  return dps::AIMPubMsgStructContent(elements);
}

} // namespace fim
} // namespace alibaba
