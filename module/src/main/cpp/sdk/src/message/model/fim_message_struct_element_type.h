#pragma once
#include <functional>

namespace alibaba {
namespace fim {

enum class FIMMessageStructElementType : int {
  /**
   * 未知节点
   */
  ELEMENT_TYPE_UNKNOWN = 0,
  /**
   * 文本节点
   */
  ELEMENT_TYPE_TEXT = 1,
  /**
   * uid 节点
   */
  ELEMENT_TYPE_UID = 2,
  /**
   * at 节点
   */
  ELEMENT_TYPE_AT = 3,
};

}
} // namespace alibaba

namespace std {
template <> struct hash<::alibaba::fim::FIMMessageStructElementType> {
    size_t operator()(::alibaba::fim::FIMMessageStructElementType type) const {
        return std::hash<int>()(static_cast<int>(type));
    }
};
} // namespace std
