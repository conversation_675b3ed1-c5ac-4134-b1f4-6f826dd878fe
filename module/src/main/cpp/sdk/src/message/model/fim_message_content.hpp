#pragma once
#include "fim_message_combine_forward_content.hpp"
#include "fim_message_content.h"
#include "fim_message_simple_content.hpp"
#include "fim_message_text_content.hpp"
// AIMPubMsgContent
#include "aim_pub_msg_content.h"
#include "fim_message_reply_content.hpp"
// FIMMessageContentType
#include "fim_message_content_type.h"

namespace alibaba {
namespace fim {
inline FIMMessageContent
FIMMessageContentFrom(const dps::AIMPubMsgContent &aimContent) {

  FIMMessageContentType contentType =
      static_cast<FIMMessageContentType>(aimContent.content_type);
  return FIMMessageContent(
      contentType, FIMMessageTextContentFrom(aimContent.text_content),
      FIMMessageFromImageContent(aimContent.image_content),
      FIMMessageFromAudioContent(aimContent.audio_content),
      FIMMessageFromVideoContent(aimContent.video_content),
      FIMMessageFromGeoContent(aimContent.geo_content),
      FIMMessageFromCustomContentFrom(aimContent.custom_content),
      FIMMessageFromStructContent(aimContent.struct_content),
      FIMMessageFromFileContent(aimContent.file_content),
      FIMMessageFromReplyContent(aimContent.reply_content),
      FIMMessageFromCombineForwardContent(aimContent.combine_forward_content));
}

// Function to convert FIMMessageContent to AIMPubMsgContent
inline dps::AIMPubMsgContent
AIMPubMsgContentFrom(const FIMMessageContent &fimContent) {
  AIMMsgContentType contentType =
      static_cast<AIMMsgContentType>(fimContent.content_type);

  return AIMPubMsgContent(
      contentType, AIMPubMsgTextContentFrom(fimContent.text_content),
      AIMMsgImageContentFrom(fimContent.image_content),
      AIMPubMsgAudioContentFrom(fimContent.audio_content),
      AIMMsgVideoContentFrom(fimContent.video_content),
      AIMMsgGeoContentFrom(fimContent.geo_content),
      AIMMsgCustomContentFrom(fimContent.custom_content),
      AIMPubMsgStructContentFrom(fimContent.struct_content),
      AIMMsgFileContentFrom(fimContent.file_content),
      AIMPubMsgReplyContentFrom(fimContent.reply_content),
      AIMPubMsgCombineForwardContentFrom(
          fimContent.combine_forward_content));
}

} // namespace fim
} // namespace alibaba
