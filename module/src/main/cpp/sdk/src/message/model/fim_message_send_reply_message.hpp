#pragma once

#include "fim_message_content.h"
#include "fim_message_content.hpp"
#include "fim_message_reply_content.h"
#include "fim_message_send_reply_message.h"
#include "aim_pub_msg_send_reply_message.h"
#include "fim_message_send_reply_message.hpp"


namespace alibaba {
namespace fim {

// Convert DPS' AIMPubMsgSendReplyMessage to FIM's version
inline FIMMessageSendReplyMessage ConvertToFIMPubMsgSendReplyMessage(const dps::AIMPubMsgSendReplyMessage& aimMessage) {
    FIMMessageContent reply_content = FIMMessageContentFrom(aimMessage.reply_content);
  return FIMMessageSendReplyMessage(
      aimMessage.appCid,
      aimMessage.reference_mid,
      reply_content,
      aimMessage.receivers,
      aimMessage.extension,
      aimMessage.local_extension,
      aimMessage.callback_ctx);
}

// Convert FIM's FIMPubMsgSendReplyMessage to DPS' version
inline dps::AIMPubMsgSendReplyMessage ConvertToAIMPubMsgSendReplyMessage(const FIMMessageSendReplyMessage& fimMessage) {
  return dps::AIMPubMsgSendReplyMessage(
      fimMessage.appCid,
      fimMessage.reference_mid,
      AIMPubMsgContentFrom(fimMessage.reply_content), // Assuming this conversion function is already defined
      fimMessage.receivers,
      fimMessage.extension,
      fimMessage.local_extension,
      fimMessage.callback_ctx);
}

} // namespace fim
} // namespace alibaba
