#pragma once
#include <string>
#include <vector>
#include <map>

namespace alibaba {
namespace dps {
    // 枚举的前向声明
    enum class AIMMsgImageFileType;
}

namespace fim {
using namespace dps; // 使用 dps 命名空间中定义的枚举类型

/**
 * 视频消息内容
 */
struct FIMMessageVideoContent {
  /**
   * 视频本地路径（发送方）
   */
  std::string local_path;
  /**
   * 上传本地路径（发送方）
   */
  std::string upload_path;
  /**
   * 上传媒体类型（发送方）
   */
  std::string mime_type;
  /**
   * 视频文件url
   */
  std::string url;
  /**
   * media id
   */
  std::string media_id;
  /**
   * 视频封面本地路径（发送方）
   */
  std::string cover_local_path;
  /**
   * 视频封面上传本地路径（发送方）
   */
  std::string cover_upload_path;
  /**
   * 视频封面
   */
  std::string cover_url;
  /**
   * 视频封面 media id
   */
  std::string cover_media_id;
  /**
   * 视频封面类型（发送方）ImageFileType
   */
  AIMMsgImageFileType cover_file_type;
  /**
   * 视频封面 MIME 类型（发送方）
   */
  std::string cover_mime_type;
  /**
   * 视频名字
   */
  std::string file_name;
  /**
   * 文件类型，取文件后缀
   */
  std::string file_type;
  /**
   * 文件大小(B)
   */
  int64_t file_size;
  /**
   * 视频时长(ms)
   */
  int64_t duration;
  /**
   * 视频高度
   */
  int32_t height;
  /**
   * 视频宽度
   */
  int32_t width;
  /**
   * 视频封面宽度
   */
  int32_t cover_width;
  /**
   * 视频封面高度
   */
  int32_t cover_height;

  FIMMessageVideoContent() : file_size(0), duration(0), height(0), width(0), cover_width(0), cover_height(0), 
                             cover_file_type(AIMMsgImageFileType::IMAGE_FILE_TYPE_UNKNOWN) {}

  FIMMessageVideoContent(std::string local_path_, std::string upload_path_,
                         std::string mime_type_, std::string url_,
                         std::string media_id_, std::string cover_local_path_,
                         std::string cover_upload_path_, std::string cover_url_,
                         std::string cover_media_id_,
                         AIMMsgImageFileType cover_file_type_,
                         std::string cover_mime_type_, std::string file_name_,
                         std::string file_type_, int64_t file_size_,
                         int64_t duration_, int32_t height_, int32_t width_,
                         int32_t cover_width_, int32_t cover_height_)
      : local_path(std::move(local_path_)),
        upload_path(std::move(upload_path_)), mime_type(std::move(mime_type_)),
        url(std::move(url_)), media_id(std::move(media_id_)),
        cover_local_path(std::move(cover_local_path_)),
        cover_upload_path(std::move(cover_upload_path_)),
        cover_url(std::move(cover_url_)),
        cover_media_id(std::move(cover_media_id_)),
        cover_file_type(cover_file_type_),
        cover_mime_type(std::move(cover_mime_type_)),
        file_name(std::move(file_name_)), file_type(std::move(file_type_)),
        file_size(file_size_), duration(duration_), 
        height(height_), width(width_),
        cover_width(cover_width_), cover_height(cover_height_) {}
};
}
}