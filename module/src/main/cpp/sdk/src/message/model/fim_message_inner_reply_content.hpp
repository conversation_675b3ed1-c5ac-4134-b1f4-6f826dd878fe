#pragma once
#include "fim_message_content_type.h"
#include "fim_message_inner_reply_content.h"
#include "aim_pub_msg_inner_reply_content.h" // Assuming this is the original header file
#include "fim_message_simple_content.hpp"

namespace alibaba {
namespace fim {

// Assuming conversion functions for FIMMsgContentType and FIMPubMsgSimpleContent exist
inline FIMMessageInnerReplyContent FIMMessageInnerReplyContentFrom(const dps::AIMPubMsgInnerReplyContent& aimContent) {
  return FIMMessageInnerReplyContent(
    static_cast<FIMMessageContentType>(aimContent.content_type),
    FIMMessageSimpleContentFrom(aimContent.content)
  );
}

inline dps::AIMPubMsgInnerReplyContent AIMPubMsgInnerReplyContentFrom(const FIMMessageInnerReplyContent& fimContent) {
  return dps::AIMPubMsgInnerReplyContent(
    static_cast<dps::AIMMsgContentType>(fimContent.content_type),
    AIMPubMsgSimpleContentFrom(fimContent.content)
  );
}

} // namespace fim
} // namespace alibaba
