#pragma once

#include "fim_message_send_forward_message.h"
// Include the corresponding DPS header if it's in a separate file
#include "aim_pub_msg_send_forward_message.h"

namespace alibaba {
namespace fim {

/**
 * Convert FIM's FIMMessageSendForwardMessage to DPS' AIMPubMsgSendForwardMessage
 */
inline dps::AIMPubMsgSendForwardMessage AIMPubMsgSendForwardMessageFrom(const FIMMessageSendForwardMessage& fimMessage) {
  return dps::AIMPubMsgSendForwardMessage(
      fimMessage.appCid,
      fimMessage.mids,
      fimMessage.to_appCid,
      fimMessage.receivers,
      fimMessage.extension,
      fimMessage.callback_ctx);
}

inline dps::AIMPubMsgSendForwardMessage FIMMessageFromSendForwardMessage(const FIMMessageSendForwardMessage& fimMessage) {
  return dps::AIMPubMsgSendForwardMessage(
      fimMessage.appCid,
      fimMessage.mids,
      fimMessage.to_appCid,
      fimMessage.receivers,
      fimMessage.extension,
      fimMessage.callback_ctx);
}

/**
 * Convert DPS' AIMPubMsgSendForwardMessage to FIM's FIMMessageSendForwardMessage
 */
inline FIMMessageSendForwardMessage FIMMessageSendForwardMessageFrom(const dps::AIMPubMsgSendForwardMessage& aimMessage) {
  return FIMMessageSendForwardMessage(
      aimMessage.appCid,
      aimMessage.mids,
      aimMessage.to_appCid,
      aimMessage.receivers,
      aimMessage.extension,
      aimMessage.callback_ctx);
}

inline FIMMessageSendForwardMessage ConvertToAIMPubMsgSendForwardMessage(const dps::AIMPubMsgSendForwardMessage& aimMessage) {
  return FIMMessageSendForwardMessage(
      aimMessage.appCid,
      aimMessage.mids,
      aimMessage.to_appCid,
      aimMessage.receivers,
      aimMessage.extension,
      aimMessage.callback_ctx);
}

} // namespace fim
} // namespace alibaba
