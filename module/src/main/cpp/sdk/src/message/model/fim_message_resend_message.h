#pragma once

#include <string>
#include <map>

namespace alibaba {
namespace fim {

struct FIMMessageReSendMessage {
  std::string appCid;
  std::string localid;
  std::map<std::string, std::string> callback_ctx;

  FIMMessageReSendMessage() {}

  FIMMessageReSendMessage(std::string appCid_, std::string localid_,
                          std::map<std::string, std::string> callback_ctx_)
      : appCid(std::move(appCid_)), localid(std::move(localid_)),
        callback_ctx(std::move(callback_ctx_)) {}
};

} // namespace fim
} // namespace alibaba
