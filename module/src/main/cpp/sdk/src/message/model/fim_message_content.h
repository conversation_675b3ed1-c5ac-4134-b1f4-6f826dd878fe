#pragma once

#include "fim_message_audio_content.h"           // For FIMMessageAudioContent
#include "fim_message_combine_forward_content.h" // For FIMMessageCombineForwardContent
#include "fim_message_custom_content.h"          // For FIMMessageCustomContent
#include "fim_message_file_content.h"            // For FIMMessageFileContent
#include "fim_message_geo_content.h"             // For FIMMessageGeoContent
#include "fim_message_goods_content.h"           // For FIMMessageGoodsContent
#include "fim_message_image_content.h"           // For FIMMessageImageContent
#include "fim_message_order_content.h"           // For FIMMessageOrderContent
#include "fim_message_reply_content.h"           // For FIMMessageReplyContent
#include "fim_message_struct_content.h"          // For FIMMessageStructContent
#include "fim_message_text_content.h"            // For FIMMessageTextContent
#include "fim_message_video_content.h"           // For FIMMessageVideoContent
// FIMMessageContentType
#include "fim_message_content_type.h"

namespace alibaba {
namespace fim {

struct FIMMessageContent {
  /*
  * 消息内容类型
  */
  FIMMessageContentType content_type;
  /*
  * 文本消息内容
  */
  FIMMessageTextContent text_content;
  /*
  * 图片消息内容
  */
  FIMMessageImageContent image_content;
  /*
  * 语音消息内容
  */
  FIMMessageAudioContent audio_content;
  /*
  * 视频消息内容
  */
  FIMMessageVideoContent video_content;
  /*
  * 地理位置消息内容
  */
  FIMMessageGeoContent geo_content;
  /*
  * 自定义消息内容
  */
  FIMMessageCustomContent custom_content;
  /*
  * 结构化消息内容
  */
  FIMMessageStructContent struct_content;
  /*
  * 文件消息内容
  */
  FIMMessageFileContent file_content;
  /*
  * 回复消息内容
  */
  FIMMessageReplyContent reply_content;
  /*
  * 合并转发消息内容
  */
  FIMMessageCombineForwardContent combine_forward_content;
  /*
  * 商品消息内容
  */
  FIMMessageGoodsContent goods_content;
  /*
  * 订单消息内容
  */
  FIMMessageOrderContent order_content;

  FIMMessageContent()
      : content_type(FIMMessageContentType::CONTENT_TYPE_UNKNOW) {}

  // Constructor with parameters for each content type
  FIMMessageContent(FIMMessageContentType content_type_,
                    FIMMessageTextContent text_content_,
                    FIMMessageImageContent image_content_,
                    FIMMessageAudioContent audio_content_,
                    FIMMessageVideoContent video_content_,
                    FIMMessageGeoContent geo_content_,
                    FIMMessageCustomContent custom_content_,
                    FIMMessageStructContent struct_content_,
                    FIMMessageFileContent file_content_,
                    FIMMessageReplyContent reply_content_,
                    FIMMessageCombineForwardContent combine_forward_content_)
      : content_type(content_type_), text_content(std::move(text_content_)),
        image_content(std::move(image_content_)),
        audio_content(std::move(audio_content_)),
        video_content(std::move(video_content_)),
        geo_content(std::move(geo_content_)),
        custom_content(std::move(custom_content_)),
        struct_content(std::move(struct_content_)),
        file_content(std::move(file_content_)),
        reply_content(std::move(reply_content_)),
        combine_forward_content(std::move(combine_forward_content_)) {}

  FIMMessageContent(FIMMessageContentType content_type_,
                    FIMMessageTextContent text_content_,
                    FIMMessageImageContent image_content_,
                    FIMMessageAudioContent audio_content_,
                    FIMMessageVideoContent video_content_,
                    FIMMessageGeoContent geo_content_,
                    FIMMessageCustomContent custom_content_,
                    FIMMessageStructContent struct_content_,
                    FIMMessageFileContent file_content_,
                    FIMMessageReplyContent reply_content_,
                    FIMMessageCombineForwardContent combine_forward_content_,
                    FIMMessageGoodsContent goods_content_,
                    FIMMessageOrderContent order_content_) {}
};

} // namespace fim
} // namespace alibaba
