#pragma once
#include "fim_message_simple_content.h"

#include "aim_pub_msg_simple_content.h"
//AIMMsgImageContent
#include "aim_msg_image_content.h"

#include "fim_message_audio_content.hpp"
#include "fim_message_image_content.hpp"
#include "fim_message_text_content.hpp"
#include "fim_message_video_content.hpp"
#include "fim_message_geo_content.hpp"
#include "fim_message_custom_content.hpp"
#include "fim_message_struct_content.hpp"
#include "fim_message_file_content.hpp"

namespace alibaba {
namespace fim {

// Convert DPS' AIMPubMsgSimpleContent to FIM's version
inline FIMMessageSimpleContent FIMMessageSimpleContentFrom(const dps::AIMPubMsgSimpleContent& aimSimpleContent) {
  FIMMessageSimpleContent fimSimpleContent;
  fimSimpleContent.text_content = FIMMessageTextContentFrom(aimSimpleContent.text_content); // Assuming this function exists
  fimSimpleContent.image_content = FIMMessageFromImageContent(aimSimpleContent.image_content); // Assuming this function exists
  fimSimpleContent.audio_content = FIMMessageFromAudioContent(aimSimpleContent.audio_content); // Assuming this function exists
  fimSimpleContent.video_content = FIMMessageFromVideoContent(aimSimpleContent.video_content); // Assuming this function exists
  fimSimpleContent.geo_content = FIMMessageFromGeoContent(aimSimpleContent.geo_content); // Assuming this function exists
  fimSimpleContent.custom_content = FIMMessageFromCustomContentFrom(aimSimpleContent.custom_content); // Assuming this function exists
  fimSimpleContent.struct_content = FIMMessageFromStructContent(aimSimpleContent.struct_content); // Assuming this function exists
  fimSimpleContent.file_content = FIMMessageFromFileContent(aimSimpleContent.file_content); // Assuming this function exists
  return fimSimpleContent;
}

// Convert FIM's FIMMessageSimpleContent to DPS' version
inline dps::AIMPubMsgSimpleContent AIMPubMsgSimpleContentFrom(const FIMMessageSimpleContent& fimSimpleContent) {
  dps::AIMPubMsgSimpleContent aimSimpleContent;
  aimSimpleContent.text_content = AIMPubMsgTextContentFrom(fimSimpleContent.text_content); // Assuming this function exists
  aimSimpleContent.image_content = AIMMsgImageContentFrom(fimSimpleContent.image_content); // Assuming this function exists
  aimSimpleContent.audio_content = AIMPubMsgAudioContentFrom(fimSimpleContent.audio_content); // Assuming this function exists
  aimSimpleContent.video_content = AIMMsgVideoContentFrom(fimSimpleContent.video_content); // Assuming this function exists
  aimSimpleContent.geo_content = AIMMsgGeoContentFrom(fimSimpleContent.geo_content); // Assuming this function exists
  aimSimpleContent.custom_content = AIMMsgCustomContentFrom(fimSimpleContent.custom_content); // Assuming this function exists
  aimSimpleContent.struct_content = AIMPubMsgStructContentFrom(fimSimpleContent.struct_content); // Assuming this function exists
  aimSimpleContent.file_content = AIMMsgFileContentFrom(fimSimpleContent.file_content); // Assuming this function exists
  return aimSimpleContent;
}

} // namespace fim
} // namespace alibaba
