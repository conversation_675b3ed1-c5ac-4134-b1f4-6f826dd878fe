#pragma once
#include "fim_message_custom_content_binary_data.h"
#include "fim_message_card_type.h"
#include "json.hpp"
#include <algorithm>
#include <string>
#include <vector>

namespace alibaba {
namespace fim {

/**
 * 自定义消息内容
 */
struct FIMMessageCustomContent {
  int32_t type = 101;
  std::string title;
  std::string summary;
  std::string degrade;
  // json string
  std::string data;


  FIMMessageCustomContent() {}
  FIMMessageCustomContent(int32_t type_, std::string data_,
                          std::string title_, std::string summary_,
                          std::string degrade_)
      : type(type_), data(std::move(data_)),
        title(std::move(title_)), summary(std::move(summary_)),
        degrade(std::move(degrade_)) {}

  // FIMMessageCustomContent(int32_t type_, std::string title_,
  //                         std::string summary_, std::string degrade_,
  //                         FIMMessageCustomContentBinaryData binaryDataStruct_)
  //     : type(type_), title(std::move(title_)), summary(std::move(summary_)),
  //       degrade(std::move(degrade_)),
  //       binaryDataStruct(std::move(binaryDataStruct_)) {}

  // FIMMessageCustomContent(int32_t type_, std::vector<uint8_t> binary_data_,
  //                         std::string title_, std::string summary_,
  //                         std::string degrade_,
  //                         FIMMessageCustomContentBinaryData binaryDataStruct_)
  //     : type(type_), binary_data(std::move(binary_data_)),
  //       title(std::move(title_)), summary(std::move(summary_)),
  //       degrade(std::move(degrade_)),
  //       binaryDataStruct(std::move(binaryDataStruct_)) {}
};

} // namespace fim
} // namespace alibaba
