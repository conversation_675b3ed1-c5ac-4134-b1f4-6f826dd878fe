#pragma once
#include "fim_message_file_content.h"
#include "aim_msg_file_content.h"

namespace alibaba {
namespace fim {

// 将 AIMMsgFileContent 转换为 FIMMessageFileContent
inline FIMMessageFileContent FIMMessageFromFileContent(const dps::AIMMsgFileContent& aimFileContent) {
  FIMMessageFileContent fimFileContent;
  fimFileContent.local_path = aimFileContent.local_path;
  fimFileContent.file_name = aimFileContent.file_name;
  fimFileContent.file_type = aimFileContent.file_type;
  fimFileContent.media_id = aimFileContent.media_id;
  fimFileContent.mime_type = aimFileContent.mime_type;
  fimFileContent.url = aimFileContent.url;
  fimFileContent.file_size = aimFileContent.file_size;
  return fimFileContent;
}

// 将 FIMMessageFileContent 转换为 AIMMsgFileContent
inline dps::AIMMsgFileContent AIMMsgFileContentFrom(const FIMMessageFileContent& fimFileContent) {
  dps::AIMMsgFileContent aimFileContent;
  aimFileContent.local_path = fimFileContent.local_path;
  aimFileContent.file_name = fimFileContent.file_name;
  aimFileContent.file_type = fimFileContent.file_type;
  aimFileContent.media_id = fimFileContent.media_id;
  aimFileContent.mime_type = fimFileContent.mime_type;
  aimFileContent.url = fimFileContent.url;
  aimFileContent.file_size = fimFileContent.file_size;
  return aimFileContent;
}

} // namespace fim
} // namespace alibaba
