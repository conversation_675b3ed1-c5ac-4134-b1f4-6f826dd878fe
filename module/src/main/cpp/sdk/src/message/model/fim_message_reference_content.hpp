#pragma once
#include "fim_message_reference_content.h"
#include "aim_pub_msg_reference_content.h"
#include "fim_message_simple_content.hpp" 
#include "fim_message_content_type.hpp"

namespace alibaba {
namespace fim {

inline FIMMessageReferenceContent FIMMessageReferenceContentFrom(const dps::AIMPubMsgReferenceContent& aimContent) {
  return FIMMessageReferenceContent(
    FIMMessageContentTypeFrom(aimContent.content_type), // Assuming a conversion function exists
    FIMMessageSimpleContentFrom(aimContent.content) // Assuming a conversion function exists
  );
}

inline dps::AIMPubMsgReferenceContent AIMPubMsgReferenceContentFrom(const FIMMessageReferenceContent& fimContent) {
  return dps::AIMPubMsgReferenceContent(
    AIMMsgContentTypeFrom(fimContent.content_type), // Assuming a conversion function exists
    AIMPubMsgSimpleContentFrom(fimContent.content) // Assuming a conversion function exists
  );
}

} // namespace fim
} // namespace alibaba
