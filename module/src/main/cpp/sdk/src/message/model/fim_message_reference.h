#pragma once
#include <string>
#include <map>
#include "fim_message_reference_content.h" // 假定这个头文件存在并且已经定义了FIMMessageReferenceContent

namespace alibaba {
namespace fim {

struct FIMMessageReference final {
  std::string sender;
  std::string appCid;
  std::string mid;
  int64_t created_at = 0;
  FIMMessageReferenceContent reference_content;
  std::map<std::string, std::string> extension;

  FIMMessageReference(std::string sender_, std::string appCid_, std::string mid_,
                      int64_t created_at_,
                      FIMMessageReferenceContent reference_content_,
                      std::map<std::string, std::string> extension_)
      : sender(std::move(sender_)), appCid(std::move(appCid_)),
        mid(std::move(mid_)), created_at(created_at_),
        reference_content(std::move(reference_content_)),
        extension(std::move(extension_)) {}

  FIMMessageReference() {}
};

} // namespace fim
} // namespace alibaba
