#pragma once
#include <string>

namespace alibaba {
namespace fim {

// Structure for a structured message UID element
struct FIMMessageStructElementUid final {
  std::string uid;
  std::string default_nick;
  std::string prefix;

  FIMMessageStructElementUid(std::string uid_, std::string default_nick_, std::string prefix_)
      : uid(std::move(uid_)), default_nick(std::move(default_nick_)), prefix(std::move(prefix_)) {}

  FIMMessageStructElementUid() {}
};

} // namespace fim
} // namespace alibaba
