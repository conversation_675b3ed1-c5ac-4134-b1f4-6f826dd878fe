#pragma once
#include <string>
#include <vector>
#include <map>
//AIMMsgImageCompressType
#include "aim_msg_image_compress_type.h"
//AIMMsgImageFileType
#include "aim_msg_image_file_type.h"
//AIMMsgOrientation
#include "aim_msg_orientation.h"

namespace alibaba {
namespace fim {
using namespace dps; // 使用 dps 命名空间中定义的枚举类型

/**
 * 图片消息内容
 */
struct FIMMessageImageContent {
  /**
   * 图片本地路径（发送方）
   */
  std::string local_path;
  /**
   * 上传本地路径（发送方）
   */
  std::string upload_path;
  /**
   * 上传媒体类型（发送方）
   */
  std::string mime_type;
  /**
   * 图片url
   */
  std::string original_url;
  /**
   * 缩略图url
   */
  std::string thumbnail_url;
  /**
   * 模糊图数据
   */
  std::vector<uint8_t> blurred_data;
  /**
   * media id
   */
  std::string media_id;
  /**
   * 图片名字
   */
  std::string file_name;
  /**
   * 高(像素)
   */
  int32_t height;
  /**
   * 宽(像素)
   */
  int32_t width;
  /**
   * 文件大小(B)
   */
  int32_t size;
  /**
   * 图片类型
   */
  AIMMsgImageCompressType type;
  /**
   * 文件类型
   */
  AIMMsgImageFileType file_type;
  /**
   * 旋转类型
   */
  AIMMsgOrientation orientation;
  /**
   * 扩展信息
   */
  std::map<std::string, std::string> extension;

  FIMMessageImageContent() : height(-1), width(-1), size(-1), 
                             type(AIMMsgImageCompressType::IMAGE_COMPRESS_TYPE_UNKNOWN),
                             file_type(AIMMsgImageFileType::IMAGE_FILE_TYPE_UNKNOWN),
                             orientation(AIMMsgOrientation::ORIENTATION_UNKNOWN) {}

  FIMMessageImageContent(std::string local_path_, std::string upload_path_,
                         std::string mime_type_, std::string original_url_,
                         std::string thumbnail_url_,
                         std::vector<uint8_t> blurred_data_, std::string media_id_,
                         std::string file_name_, int32_t height_, int32_t width_,
                         int32_t size_, AIMMsgImageCompressType type_,
                         AIMMsgImageFileType file_type_,
                         AIMMsgOrientation orientation_,
                         std::map<std::string, std::string> extension_)
      : local_path(std::move(local_path_)),
        upload_path(std::move(upload_path_)), mime_type(std::move(mime_type_)),
        original_url(std::move(original_url_)),
        thumbnail_url(std::move(thumbnail_url_)),
        blurred_data(std::move(blurred_data_)), media_id(std::move(media_id_)),
        file_name(std::move(file_name_)), height(height_), width(width_),
        size(size_), type(type_), file_type(file_type_),
        orientation(orientation_), extension(std::move(extension_)) {}
};
} // namespace fim
} // namespace alibaba
