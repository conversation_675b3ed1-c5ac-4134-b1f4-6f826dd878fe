#pragma once

// Include necessary header files or forward declare any required types

#include "fim_message_simple_content.h" // For FIMMessageSimpleContent
#include "fim_message_reply_content.h" // For FIMMessageReplyContent
#include "fim_message_content_type.h" 

namespace alibaba {
namespace fim {

struct FIMMessageInnerCombineContent {
  FIMMessageContentType content_type = FIMMessageContentType::CONTENT_TYPE_UNKNOW;
  FIMMessageSimpleContent simple_content;
  FIMMessageReplyContent reply_content;

  FIMMessageInnerCombineContent() {}

  FIMMessageInnerCombineContent(FIMMessageContentType content_type_,
                                FIMMessageSimpleContent simple_content_,
                                FIMMessageReplyContent reply_content_)
      : content_type(content_type_),
        simple_content(std::move(simple_content_)),
        reply_content(std::move(reply_content_)) {}
};

} // namespace fim
} // namespace alibaba
