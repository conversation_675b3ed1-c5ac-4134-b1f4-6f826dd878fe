#pragma once

#include "fim_message_content_type.h"
#include "fim_message_reference.h"
#include "fim_message_inner_reply_content.h"

namespace alibaba {
namespace fim {

struct FIMMessageReplyContent {
  /* 
  * 回复的消息
  */
  FIMMessageReference reference_msg;

  FIMMessageInnerReplyContent reply_content;

  FIMMessageReplyContent() {}

  FIMMessageReplyContent(FIMMessageReference reference_msg_,
                        FIMMessageInnerReplyContent reply_content_)
      : reference_msg(std::move(reference_msg_)),
        reply_content(std::move(reply_content_)) {}
};

} // namespace fim
} // namespace alibaba
