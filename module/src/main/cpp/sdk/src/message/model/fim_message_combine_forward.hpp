#pragma once
#include "fim_message_combine_forward.h"
#include "aim_pub_msg_combine_forward.h"
#include "fim_message_inner_combine_content.hpp"

namespace alibaba {
namespace fim {

inline FIMMessageCombineForward FIMMessageFromCombineForward(const dps::AIMPubMsgCombineForward& aimCombineForward) {
  return FIMMessageCombineForward(aimCombineForward.sender, aimCombineForward.appCid, aimCombineForward.mid, aimCombineForward.created_at, FIMMessageFromInnerCombineContent(aimCombineForward.combine_content), aimCombineForward.extension);
}

inline dps::AIMPubMsgCombineForward AIMPubMsgCombineForwardFrom(const FIMMessageCombineForward& fimCombineForward) {
  return dps::AIMPubMsgCombineForward(fimCombineForward.sender, fimCombineForward.appCid, fimCombineForward.mid, fimCombineForward.created_at, ConvertToAIMPubMsgInnerCombineContent(fimCombineForward.combine_content), fimCombineForward.extension);
}

} // namespace fim
} // namespace alibaba
