//fim_message_send_message.h
#ifndef fim_send_message_h
#define fim_send_message_h


#include <stdio.h>
#include <memory>
#include <string>
#include <vector>
#include <functional>
#include "fim_message.h"
#include "fim_error.h"
#include "fliggy_im_sdk.h"

namespace alibaba {
namespace fim {

//       std::string appCid;
//   AIMPubMsgContent content;
//   std::vector<std::string> receivers;
//   std::map<std::string, std::string> extension;
//   std::map<std::string, std::string> local_extension;
//   std::map<std::string, std::string> callback_ctx;

    struct FIMSDK_API FIMSendMessage {
        std::string appCid;
        AIMPubMsgContent content;
        std::vector<std::string> receivers;
        std::map<std::string, std::string> extension;
        std::map<std::string, std::string> local_extension;
        std::map<std::string, std::string> callback_ctx;
        /**
        * 如果不为空，则使用custom_localid作为localid
        */
        std::string custom_localid;
    };

    struct FIMSDK_API FIMTextSendMessage: FIMSendMessage {
        std::string appCid;
        AIMPubMsgContent content;
        std::vector<std::string> receivers;
        std::map<std::string, std::string> extension;
        std::map<std::string, std::string> local_extension;
        std::map<std::string, std::string> callback_ctx;
        /**
        * 如果不为空，则使用custom_localid作为localid
        */
        std::string custom_localid;

    };
    // FIMTextSendMessage
    // FIMAudioSendMessage
    // FIMImageSendMessage
}
}

#endif // fim_send_message_h
