#include "fim_new_message.h"
#include "aim_pub_new_message.h"
#include "fim_message.hpp"
#include "aim_pub_message.h"

namespace alibaba {
namespace fim {

inline FIMNewMessage FIMNewMessageFrom(const dps::AIMPubNewMessage &aimPubNewMessage) {
    FIMMessage fimMessage = FIMMessageFrom(aimPubNewMessage.msg);
    return FIMNewMessage(fimMessage, aimPubNewMessage.type);
}

inline  std::vector<FIMNewMessage> FIMNewMessageListFrom(const std::vector<dps::AIMPubNewMessage> &msgs) {
    std::vector<FIMNewMessage> fimMessageList;
    fimMessageList.reserve(msgs.size());
    for (const auto &msg : msgs) {
        fimMessageList.push_back(FIMNewMessageFrom(msg));
    }
    return fimMessageList;
}

// 正确的逻辑不应该存在 FIMNewMessage -> AIMPubNewMessage 的转换
// inline AIMPubNewMessage AIMPubNewMessageFrom(const FIMNewMessage &fimMessage) {
//     AIMPubMessage aimMessage = AIMPubMessageFrom(fimMessage.msg);
//     return AIMPubNewMessage(aimMessage, fimMessage.type);
// }

} // namespace fim
} // namespace alibaba
