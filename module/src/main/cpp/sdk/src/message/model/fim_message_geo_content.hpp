#pragma once
#include "fim_message_geo_content.h"
#include "aim_msg_geo_content.h"

namespace alibaba {
namespace fim {

// 将 AIMMsgGeoContent 转换为 FIMMessageGeoContent
inline FIMMessageGeoContent FIMMessageFromGeoContent(const dps::AIMMsgGeoContent& aimGeoContent) {
  FIMMessageGeoContent fimGeoContent;
  fimGeoContent.pic_local_path = aimGeoContent.pic_local_path;
  fimGeoContent.pic_upload_path = aimGeoContent.pic_upload_path;
  fimGeoContent.mime_type = aimGeoContent.mime_type;
  fimGeoContent.pic_url = aimGeoContent.pic_url;
  fimGeoContent.pic_media_id = aimGeoContent.pic_media_id;
  fimGeoContent.pic_file_type = aimGeoContent.pic_file_type;
  fimGeoContent.pic_width = aimGeoContent.pic_width;
  fimGeoContent.pic_height = aimGeoContent.pic_height;
  fimGeoContent.latitude = aimGeoContent.latitude;
  fimGeoContent.longitude = aimGeoContent.longitude;
  fimGeoContent.location_name = aimGeoContent.location_name;
  return fimGeoContent;
}

// 将 FIMMessageGeoContent 转换为 AIMMsgGeoContent
inline dps::AIMMsgGeoContent AIMMsgGeoContentFrom(const FIMMessageGeoContent& fimGeoContent) {
  dps::AIMMsgGeoContent aimGeoContent;
  aimGeoContent.pic_local_path = fimGeoContent.pic_local_path;
  aimGeoContent.pic_upload_path = fimGeoContent.pic_upload_path;
  aimGeoContent.mime_type = fimGeoContent.mime_type;
  aimGeoContent.pic_url = fimGeoContent.pic_url;
  aimGeoContent.pic_media_id = fimGeoContent.pic_media_id;
  aimGeoContent.pic_file_type = fimGeoContent.pic_file_type;
  aimGeoContent.pic_width = fimGeoContent.pic_width;
  aimGeoContent.pic_height = fimGeoContent.pic_height;
  aimGeoContent.latitude = fimGeoContent.latitude;
  aimGeoContent.longitude = fimGeoContent.longitude;
  aimGeoContent.location_name = fimGeoContent.location_name;
  return aimGeoContent;
}

} // namespace fim
} // namespace alibaba
