#pragma once
#include "fim_message_struct_element_at.h"
#include "aim_pub_msg_struct_element_at.h"

namespace alibaba {
namespace fim {

inline FIMMessageStructElementAt FIMMessageStructElementAtFrom(const dps::AIMPubMsgStructElementAt& aimElementAt) {
  return FIMMessageStructElementAt(aimElementAt.is_at_all, aimElementAt.uid, aimElementAt.default_nick);
}

inline dps::AIMPubMsgStructElementAt AIMPubMsgStructElementAtFrom(const FIMMessageStructElementAt& fimElementAt) {
  return dps::AIMPubMsgStructElementAt(fimElementAt.is_at_all, fimElementAt.uid, fimElementAt.default_nick);
}

} // namespace fim
} // namespace alibaba
