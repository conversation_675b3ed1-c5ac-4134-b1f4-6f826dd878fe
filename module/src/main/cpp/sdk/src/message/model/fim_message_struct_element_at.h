#pragma once

#include <string>
#include <utility>

namespace alibaba {
namespace fim {

/**
 * 结构化消息At元素
 */
struct FIMMessageStructElementAt final {

  bool is_at_all = false;
  std::string uid;
  std::string default_nick;

  FIMMessageStructElementAt(bool is_at_all_, std::string uid_,
                           std::string default_nick_)
      : is_at_all(std::move(is_at_all_)), uid(std::move(uid_)),
        default_nick(std::move(default_nick_)) {}

  FIMMessageStructElementAt() {}
};

} // namespace dps
} // namespace alibaba
