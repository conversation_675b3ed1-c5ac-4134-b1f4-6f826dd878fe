#pragma once
#include "fim_message_video_content.h"
#include "aim_msg_video_content.h"

namespace alibaba {
namespace fim {

// 将 AIMMsgVideoContent 转换为 FIMMessageVideoContent
inline FIMMessageVideoContent FIMMessageFromVideoContent(const dps::AIMMsgVideoContent& aimVideoContent) {
  FIMMessageVideoContent fimVideoContent;
  fimVideoContent.local_path = aimVideoContent.local_path;
  fimVideoContent.upload_path = aimVideoContent.upload_path;
  fimVideoContent.mime_type = aimVideoContent.mime_type;
  fimVideoContent.url = aimVideoContent.url;
  fimVideoContent.media_id = aimVideoContent.media_id;
  fimVideoContent.cover_local_path = aimVideoContent.cover_local_path;
  fimVideoContent.cover_upload_path = aimVideoContent.cover_upload_path;
  fimVideoContent.cover_url = aimVideoContent.cover_url;
  fimVideoContent.cover_media_id = aimVideoContent.cover_media_id;
  fimVideoContent.cover_file_type = aimVideoContent.cover_file_type;
  fimVideoContent.cover_mime_type = aimVideoContent.cover_mime_type;
  fimVideoContent.file_name = aimVideoContent.file_name;
  fimVideoContent.file_type = aimVideoContent.file_type;
  fimVideoContent.file_size = aimVideoContent.file_size;
  fimVideoContent.duration = aimVideoContent.duration;
  fimVideoContent.height = aimVideoContent.height;
  fimVideoContent.width = aimVideoContent.width;
  fimVideoContent.cover_width = aimVideoContent.cover_width;
  fimVideoContent.cover_height = aimVideoContent.cover_height;
  return fimVideoContent;
}

// 将 FIMMessageVideoContent 转换为 AIMMsgVideoContent
inline dps::AIMMsgVideoContent AIMMsgVideoContentFrom(const FIMMessageVideoContent& fimVideoContent) {
  dps::AIMMsgVideoContent aimVideoContent;
  aimVideoContent.local_path = fimVideoContent.local_path;
  aimVideoContent.upload_path = fimVideoContent.upload_path;
  aimVideoContent.mime_type = fimVideoContent.mime_type;
  aimVideoContent.url = fimVideoContent.url;
  aimVideoContent.media_id = fimVideoContent.media_id;
  aimVideoContent.cover_local_path = fimVideoContent.cover_local_path;
  aimVideoContent.cover_upload_path = fimVideoContent.cover_upload_path;
  aimVideoContent.cover_url = fimVideoContent.cover_url;
  aimVideoContent.cover_media_id = fimVideoContent.cover_media_id;
  aimVideoContent.cover_file_type = fimVideoContent.cover_file_type;
  aimVideoContent.cover_mime_type = fimVideoContent.cover_mime_type;
  aimVideoContent.file_name = fimVideoContent.file_name;
  aimVideoContent.file_type = fimVideoContent.file_type;
  aimVideoContent.file_size = fimVideoContent.file_size;
  aimVideoContent.duration = fimVideoContent.duration;
  aimVideoContent.height = fimVideoContent.height;
  aimVideoContent.width = fimVideoContent.width;
  aimVideoContent.cover_width = fimVideoContent.cover_width;
  aimVideoContent.cover_height = fimVideoContent.cover_height;
  return aimVideoContent;
}

} // namespace fim
} // namespace alibaba
