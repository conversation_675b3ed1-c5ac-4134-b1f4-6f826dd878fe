#ifndef ALIBABA_CLOUD_FIM_MESSAGE_HPP
#define ALIBABA_CLOUD_FIM_MESSAGE_HPP
#include "aim_pub_message.h"
#include "fim_message.h"
#include "fim_message_content.h"
#include "fim_message_content.hpp"

namespace alibaba {
namespace fim {
inline FIMMessage FIMMessageFrom(const dps::AIMPubMessage &aimMsg) {
    fim::FIMMessageContent content = FIMMessageContentFrom(aimMsg.content);
  return FIMMessage(aimMsg.appCid, aimMsg.mid, aimMsg.localid, aimMsg.sender,
                    aimMsg.sender_tag, aimMsg.created_at, aimMsg.unread_count,
                    aimMsg.receiver_count, aimMsg.receivers, aimMsg.is_read,
                    aimMsg.extension, aimMsg.local_extension,
                    aimMsg.user_extension, content, aimMsg.status,
                    aimMsg.is_delete, aimMsg.is_recall, aimMsg.is_disable_read,
                    aimMsg.is_local, aimMsg.biz_info, aimMsg.display_style,
                    aimMsg.recall_feature);
}


inline std::vector<FIMMessage> FIMMessageListFrom(const std::vector<dps::AIMPubMessage> &aimMsgList) {
  std::vector<FIMMessage> fimMsgList;
  for (const auto &aimMsg : aimMsgList) {
    fimMsgList.push_back(FIMMessageFrom(aimMsg));
  }
  return fimMsgList;
}

// 正确的逻辑不应该存在 FIMMessage -> AIMPubMessage 的转换
// inline AIMPubMessage AIMPubMessageFrom(const FIMMessage &fimMessage) {
//   AIMPubMsgContent content = AIMPubMsgContentFrom(fimMessage.content);
//   return AIMPubMessage(fimMessage.appCid, fimMessage.mid, fimMessage.localid,
//                        fimMessage.sender, fimMessage.sender_tag,
//                        fimMessage.created_at, fimMessage.unread_count,
//                        fimMessage.receiver_count, fimMessage.receivers,
//                        fimMessage.is_read, fimMessage.extension,
//                        fimMessage.local_extension, fimMessage.user_extension,
//                        content, fimMessage.status, fimMessage.is_delete,
//                        fimMessage.is_recall, fimMessage.is_disable_read,
//                        fimMessage.is_local, fimMessage.biz_info,
//                        fimMessage.display_style, fimMessage.recall_feature);
// }

} // namespace fim

} // namespace alibaba

#endif /* ALIBABA_CLOUD_FIM_MESSAGE_HPP */