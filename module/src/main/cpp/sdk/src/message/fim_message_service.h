//
//  fim_test.hpp
//  FliggyIMSDK
//
//  Created by 蒙晨 on 2024/2/5.
//

#ifndef fim_message_service_h
#define fim_message_service_h

#include "fim_error.h"
#include "fim_message.h"
#include "fim_message_resend_message.h"
#include "fim_message_send_forward_message.h"
#include "fim_message_send_reply_message.h"
#include "fim_message_change_listener.h"
#include "fim_new_message.h"
#include "fim_message_listener.h"
#include "fim_message_content_base.h"
#include "fim_send_message_models.h"
#include <unordered_map>
#include <mutex>
#include <functional>
#include <memory>
#include <stdio.h>
#include <string>
#include <vector>

namespace alibaba {
namespace dps {
struct AIMMsgFilter;
struct AIMPubMsgLocalExtensionUpdateInfo;
struct AIMPubMsgBizUpdateInfo;
struct AIMPubMsgReadStatus;
struct AIMPubMsgSendReplyMessage;
struct AIMPubMsgSendForwardMessage;

class AIMPubMsgSendMsgListener;
class AIMPubMsgListLocalMsgsListener;
class AIMPubMsgListNextMsgsListener;
class AIMPubMsgListPreviousMsgsListener;
class AIMPubMsgReSendMsgListener;
class AIMPubMsgSendMsgToLocalListener;
class AIMPubMsgGetMsgListener;
class AIMPubMsgGetLocalMsgListener;
class AIMPubMsgGetLocalMsgsListener;
class AIMMsgDeleteMsgListener;
class AIMMsgDeleteLocalMsgListener;
class AIMMsgRecallMsgListener;
class AIMMsgUpdateLocalExtensionListener;
class AIMMsgUpdateLocalMsgsBizInfoListener;
class AIMPubMsgListMsgsReadStatus;
class AIMMsgForwardMsgListener;
class AIMPubMsgCombineForwardMsgListener;

class AIMPubMsgListener;
class AIMPubMsgChangeListener;

} // namespace dps
} // namespace alibaba

enum class FIMMsgImageFileType : int {
  IMAGE_FILE_TYPE_UNKNOWN = -1,
  IMAGE_FILE_TYPE_WEBP = 1,
  IMAGE_FILE_TYPE_PNG = 2,
  IMAGE_FILE_TYPE_JPG = 3,
  IMAGE_FILE_TYPE_GIF = 4,
};

namespace alibaba {
namespace fim {

class FIMSDK_API FIMMessageService {
public:
  /**
   * 发送消息
   * @param msg 发送的消息对象
   * @param listener 监听器
   * @param user_data 用户数据（"trace_id"："_trace_id_example"）
   */
//   static void
//   SendMessage(const FIMSendMessageBase &msg,
//               const std::shared_ptr<dps::AIMPubMsgSendMsgListener> &listener,
//               const std::map<std::string, std::string> &user_data);

  static void
  SendMessage(const FIMSendMessageBase &msg,
              const std::function<void(double progress)> &OnProgress,
              const std::function<void(const FIMMessage &msg)> &OnSuccess,
              const std::function<void(const FIMError &error)>
                  &OnFailure,
              const std::map<std::string, std::string> &user_data);

    static void SendAimMessage(AIMPubMsgSendMessage msg,
                     const std::function<void(double progress)> &OnProgress,
                     const std::function<void(const FIMMessage &msg)> &OnSuccess,
                     const std::function<void(const FIMError &error)> &OnFailure,
                     const std::map<std::string, std::string> &user_data);
  /**
   * 获取上一页特定类型消息，按时间升序排列
   * 注意：该接口只返回本地连续数据
   * @param appCid 会话唯一id
   * @param cursor 消息事件游标,消息的创建时间，如果是最新的开始
   * AIM_MAX_MSG_CURSOR
   * @param count  返回的结果数量，最大100
   * @param listener 监听器
   */
//   static void ListPreviousLocalMsgs(
//       const std::string &appCid, int64_t cursor, int32_t count,
//       const std::shared_ptr<AIMPubMsgListLocalMsgsListener> &listener);
  static void ListPreviousLocalMsgs(
      const std::string &appCid, int64_t cursor, int32_t count,
      const std::function<void(const std::vector<FIMMessage> &msgs,
                               bool has_more)> &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 获取下一页特定类型消息，按时间升序排列
   * 注意：该接口只返回本地连续数据
   * @param appCid 会话唯一id
   * @param cursor 消息事件游标,消息的创建时间，如果是最新的开始
   * AIM_MAX_MSG_CURSOR
   * @param count  返回的结果数量，最大100
   * @param listener 监听器
   */
//   static void ListNextLocalMsgs(
//       const std::string &appCid, int64_t cursor, int32_t count,
//       const std::shared_ptr<AIMPubMsgListLocalMsgsListener> &listener);
  static void ListNextLocalMsgs(
      const std::string &appCid, int64_t cursor, int32_t count,
      const std::function<void(const std::vector<FIMMessage> &msgs,
                               bool has_more)> &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 获取下一页消息，按时间升序排列
   * @param appCid 会话唯一id
   * @param cursor 消息事件游标,消息的创建时间，如果是最新的开始
   * AIM_MAX_MSG_CURSOR
   * @param count 返回的结果数量，最大100
   * @param listener 监听器
   */
//   static void
//   ListNextMsgs(const std::string &appCid, int64_t cursor, int32_t count,
//                const std::shared_ptr<AIMPubMsgListNextMsgsListener> &listener);
  static void
  ListNextMsgs(const std::string &appCid, int64_t cursor, int32_t count,
               const std::function<void(const std::vector<FIMMessage> &msgs,
                                        bool has_more)> &OnSuccess,
               const std::function<
                   void(const std::vector<std::vector<FIMMessage>> &msgs,
                        const FIMError &error)> &OnFailure);

  /**
   * 获取上一页消息，按时间升序排列
   * @param appCid 会话唯一id
   * @param cursor 消息事件游标,消息的创建时间，如果是最新的开始
   * AIM_MAX_MSG_CURSOR
   * @param count 返回的结果数量，最大100
   * @param listener 监听器
   */
//   static void ListPreviousMsgs(
//       const std::string &appCid, int64_t cursor, int32_t count,
//       const std::shared_ptr<AIMPubMsgListPreviousMsgsListener> &listener);
  static void ListPreviousMsgs(
      const std::string &appCid, int64_t cursor, int32_t count,
      const std::function<void(const std::vector<FIMMessage> &msgs,
                               bool has_more)> &OnSuccess,
      const std::function<
          void(const std::vector<std::vector<FIMMessage>> &msgs,
               const FIMError &error)> &OnFailure);

  /**
   * 重发消息
   * @param resend_msg 重发消息结构
   * @param listener 监听器
   * @param user_data 用户数据
   */
//   static void ResendMessage(
//       const FIMMessageReSendMessage &resend_msg,
//       const std::shared_ptr<dps::AIMPubMsgReSendMsgListener> &listener,
//       const std::map<std::string, std::string> &user_data);
  static void
  ResendMessage(const FIMMessageReSendMessage &resend_msg,
                const std::function<void(double progress)> &OnProgress,
                const std::function<void(const FIMMessage &msg)> &OnSuccess,
                const std::function<void(const FIMError &error)> &OnFailure,
                const std::map<std::string, std::string> &user_data);

  /**
   * 发送消息到本地不发送
   * @param msg 发送的消息对象
   * @param listener 监听器
   */
//   static void SendMessageTolocal(
//       const FIMSendMessageBase &msg,
//       const std::shared_ptr<AIMPubMsgSendMsgToLocalListener> &listener);
  static void SendMessageTolocal(
      const FIMSendMessageBase &msg,
      const std::function<void(const FIMMessage &msg)> &OnSuccess,
      const std::function<void(const FIMError &error)>
          &OnFailure);
    
    static void SendAimMessageTolocal(
        const AIMPubMsgSendMessage &msg,
        const std::function<void(const FIMMessage &msg)> &OnSuccess,
                                      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 通过mid获取消息,本地不存在到服务端拉取
   * @param appCid 会话唯一id
   * @param mid 消息唯一id
   * @param listener 监听器
   */
//   static void
//   GetMessage(const std::string &appCid, const std::string &mid,
//              const std::shared_ptr<AIMPubMsgGetMsgListener> &listener);
  static void
  GetMessage(const std::string &appCid, const std::string &mid,
             const std::function<void(const FIMMessage &msg)> &OnSuccess,
             const std::function<void(const FIMError &error)>
                 &OnFailure);

  /**
   * 通过mid获取本地消息
   * @param appCid 会话唯一id
   * @param local_id 消息本地唯一id
   * @param listener 监听器
   */
//   static void GetLocalMessage(
//       const std::string &appCid, const std::string &local_id,
//       const std::shared_ptr<AIMPubMsgGetLocalMsgListener> &listener);
  static void GetLocalMessage(
      const std::string &appCid, const std::string &local_id,
      const std::function<void(const FIMMessage &msg)> &OnSuccess,
      const std::function<void(const FIMError &error)>
          &OnFailure);

  /**
   * 根据条件获取本地消息
   * 注意：该接口只返回本地数据，且不保证连续
   * @param appCid 会话唯一id
   * @param cursor 消息事件游标,消息的创建时间，如果是最新的开始
   * AIM_MAX_MSG_CURSOR
   * @param count  返回的结果数量，最大100
   * @param forward true: cursor 时间更大的数据， false： cursor时间更小的数据
   * @param filter 过滤条件
   * @param listener 监听器
   */
//   static void GetLocalMessages(
//       const std::string &appCid, int64_t cursor, int32_t count, bool forward,
//       const AIMMsgFilter &filter,
//       const std::shared_ptr<AIMPubMsgGetLocalMsgsListener> &listener);
  static void GetLocalMessages(
      const std::string &appCid, int64_t cursor, int32_t count, bool forward,
      const AIMMsgFilter &filter,
      const std::function<void(const std::vector<FIMMessage> &msgs)>
          &OnSuccess,
      const std::function<void(const FIMError &error)>
          &OnFailure);

  /**
   * 删除消息，消息将从列表中移除，同步入云端
   * @param appCid 会话唯一id
   * @param mids 消息id列表
   * @param listener 监听器
   */
//   virtual void
//   DeleteMessage(const std::string &appCid, const std::vector<std::string> &mids,
//                 const std::shared_ptr<AIMMsgDeleteMsgListener> &listener);
static void DeleteMessage(const std::string &appCid, const std::vector<std::string> &mids,
                const std::function<void()> &OnSuccess,
                const std::function<void(const FIMError &error)>
                    &OnFailure);

  /**
   * 删除本地消息，云端不同步
   * @param appCid 会话唯一id
   * @param local_ids 消息id
   * @param listener 监听器
   */
//   static void DeleteLocalMessage(
//       const std::string &appCid, const std::vector<std::string> &local_ids,
//       const std::shared_ptr<AIMMsgDeleteLocalMsgListener> &listener);
  static void DeleteLocalMessage(
      const std::string &appCid, const std::vector<std::string> &local_ids,
      const std::function<void()> &OnSuccess,
      const std::function<void(const FIMError &error)>
          &OnFailure);

  /**
   * 撤回一条已发送的消息
   * @param appCid 会话唯一id
   * @param mid 消息id
   * @param listener 监听器
   */
//   virtual void
//   RecallMessage(const std::string &appCid, const std::string &mid,
//                 const std::shared_ptr<AIMMsgRecallMsgListener> &listener);
  static void
  RecallMessage(const std::string &appCid, const std::string &mid,
                const std::function<void()> &OnSuccess,
                const std::function<void(const FIMError &error)>
                    &OnFailure);

  /**
   * 全量更新本地消息 local extension，不同步到云端
   * @param update_infos 更新消息Extension信息列表
   * @param listener 监听器
   */
//   static void UpdateLocalExtension(
//       const std::vector<AIMPubMsgLocalExtensionUpdateInfo> &update_infos,
//       const std::shared_ptr<AIMMsgUpdateLocalExtensionListener> &listener);
  static void UpdateLocalExtension(
      const std::vector<AIMPubMsgLocalExtensionUpdateInfo> &update_infos,
      const std::function<void()> &OnSuccess,
      const std::function<void(const FIMError &error)>
          &OnFailure);

  /**
   * 根据 key 局部更新本地消息 local extension, 不同步到云端
   * @param update_infos 更新消息Extension信息列表
   * @param listener 监听器
   */
//   static void UpdateLocalExtensionByKey(
//       const std::vector<AIMPubMsgLocalExtensionUpdateInfo> &update_infos,
//       const std::shared_ptr<AIMMsgUpdateLocalExtensionListener> &listener);
  static void UpdateLocalExtensionByKey(
      const std::vector<AIMPubMsgLocalExtensionUpdateInfo> &update_infos,
      const std::function<void()> &OnSuccess,
      const std::function<void(const FIMError &error)>
          &OnFailure);

  /**
   * 更新本地消息 biz 相关字段
   * @param update_infos 更新消息Biz信息列表
   * @param listener 监听器
   */
//   static void UpdateLocalMessagesBizInfo(
//       const std::vector<AIMPubMsgBizUpdateInfo> &update_infos,
//       const std::shared_ptr<AIMMsgUpdateLocalMsgsBizInfoListener>
//           &listener);
  static void UpdateLocalMessagesBizInfo(
      const std::vector<AIMPubMsgBizUpdateInfo> &update_infos,
      const std::function<void()> &OnSuccess,
      const std::function<void(const FIMError &error)>
          &OnFailure);

  /**
   * 获取消息已读状态
   * @param appCid 会话唯一id
   * @param mid 消息唯一id
   * @param listener 监听器
   */
//   static void ListMessagesReadStatus(
//       const std::string &appCid, const std::string &mid,
//       const std::shared_ptr<AIMPubMsgListMsgsReadStatus> &listener);
  static void ListMessagesReadStatus(
      const std::string &appCid, const std::string &mid,
      const std::function<void(const AIMPubMsgReadStatus &status)> &OnSuccess,
      const std::function<void(const FIMError &error)>
          &OnFailure);

  /**
   * 读取接收到消息，消息进入已读状态，同步入云端
   * 人数。
   * @param appCid 会话唯一id
   * @param mids 批量消息mid
   */
  static void UpdateMessageToRead(const std::string &appCid,
                                   const std::vector<std::string> &mids);

  /**
   * 生成消息localId,本地唯一
   */
  virtual int64_t GenerateMsgLocalId();

  /**
   * 注册消息变动的监听器，如，消息新增、删除、更新
   * @param listener 消息监听器
   */
  static bool
  AddMsgListener(const std::shared_ptr<FIMMessageListener> &listener);

  /**
   * 删除消息的监听器
   * @param listener 消息监听器
   */
  static bool
  RemoveMsgListener(const std::shared_ptr<FIMMessageListener> &listener);

  /**
   * 删除所有消息的监听器
   */
  static void RemoveAllMsgListener();

  /**
   * 注册消息属性变更的监听器
   * @param listener 变更监听器
   */

static bool AddMsgChangeListener(
      const std::shared_ptr<FIMMessageChangeListener> &listener);

  /**
   * 删除消息的属性监听器
   * @param listener 变更监听器
   */
static bool RemoveMsgChangeListener(
      const std::shared_ptr<FIMMessageChangeListener> &listener);

  /**
   * 删除所有消息的属性监听器
   */
static void RemoveAllMsgChangeListener();

  /**
   * 回复消息
   * @param msg 发送的回复消息对象
   * @param listener 监听器
   */
//   virtual void
//   ReplyMessage(const FIMMessageSendReplyMessage &msg,
//                const std::shared_ptr<AIMPubMsgReSendMsgListener> &listener,
//                const std::map<std::string, std::string> &user_data);
  static void
  ReplyMessage(const FIMMessageSendReplyMessage &msg,
               const std::function<void(double progress)> &OnProgress,
               const std::function<void(const FIMMessage &msg)> &OnSuccess,
               const std::function<void(const FIMError &error)>
                   &OnFailure,
               const std::map<std::string, std::string> &user_data);
  static void ReplyAimMessage(
        const AIMPubMsgSendReplyMessage &aimMessage,
        const std::function<void(double progress)> &OnProgress,
        const std::function<void(const FIMMessage &msg)> &OnSuccess,
        const std::function<void(const FIMError &error)> &OnFailure,
                                                 const std::map<std::string, std::string> &user_data);
  /**
   * 转发消息
   * @param msg 发送的消息对象
   * @param listener 监听器
   * @param user_data 用户数据（"trace_id"："_trace_id_example"）
   */
//   virtual void
//   ForwardMessage(const FIMMessageSendForwardMessage &mgs,
//                  const std::shared_ptr<AIMMsgForwardMsgListener> &listener,
//                  const std::map<std::string, std::string> &user_data);
  static void ForwardMessage(
      const FIMMessageSendForwardMessage &mgs,
      const std::function<void()> &OnSuccess,
      const std::function<void(const FIMError &error)>
          &OnFailure,
      const std::map<std::string, std::string> &user_data);
    
static void ForwardAimMessage(
    const AIMPubMsgSendForwardMessage &aimMessage,
    const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure,
                                                 const std::map<std::string, std::string> &user_data);

  /**
   * 合并转发消息
   * @param msg 发送的消息对象
   * @param listener 监听器
   * @param user_data 用户数据（"trace_id"："_trace_id_example"）
   */
//   static void CombineForwardMessage(
//       const FIMMessageSendForwardMessage &mgs,
//       const std::shared_ptr<AIMPubMsgCombineForwardMsgListener> &listener,
//       const std::map<std::string, std::string> &user_data);
  static void CombineForwardMessage(
      const FIMMessageSendForwardMessage &mgs,
      const std::function<void(const FIMMessage &msg)> &OnSuccess,
      const std::function<void(const FIMError &error)>
          &OnFailure,
      const std::map<std::string, std::string> &user_data);
    
static void CombineForwardAimMessage(
    const AIMPubMsgSendForwardMessage &aimMessage,
    const std::function<void(const FIMMessage &msg)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure,
    const std::map<std::string, std::string> &user_data);

  /**
   * 设置拉消息时是否需要receivers字段，默认为true
   * @param need 是否需要
   */
  static void SetNeedReceivers(bool need);
};

} // namespace fim
} // namespace alibaba
#endif /* fim_message_service */
