#include "fim_group_service.h"
#include "aim_pub_c_api.h"
#include "aim_pub_module.h"
#include "dps_error.h"
#include "fim_conversation.hpp"
#include "fim_engine.h"
#include "fim_error.hpp"
// AIMPubGroupCreateGroupConvParam
#include "aim_pub_group_create_group_conv_param.h"
#include "aim_pub_group_service.h"
#include <string>
// AIMPubGroupMemberUpdateNick
#include "aim_pub_group_member_update_nick.h"
#include "fim_group_member.hpp"
// AIMGroupLeave
#include "aim_pub_group_leave.h"
#include "fim_group_user_info.hpp"
#include "log_service.h"
// AIMPubGroupUpdateSilenceAll
#include "aim_pub_group_update_silence_all.h"
// AIMPubGroupUpdateSilencedWhiteList
#include "aim_pub_group_update_silenced_white_list.h"
// AIMPubGroupUpdateSilencedBlackList
#include "aim_pub_group_update_silenced_black_list.h"
// AIMPubGroupUpdateAdmins
#include "aim_pub_group_update_admins.h"
// AIMPubGroupKick
#include "aim_pub_group_kick.h"

namespace alibaba {
namespace fim {

using json = nlohmann::json;

std::shared_ptr<AIMPubGroupService> getCurrentGroupService() {
  alibaba::dps::AIMPubModule *rawModulePtr =
      GetAIMPubModuleInstance((FIMEngine::GetInstance().fuid));
  if (rawModulePtr == nullptr) {
    return nullptr;
  }
  return rawModulePtr->GetGroupService();
}

void UdateGroupManager(
    const std::string app_cid, const std::vector<std::string> fuidList,
    bool admin, const std::function<void(const std::string &body)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  if (fuidList.empty() || app_cid.empty()) {
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "params is error", "请检查参数");
    OnFailure(error);
    return;
  }
  json params = {{"fuids", fuidList}, {"fcid", app_cid}, {"admin", admin}};
  FIMEngine::GetInstance().network_service->Mtop(
      "mtop.fliggy.fim.group.member.setAdmin", "1.0", params,
      [OnFailure, OnSuccess](const FIMHttpResponse &response) {
        if (!response.error_message.empty()) {
          FIMError error =
              FIMError(FIMErrorCode::FIM_ERR_NETWORK_ERROR,
                       "UdateGroupManager group failed", response.error_message);
          OnFailure(error);
          return;
        }
        if (response.body.empty()) {
          FIMError error =
              FIMError(FIMErrorCode::FIM_ERR_NETWORK_ERROR,
                       "UdateGroupManager group failed", "UdateGroupManager group failed");
          OnFailure(error);
          return;
        }
        OnSuccess(response.body);
      });
}
/**
 * 设置全员禁言
 * 调用权限：群主或群管理员
 * 禁言后仅群主和管理员可发言
 * @param param AIMGroupUpdateSilenceAll 结构
 * @param listener 监听器
 */
void FIMGroupSerivce::SilenceAll(
    const std::string &app_cid, const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubGroupService> groupService = getCurrentGroupService();
  if (groupService == nullptr) {
    LogService::ErrorLog(
        []() { return "Silence All: get groupService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get groupService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  AIMPubGroupUpdateSilenceAll silenceAll = AIMPubGroupUpdateSilenceAll(app_cid);
  groupService->SilenceAll(
      silenceAll, OnSuccess, [OnFailure](const DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "Silence All OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      });
}
/**
 * 取消全员禁言
 * 调用权限：群主或群管理员
 * @param param AIMGroupUpdateSilenceAll 结构
 * @param listener 监听器
 */
void FIMGroupSerivce::CancelSilenceAll(
    const std::string &app_cid, const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubGroupService> groupService = getCurrentGroupService();
  if (groupService == nullptr) {
    LogService::ErrorLog(
        []() { return "Cancel Silence All: get groupService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get groupService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  AIMPubGroupUpdateSilenceAll silenceAll = AIMPubGroupUpdateSilenceAll(app_cid);
  groupService->CancelSilenceAll(
      silenceAll, OnSuccess, [OnFailure](const DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "Cancel Silence All OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      });
}

/**
 * 增加白名单用户(在设置全员禁言后，可以设置白名单，白名单中的用户可以发言)
 * 调用权限：群主或群管理员,
 * @param param AIMGroupUpdateSilencedWhiteList 结构
 * @param listener 监听器
 */
void FIMGroupSerivce::AddSilencedWhitelist(
    const std::string &app_cid, const std::vector<FIMGroupUserInfo> &members,
    const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubGroupService> groupService = getCurrentGroupService();
  if (groupService == nullptr) {
    LogService::ErrorLog(
        []() { return "Add Silenced White List: get groupService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get groupService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  /**
   * 群appCid
   */
  std::string appCid;
  /**
   * 操作成员
   */
  std::vector<AIMPubGroupUserInfo> aimMembers =
      AIMPubGroupUserInfoListFrom(members);
  AIMPubGroupUpdateSilencedWhiteList silencedWhiteList =
      AIMPubGroupUpdateSilencedWhiteList(app_cid, aimMembers);
  groupService->AddSilencedWhitelist(
      silencedWhiteList, OnSuccess, [OnFailure](const DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "Add Silenced White List OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      });
}
/**
 * 增加禁言用户
 * 调用权限：群主或群管理员
 * @param param AIMGroupUpdateSilencedBlackList 结构
 * @param listener 监听器
 */
void FIMGroupSerivce::AddSilencedBlacklist(
    const std::string &app_cid, const std::vector<FIMGroupUserInfo> &members,
    int64_t duration, const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubGroupService> groupService = getCurrentGroupService();
  if (groupService == nullptr) {
    LogService::ErrorLog(
        []() { return "Add Silenced Black List: get groupService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get groupService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  std::vector<AIMPubGroupUserInfo> aimMembers =
      AIMPubGroupUserInfoListFrom(members);

  AIMPubGroupUpdateSilencedBlackList silencedBlackList =
      AIMPubGroupUpdateSilencedBlackList(app_cid, aimMembers, duration);
  groupService->AddSilencedBlacklist(
      silencedBlackList, OnSuccess, [OnFailure](const DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "Add Silenced Black List OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      });
}

/**
 * 删除禁言用户
 * 调用权限：群主或群管理员
 * @param param AIMGroupUpdateSilencedBlackList 结构
 * @param listener 监听器
 */
void FIMGroupSerivce::RemoveSilencedBlacklist(
    const std::string &app_cid, const std::vector<FIMGroupUserInfo> &members,
    const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubGroupService> groupService = getCurrentGroupService();
  if (groupService == nullptr) {
    LogService::ErrorLog([]() {
      return "Remove Silenced Black List: get groupService failure";
    });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get groupService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  std::vector<AIMPubGroupUserInfo> aimMembers =
      AIMPubGroupUserInfoListFrom(members);

  AIMPubGroupUpdateSilencedBlackList silencedBlackList =
      AIMPubGroupUpdateSilencedBlackList(app_cid, aimMembers, 0);
  groupService->RemoveSilencedBlacklist(
      silencedBlackList, OnSuccess, [OnFailure](const DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "Remove Silenced Black List OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      });
}
/**
 * 添加群管理员
 * 调用权限：群主或群管理员
 * @param param AIMGroupUpdateAdmins 结构
 * @param listener 监听器
 */

void FIMGroupSerivce::AddAdmins(
    const std::string &app_cid,
    const std::vector<std::string> &fuidList,
    const std::function<void(const std::string &body)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  UdateGroupManager(app_cid, fuidList, true, OnSuccess, OnFailure);
}

/**
 * 删除群管理员
 * 调用权限：群主或群管理员
 * @param param AIMGroupUpdateAdmins 结构
 * @param listener 监听器
 */
void FIMGroupSerivce::RemoveAdmins(const std::string &app_cid,
               const std::vector<std::string> &fuidList,
               const std::function<void(const std::string &body)> &OnSuccess,
               const std::function<void(const FIMError &error)> &OnFailure) {
  UdateGroupManager(app_cid, fuidList, false, OnSuccess, OnFailure);
}
/**
 * 解散群
 * @param appCid 会话appCid
 * @param listener 监听器
 */
void FIMGroupSerivce::Dismiss(
    const std::string &app_cid, const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubGroupService> groupService = getCurrentGroupService();
  if (groupService == nullptr) {
    LogService::ErrorLog(
        []() { return "Dismiss Group: get groupService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get groupService failure", "获取会话失败");
    OnFailure(error);
    return;
  }

  groupService->Dismiss(app_cid, OnSuccess, [OnFailure](const DPSError &error) {
    LogService::ErrorLog([error]() {
      std::string result = "Dismiss Group OnFailure: ";
      result += "<error.code:" + std::to_string(error.code) +
                " error.message:" + error.developer_message + ">";
      return result;
    });
    OnFailure(FIMErrorFrom(error));
  });
}

/**
 * 退出群
 * @param param AIMGroupLeave 结构
 * @param listener 监听器
 */
void FIMGroupSerivce::Leave(
    const std::string app_cid, const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubGroupService> groupService = getCurrentGroupService();
  if (groupService == nullptr) {
    LogService::ErrorLog(
        []() { return "Leave Group: get groupService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get groupService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  AIMPubGroupLeave leave = AIMPubGroupLeave(app_cid);
  groupService->Leave(leave, OnSuccess, [OnFailure](const DPSError &error) {
    LogService::ErrorLog([error]() {
      std::string result = "Leave Group OnFailure: ";
      result += "<error.code:" + std::to_string(error.code) +
                " error.message:" + error.developer_message + ">";
      return result;
    });
    OnFailure(FIMErrorFrom(error));
  });
}
/**
 * 删除群成员
 * @param param AIMGroupKick 结构
 * @param listener 监听器
 */
void FIMGroupSerivce::RemoveMembers(
    const std::string &app_cid, const std::vector<FIMGroupUserInfo> &members,
    const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubGroupService> groupService = getCurrentGroupService();
  if (groupService == nullptr) {
    LogService::ErrorLog(
        []() { return "Remove Members: get groupService failure"; });
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get groupService failure", "获取会话失败");
    OnFailure(error);
    return;
  }
  std::vector<AIMPubGroupUserInfo> aimMembers =
      AIMPubGroupUserInfoListFrom(members);
  AIMPubGroupKick kick = AIMPubGroupKick(app_cid, aimMembers);
  groupService->RemoveMembers(
      kick, OnSuccess, [OnFailure](const DPSError &error) {
        LogService::ErrorLog([error]() {
          std::string result = "Remove Members OnFailure: ";
          result += "<error.code:" + std::to_string(error.code) +
                    " error.message:" + error.developer_message + ">";
          return result;
        });
        OnFailure(FIMErrorFrom(error));
      });
}
/**
 * 增加群变更的监听器
 * @param OnSuccess 成功回调
 * @param OnFailure 失败回调
 */
void FIMGroupSerivce::AddGroupChangeListener(
    const std::shared_ptr<FIMGroupChangeListener> &listener) {
  std::shared_ptr<AIMPubGroupService> service = getCurrentGroupService();
  if (service) {
    service->AddGroupChangeListener(listener);
  }
}

/**
 * 删除群变更的监听器
 * @param OnSuccess 成功回调
 * @param OnFailure 失败回调
 */
void FIMGroupSerivce::RemoveGroupChangeListener(
    const std::shared_ptr<FIMGroupChangeListener> &listener) {
  std::shared_ptr<AIMPubGroupService> service = getCurrentGroupService();
  if (service) {
    service->RemoveGroupChangeListener(listener);
  }
}

/**
 * 删除所有会话的监听器
 */
void FIMGroupSerivce::RemoveAllGroupChangeListener() {
  std::shared_ptr<AIMPubGroupService> service = getCurrentGroupService();
  if (service) {
    service->RemoveAllGroupChangeListener();
  }
}

/**
 * 注册会话成员增量变更的监听器
 * @param OnSuccess 成功回调
 * @param OnFailure 失败回调
 */
void FIMGroupSerivce::AddGroupMemberChangeListener(
    const std::shared_ptr<FIMGroupMemberChangeListener> &listener) {
  std::shared_ptr<AIMPubGroupService> service = getCurrentGroupService();
  if (service) {
    service->AddGroupMemberChangeListener(listener);
  }
}

/**
 * 删除会话成员增量变更的监听器
 */
void FIMGroupSerivce::RemoveGroupMemberChangeListener(
    const std::shared_ptr<FIMGroupMemberChangeListener> &listener) {
  std::shared_ptr<AIMPubGroupService> service = getCurrentGroupService();
  if (service) {
    service->RemoveGroupMemberChangeListener(listener);
  }
}

/**
 * 删除所有会话成员的监听器
 */
void FIMGroupSerivce::RemoveAllGroupMemberChangeListener() {
  std::shared_ptr<AIMPubGroupService> service = getCurrentGroupService();
  if (service) {
    service->RemoveAllGroupMemberChangeListener();
  }
}

/**
 * 更新群成员角色
 * @param param AIMGroupMemberUpdateRole 结构
 * @param OnSuccess 成功回调
 * @param OnFailure 失败回调
 */
// void FIMGroupSerivce::UpdateGroupMemberRole(
//     const AIMPubGroupMemberUpdateRole &param,
//     const std::function<void(const std::vector<FIMGroupMember> &members)>
//         &OnSuccess,
//     const std::function<void(const FIMError &error)> &OnFailure);

/**
 * 更新群公告 暂不提供，这里需要走飞猪服务端
 * @param param AIMGroupUpdateAnnouncement 结构
 */
//   void FIMGroupSerivce::UpdateAnnouncement(
//       const AIMPubGroupUpdateAnnouncement &param,
//       const std::function<void()> &OnSuccess,
//       const std::function<void(const FIMError &error)> &OnFailure);

/**
 * 获取群公告
 * @param cid 需要获取群公告的cid
 * @param listener 获取群公告的回调
 */
// void FIMGroupSerivce::GetAnnouncement(
//     const std::string &cid,
//     const std::function<void(const AIMPubGroupAnnouncement &announcement)>
//         &OnSuccess,
//     const std::function<void(const FIMError &error)> &OnFailure);

/**
 * 添加群管理员
 * 调用权限：群主或群管理员
 * @param app_cid 群会话 id
 * @param operator_nick 操作者昵称
 * @param members 要添加的管理员
 * @param OnSuccess 成功回调
 * @param OnFailure 失败回调
 */
// void FIMGroupSerivce::AddAdmins(
//     const std::string app_cid, const std::string operator_nick,
//     std::vector<FIMGroupMember> members, const std::function<void()>
//     &OnSuccess, const std::function<void(const FIMError &error)>
//     &OnFailure);

/**
 * 删除群管理员
 * 调用权限：群主或群管理员
 * @param app_cid 群会话 id
 * @param operator_nick 操作者昵称
 * @param members 要添加的管理员
 * @param OnSuccess 成功回调
 * @param OnFailure 失败回调
 */

/**
 * 查询全量群管理员
 * 调用权限：群主或群管理员
 * @param cid cid 会话cid
 * @param OnSuccess 成功回调
 * @param OnFailure 失败回调
 */
// void FIMGroupSerivce::ListAllAdmins(
//     const std::string &appCid,
//     const std::function<void(const std::vector<FIMGroupMember> &members)>
//         &OnSuccess,
//     const std::function<void(const FIMError &error)> &OnFailure);
} // namespace fim
} // namespace alibaba