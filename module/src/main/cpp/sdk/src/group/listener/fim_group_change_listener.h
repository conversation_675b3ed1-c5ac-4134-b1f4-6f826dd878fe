#ifndef fim_group_change_listener_h
#define fim_group_change_listener_h

#include "fim_conversation.h"
#include "fim_group_announcement.h"
#include "fim_group_role_changed_notify.h"
#include "fliggy_im_sdk.h"
#include "aim_pub_group_change_listener.h"
#include <memory>
#include <string>
#include <vector>
namespace alibaba {
namespace fim {

/**
 * 群变更（增量）
 */
class FIMSDK_API FIMGroupChangeListener: public dps::AIMPubGroupChangeListener {
public:
  virtual ~FIMGroupChangeListener() {}

  /**
   * title变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnGroupTitleChanged(const std::vector<FIMConversation> &convs) = 0;

  /**
   * icon变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnGroupIconChanged(const std::vector<FIMConversation> &convs) = 0;

  /**
   * 群成员数变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnGroupMemberCountChanged(const std::vector<FIMConversation> &convs) = 0;

  /**
   * 群主变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnGroupOwnerChanged(const std::vector<FIMConversation> &convs) = 0;

  /**
   * 群禁言状态变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnGroupSilenceAllChanged(const std::vector<FIMConversation> &convs) = 0;

  /**
   * 群禁言黑白名单状态变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnGroupSilencedStatusChanged(const std::vector<FIMConversation> &convs) = 0;

  /**
   * 群禁言截止时间变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnGroupSilencedEndtimeChanged(const std::vector<FIMConversation> &convs) = 0;

  /**
   * 群成员角色变更
   * @param param 群成员角色变更的详细信息
   */
  virtual void
  OnGroupMemberRoleChanged(const FIMGroupRoleChangedNotify &param) = 0;

  /**
   * 群成员权限变更
   * @param convs 全量的会话结构
   */
  virtual void OnGroupMemberPermissionsChanged(
      const std::vector<FIMConversation> &convs) = 0;

  /**
   * 群公告变更
   * @param appCid 群公告变更的cid
   * @param announcement 公告变更内容
   */
  virtual void
  OnGroupAnnouncementChanged(const std::string &appCid,
                             const FIMGroupAnnouncement &announcement) = 0;

  /**
   * 群已读功能开启变更
   * @param convs 全量的会话结构
   */
  virtual void OnGroupReadReceiptsEnabledChanged(
      const std::vector<FIMConversation> &convs) = 0;

  /**
   * 群管理员变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnGroupAdminChanged(const std::vector<FIMConversation> &convs) = 0;

  /**
   * 群成员数量限制变更
   * @param convs 全量的会话结构
   */
  virtual void
  OnGroupMemberLimitChanged(const std::vector<FIMConversation> &convs) = 0;

private:
  /**
   * title变更
   * @param convs 全量的会话结构
   */
void
  OnGroupTitleChanged(const std::vector<AIMPubConversation> &convs) override;

  /**
   * icon变更
   * @param convs 全量的会话结构
   */
void
  OnGroupIconChanged(const std::vector<AIMPubConversation> &convs) override;

  /**
   * 群成员数变更
   * @param convs 全量的会话结构
   */
void
  OnGroupMemberCountChanged(const std::vector<AIMPubConversation> &convs) override;

  /**
   * 群主变更
   * @param convs 全量的会话结构
   */
void
  OnGroupOwnerChanged(const std::vector<AIMPubConversation> &convs) override;

  /**
   * 群禁言状态变更
   * @param convs 全量的会话结构
   */
void
  OnGroupSilenceAllChanged(const std::vector<AIMPubConversation> &convs) override;

  /**
   * 群禁言黑白名单状态变更
   * @param convs 全量的会话结构
   */
void OnGroupSilencedStatusChanged(
      const std::vector<AIMPubConversation> &convs) override;

  /**
   * 群禁言截止时间变更
   * @param convs 全量的会话结构
   */
void OnGroupSilencedEndtimeChanged(
      const std::vector<AIMPubConversation> &convs) override;

  /**
   * 群成员角色变更
   * @param convs 全量的会话结构
   */
void
  OnGroupMemberRoleChanged(const AIMPubGroupRoleChangedNotify &param) override;

  /**
   * 群成员权限变更
   * @param convs 全量的会话结构
   */
void OnGroupMemberPermissionsChanged(
      const std::vector<AIMPubConversation> &convs) override;
  /**
   * 群公告变更
   * @param cid 群公告变更的cid
   * @param announcement 公告变更内容
   */
void
  OnGroupAnnouncementChanged(const std::string &appCid,
                             const AIMPubGroupAnnouncement &announcement) override;

  /**
   * 群已读功能开启变更
   * @param convs 全量的会话结构
   */
void OnGroupReadReceiptsEnabledChanged(
      const std::vector<AIMPubConversation> &convs) override;

  /**
   * 群管理员变更
   * @param convs 全量的会话结构
   */
void
  OnGroupAdminChanged(const std::vector<AIMPubConversation> &convs) override;

  /**
   * 群成员数量限制变更
   * @param convs 全量的会话结构
   */
void
  OnGroupMemberLimitChanged(const std::vector<AIMPubConversation> &convs) override;
};

} // namespace fim
} // namespace alibaba

#endif /* fim_group_change_listener_h */
