#ifndef fim_group_member_change_listener_h
#define fim_group_member_change_listener_h

#include "aim_pub_group_member_change_listener.h"
#include "fim_group_member.h"
#include "fliggy_im_sdk.h"
#include <memory>
#include <vector>

namespace alibaba {
namespace fim {

class FIMSDK_API FIMGroupMemberChangeListener
    : public dps::AIMPubGroupMemberChangeListener {
public:
  virtual ~FIMGroupMemberChangeListener() {}

  /**
   * 新增成员
   * @param members 成员
   */
  virtual void OnAddedMembers(const std::vector<FIMGroupMember> &members) = 0;

  /**
   * 删除成员
   * @param members 成员
   */
  virtual void OnRemovedMembers(const std::vector<FIMGroupMember> &members) = 0;

  /**
   * 成员变更
   * @param members 成员
   */
  virtual void OnUpdatedMembers(const std::vector<FIMGroupMember> &members) = 0;


private:
  /**
   * 新增成员
   * @param members 成员
   */
  void OnAddedMembers(const std::vector<dps::AIMPubGroupMember> &members) override;

  /**
   * 删除成员
   * @param members 成员
   */
  void OnRemovedMembers(const std::vector<dps::AIMPubGroupMember> &members) override;

  /**
   * 成员变更
   * @param members 成员
   */
  void OnUpdatedMembers(const std::vector<dps::AIMPubGroupMember> &members) override;
};

} // namespace fim
} // namespace alibaba

#endif /* fim_group_member_change_listener_h */
