#include "fim_group_change_listener.h"
#include "aim_pub_conversation.h"
#include "aim_pub_group_change_listener.h"
#include "fim_conversation.h"
#include "fim_conversation.hpp"
#include "fim_group_announcement.hpp"
#include "fim_group_role_changed_notify.hpp"
#include <memory>

namespace alibaba {
namespace fim {

using namespace alibaba::dps;

void FIMGroupChangeListener::OnGroupTitleChanged(
    const std::vector<AIMPubConversation> &convs) {

  std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
  OnGroupTitleChanged(fimConvs);
}

void FIMGroupChangeListener::OnGroupIconChanged(
    const std::vector<AIMPubConversation> &convs) {

  std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
  OnGroupIconChanged(fimConvs);
}

void FIMGroupChangeListener::OnGroupMemberCountChanged(
    const std::vector<AIMPubConversation> &convs) {

  std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
  OnGroupMemberCountChanged(fimConvs);
}

void FIMGroupChangeListener::OnGroupOwnerChanged(
    const std::vector<AIMPubConversation> &convs) {

  std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
  OnGroupOwnerChanged(fimConvs);
}

void FIMGroupChangeListener::OnGroupSilenceAllChanged(
    const std::vector<AIMPubConversation> &convs) {

  std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
  OnGroupSilenceAllChanged(fimConvs);
}

void FIMGroupChangeListener::OnGroupSilencedStatusChanged(
    const std::vector<AIMPubConversation> &convs) {

  std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
  OnGroupSilencedStatusChanged(fimConvs);
}

void FIMGroupChangeListener::OnGroupSilencedEndtimeChanged(
    const std::vector<AIMPubConversation> &convs) {

  std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
  OnGroupSilencedEndtimeChanged(fimConvs);
}

void FIMGroupChangeListener::OnGroupMemberRoleChanged(
    const AIMPubGroupRoleChangedNotify &param) {

  FIMGroupRoleChangedNotify fimNotify = FIMGroupRoleChangedNotifyFrom(param);
  OnGroupMemberRoleChanged(fimNotify);
}

void FIMGroupChangeListener::OnGroupMemberPermissionsChanged(
    const std::vector<AIMPubConversation> &convs) {

  std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
  OnGroupMemberPermissionsChanged(fimConvs);
}


void FIMGroupChangeListener::OnGroupAnnouncementChanged(
    const std::string &appCid, const AIMPubGroupAnnouncement &announcement) {

  FIMGroupAnnouncement fim = FIMGroupAnnouncementFrom(announcement);
  OnGroupAnnouncementChanged(appCid, fim);
}

void FIMGroupChangeListener::OnGroupReadReceiptsEnabledChanged(
    const std::vector<AIMPubConversation> &convs) {

  std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
  OnGroupReadReceiptsEnabledChanged(fimConvs);
}

void FIMGroupChangeListener::OnGroupAdminChanged(
    const std::vector<AIMPubConversation> &convs) {

  std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
  OnGroupAdminChanged(fimConvs);
}

void FIMGroupChangeListener::OnGroupMemberLimitChanged(
    const std::vector<AIMPubConversation> &convs) {

  std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
  OnGroupMemberLimitChanged(fimConvs);
}

} // namespace fim
} // namespace alibaba
