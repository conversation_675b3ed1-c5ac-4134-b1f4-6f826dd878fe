//
// fim_group_member_change_listener.cpp
// FliggyIMSDK
//
// Created on 2024/2/19.
//

#include "fim_group_member_change_listener.h"
#include "aim_pub_group_member.h"
#include "aim_pub_group_member_change_listener.h"
#include "fim_group_member.hpp"
#include <memory.h>
#include <stdio.h>
#include <vector>

namespace alibaba {
namespace fim {

using namespace alibaba::dps;

  

  void FIMGroupMemberChangeListener::OnAddedMembers(const std::vector<AIMPubGroupMember> &members) {
    std::vector<FIMGroupMember> fimMembers = FIMGroupMemberListFrom(members);
    OnAddedMembers(fimMembers);
  }

  void FIMGroupMemberChangeListener::OnRemovedMembers(const std::vector<AIMPubGroupMember> &members) {
    std::vector<FIMGroupMember> fimMembers = FIMGroupMemberListFrom(members);
    OnRemovedMembers(fimMembers);
  }

  void FIMGroupMemberChangeListener::OnUpdatedMembers(const std::vector<AIMPubGroupMember> &members) {
    std::vector<FIMGroupMember> fimMembers = FIMGroupMemberListFrom(members);
    OnUpdatedMembers(fimMembers);
  }
} // namespace fim
} // namespace alibaba
