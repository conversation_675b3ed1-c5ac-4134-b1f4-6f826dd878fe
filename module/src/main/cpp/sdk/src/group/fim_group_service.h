
#pragma once

#include "fim_conversation.h"
#include "fim_error.h"
#include "fim_group_change_listener.h"
#include "fim_group_member_change_listener.h"
#include "fim_group_permission.h"
#include "fim_group_user_info.h"
#include "fliggy_im_sdk.h"
#include <string>
#include <vector>

namespace alibaba {
namespace dps {
struct AIMPubGroupSilencedInfo;
struct AIMPubGroupSetMemberPermission;
struct AIMPubGroupAnnouncement;
struct AIMPubGroupMemberUpdateRole;
} // namespace dps
namespace fim {
struct FIMGroupMember;

class FIMSDK_API FIMGroupSerivce {
public:
  /**
   * 设置全员禁言
   * 调用权限：群主或群管理员
   * 禁言后仅群主和管理员可发言
   * @param param AIMGroupUpdateSilenceAll 结构
   * @param listener 监听器
   */
  static void
  SilenceAll(const std::string &app_cid, const std::function<void()> &OnSuccess,
             const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 取消全员禁言
   * 调用权限：群主或群管理员
   * @param param AIMGroupUpdateSilenceAll 结构
   * @param listener 监听器
   */
  static void
  CancelSilenceAll(const std::string &app_cid,
                   const std::function<void()> &OnSuccess,
                   const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 增加白名单用户(在设置全员禁言后，可以设置白名单，白名单中的用户可以发言)
   * 调用权限：群主或群管理员,
   * @param param AIMGroupUpdateSilencedWhiteList 结构
   * @param listener 监听器
   */
  static void AddSilencedWhitelist(
      const std::string &app_cid, const std::vector<FIMGroupUserInfo> &members,
      const std::function<void()> &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);
  /**
   * 增加禁言用户
   * 调用权限：群主或群管理员
   * @param param AIMGroupUpdateSilencedBlackList 结构
   * @param listener 监听器
   */
  static void AddSilencedBlacklist(
      const std::string &app_cid, const std::vector<FIMGroupUserInfo> &members,
      int64_t duration, const std::function<void()> &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 删除禁言用户
   * 调用权限：群主或群管理员
   * @param param AIMGroupUpdateSilencedBlackList 结构
   * @param listener 监听器
   */
  static void RemoveSilencedBlacklist(
      const std::string &app_cid, const std::vector<FIMGroupUserInfo> &members,
      const std::function<void()> &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);
  /**
   * 添加群管理员
   * 调用权限：群主或群管理员
   * @param param AIMGroupUpdateAdmins 结构
   * @param listener 监听器
   */
  static void
  AddAdmins(const std::string &app_cid,
            const std::vector<std::string> &fuidList,
            const std::function<void(const std::string &body)> &OnSuccess, 
            const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 删除群管理员
   * 调用权限：群主或群管理员
   * @param param AIMGroupUpdateAdmins 结构
   * @param listener 监听器
   */
  static void
  RemoveAdmins(const std::string &app_cid,
               const std::vector<std::string> &fuidList,
               const std::function<void(const std::string &body)> &OnSuccess,
               const std::function<void(const FIMError &error)> &OnFailure);
  /**
   * 解散群
   * @param appCid 会话appCid
   * @param listener 监听器
   */
  static void
  Dismiss(const std::string &app_cid, const std::function<void()> &OnSuccess,
          const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 退出群
   * @param param AIMGroupLeave 结构
   * @param listener 监听器
   */
  static void
  Leave(const std::string app_cid, const std::function<void()> &OnSuccess,
        const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 删除群成员
   * @param param AIMGroupKick 结构
   * @param listener 监听器
   */
  static void
  RemoveMembers(const std::string &app_cid,
               const std::vector<FIMGroupUserInfo> &members,
                const std::function<void()> &OnSuccess,
                const std::function<void(const FIMError &error)>
                    &OnFailure);
  /**
   * 增加群变更的监听器
   * @param OnSuccess 成功回调
   * @param OnFailure 失败回调
   */
  static void AddGroupChangeListener(
      const std::shared_ptr<FIMGroupChangeListener> &listener);

  /**
   * 删除群变更的监听器
   * @param OnSuccess 成功回调
   * @param OnFailure 失败回调
   */
  static void RemoveGroupChangeListener(
      const std::shared_ptr<FIMGroupChangeListener> &listener);

  /**
   * 删除所有会话的监听器
   */
  static void RemoveAllGroupChangeListener();

  /**
   * 注册会话成员增量变更的监听器
   * @param OnSuccess 成功回调
   * @param OnFailure 失败回调
   */
  static void AddGroupMemberChangeListener(
      const std::shared_ptr<FIMGroupMemberChangeListener> &listener);

  /**
   * 删除会话成员增量变更的监听器
   * @param OnSuccess 成功回调
   * @param OnFailure 失败回调
   */
  static void RemoveGroupMemberChangeListener(
      const std::shared_ptr<FIMGroupMemberChangeListener> &listener);

  /**
   * 删除所有会话成员的监听器
   */
  static void RemoveAllGroupMemberChangeListener();
};

} // namespace fim
} // namespace alibaba
