#pragma once
#include "fim_group_announcement.h"
#include "aim_pub_group_announcement.h"

namespace alibaba {
namespace fim {

using namespace alibaba::dps;

inline FIMGroupAnnouncement FIMGroupAnnouncementFrom(const AIMPubGroupAnnouncement& aimAnnouncement) {
  return FIMGroupAnnouncement(
    aimAnnouncement.announcement,
    aimAnnouncement.operator_uid,
    aimAnnouncement.modify_time
  );
}

inline AIMPubGroupAnnouncement AIMPubGroupAnnouncementFrom(const FIMGroupAnnouncement& fimAnnouncement) {
  return AIMPubGroupAnnouncement(
    fimAnnouncement.announcement,
    fimAnnouncement.operator_uid,
    fimAnnouncement.modify_time
  );
}

} // namespace fim
} // namespace alibaba
