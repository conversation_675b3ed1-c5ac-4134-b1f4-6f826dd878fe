#pragma once
#include "fim_group_member_role.h"
#include <cstdint>
#include <map>
#include <string>

namespace alibaba {
namespace fim {

struct FIMGroupMember final {
  /**
   * 群id
   */
  std::string appCid;
  /**
   * 用户id
   */
  std::string uid;
  /**
   * 用户角色
   */
  FIMGroupMemberRole role;
  /**
   * 创建时间
   */
  int64_t created_at = 0;
  /**
   * 群昵称
   */
  std::string group_nick;
  /**
   *扩展信息
   */
  std::map<std::string, std::string> extension;

  FIMGroupMember(std::string appCid_, std::string uid_,
                 FIMGroupMemberRole role_, int64_t created_at_,
                 std::string group_nick_,
                 std::map<std::string, std::string> extension_)
      : appCid(std::move(appCid_)), uid(std::move(uid_)),
        role(std::move(role_)), created_at(std::move(created_at_)),
        group_nick(std::move(group_nick_)), extension(std::move(extension_)) {}

  FIMGroupMember() {}
};

} // namespace fim
} // namespace alibaba
