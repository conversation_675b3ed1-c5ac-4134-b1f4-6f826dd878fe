#pragma once
#include "fim_group_member_role.h"
#include "aim_group_member_role.h"
#include "fim_group_member_role.hpp"

namespace alibaba {
namespace fim {
using namespace alibaba::dps;

inline FIMGroupMemberRole FIMGroupMemberRoleFrom(const AIMGroupMemberRole& aimGroupMemberRole) {
  return FIMGroupMemberRole(
    static_cast<FIMGroupMemberRoleType>(aimGroupMemberRole.role),
    aimGroupMemberRole.custom_role
  );
}

inline AIMGroupMemberRole AIMGroupMemberRoleFrom(const FIMGroupMemberRole& fimGroupMemberRole) {
  return AIMGroupMemberRole {
    static_cast<AIMGroupMemberRoleType>(fimGroupMemberRole.role),
    fimGroupMemberRole.custom_role
  };
}

} // namespace fim
} // namespace alibaba
