#pragma once
#include "fim_group_user_info.h"
#include "aim_pub_group_user_info.h"
#include <vector>

namespace alibaba {
namespace fim {

inline FIMGroupUserInfo FIMGroupUserInfoFrom(const dps::AIMPubGroupUserInfo& aimUserInfo) {
  return FIMGroupUserInfo(
    aimUserInfo.uid,
    aimUserInfo.nick_name,
    aimUserInfo.extension
  );
}

inline dps::AIMPubGroupUserInfo AIMPubGroupUserInfoFrom(const FIMGroupUserInfo& fimUserInfo) {
  return dps::AIMPubGroupUserInfo(
    fimUserInfo.uid,
    fimUserInfo.nick_name,
    fimUserInfo.extension
  );
}

inline std::vector<dps::AIMPubGroupUserInfo>  AIMPubGroupUserInfoListFrom(const std::vector<FIMGroupUserInfo>& fimUserInfos) {
  std::vector<dps::AIMPubGroupUserInfo> aimUserInfos;
  for (const auto& fimUserInfo : fimUserInfos) {
    aimUserInfos.push_back(AIMPubGroupUserInfoFrom(fimUserInfo));
  }
  return aimUserInfos;
}

} // namespace fim
} // namespace alibaba
