#pragma once
#include "fim_group_role_changed_notify.h"
#include "aim_pub_group_role_changed_notify.h"
#include "fim_group_member_role.hpp"

namespace alibaba {
namespace fim {

using namespace alibaba::dps;

inline FIMGroupRoleChangedNotify FIMGroupRoleChangedNotifyFrom(const AIMPubGroupRoleChangedNotify& aimNotify) {
  return FIMGroupRoleChangedNotify(
    aimNotify.appCid,
    FIMGroupMemberRoleFrom(aimNotify.role),
    aimNotify.uids
  );
}

inline AIMPubGroupRoleChangedNotify AIMPubGroupRoleChangedNotifyFrom(const FIMGroupRoleChangedNotify& fimNotify) {
  return AIMPubGroupRoleChangedNotify(
    fimNotify.appCid,
    AIMGroupMemberRoleFrom(fimNotify.role),
    fimNotify.uids
  );
}

} // namespace fim
} // namespace alibaba
