#pragma once
#include "aim_pub_group_member.h"
#include "fim_group_member.h"
#include "fim_group_member_role.hpp"
#include <vector>

namespace alibaba {
namespace fim {

inline FIMGroupMember
FIMGroupMemberFrom(const dps::AIMPubGroupMember &aimGroupMember) {
  return FIMGroupMember(aimGroupMember.appCid, aimGroupMember.uid,
                        FIMGroupMemberRoleFrom(aimGroupMember.role),
                        aimGroupMember.created_at, aimGroupMember.group_nick,
                        aimGroupMember.extension);
}

inline dps::AIMPubGroupMember
AIMPubGroupMemberFrom(const FIMGroupMember &fimGroupMember) {
  return dps::AIMPubGroupMember(
      fimGroupMember.appCid, fimGroupMember.uid,
      AIMGroupMemberRoleFrom(fimGroupMember.role), fimGroupMember.created_at,
      fimGroupMember.group_nick, fimGroupMember.extension);
}

inline std::vector<FIMGroupMember> FIMGroupMemberListFrom(
    const std::vector<dps::AIMPubGroupMember> &aimGroupMemberList) {
  std::vector<FIMGroupMember> fimGroupMemberList;
  fimGroupMemberList.reserve(aimGroupMemberList.size());
  for (const auto &aimGroupMember : aimGroupMemberList) {
    fimGroupMemberList.push_back(FIMGroupMemberFrom(aimGroupMember));
  }
  return fimGroupMemberList;
}

} // namespace fim
} // namespace alibaba
