#pragma once

#include "fliggy_im_sdk.h"
#include <cstdint>
#include <utility>

namespace alibaba {
namespace fim {

enum class FIMGroupMemberRoleType : int {
  /**
   * 未知类型
   */
  GROUP_MEMBER_ROLE_TYPE_UNKNOW = 0,
  /**
   * 群主
   */
  GROUP_MEMBER_ROLE_TYPE_OWNER = 1,
  /**
   * 管理员
   */
  GROUP_MEMBER_ROLE_TYPE_ADMIN = 2,
  /**
   * 普通
   */
  GROUP_MEMBER_ROLE_TYPE_NORMAL = 3,
  /**
   * 自定义角色
   */
  GROUP_MEMBER_ROLE_TYPE_CUSTOM = 100,
};

/**
 * 群成员角色
 */
struct FIMSDK_API FIMGroupMemberRole final {

  /**
   * 角色类型
   */
  FIMGroupMemberRoleType role =
      FIMGroupMemberRoleType::GROUP_MEMBER_ROLE_TYPE_UNKNOW;
  /**
   * 当 role 为业务自定义类型时，使用该字段获取
   */
  int32_t custom_role = 0;

  FIMGroupMemberRole(FIMGroupMemberRoleType role_, int32_t custom_role_)
      : role(std::move(role_)), custom_role(std::move(custom_role_)) {}

  FIMGroupMemberRole() {}
};

} // namespace dps
} // namespace alibaba
