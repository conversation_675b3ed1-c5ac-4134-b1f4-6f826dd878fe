#pragma once
#include "fim_group_member_role.h"
#include <string>
#include <vector>

namespace alibaba {
namespace fim {

/**
 * 群成员角色变更
 */
struct FIMGroupRoleChangedNotify final {
  /**
   * 群appCid
   */
  std::string appCid;
  /**
   * 角色
   */
  FIMGroupMemberRole role;
  /**
   * 变更成员的uid
   */
  std::vector<std::string> uids;
  FIMGroupRoleChangedNotify(std::string appCid_, FIMGroupMemberRole role_,
                            std::vector<std::string> uids_)
      : appCid(std::move(appCid_)), role(std::move(role_)),
        uids(std::move(uids_)) {}
  FIMGroupRoleChangedNotify() {}
};

} // namespace fim
} // namespace alibaba
