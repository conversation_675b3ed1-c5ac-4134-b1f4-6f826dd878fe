//
//  fim_bridge_event_center.cpp
//  FliggyIM
//
//  Created by 蒙晨 on 2024/8/6.
//

#include "fim_fbridge.h"
#include "fim_message_c_listener.h"
//FIMMessageService
#include "fim_message_service.h"
#include <mutex>

namespace alibaba {
namespace fim {

FIMFBridge& FIMFBridge::GetInstance() {
    static FIMFBridge instance;
    return instance;
}

void FIMFBridge::RegisterEventDispatcher(std::unique_ptr<FIMDispathEventToFlutter> dispatcher) {
    std::lock_guard<std::mutex> lock(m_mutex);
    if (!m_flutterEventDispatcher) {
        m_flutterEventDispatcher = std::move(dispatcher);
    }
}

void FIMFBridge::DispatchEvent(const std::string& bizName, const std::string& methodName, const nlohmann::json& args) {
    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_flutterEventDispatcher) {
        m_flutterEventDispatcher->DispatchEvent(bizName, methodName, args);
    }
}

void FIMFBridge::DispatchEvent(const std::string& methodName, const nlohmann::json& args) {
    DispatchEvent("fliggy_im", methodName, args);
}

bool FIMFBridge::AddMessageListener() {
    std::lock_guard<std::mutex> lock(m_mutex);
    if (!g_messageListener) {
        g_messageListener = std::make_shared<FIMMessageListener_C>();
    }
    return FIMMessageService::AddMsgListener(g_messageListener);
}

bool FIMFBridge::RemoveMessageListener() {
    std::lock_guard<std::mutex> lock(m_mutex);
    if (g_messageListener) {
        return FIMMessageService::RemoveMsgListener(g_messageListener);
    }
    return false;
}

bool FIMFBridge::AddMessageChangeListener() {
    std::lock_guard<std::mutex> lock(m_mutex);
    if (!g_messageChangeListener) {
        g_messageChangeListener = std::make_shared<FIMMessageChangeListener_C>();
    }
    return FIMMessageService::AddMsgChangeListener(g_messageChangeListener);
}

bool FIMFBridge::RemoveMessageChangeListener() {
    std::lock_guard<std::mutex> lock(m_mutex);
    if (g_messageChangeListener) {
        return FIMMessageService::RemoveMsgChangeListener(g_messageChangeListener);
    }
    return false;
}

} // namespace fim
} // namespace alibaba
