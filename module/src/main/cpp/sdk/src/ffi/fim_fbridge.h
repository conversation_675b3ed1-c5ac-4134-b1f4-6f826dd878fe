//
//  fim_bridge_event_center.hpp
//  FliggyIM
//
//  Created by 蒙晨 on 2024/8/6.
//

#ifndef fim_fbridge_h
#define fim_fbridge_h

#include <string>
#include <vector>
#include <memory>
#include "fim_dispatch_event_to_flutter.h"
#include "fliggy_im_sdk.h"
#include "fim_message_c_listener.h"

namespace alibaba {
namespace fim {

class FIMSDK_API FIMFBridge {
public:
    static FIMFBridge &GetInstance();
    // 删除拷贝构造函数和赋值操作符
    FIMFBridge(const FIMFBridge&) = delete;
    FIMFBridge& operator=(const FIMFBridge&) = delete;

    // 注册 FIMDispathEventToFlutter
    void RegisterEventDispatcher(std::unique_ptr<FIMDispathEventToFlutter> dispatcher);

    // 静态方法用于分发事件
    void DispatchEvent(const std::string& methodName, const nlohmann::json& args);
    
    bool AddMessageListener();
    
    bool RemoveMessageListener();
    
    bool AddMessageChangeListener();
    
    bool RemoveMessageChangeListener();

private:
    FIMFBridge() = default;
    ~ FIMFBridge() = default;
    std::mutex m_mutex;
    std::unique_ptr<FIMDispathEventToFlutter> m_flutterEventDispatcher;
    std::shared_ptr<FIMMessageListener_C> g_messageListener;
    std::shared_ptr<FIMMessageChangeListener_C> g_messageChangeListener;

    void DispatchEvent(const std::string& bizName, const std::string& methodName, const nlohmann::json& args);
};

} // namespace fim
} // namespace alibaba

#endif

