#ifndef FIM_MESSAGE_SERVICE_C_API_H
#define FIM_MESSAGE_SERVICE_C_API_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>

// 简化的结构体定义
typedef struct FIMMessage FIMMessage;
typedef struct FIMError FIMError;
typedef struct FIMSendMessageBase FIMSendMessageBase;
typedef struct FIMMessageReSendMessage FIMMessageReSendMessage;
typedef struct AIMMsgFilter AIMMsgFilter;
typedef struct AIMPubMsgLocalExtensionUpdateInfo AIMPubMsgLocalExtensionUpdateInfo;
typedef struct AIMPubMsgBizUpdateInfo AIMPubMsgBizUpdateInfo;
typedef struct AIMPubMsgReadStatus AIMPubMsgReadStatus;
typedef struct FIMMessageListener FIMMessageListener;
typedef struct FIMMessageChangeListener FIMMessageChangeListener;
typedef struct FIMMessageSendReplyMessage FIMMessageSendReplyMessage;
typedef struct FIMMessageSendForwardMessage FIMMessageSendForwardMessage;

// 枚举定义
typedef enum {
    FIM_IMAGE_FILE_TYPE_UNKNOWN = -1,
    FIM_IMAGE_FILE_TYPE_WEBP = 1,
    FIM_IMAGE_FILE_TYPE_PNG = 2,
    FIM_IMAGE_FILE_TYPE_JPG = 3,
    FIM_IMAGE_FILE_TYPE_GIF = 4,
} FIMMsgImageFileType;

// 回调函数类型定义
typedef void (*OnProgressCallback)(double progress);
typedef void (*OnSuccessCallback)(const FIMMessage* msg);
typedef void (*OnFailureCallback)(const FIMError* error);
typedef void (*OnSuccessVoidCallback)(void);
typedef void (*OnSuccessMessagesCallback)(const FIMMessage* msgs, int msgs_size, bool has_more);
typedef void (*OnFailureMessagesCallback)(const FIMMessage** msgs, int msgs_dimensions, const FIMError* error);

// 函数声明
void FIM_SendMessage(const FIMSendMessageBase* msg, OnProgressCallback on_progress, OnSuccessCallback on_success, OnFailureCallback on_failure);

void FIM_ListPreviousLocalMsgs(const char* appCid, int64_t cursor, int32_t count, OnSuccessMessagesCallback on_success, OnFailureCallback on_failure);

void FIM_ListNextLocalMsgs(const char* appCid, int64_t cursor, int32_t count, OnSuccessMessagesCallback on_success, OnFailureCallback on_failure);

void FIM_ListNextMsgs(const char* appCid, int64_t cursor, int32_t count, OnSuccessMessagesCallback on_success, OnFailureMessagesCallback on_failure);

void FIM_ListPreviousMsgs(const char* appCid, int64_t cursor, int32_t count, OnSuccessMessagesCallback on_success, OnFailureMessagesCallback on_failure);

void FIM_ResendMessage(const FIMMessageReSendMessage* resend_msg, OnProgressCallback on_progress, OnSuccessCallback on_success, OnFailureCallback on_failure);

void FIM_SendMessageTolocal(const FIMSendMessageBase* msg, OnSuccessCallback on_success, OnFailureCallback on_failure);

void FIM_GetMessage(const char* appCid, const char* mid, OnSuccessCallback on_success, OnFailureCallback on_failure);

void FIM_GetLocalMessage(const char* appCid, const char* local_id, OnSuccessCallback on_success, OnFailureCallback on_failure);

void FIM_GetLocalMessages(const char* appCid, int64_t cursor, int32_t count, bool forward, const AIMMsgFilter* filter, OnSuccessMessagesCallback on_success, OnFailureCallback on_failure);

void FIM_DeleteMessage(const char* appCid, const char** mids, int mids_size, OnSuccessVoidCallback on_success, OnFailureCallback on_failure);

void FIM_DeleteLocalMessage(const char* appCid, const char** local_ids, int local_ids_size, OnSuccessVoidCallback on_success, OnFailureCallback on_failure);

void FIM_RecallMessage(const char* appCid, const char* mid, OnSuccessVoidCallback on_success, OnFailureCallback on_failure);

void FIM_UpdateLocalExtension(const AIMPubMsgLocalExtensionUpdateInfo* update_infos, int update_infos_size, OnSuccessVoidCallback on_success, OnFailureCallback on_failure);

void FIM_UpdateLocalExtensionByKey(const AIMPubMsgLocalExtensionUpdateInfo* update_infos, int update_infos_size, OnSuccessVoidCallback on_success, OnFailureCallback on_failure);

void FIM_UpdateLocalMessagesBizInfo(const AIMPubMsgBizUpdateInfo* update_infos, int update_infos_size, OnSuccessVoidCallback on_success, OnFailureCallback on_failure);

void FIM_ListMessagesReadStatus(const char* appCid, const char* mid, void (*on_success)(const AIMPubMsgReadStatus* status), OnFailureCallback on_failure);

void FIM_UpdateMessageToRead(const char* appCid, const char** mids, int mids_size);

int64_t FIM_GenerateMsgLocalId(void);

bool FIM_AddMsgListener(const FIMMessageListener* listener);

bool FIM_RemoveMsgListener(const FIMMessageListener* listener);

void FIM_RemoveAllMsgListener(void);

bool FIM_AddMsgChangeListener(const FIMMessageChangeListener* listener);

bool FIM_RemoveMsgChangeListener(const FIMMessageChangeListener* listener);

void FIM_RemoveAllMsgChangeListener(void);

void FIM_ReplyMessage(const FIMMessageSendReplyMessage* msg, OnProgressCallback on_progress, OnSuccessCallback on_success, OnFailureCallback on_failure);

void FIM_ForwardMessage(const FIMMessageSendForwardMessage* msg, OnSuccessVoidCallback on_success, OnFailureCallback on_failure);

void FIM_CombineForwardMessage(const FIMMessageSendForwardMessage* msg, OnSuccessCallback on_success, OnFailureCallback on_failure);

void FIM_SetNeedReceivers(bool need);

#ifdef __cplusplus
}
#endif

#endif // FIM_MESSAGE_SERVICE_C_API_H
