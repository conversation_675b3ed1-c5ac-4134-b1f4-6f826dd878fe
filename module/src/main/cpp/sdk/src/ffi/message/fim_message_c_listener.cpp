//
//  fim_message_c_listener.cpp
//  FliggyIM
//
//  Created by 蒙晨 on 2024/8/6.
//

#include "fim_message_c_listener.h"
#include <vector>
#include <string>
#include <functional>
#include <mutex>
#include <thread>
#include <queue>
#include <condition_variable>
#include "fim_fbridge.h"
#include "fim_message_2_json.h"

namespace alibaba {
namespace fim {


void FIMMessageListener_C::OnAddedMessages(const std::vector<FIMNewMessage> &msgs) {
    nlohmann::json j;
    j["messages"] = nlohmann::json::array();
    for (const FIMNewMessage& msg : msgs) {
        j["messages"].push_back(fimNewMessage2Json(msg));
    }
    FIMFBridge::GetInstance().DispatchEvent("OnAddedMessages", j);
}

void FIMMessageListener_C::OnRemovedMessages(const std::vector<FIMMessage> &msgs) {
    nlohmann::json j;
    j["messages"] = nlohmann::json::array();
    for (const auto& msg : msgs) {
        j["messages"].push_back(fimMessage2Json(msg));
    }
    FIMFBridge::GetInstance().DispatchEvent("OnRemovedMessages", j);
}

void FIMMessageListener_C::OnStoredMessages(const std::vector<FIMMessage> &msgs) {
    nlohmann::json j;
    j["messages"] = nlohmann::json::array();
    for (const auto& msg : msgs) {
        j["messages"].push_back(fimMessage2Json(msg));
    }
    FIMFBridge::GetInstance().DispatchEvent("OnStoredMessages", j);
}



/**
   * 消息未读数变更，作为消息的发送者，表示单聊对方或者群聊群内其他成员
   * 没有读取该条消息的人数，如果未读数是0，表示所有人已读
   * @param msgs 发生变化的消息(有效字段appCid/mid/unread_count）
   */
void FIMMessageChangeListener_C::OnMsgUnreadCountChanged(const std::vector<FIMMessage> &msgs) {
    nlohmann::json j;
    j["messages"] = nlohmann::json::array();
    for (const auto& msg : msgs) {
        j["messages"].push_back(fimMessage2Json(msg));
    }
    FIMFBridge::GetInstance().DispatchEvent("OnMsgUnreadCountChanged", j);
}

  /**
   * 消息未读数变更，作为消息的接收者，多端同步消息已读状态
   * @param msgs 发生变化的消息(有效字段appCid/mid/is_read）
   */
void FIMMessageChangeListener_C::OnMsgReadStatusChanged(const std::vector<FIMMessage> &msgs) {
    nlohmann::json j;
    j["messages"] = nlohmann::json::array();
    for (const auto& msg : msgs) {
        j["messages"].push_back(fimMessage2Json(msg));
    }
    FIMFBridge::GetInstance().DispatchEvent("OnMsgReadStatusChanged", j);
}


  /**
   * 消息扩展信息变更
   * @param msgs 发生变化的消息(有效字段appCid/mid/extension)
   */
void FIMMessageChangeListener_C::OnMsgExtensionChanged(const std::vector<FIMMessage> &msgs) {
    nlohmann::json j;
    j["messages"] = nlohmann::json::array();
    for (const auto& msg : msgs) {
        j["messages"].push_back(fimMessage2Json(msg));
    }
    FIMFBridge::GetInstance().DispatchEvent("OnMsgExtensionChanged", j);
}

  /**
   * 消息本地扩展信息变更
   * @param msgs 发生变化的消息(有效字段appCid/localid/local_extension)
   */
void FIMMessageChangeListener_C::OnMsgLocalExtensionChanged(const std::vector<FIMMessage> &msgs) {
    nlohmann::json j;
    j["messages"] = nlohmann::json::array();
    for (const auto& msg : msgs) {
        j["messages"].push_back(fimMessage2Json(msg));
    }
    FIMFBridge::GetInstance().DispatchEvent("OnMsgLocalExtensionChanged", j);
}

  /**
   * 业务方自定义消息扩展信息变更
   * @param msgs 发生变化的消息(有效字段appCid/mid/user_extension字段)
   */
void FIMMessageChangeListener_C::OnMsgUserExtensionChanged(const std::vector<FIMMessage> &msgs) {
    nlohmann::json j;
    j["messages"] = nlohmann::json::array();
    for (const auto& msg : msgs) {
        j["messages"].push_back(fimMessage2Json(msg));
    }
    FIMFBridge::GetInstance().DispatchEvent("OnMsgUserExtensionChanged", j);
}

  /**
   * 消息被撤回
   * @param msgs 发生变化的消息(有效字段appCid/mid/is_recall字段)
   */
void FIMMessageChangeListener_C::OnMsgRecalled(const std::vector<FIMMessage> &msgs) {
    nlohmann::json j;
    j["messages"] = nlohmann::json::array();
    for (const auto& msg : msgs) {
        j["messages"].push_back(fimMessage2Json(msg));
    }
    FIMFBridge::GetInstance().DispatchEvent("OnMsgRecalled", j);
}

  /**
   * 消息状态变更，比如：消息状态从发送中变成了发送失败
   * @param msgs
   * 发生变化的消息(有效字段status/mid/created_at/unread_count/receiver_count/content)
   */
void FIMMessageChangeListener_C::OnMsgStatusChanged(const std::vector<FIMMessage> &msgs) {
    nlohmann::json j;
    j["messages"] = nlohmann::json::array();
    for (const auto& msg : msgs) {
        j["messages"].push_back(fimMessage2Json(msg));
    }
    FIMFBridge::GetInstance().DispatchEvent("OnMsgStatusChanged", j);
}

  /**
   * 消息发送进度变更
   * @param progress 发送进度
   */
void FIMMessageChangeListener_C::OnMsgSendMediaProgressChanged(const AIMMsgSendMediaProgress &progress) {
    nlohmann::json j;
    j["cid"] = progress.cid;
    j["localid"] = progress.localid;
    j["progress"] = progress.progress;
    FIMFBridge::GetInstance().DispatchEvent("OnMsgSendMediaProgressChanged", j);
}

}// namespace fim
}// namespace alibaba
