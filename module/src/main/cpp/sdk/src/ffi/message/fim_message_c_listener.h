//
//  fim_message_c_listener.hpp
//  FliggyIM
//
//  Created by 蒙晨 on 2024/8/6.
//

#ifndef fim_message_c_listener_hpp
#define fim_message_c_listener_hpp
#include "fim_message_service.h"
#include "fliggy_im_sdk.h"
//FIMMessageListener
#include "fim_message_listener.h"

#include <stdio.h>

namespace alibaba {
namespace fim {
// FIMMessageListener 类的实现
class FIMMessageListener_C : public FIMMessageListener {
public:
    /**
     * 消息新增
     * 发送消息或收到推送消息时，触发该回调
     * 当从服务端拉取历史消息时，不会触发该回调
     * @param msgs 新增消息
     */
    virtual void OnAddedMessages(const std::vector<FIMNewMessage> &msgs) override;
    
    /**
     * 消息删除
     * @param msgs 变更消息
     */
    virtual void OnRemovedMessages(const std::vector<FIMMessage> &msgs) override;
    
    /**
     * 当消息数据库内有消息添加时，触发该回调
     * 包括发送，推送及拉取历史消息
     * 注意：
     * 1. 不保证传入消息 msgs 的顺序
     * 2. onStored 回调的顺序也不保证消息组间的顺序
     * @param msgs 变更消息
     */
    virtual void OnStoredMessages(const std::vector<FIMMessage> &msgs) override;
};

class FIMMessageChangeListener_C: public FIMMessageChangeListener {
    /**
     * 消息未读数变更，作为消息的发送者，表示单聊对方或者群聊群内其他成员
     * 没有读取该条消息的人数，如果未读数是0，表示所有人已读
     * @param msgs 发生变化的消息(有效字段appCid/mid/unread_count）
     */
    virtual void
    OnMsgUnreadCountChanged(const std::vector<FIMMessage> &msgs) override;
    
    /**
     * 消息未读数变更，作为消息的接收者，多端同步消息已读状态
     * @param msgs 发生变化的消息(有效字段appCid/mid/is_read）
     */
    virtual void
    OnMsgReadStatusChanged(const std::vector<FIMMessage> &msgs) override;
    
    /**
     * 消息扩展信息变更
     * @param msgs 发生变化的消息(有效字段appCid/mid/extension)
     */
    virtual void
    OnMsgExtensionChanged(const std::vector<FIMMessage> &msgs) override;
    
    /**
     * 消息本地扩展信息变更
     * @param msgs 发生变化的消息(有效字段appCid/localid/local_extension)
     */
    virtual void
    OnMsgLocalExtensionChanged(const std::vector<FIMMessage> &msgs) override;
    
    /**
     * 业务方自定义消息扩展信息变更
     * @param msgs 发生变化的消息(有效字段appCid/mid/user_extension字段)
     */
    virtual void
    OnMsgUserExtensionChanged(const std::vector<FIMMessage> &msgs) override;
    
    /**
     * 消息被撤回
     * @param msgs 发生变化的消息(有效字段appCid/mid/is_recall字段)
     */
    virtual void OnMsgRecalled(const std::vector<FIMMessage> &msgs) override;
    
    /**
     * 消息状态变更，比如：消息状态从发送中变成了发送失败
     * @param msgs
     * 发生变化的消息(有效字段status/mid/created_at/unread_count/receiver_count/content)
     */
    virtual void OnMsgStatusChanged(const std::vector<FIMMessage> &msgs) override;
    
    /**
     * 消息发送进度变更
     * @param progress 发送进度
     */
    virtual void
    OnMsgSendMediaProgressChanged(const AIMMsgSendMediaProgress &progress) override;
};

}
}
#endif /* fim_message_c_listener_hpp */
