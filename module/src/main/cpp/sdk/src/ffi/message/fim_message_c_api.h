//
//  fim_message_c_api.h
//  FliggyIM
//
//  Created by 蒙晨 on 2024/8/5.
//

#ifndef fim_message_c_api_h
#define fim_message_c_api_h

#include "fliggy_im_sdk.h"
#include "fim_method_invoker.h"

namespace alibaba {
namespace fim {


void GetMyFuid(const FIMInvokerResultCallback& OnResult);

void AddMsgListener(const FIMInvokerResultCallback& OnResult);

void RemoveMsgListener(const FIMInvokerResultCallback& OnResult);

//  static void RemoveAllMsgListener();
void RemoveAllMsgListener(const FIMInvokerResultCallback& OnResult);

void AddMsgChangeListener(const FIMInvokerResultCallback& OnResult);

//static bool RemoveMsgChangeListener(
//      const std::shared_ptr<FIMMessageChangeListener> &listener);
void RemoveMsgChangeListener(const FIMInvokerResultCallback& OnResult);

//static void RemoveAllMsgChangeListener();
void RemoveAllMsgChangeListener(const FIMInvokerResultCallback& OnResult);


//extern "C" {

//  static void
//  SendMessage(const FIMSendMessageBase &msg,
//              const std::function<void(double progress)> &OnProgress,
//              const std::function<void(const FIMMessage &msg)> &OnSuccess,
//              const std::function<void(const FIMError &error)>
//                  &OnFailure,
//              const std::map<std::string, std::string> &user_data);
//void SendMessage(const json& args, const FIMInvokerResultCallback& OnResult);
void SendMessage(const json& args, const FIMInvokerResultCallback& OnResult);

//  static void ListPreviousLocalMsgs(
//      const std::string &appCid, int64_t cursor, int32_t count,
//      const std::function<void(const std::vector<FIMMessage> &msgs,
//                               bool has_more)> &OnSuccess,
//      const std::function<void(const FIMError &error)> &OnFailure);
void ListPreviousLocalMsgs(const json& args, const FIMInvokerResultCallback& OnResult);
//
//  /**
//   * 获取下一页特定类型消息，按时间升序排列
//   * 注意：该接口只返回本地连续数据
//   * @param appCid 会话唯一id
//   * @param cursor 消息事件游标,消息的创建时间，如果是最新的开始
//   * AIM_MAX_MSG_CURSOR
//   * @param count  返回的结果数量，最大100
//   * @param listener 监听器
//   */
//  static void ListNextLocalMsgs(
//      const std::string &appCid, int64_t cursor, int32_t count,
//      const std::function<void(const std::vector<FIMMessage> &msgs,
//                               bool has_more)> &OnSuccess,
//      const std::function<void(const FIMError &error)> &OnFailure);
void ListNextLocalMsgs(const json& args, const FIMInvokerResultCallback& OnResult);
//
//  /**
//   * 获取下一页消息，按时间升序排列
//   * @param appCid 会话唯一id
//   * @param cursor 消息事件游标,消息的创建时间，如果是最新的开始
//   * AIM_MAX_MSG_CURSOR
//   * @param count 返回的结果数量，最大100
//   * @param listener 监听器
//   */

//  ListNextMsgs(const std::string &appCid, int64_t cursor, int32_t count,
//               const std::function<void(const std::vector<FIMMessage> &msgs,
//                                        bool has_more)> &OnSuccess,
//               const std::function<
//                   void(const std::vector<std::vector<FIMMessage>> &msgs,
//                        const FIMError &error)> &OnFailure);
void ListNextMsgs(const json& args, const FIMInvokerResultCallback& OnResult);
//
//  /**
//   * 获取上一页消息，按时间升序排列
//   * @param appCid 会话唯一id
//   * @param cursor 消息事件游标,消息的创建时间，如果是最新的开始
//   * AIM_MAX_MSG_CURSOR
//   * @param count 返回的结果数量，最大100
//   * @param listener 监听器
//   */
//  static void ListPreviousMsgs(
//      const std::string &appCid, int64_t cursor, int32_t count,
//      const std::function<void(const std::vector<FIMMessage> &msgs,
//                               bool has_more)> &OnSuccess,
//      const std::function<
//          void(const std::vector<std::vector<FIMMessage>> &msgs,
//               const FIMError &error)> &OnFailure);
void ListPreviousMsgs(const json& args, const FIMInvokerResultCallback& OnResult);
//
//  /**
//   * 重发消息
//   * @param resend_msg 重发消息结构
//   * @param listener 监听器
//   * @param user_data 用户数据
//   */
//  static void
//  ResendMessage(const FIMMessageReSendMessage &resend_msg,
//                const std::function<void(double progress)> &OnProgress,
//                const std::function<void(const FIMMessage &msg)> &OnSuccess,
//                const std::function<void(const FIMError &error)> &OnFailure,
//                const std::map<std::string, std::string> &user_data);
void ResendMessage(const json& args, const FIMInvokerResultCallback& OnResult);
//
//  /**
//   * 发送消息到本地不发送
//   * @param msg 发送的消息对象
//   * @param listener 监听器
//   */
//  static void SendMessageTolocal(
//      const FIMSendMessageBase &msg,
//      const std::function<void(const FIMMessage &msg)> &OnSuccess,
//      const std::function<void(const FIMError &error)>
//          &OnFailure);
void SendMessageTolocal(const json& args, const FIMInvokerResultCallback& OnResult);
//
//  /**
//   * 通过mid获取消息,本地不存在到服务端拉取
//   * @param appCid 会话唯一id
//   * @param mid 消息唯一id
//   * @param listener 监听器
//   */
//  static void
//  GetMessage(const std::string &appCid, const std::string &mid,
//             const std::function<void(const FIMMessage &msg)> &OnSuccess,
//             const std::function<void(const FIMError &error)>
//                 &OnFailure);
void GetMessage(const json& args, const FIMInvokerResultCallback& OnResult);
//
//  /**
//   * 通过mid获取本地消息
//   * @param appCid 会话唯一id
//   * @param local_id 消息本地唯一id
//   * @param listener 监听器
//   */
//  static void GetLocalMessage(
//      const std::string &appCid, const std::string &local_id,
//      const std::function<void(const FIMMessage &msg)> &OnSuccess,
//      const std::function<void(const FIMError &error)>
//          &OnFailure);
void GetLocalMessage(const json& args, const FIMInvokerResultCallback& OnResult);
//
//  /**
//   * 根据条件获取本地消息
//   * 注意：该接口只返回本地数据，且不保证连续
//   * @param appCid 会话唯一id
//   * @param cursor 消息事件游标,消息的创建时间，如果是最新的开始
//   * AIM_MAX_MSG_CURSOR
//   * @param count  返回的结果数量，最大100
//   * @param forward true: cursor 时间更大的数据， false： cursor时间更小的数据
//   * @param filter 过滤条件
//   * @param listener 监听器
//   */
//  static void GetLocalMessages(
//      const std::string &appCid, int64_t cursor, int32_t count, bool forward,
//      const AIMMsgFilter &filter,
//      const std::function<void(const std::vector<FIMMessage> &msgs)>
//          &OnSuccess,
//      const std::function<void(const FIMError &error)>
//          &OnFailure);
void GetLocalMessages(const json& args, const FIMInvokerResultCallback& OnResult);
//
//  /**
//   * 删除消息，消息将从列表中移除，同步入云端
//   * @param appCid 会话唯一id
//   * @param mids 消息id列表
//   * @param listener 监听器
//   */
//static void DeleteMessage(const std::string &appCid, const std::vector<std::string> &mids,
//                const std::function<void()> &OnSuccess,
//                const std::function<void(const FIMError &error)>
//                    &OnFailure);
void DeleteMessage(const json& args, const FIMInvokerResultCallback& OnResult);
//
//  /**
//   * 删除本地消息，云端不同步
//   * @param appCid 会话唯一id
//   * @param local_ids 消息id
//   * @param listener 监听器
//   */
//  static void DeleteLocalMessage(
//      const std::string &appCid, const std::vector<std::string> &local_ids,
//      const std::function<void()> &OnSuccess,
//      const std::function<void(const FIMError &error)>
//          &OnFailure);
void DeleteLocalMessage(const json& args, const FIMInvokerResultCallback& OnResult);
//
//  /**
//   * 撤回一条已发送的消息
//   * @param appCid 会话唯一id
//   * @param mid 消息id
//   * @param listener 监听器
//   */
//  RecallMessage(const std::string &appCid, const std::string &mid,
//                const std::function<void()> &OnSuccess,
//                const std::function<void(const FIMError &error)>
//                    &OnFailure);

void RecallMessage(const json& args, const FIMInvokerResultCallback& OnResult);

//
//  /**
//   * 全量更新本地消息 local extension，不同步到云端
//   * @param update_infos 更新消息Extension信息列表
//   * @param listener 监听器
//   */
//  static void UpdateLocalExtension(
//      const std::vector<AIMPubMsgLocalExtensionUpdateInfo> &update_infos,
//      const std::function<void()> &OnSuccess,
//      const std::function<void(const FIMError &error)>
//          &OnFailure);
void UpdateLocalExtension(const json& args, const FIMInvokerResultCallback& OnResult);
//
//  /**
//   * 根据 key 局部更新本地消息 local extension, 不同步到云端
//   * @param update_infos 更新消息Extension信息列表
//   * @param listener 监听器
//   */
//  static void UpdateLocalExtensionByKey(
//      const std::vector<AIMPubMsgLocalExtensionUpdateInfo> &update_infos,
//      const std::function<void()> &OnSuccess,
//      const std::function<void(const FIMError &error)>
//          &OnFailure);
void UpdateLocalExtensionByKey(const json& args, const FIMInvokerResultCallback& OnResult);
//
//  /**
//   * 更新本地消息 biz 相关字段
//   * @param update_infos 更新消息Biz信息列表
//   * @param listener 监听器
//   */
//  static void UpdateLocalMessagesBizInfo(
//      const std::vector<AIMPubMsgBizUpdateInfo> &update_infos,
//      const std::function<void()> &OnSuccess,
//      const std::function<void(const FIMError &error)>
//          &OnFailure);
void UpdateLocalMessagesBizInfo(const json& args, const FIMInvokerResultCallback& OnResult);
//
//  /**
//   * 获取消息已读状态
//   * @param appCid 会话唯一id
//   * @param mid 消息唯一id
//   * @param listener 监听器
//   */
//  static void ListMessagesReadStatus(
//      const std::string &appCid, const std::string &mid,
//      const std::function<void(const AIMPubMsgReadStatus &status)> &OnSuccess,
//      const std::function<void(const FIMError &error)>
//          &OnFailure);
void ListMessagesReadStatus(const json& args, const FIMInvokerResultCallback& OnResult);
//
//  /**
//   * 读取接收到消息，消息进入已读状态，同步入云端
//   * 人数。
//   * @param appCid 会话唯一id
//   * @param mids 批量消息mid
//   */
//  static void UpdateMessageToRead(const std::string &appCid,
//                                   const std::vector<std::string> &mids);
void UpdateMessageToRead(const json& args, const FIMInvokerResultCallback& OnResult);
//
//  /**
//   * 生成消息localId,本地唯一
//   */
//  virtual int64_t GenerateMsgLocalId();
//void GenerateMsgLocalId(const json& args, const FIMInvokerResultCallback& OnResult);
//
//  /**
//   * 注册消息变动的监听器，如，消息新增、删除、更新
//   * @param listener 消息监听器
//   */
//  static bool
//  AddMsgListener(const std::shared_ptr<FIMMessageListener> &listener);
void AddMsgListener();

//
//  /**
//   * 删除消息的监听器
//   * @param listener 消息监听器
//   */
//  static bool
//  RemoveMsgListener(const std::shared_ptr<FIMMessageListener> &listener);
void RemoveMsgListener();
//
//  /**
//   * 删除所有消息的监听器
//   */
//  static void RemoveAllMsgListener();
void RemoveAllMsgListener();
//
//  /**
//   * 注册消息属性变更的监听器
//   * @param listener 变更监听器
//   */
//
//static bool AddMsgChangeListener(
//      const std::shared_ptr<FIMMessageChangeListener> &listener);
void AddMsgChangeListener();
//
//  /**
//   * 删除消息的属性监听器
//   * @param listener 变更监听器
//   */
//static bool RemoveMsgChangeListener(
//      const std::shared_ptr<FIMMessageChangeListener> &listener);
void RemoveMsgChangeListener();
//
//  /**
//   * 删除所有消息的属性监听器
//   */
//static void RemoveAllMsgChangeListener();
void RemoveAllMsgChangeListener();

//
//  /**
//   * 回复消息
//   * @param msg 发送的回复消息对象
//   * @param listener 监听器
//   */
//  static void
//  ReplyMessage(const FIMMessageSendReplyMessage &msg,
//               const std::function<void(double progress)> &OnProgress,
//               const std::function<void(const FIMMessage &msg)> &OnSuccess,
//               const std::function<void(const FIMError &error)>
//                   &OnFailure,
//               const std::map<std::string, std::string> &user_data);
void ReplyMessage(const json& args, const FIMInvokerResultCallback& OnResult);


//
//  /**
//   * 转发消息
//   * @param msg 发送的消息对象
//   * @param listener 监听器
//   * @param user_data 用户数据（"trace_id"："_trace_id_example"）
//   */
//  static void ForwardMessage(
//      const FIMMessageSendForwardMessage &mgs,
//      const std::function<void()> &OnSuccess,
//      const std::function<void(const FIMError &error)>
//          &OnFailure,
//      const std::map<std::string, std::string> &user_data);
void ForwardMessage(const json& args, const FIMInvokerResultCallback& OnResult);
//
//  /**
//   * 合并转发消息
//   * @param msg 发送的消息对象
//   * @param listener 监听器
//   * @param user_data 用户数据（"trace_id"："_trace_id_example"）
//   */
//  static void CombineForwardMessage(
//      const FIMMessageSendForwardMessage &mgs,
//      const std::function<void(const FIMMessage &msg)> &OnSuccess,
//      const std::function<void(const FIMError &error)>
//          &OnFailure,
//      const std::map<std::string, std::string> &user_data);
void CombineForwardMessage(const json& args, const FIMInvokerResultCallback& OnResult);
//
//  /**
//   * 设置拉消息时是否需要receivers字段，默认为true
//   * @param need 是否需要
//   */
//  static void SetNeedReceivers(bool need);
void SetNeedReceivers(const json& args, const FIMInvokerResultCallback& OnResult);


//}

}// namespace fim
}// namespace alibaba



#endif /* fim_message_c_api_h */
