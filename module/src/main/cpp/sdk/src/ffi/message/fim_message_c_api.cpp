//
//  fim_message_c_api.cpp
//  FliggyIM
//
//  Created by 蒙晨 on 2024/8/5.
//

#include <stdio.h>
#include "fim_message_c_api.h"
#include "fim_message_service.h"
#include "fim_message_2_json.h"
#include "fim_conv_2_json.h"
#include "json_to_pub_msg_send_message.h"
#include "json_to_message_re_send_message.hpp"
#include "json_to_AIMPubMsgLocalExtensionUpdateInfo.hpp"
#include "json_to_AIMPubMsgBizUpdateInfo.hpp"
#include "json_from_AIMPubMsgReadStatus.hpp"
#include "json_AIMPubMsgSendReplyMessage.hpp"
#include "json_AIMPubMsgSendForwardMessage.hpp"
#include "fim_message_c_listener.h"
#include "aim_msg_filter.h"
#include "fim_fbridge.h"
#include "json.hpp"
#include "fim_engine.h"
#include "fim_method_invoker.h"
#include "fim_invoker_macro.h"
#include "fim_message_c_listener.h"

using json = nlohmann::json;
using namespace alibaba::fim;

//std::shared_ptr<FIMMessageListener_C> g_messageListener;
std::shared_ptr<FIMMessageChangeListener_C> g_messageChangeListener;

namespace alibaba {
namespace fim {


void GetMyFuid(const FIMInvokerResultCallback& OnResult) {
    std::string fuid = alibaba::fim::FIMEngine::GetInstance().fuid;
    json j;
    j["fuid"] = fuid;
    std::string result = j.dump();
    OnResult(result);
}

void AddMsgListener(const FIMInvokerResultCallback& OnResult) {
    bool added = FIMFBridge::GetInstance().AddMessageListener();
    CallbackBool(OnResult, added);
}


void RemoveMsgListener(const FIMInvokerResultCallback& OnResult) {
    bool result = FIMFBridge::GetInstance().RemoveMessageListener();
    CallbackBool(OnResult, result);
}

//  static void RemoveAllMsgListener();
void RemoveAllMsgListener(const FIMInvokerResultCallback& OnResult) {
    FIMMessageService::RemoveAllMsgListener();
    CallbackBool(OnResult, true);
}

//static bool AddMsgChangeListener(
//      const std::shared_ptr<FIMMessageChangeListener> &listener);
void AddMsgChangeListener(const FIMInvokerResultCallback& OnResult) {
    bool result = false;
    if (!g_messageChangeListener) {
        g_messageChangeListener = std::make_shared<FIMMessageChangeListener_C>();
        result = FIMMessageService::AddMsgChangeListener(g_messageChangeListener);
    }
    CallbackBool(OnResult, true);
}

//static bool RemoveMsgChangeListener(
//      const std::shared_ptr<FIMMessageChangeListener> &listener);
void RemoveMsgChangeListener(const FIMInvokerResultCallback& OnResult) {
    bool result = false;
    if (g_messageChangeListener) {
        result = FIMMessageService::RemoveMsgChangeListener(g_messageChangeListener);
        g_messageChangeListener.reset();
    }
    CallbackBool(OnResult, result);
}

//static void RemoveAllMsgChangeListener();
void RemoveAllMsgChangeListener(const FIMInvokerResultCallback& OnResult) {
    FIMMessageService::RemoveAllMsgChangeListener();
    CallbackBool(OnResult, true);
}

}// namespace fim

namespace fim {

using namespace alibaba::fim;

void SendMessage(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_OBJECT_ARG(0, message);
    GET_MAP_ARG_WITH_DEFAULT(1, userData);
    alibaba::dps::AIMPubMsgSendMessage messageModel = AIMPubMsgSendMessageFromJson(message);
    FIMMessageService::SendAimMessage(messageModel, [] (double progress) {

    }, [OnResult] (const FIMMessage &msg) {
        HandleResultOrError(OnResult, [&msg] {
            json result = fimMessage2Json(msg);
            return result.dump();
        });
    }, [OnResult] (const FIMError &error) {
        CallbackFIMError(OnResult, error);
    }, userData);
}

void ListPreviousLocalMsgs(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, appCid);
    GET_INT_ARG(1, cursor);
    GET_INT32_ARG(2, count);

    FIMMessageService::ListPreviousLocalMsgs(appCid,
                                             cursor,
                                             count,
                                             [OnResult](const std::vector<FIMMessage> &msgs, bool has_more) {
        HandleResultOrError(OnResult, [msgs, has_more] {
            json result;
            result["has_more"] = has_more;
            result["messages"] = json::array();
            for (const auto &msg : msgs) {
                result["messages"].push_back(fimMessage2Json(msg));
            }
            return result.dump();
        });
    }, [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    });
}

void ListNextLocalMsgs(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, appCid);
    GET_INT_ARG(1, cursor);
    GET_INT32_ARG(2, count);
    FIMMessageService::ListNextLocalMsgs(appCid,
                                        cursor,
                                         count,
                                         [OnResult](const std::vector<FIMMessage> &msgs, bool has_more) {
            HandleResultOrError(OnResult, [msgs, has_more] {
                nlohmann::json result;
                result["has_more"] = has_more;
                result["messages"] = nlohmann::json::array();
                for (const auto &msg : msgs) {
                    result["messages"].push_back(fimMessage2Json(msg));
                }
                return result.dump();
            });

        },
        [OnResult](const FIMError &error) {
            CallbackFIMError(OnResult, error);
        });

}

void ListNextMsgs(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, appCid);
    GET_INT_ARG(1, cursor);
    GET_INT32_ARG(2, count);
    FIMMessageService::ListNextMsgs(appCid,
                                    cursor,
                                    count,
                                    [OnResult](const std::vector<FIMMessage> &msgs, bool has_more) {
            HandleResultOrError(OnResult, [msgs, has_more] {
                nlohmann::json result;
                result["has_more"] = has_more;
                result["messages"] = nlohmann::json::array();
                for (const auto &msg : msgs) {
                    result["messages"].push_back(fimMessage2Json(msg));
                }
                return result.dump();
            });
        },
        [OnResult](const std::vector<std::vector<FIMMessage>> &msgs,
            const FIMError &error) {
            HandleResultOrError(OnResult, [msgs, error] {
                nlohmann::json result;
                nlohmann::json errorJson = fimError2Json(error);
                result["error"] = errorJson;
                result["messages"] = nlohmann::json::array();
                for (const auto &msgsi : msgs) {
                    nlohmann::json msgsJson = nlohmann::json::array();
                    for (const auto &msg : msgsi) {
                        msgsJson.push_back(fimMessage2Json(msg));
                    }
                    result["messages"].push_back(msgsJson);
                }
                return result.dump();
            });
        });

}

void ListPreviousMsgs(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, appCid);
    GET_INT_ARG(1, cursor);
    GET_INT32_ARG(2, count);

    FIMMessageService::ListPreviousMsgs(appCid,
                                             cursor,
                                             count,
        [OnResult](const std::vector<FIMMessage> &msgs, bool has_more) {
            HandleResultOrError(OnResult, [msgs, has_more] {
                nlohmann::json result;
                result["has_more"] = has_more;
                result["messages"] = nlohmann::json::array();
                for (const auto &msg : msgs) {
                    result["messages"].push_back(fimMessage2Json(msg));
                }
                return result.dump();
            });
        },
        [OnResult](const std::vector<std::vector<FIMMessage>> &msgs,
            const FIMError &error) {
            HandleResultOrError(OnResult, [msgs, error] {
                nlohmann::json result;
                nlohmann::json errorJson = fimError2Json(error);
                result["error"] = errorJson;
                result["messages"] = nlohmann::json::array();
                for (const auto &msgsi : msgs) {
                    nlohmann::json msgsJson = nlohmann::json::array();
                    for (const auto &msg : msgsi) {
                        msgsJson.push_back(fimMessage2Json(msg));
                    }
                    result["messages"].push_back(msgsJson);
                }
                return result.dump();
            });
        });
}

void ResendMessage(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_OBJECT_ARG(0, message);
    FIMMessageReSendMessage messageModel = FIMMessageReSendMessageFromJson(message);
    GET_MAP_ARG_WITH_DEFAULT(1, userData);


    FIMMessageService::ResendMessage(messageModel, [] (double progress) {
//        nlohmann::json progressJson;
//        progressJson["progress"] = progress;
//        void progressData = InitFieldWithDouble(progress);
//        promise->set_value(progressData);
    }, [OnResult] (const FIMMessage &msg) {
        HandleResultOrError(OnResult, [msg] {
            json result = fimMessage2Json(msg);
            return result.dump();
        });
    }, [OnResult] (const FIMError &error) {
        CallbackFIMError(OnResult, error);
    }, userData);
    
}

void SendMessageTolocal(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_OBJECT_ARG(0, message);
    alibaba::dps::AIMPubMsgSendMessage messageModel = AIMPubMsgSendMessageFromJson(message);

    FIMMessageService::SendAimMessageTolocal(messageModel, [OnResult] (const FIMMessage &msg) {
        HandleResultOrError(OnResult, [msg] {
            json result = fimMessage2Json(msg);
            return result.dump();
        });
    }, [OnResult] (const FIMError &error) {
        CallbackFIMError(OnResult, error);
    });
}

void GetMessage(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, appCid);
    GET_STRING_ARG(1, mid);
    FIMMessageService::GetMessage(appCid, mid, [OnResult] (const FIMMessage &msg) {
        HandleResultOrError(OnResult, [msg] {
            json result = fimMessage2Json(msg);
            return result.dump();
        });
    }, [OnResult] (const FIMError &error) {
        CallbackFIMError(OnResult, error);
    });
}

//  static void GetLocalMessage(
//      const std::string &appCid, const std::string &local_id,
//      const std::function<void(const FIMMessage &msg)> &OnSuccess,
//      const std::function<void(const FIMError &error)>
//          &OnFailure);
void GetLocalMessage(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, appCid);
    GET_STRING_ARG(1, localId);
    FIMMessageService::GetLocalMessage(appCid, localId, [OnResult] (const FIMMessage &msg) {
        HandleResultOrError(OnResult, [msg] {
            json result = fimMessage2Json(msg);
            return result.dump();
        });
    }, [OnResult] (const FIMError &error) {
        CallbackFIMError(OnResult, error);
    });
}

void GetLocalMessages(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, appCid);
    GET_INT_ARG(1, cursor);
    GET_INT32_ARG(2, count_32);
    GET_BOOL_ARG(3, forward);
    GET_BOOL_ARG(4, filter_recall);
    GET_BOOL_ARG(5, order_in_asc);
    

    alibaba::dps::AIMMsgFilter filter;
    filter.filter_recall = filter_recall;
    filter.order_in_asc = order_in_asc;

    FIMMessageService::GetLocalMessages(appCid, cursor, count_32, forward, filter, [OnResult] (const std::vector<FIMMessage> &msgs) {
        HandleResultOrError(OnResult, [msgs] {
            json result;
            result["messages"] = json::array();
            for (const auto &msg : msgs) {
                result["messages"].push_back(fimMessage2Json(msg));
            }
            return result.dump();
        });
    }, [OnResult] (const FIMError &error) {
        CallbackFIMError(OnResult, error);
    });
}

void DeleteLocalMessage(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, appCid);
    GET_STRING_VECTOR_ARG(1, mids);

    FIMMessageService::DeleteLocalMessage(appCid, mids, [OnResult] {
        CallbackBool(OnResult, true);
    }, [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    });
}

void DeleteMessage(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, appCid);
    GET_STRING_VECTOR_ARG(1, mids);

    FIMMessageService::DeleteMessage(appCid, mids, [OnResult] {
        CallbackBool(OnResult, true);

    }, [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    });
}

void RecallMessage(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, appCid);
    GET_STRING_ARG(1, mid);

    FIMMessageService::RecallMessage(appCid, mid, [OnResult] {
        CallbackBool(OnResult, true);

    }, [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    });
    
}

//  static void UpdateLocalExtension(
//      const std::vector<AIMPubMsgLocalExtensionUpdateInfo> &update_infos,
//      const std::function<void()> &OnSuccess,
//      const std::function<void(const FIMError &error)>
//          &OnFailure);
void UpdateLocalExtension(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_OBJECT_ARG(0, array);
    std::vector<AIMPubMsgLocalExtensionUpdateInfo> update_infos;
    for (const auto &update_info : array) {
        update_infos.push_back(AIMPubMsgLocalExtensionUpdateInfoFromJson(update_info));
    }
    FIMMessageService::UpdateLocalExtension(update_infos, [OnResult] {
        CallbackBool(OnResult, true);
    }, [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    });
    
}

//  static void UpdateLocalExtensionByKey(
//      const std::vector<AIMPubMsgLocalExtensionUpdateInfo> &update_infos,
//      const std::function<void()> &OnSuccess,
//      const std::function<void(const FIMError &error)>
//          &OnFailure);
void UpdateLocalExtensionByKey(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_OBJECT_ARG(0, array);
    std::vector<AIMPubMsgLocalExtensionUpdateInfo> update_infos;
    for (const auto &update_info : array) {
        update_infos.push_back(AIMPubMsgLocalExtensionUpdateInfoFromJson(update_info));
    }

    FIMMessageService::UpdateLocalExtensionByKey(update_infos, [OnResult] {
        CallbackBool(OnResult, true);
    }, [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    });
    
}

//  static void UpdateLocalMessagesBizInfo(
//      const std::vector<AIMPubMsgBizUpdateInfo> &update_infos,
//      const std::function<void()> &OnSuccess,
//      const std::function<void(const FIMError &error)>
//          &OnFailure);
void UpdateLocalMessagesBizInfo(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_ARRAY_ARG(0, array);
    std::vector<AIMPubMsgBizUpdateInfo> update_infos;
    for (const auto &update_info : array) {
        update_infos.push_back(AIMPubMsgBizUpdateInfoFromJson(update_info));
    }
    FIMMessageService::UpdateLocalMessagesBizInfo(update_infos, [OnResult] {
        CallbackBool(OnResult, true);
    }, [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    });
    
}

//  static void ListMessagesReadStatus(
//      const std::string &appCid, const std::string &mid,
//      const std::function<void(const AIMPubMsgReadStatus &status)> &OnSuccess,
//      const std::function<void(const FIMError &error)>
//          &OnFailure);
void ListMessagesReadStatus(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, appCid);
    GET_STRING_ARG(1, mid);
    GET_MAP_ARG_WITH_DEFAULT(2, userData);
    
    FIMMessageService::ListMessagesReadStatus(appCid, mid, [OnResult] (const AIMPubMsgReadStatus &status) {
        HandleResultOrError(OnResult, [status] {
            json result = JsonFromAIMPubMsgReadStatus(status);
            return result.dump();
        });
    }, [OnResult] (const FIMError &error) {
        CallbackFIMError(OnResult, error);
    });
    
}

//  static void UpdateMessageToRead(const std::string &appCid,
//                                   const std::vector<std::string> &mids);
void UpdateMessageToRead(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, appCid);
    GET_STRING_VECTOR_ARG(1, mids);
    FIMMessageService::UpdateMessageToRead(appCid, mids);
    CallbackBool(OnResult, true);
}
// virtual int64_t GenerateMsgLocalId();
//void GenerateMsgLocalId(const json& args, const FIMInvokerResultCallback& OnResult) {
//    int64_t result = FIMMessageService::GenerateMsgLocalId();
//    void successData = InitFieldWithInt(result);
//    return successData;
//}

//  ReplyMessage(const FIMMessageSendReplyMessage &msg,
//               const std::function<void(double progress)> &OnProgress,
//               const std::function<void(const FIMMessage &msg)> &OnSuccess,
//               const std::function<void(const FIMError &error)>
//                   &OnFailure,
//               const std::map<std::string, std::string> &user_data);
void ReplyMessage(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_OBJECT_ARG(0, jsonobj);
    GET_MAP_ARG_WITH_DEFAULT(1, userData);
    AIMPubMsgSendReplyMessage message = AIMPubMsgSendReplyMessageFromJson(jsonobj);
    FIMMessageService::ReplyAimMessage(message, [OnResult] (double progress) {
//        nlohmann::json progressJson;
//        progressJson["progress"] = progress;
//        void progressData = InitFieldWithDouble(progress);
//        promise->set_value(progressData);
    }, [OnResult] (const FIMMessage &msg) {
        HandleResultOrError(OnResult, [msg] {
            json result = fimMessage2Json(msg);
            return result.dump();
        });
    }, [OnResult] (const FIMError &error) {
        CallbackFIMError(OnResult, error);
    }, userData);
    
}

//  static void ForwardMessage(
//      const FIMMessageSendForwardMessage &mgs,
//      const std::function<void()> &OnSuccess,
//      const std::function<void(const FIMError &error)>
//          &OnFailure,
//      const std::map<std::string, std::string> &user_data);
void ForwardMessage(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_OBJECT_ARG(0, jsonobj);
    AIMPubMsgSendForwardMessage message = AIMPubMsgSendForwardMessageFromJson(jsonobj);
    GET_MAP_ARG_WITH_DEFAULT(1, user_data);

    FIMMessageService::ForwardAimMessage(message, [OnResult] {
        CallbackBool(OnResult, true);
    }, [OnResult] (const FIMError &error) {
        CallbackFIMError(OnResult, error);
    }, user_data);
    
}


//  static void CombineForwardMessage(
//      const FIMMessageSendForwardMessage &mgs,
//      const std::function<void(const FIMMessage &msg)> &OnSuccess,
//      const std::function<void(const FIMError &error)>
//          &OnFailure,
//      const std::map<std::string, std::string> &user_data);
void CombineForwardMessage(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_OBJECT_ARG(0, jsonObj);
    AIMPubMsgSendForwardMessage message = AIMPubMsgSendForwardMessageFromJson(jsonObj);

    GET_MAP_ARG_WITH_DEFAULT(1, user_data);
    FIMMessageService::CombineForwardAimMessage(message, [OnResult] (const FIMMessage &msg) {
        HandleResultOrError(OnResult, [msg] {
            json result = fimMessage2Json(msg);
            return result.dump();
        });
    }, [OnResult] (const FIMError &error) {
        CallbackFIMError(OnResult, error);
    }, user_data);
    
}

//  static void SetNeedReceivers(bool need);
void SetNeedReceivers(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_BOOL_ARG(0, need);
    FIMMessageService::SetNeedReceivers(need);
    CallbackBool(OnResult, true);
}

}// namespace fim

}// namespace alibaba

