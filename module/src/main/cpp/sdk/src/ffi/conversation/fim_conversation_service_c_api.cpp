#include "fim_conversation_service_c_api.h"
#include "fim_error_2_json.h"
#include "fim_conv_2_json.h"
#include "fim_conversation_service.h"
#include <future>
#include "fim_invoker_macro.h"
//AIMPubConvService
#include "aim_pub_conv_service.h"
#include "aim_pub_module.h"
#include "fim_engine.h"
#include "aim_pub_c_api.h"
#include "json_aim_pub_conv_create_single_conv_param.hpp"
#include "fim_conversation.hpp"
#include "fim_error.hpp"

namespace alibaba {
namespace fim {

void CreateCustomSingleConversation(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_MAP_ARG_WITH_DEFAULT(0, ext);
    alibaba::fim::FIMConversationService::CreateCustomSingleConversation(ext, [OnResult](const FIMConversation &conv) {
        HandleResultOrError(OnResult, [conv] {
            return fimConv2Json(conv).dump();
        });
    }, [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    });
}

/**
 * 创建单聊会话 - 直接调用aim api
 * @param args json数据
 * @param OnResult
 * 支持版本： 9.10.3
 */
void v_9_10_3_CreateAimCustomSingleConversation(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_OBJECT_ARG(0, paramJson);
    AIMPubConvCreateSingleConvParam param = AIMPubConvCreateSingleConvParamFromJson(paramJson);
    getCurrentConvService()->CreateSingleConversation(param, [OnResult](const AIMPubConversation &conv) {
        HandleResultOrError(OnResult, [&conv] {
            FIMConversation fimConv = FIMConversationFrom(conv);
            return fimConv2Json(fimConv).dump();

        });
    }, [OnResult](const ::alibaba::dps::DPSError &error) {
        CallbackFIMError(OnResult, FIMErrorFrom(error));
    });
}


void GetConversation(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, appCid);
    FIMConversationService::GetConversation(appCid,
                                            [OnResult](const FIMConversation &conv) {
        HandleResultOrError(OnResult, [conv] {
            nlohmann::json result = fimConv2Json(conv);
            return result.dump();
        });
    }, [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    });
}


void GetConversations(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_VECTOR_ARG(0, appCids);
    FIMConversationService::GetConversations(appCids,
                                             [OnResult](const std::vector<FIMConversation> &convs) {
        HandleResultOrError(OnResult, [convs] {
            nlohmann::json result;
            result["convs"] = nlohmann::json::array();
            for (const auto &conv: convs) {
                result["convs"].push_back(fimConv2Json(conv));
            }
            return result.dump();
        });
    }, [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    });
}


void ClearConversation(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, appCid);
    FIMConversationService::Clear(appCid, [OnResult]() {
        CallbackBool(OnResult, true);
    }, [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    });
}

void MuteConversation(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, appCid);
    GET_BOOL_ARG(1, mute);
    FIMConversationService::Mute(appCid, mute, [OnResult]() {
        CallbackBool(OnResult, true);
    }, [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    });
}


void CreateStandardSingleConversation(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, uid);
    GET_MAP_ARG_WITH_DEFAULT(1, ext);
    FIMConversationService::CreateStandardSingleConversation(uid, ext,
                                                             [OnResult](const FIMConversation &conv) {
        HandleResultOrError(OnResult, [conv] {
            return fimConv2Json(conv).dump();
        });
    },
                                                             [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    }
                                                             );
}

void TestCreateCustomSingleConversation(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, uid);
    GET_MAP_ARG_WITH_DEFAULT(1, ext);
    FIMConversationService::TestCreateCustomSingleConversation(uid, ext,
                                                               [OnResult](const FIMConversation &conv) {
        HandleResultOrError(OnResult, [conv] {
            return fimConv2Json(conv).dump();
        });
    },
                                                               [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    }
                                                               );
}

void ListLocalConversationsWithOffset(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_INT32_ARG(0, offset);
    GET_INT32_ARG(1, count);
    
    FIMConversationService::ListLocalConversationsWithOffset(offset, count,
                                                             [OnResult](const std::vector<FIMConversation> &result) {
        HandleResultOrError(OnResult ,[result] {
            nlohmann::json jsonResult = nlohmann::json::array();
            for (const auto &conv : result) {
                jsonResult.push_back(fimConv2Json(conv));
            }
            return jsonResult.dump();
        });
    },
                                                             [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    }
                                                             );
}

void ListLocalConversationsWithCid(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, appCid);
    GET_INT32_ARG(1, count)
    
    FIMConversationService::ListLocalConversationsWithCid(appCid, count,
                                                          [OnResult](const std::vector<FIMConversation> &result) {
        HandleResultOrError(OnResult ,[result] {
            nlohmann::json jsonResult = nlohmann::json::array();
            for (const auto &conv : result) {
                jsonResult.push_back(fimConv2Json(conv));
            }
            return jsonResult.dump();
        });
    },
                                                          [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    }
                                                          );
    
    
}

void ListAllStatusLocalConvs(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_INT32_ARG(0, offset);
    GET_INT32_ARG(1, count);
    FIMConversationService::ListAllStatusLocalConvs(offset, count,
                                                    [OnResult](const std::vector<FIMConversation> &result) {
        HandleResultOrError(OnResult ,[result] {
            nlohmann::json jsonResult = nlohmann::json::array();
            for (const auto &conv : result) {
                jsonResult.push_back(fimConv2Json(conv));
            }
            return jsonResult.dump();
        });
    },
                                                    [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    }
                                                    );
}

void GetLocalConversations(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_VECTOR_ARG(0, appCids);
    FIMConversationService::GetLocalConversations(appCids,
                                                  [OnResult](const std::vector<FIMConversation> &result) {
        HandleResultOrError(OnResult ,[result] {
            nlohmann::json jsonResult = nlohmann::json::array();
            for (const auto &conv : result) {
                jsonResult.push_back(fimConv2Json(conv));
            }
            return jsonResult.dump();
        });
    },
                                                  [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    }
                                                  );
}

void GetSingleConversations(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, user_id);
    FIMConversationService::GetSingleConversations(user_id,
                                                   [OnResult](const std::vector<FIMConversation> &result) {
        HandleResultOrError(OnResult ,[result] {
            nlohmann::json jsonResult = nlohmann::json::array();
            for (const auto &conv : result) {
                jsonResult.push_back(fimConv2Json(conv));
            }
            return jsonResult.dump();
        });
    },
                                                   [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    }
                                                   );
    
    
}

void GetSingleConversationsWithUserIds(const json& args, const FIMInvokerResultCallback& OnResult) {
    
    GET_STRING_VECTOR_ARG(0, user_ids);
    FIMConversationService::GetSingleConversationsWithUserIds(user_ids,
                                                              [OnResult](const std::vector<FIMConversation> &result) {
        HandleResultOrError(OnResult ,[result] {
            nlohmann::json jsonResult = nlohmann::json::array();
            for (const auto &conv : result) {
                jsonResult.push_back(fimConv2Json(conv));
            }
            return jsonResult.dump();
        });
    },
                                                              [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    }
                                                              );
    
    
}

void RemoveLocalConversation(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, appCid);
    FIMConversationService::RemoveLocalConversation(appCid,
                                                    [OnResult]() {
        CallbackBool(OnResult, true);
    },
                                                    [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    }
                                                    );
    
    
}

void UpdateTypingStatus(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, appCid);
    GET_STRING_ARG(1, receiver_id);
    GET_INT32_ARG(2, commandInt);
    GET_INT32_ARG(3, typeInt);
    AIMConvTypingCommand command = static_cast<AIMConvTypingCommand>(commandInt);
    AIMConvTypingMessageContent type = static_cast<AIMConvTypingMessageContent>(typeInt);
    
    FIMConversationService::UpdateTypingStatus(appCid, receiver_id, command, type,
                                               [OnResult]() {
        CallbackBool(OnResult, true);
    },
                                               [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    }
                                               );
    
    
}

void UpdateDraftMessage(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, appCid);
    GET_STRING_ARG(1, draft);
    FIMConversationService::UpdateDraftMessage(appCid, draft,
                                               [OnResult]() {
        CallbackBool(OnResult, true);
    },
                                               [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    }
                                               );
    
    
}

void Conversation_UpdateLocalExtension(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_STRING_ARG(0, appCid);
    GET_MAP_ARG_WITH_DEFAULT(0, local_ext);
    
    FIMConversationService::UpdateLocalExtension(appCid, local_ext,
                                                 [OnResult]() {
        CallbackBool(OnResult, true);
    },
                                                 [OnResult](const FIMError &error) {
        CallbackFIMError(OnResult, error);
    }
                                                 );
}


}// namespace fim
}// namespace alibaba

