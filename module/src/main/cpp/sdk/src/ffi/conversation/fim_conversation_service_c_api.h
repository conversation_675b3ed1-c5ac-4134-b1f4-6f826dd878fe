#ifndef fim_converstaion_service_c_api_h
#define fim_converstaion_service_c_api_h
#include "fim_conversation_service.h"
#include "fim_invoker_macro.h"
#include "json.hpp"
#include "fim_method_invoker.h"
namespace alibaba {
namespace fim {

/**
 * 获取会话appCid对应的会话。
 * @param appCids    会话id
 */
void GetConversation(const json& args, const FIMInvokerResultCallback& OnResult);

/**
 * 获取会话appCid集合对应的会话列表。
 * @param appCids    会话集合id
 */
void GetConversations(const json& args, const FIMInvokerResultCallback& OnResult);

/**
 * 创建自定义单聊会话。和sellerId创建，会自动分配客服。
 * ext:  {
 sellerId：店铺id,
 }
 */
void CreateCustomSingleConversation(const json& args, const FIMInvokerResultCallback& OnResult);


/**
 * 清除会话所有消息
 */
void ClearConversation(const json& args, const FIMInvokerResultCallback& OnResult);

/**
 * 会话消息免打扰
 */
void MuteConversation(const json& args, const FIMInvokerResultCallback& OnResult);



void CreateStandardSingleConversation(const json& args, const FIMInvokerResultCallback& OnResult);

void TestCreateCustomSingleConversation(const json& args, const FIMInvokerResultCallback& OnResult);

void ListLocalConversationsWithOffset(const json& args, const FIMInvokerResultCallback& OnResult);

void ListLocalConversationsWithCid(const json& args, const FIMInvokerResultCallback& OnResult);

void ListAllStatusLocalConvs(const json& args, const FIMInvokerResultCallback& OnResult);

void GetLocalConversations(const json& args, const FIMInvokerResultCallback& OnResult);

void GetSingleConversations(const json& args, const FIMInvokerResultCallback& OnResult);

void GetSingleConversationsWithUserIds(const json& args, const FIMInvokerResultCallback& OnResult);

void RemoveLocalConversation(const json& args, const FIMInvokerResultCallback& OnResult);

void UpdateTypingStatus(const json& args, const FIMInvokerResultCallback& OnResult);

void UpdateDraftMessage(const json& args, const FIMInvokerResultCallback& OnResult);

void Conversation_UpdateLocalExtension(const json& args, const FIMInvokerResultCallback& OnResult);

/**
 * 创建单聊会话 - 直接调用aim api
 * @param args json数据
 * @param OnResult
 * 支持版本： 9.10.3
 */
void v_9_10_3_CreateAimCustomSingleConversation(const json& args, const FIMInvokerResultCallback& OnResult);


}
}


#endif
