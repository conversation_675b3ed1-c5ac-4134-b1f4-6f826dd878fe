//
//  fim_search_c_api.c
//  FliggyIM
//
//  Created by 蒙晨 on 2024/8/7.
//

#include "fim_search_c_api.h"
#include "fim_search_service.h"
#include "fim_search_chat_result_json.h"
#include <stdio.h>
#include "fim_invoker_macro.h"
#include "fim_method_invoker.h"
#include <future>

namespace alibaba {
namespace fim {


//static void SearchChatContent(
//    const FIMSearchChatContentParams &params,
//    const std::function<void(const std::vector<FIMSearchChatResult> &result,
//                             int32_t total_count)> &OnSuccess,
//    const std::function<void(const FIMError &error)> &OnFailure);
void SearchChatContent(const json& args, const FIMInvokerResultCallback& OnResult) {
    GET_OBJECT_ARG(0, jsonObj);
    FIMSearchChatContentParams params = FIMSearchChatContentParamsFromJson(jsonObj);

    FIMSearchService::SearchChatContent(params, [OnResult] (const std::vector<FIMSearchChatResult> &result, int32_t total_count) {
        HandleResultOrError(OnResult, [result, total_count] {
            nlohmann::json json;
            json["result"] = nlohmann::json::array();
            for (const auto &item : result) {
                json["result"].push_back(FIMSearchChatResultToJson(item));
            }
            json["total_count"] = total_count;
            return json.dump();
        });
    }, [OnResult] (const FIMError &error) {
        CallbackFIMError(OnResult, error);
    });
}

}// namespace fim
}// namespace alibaba
