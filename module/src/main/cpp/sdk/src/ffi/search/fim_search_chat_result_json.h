//
//  fim_search_chat_result_json.h
//  FliggyIM
//
//  Created by 蒙晨 on 2024/8/7.
//

#ifndef fim_search_chat_result_json_h
#define fim_search_chat_result_json_h
#include "fim_search_chat_result.h"
#include "json.hpp"
using namespace alibaba::fim;

// 假设 FIMMessage 已经有了 toJson 方法
nlohmann::json FIMSearchHighlightRangeToJson(const FIMSearchHighlightRange& range);

nlohmann::json FIMSearchChatResultToJson(const FIMSearchChatResult& result);


#endif /* fim_search_chat_result_json_h */
