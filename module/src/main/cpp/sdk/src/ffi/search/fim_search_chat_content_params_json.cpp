//
//  fim_search_chat_content_params_json.cpp
//  FliggyIM
//
//  Created by 蒙晨 on 2024/8/7.
//

#include <stdio.h>
#include "fim_search_chat_content_params_json.h"


FIMSearchChatContentParams FIMSearchChatContentParamsFromJson(const nlohmann::json& j) {
    FIMSearchChatContentParams params;
    
    if (j.contains("keyword") && !j["keyword"].is_null()) {
        params.keyword = j["keyword"].get<std::string>();
    }
    
    if (j.contains("offset") && !j["offset"].is_null()) {
        params.offset = j["offset"].get<int32_t>();
    }
    
    if (j.contains("max_num") && !j["max_num"].is_null()) {
        params.max_num = j["max_num"].get<int32_t>();
    }
    
    if (j.contains("start_time") && !j["start_time"].is_null()) {
        params.start_time = j["start_time"].get<int64_t>();
    }
    
    if (j.contains("end_time") && !j["end_time"].is_null()) {
        params.end_time = j["end_time"].get<int64_t>();
    }
    
    if (j.contains("is_auto_highlight") && !j["is_auto_highlight"].is_null()) {
        params.is_auto_highlight = j["is_auto_highlight"].get<bool>();
    }
    
    if (j.contains("is_asc") && !j["is_asc"].is_null()) {
        params.is_asc = j["is_asc"].get<bool>();
    }
    
    if (j.contains("support_msg_types") && !j["support_msg_types"].is_null()) {
        for (const auto& type : j["support_msg_types"]) {
            if (!type.is_null()) {
                params.support_msg_types.push_back(static_cast<FIMMessageContentType>(type.get<int>()));
            }
        }
    }
    
    if (j.contains("support_sub_types") && !j["support_sub_types"].is_null()) {
        for (const auto& type : j["support_sub_types"]) {
            if (!type.is_null()) {
                params.support_sub_types.push_back(type.get<int32_t>());
            }
        }
    }
    
    if (j.contains("biz_tags") && !j["biz_tags"].is_null()) {
        for (const auto& tag : j["biz_tags"]) {
            if (!tag.is_null()) {
                params.biz_tags.push_back(tag.get<std::string>());
            }
        }
    }
    
    if (j.contains("appCids") && !j["appCids"].is_null()) {
        for (const auto& cid : j["appCids"]) {
            if (!cid.is_null()) {
                params.appCids.push_back(cid.get<std::string>());
            }
        }
    }
    
    if (j.contains("sender_ids") && !j["sender_ids"].is_null()) {
        for (const auto& id : j["sender_ids"]) {
            if (!id.is_null()) {
                params.sender_ids.push_back(id.get<std::string>());
            }
        }
    }
    
    return params;
}


