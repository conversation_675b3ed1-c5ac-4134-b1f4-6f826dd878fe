//
//  fim_search_chat_result_json.cpp
//  FliggyIM
//
//  Created by 蒙晨 on 2024/8/7.
//

#include <stdio.h>
#include "fim_search_chat_result_json.h"
#include "fim_message_2_json.h"

nlohmann::json FIMSearchHighlightRangeToJson(const FIMSearchHighlightRange& range) {
    nlohmann::json j;
    j["start"] = range.start;
    j["length"] = range.length;
    return j;
}

nlohmann::json FIMSearchChatResultToJson(const FIMSearchChatResult& result) {
    nlohmann::json j;
    j["message"] = fimMessage2Json(result.message);
    j["ranges"] = nlohmann::json::array();
    for (const auto& range : result.ranges) {
        j["ranges"].push_back(FIMSearchHighlightRangeToJson(range));
    }
    return j;
}
