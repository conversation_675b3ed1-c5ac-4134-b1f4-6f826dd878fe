//
//  fim_search_c_api.h
//  FliggyIM
//
//  Created by 蒙晨 on 2024/8/7.
//

#ifndef fim_search_c_api_h
#define fim_search_c_api_h

#include <stdio.h>
#include "fim_search_chat_content_params_json.h"
#include "fim_search_chat_result_json.h"
#include "json.hpp"
#include "fim_method_invoker.h"

using json = nlohmann::json;
namespace alibaba {
namespace fim {


void SearchChatContent(const json& args, const FIMInvokerResultCallback& OnResult);

}
}

#endif /* fim_search_c_api_h */
