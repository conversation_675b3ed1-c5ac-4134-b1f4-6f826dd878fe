//
//  fim_fbridge_api.h
//  FliggyIM
//
//  Created by 蒙晨 on 2024/8/6.
//

#ifndef fim_fbridge_api_h
#define fim_fbridge_api_h

#include <stdio.h>
#include <string>
#include <map>
#include "fliggy_im_sdk.h"
#include "json.hpp"

namespace alibaba {
namespace fim {

class FIMSDK_API FIMDispathEventToFlutter {
public:
    virtual ~FIMDispathEventToFlutter() {}
    
    /**
     * 调用：FlutterUtil.fdispatchEvent(bizName, methodName, args);
     */
    virtual void DispatchEvent(const std::string &bizName, const std::string &methodName, const nlohmann::json &args) = 0;
    
};

}// namespace fim
} // namespace alibaba

#endif /* fim_fbridge_api_h */
