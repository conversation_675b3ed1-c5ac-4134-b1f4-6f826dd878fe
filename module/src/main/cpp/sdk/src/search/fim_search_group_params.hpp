#pragma once
#include "aim_search_group_params.h"
#include "fim_search_group_params.h"

namespace alibaba {
namespace fim {

inline dps::AIMSearchGroupParams
AIMSearchGroupParamsFrom(const FIMSearchGroupParams &fimSearchGroupParams) {
  return dps::AIMSearchGroupParams(
      fimSearchGroupParams.keyword, fimSearchGroupParams.offset,
      fimSearchGroupParams.max_num, fimSearchGroupParams.start_time,
      fimSearchGroupParams.end_time, fimSearchGroupParams.is_asc);
}

inline FIMSearchGroupParams FIMSearchGroupParamsFrom(
    const dps::AIMSearchGroupParams &aimSearchGroupParams) {
  return FIMSearchGroupParams(
      aimSearchGroupParams.keyword, aimSearchGroupParams.offset,
      aimSearchGroupParams.max_num, aimSearchGroupParams.start_time,
      aimSearchGroupParams.end_time, aimSearchGroupParams.is_asc);
}

} // namespace fim
} // namespace alibaba