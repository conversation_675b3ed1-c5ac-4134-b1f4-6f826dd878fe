#pragma once

#include "fim_error.h"
#include "fim_network_service.h"
#include "fim_search_chat_content_params.h"
#include "fim_search_chat_result.h"
#include "fim_search_conversation_result.h"
#include "fim_search_group_by_group_nick_params.h"
#include "fim_search_group_params.h"
#include "fim_message_content_type.h"
#include <string>
#include "fliggy_im_sdk.h"

namespace alibaba {
namespace fim {
/**
 * FIMSearchService
 */
class FIMSDK_API FIMSearchService {
public:
  ~FIMSearchService() {}

  /**
   * 搜索消息文本
   * @param params 搜索消息参数
   * @param listener 监听器
   * 如何分页:
   * 通过offset, max_num来做分页, total_cout 为返回结果
   * 1. 当offset为0时，total_count返回总的本地搜索匹配个数
   * 2. offset > 0 时, total_count为0，可以忽略
   * 示例，如何使用：
   * 1. 以20条每页进行分页，则
   * 2. offset = 0, max_num = 20, 返回 total_count为110，及第一页的20个消息
   * 3. 根据total_count计算总页码：110 / 20 + 1 = 6
   * 4. 获取第二页：offset = 20, max_num = 20, 返回下一个20条消息，total_count =
   * 0可忽略
   */
  //   virtual void SearchChatContent(
  //       const FIMSearchChatContentParams &params,
  //       const std::shared_ptr<AIMPubSearchChatContentListener> &listener) =
  //       0;
  static void SearchChatContent(
      const FIMSearchChatContentParams &params,
      const std::function<void(const std::vector<FIMSearchChatResult> &result,
                               int32_t total_count)> &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 搜索消息文本，按照会话返回，每个会话返回一个消息
   * @param params 搜索参数，offset, maxnum以消息个数作为单位，非会话
   * @param listener 监听器
   * 如何分页:
   * 通过offset, max_num来做分页, total_cout 为返回结果
   * 1. 当offset为0时，total_count返回总的本地搜索匹配个数
   * 2. offset > 0 时，total_count为0，可以忽略
   * 示例，如何使用：
   * 1. 以20条每页进行分页，则
   * 2. offset = 0, max_num = 20, 返回 total_count为110，及第一页的20个消息
   * 3. 根据total_count计算总页码：110 / 20 + 1 = 6
   * 4. 获取第二页：offset = 20, max_num = 20, 返回下一个20条消息，total_count =
   * 0可忽略
   */
  //   virtual void SearchConversationByContent(
  //       const FIMSearchChatContentParams &params,
  //       const std::shared_ptr<AIMPubSearchConvByContentListener> &listener) =
  //       0;
  static void SearchConversationByContent(
      const FIMSearchChatContentParams &params,
      const std::function<
          void(const std::vector<FIMSearchConversationResult> &result,
               int32_t total_count)> &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);
    
    
    static void SearchChatContentString(
        const std::string contentString,
        const std::function<void(const std::string &jsonString)> &OnSuccess,
                                        const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 搜索会话名称
   * @param params 群名称搜索参数
   * @param listener 监听器
   * 如何分页:
   * 通过offset, max_num来做分页, total_cout 为返回结果
   * 1. 当offset为0时，total_count返回总的本地搜索匹配个数
   * 2. offset > 0 时，total_count为0，可以忽略
   * 示例，如何使用：
   * 1. 以20条每页进行分页，则
   * 2. offset = 0, max_num = 20, 返回 total_count为110，及第一页的20个消息
   * 3. 根据total_count计算总页码：110 / 20 + 1 = 6
   * 4. 获取第二页：offset = 20, max_num = 20, 返回下一个20条消息，total_count =
   * 0可忽略
   */
  //   virtual void SearchGroupByName(
  //       const AIMSearchGroupParams &params,
  //       const std::shared_ptr<AIMPubSearchGroupByNameListener> &listener) =
  //       0;
  static void SearchGroupByName(
      const FIMSearchGroupParams &params,
      const std::function<void(const std::vector<FIMConversation> &result,
                               int32_t total_count)> &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 根据群昵称搜索相关群
   * @param params 群昵称搜索参数
   * @param listener 监听器
   * 如何分页:
   * 通过offset, max_num来做分页, total_cout 为返回结果
   * 1. 当offset为0时，total_count返回总的本地搜索匹配个数
   * 2. offset > 0 时，total_count为0，可以忽略
   * 示例，如何使用：
   * 1. 以20条每页进行分页，则
   * 2. offset = 0, max_num = 20, 返回 total_count为110，及第一页的20个消息
   * 3. 根据total_count计算总页码：110 / 20 + 1 = 6
   * 4. 获取第二页：offset = 20, max_num = 20, 返回下一个20条消息，total_count =
   * 0可忽略
   */
  //   virtual void SearchGroupByGroupNick(
  //       const AIMSearchGroupByGroupNickParams &params,
  //       const std::shared_ptr<AIMPubSearchGroupByNameListener> &listener) =
  //       0;
  static void SearchGroupByGroupNick(
      const FIMSearchGroupByGroupNickParams &params,
      const std::function<void(const std::vector<FIMConversation> &result,
                               int32_t total_count)> &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);
  /*
   * 搜索消息文本
   * @param appCid 会话id
   * @param fuid 消息发送者id
   * @param fcid 会话id
   * @param searchKey 搜索关键词
   * @param startTime 开始消息时间戳 毫秒
   * @param endTime 结束消息时间戳 毫秒
   * @param msgType 消息类型
   * @param OnSuccess 成功回调
   * @param OnFailure 失败回调
   */
  static void SearchFromServer(
      const std::string &appCid, const std::string &fuid,
      const std::string &fcid, const std::string &searchKey,
      const int64_t &startTime, const int64_t &endTime, const FIMMessageContentType &msgType,
      const std::function<void(const std::string &response)> &OnSuccess,
        const std::function<void(const FIMError &error)> &OnFailure);
};

using FIMSearchServicePtr = std::shared_ptr<FIMSearchService>;
using FIMSearchServiceWeakPtr = std::weak_ptr<FIMSearchService>;

} // namespace fim
} // namespace alibaba
