#pragma once
#include "fim_search_chat_result.h"
#include "fim_message.hpp"
#include "aim_pub_search_chat_result.h"
#include "fim_search_highlight_range.hpp"

namespace alibaba {
namespace fim {
    inline FIMSearchChatResult FIMSearchChatResultFrom(const dps::AIMPubSearchChatResult& aimSearchChatResult) {
        FIMSearchChatResult fimSearchChatResult;
        fimSearchChatResult.message = FIMMessageFrom(aimSearchChatResult.message);
        for (const auto& range : aimSearchChatResult.ranges) {
            fimSearchChatResult.ranges.push_back(FIMSearchHighlightRangeFrom(range));
        }
        return fimSearchChatResult;
    }

    inline std::vector<FIMSearchChatResult> FIMSearchChatResultListFrom(const std::vector<dps::AIMPubSearchChatResult>& aimSearchChatResults) {
        std::vector<FIMSearchChatResult> fimSearchChatResults;
        for (const auto& aimSearchChatResult : aimSearchChatResults) {
            fimSearchChatResults.push_back(FIMSearchChatResultFrom(aimSearchChatResult));
        }
        return fimSearchChatResults;
    }
} // namespace fim
} // namespace alibaba
