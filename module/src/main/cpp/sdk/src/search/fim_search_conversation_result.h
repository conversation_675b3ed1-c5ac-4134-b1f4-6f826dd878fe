#pragma once

#include "fim_conversation.h"
#include "fim_message.h"
#include "fim_search_highlight_range.h"
#include <utility>
#include <vector>
#include "fim_conversation.h"

namespace alibaba {
namespace fim {

/**
 * 会话内容搜索返回结果
 */
struct FIMSearchConversationResult final {

  /**
   * 搜索匹配的会话信息
   */
  FIMConversation conversation;
  /**
   * 该会话匹配的第一条消息
   */
  FIMMessage first_message;
  /**
   * 消息内容高亮位置
   */
  std::vector<FIMSearchHighlightRange> ranges;

  FIMSearchConversationResult(FIMConversation conversation_,
                                 FIMMessage first_message_,
                                 std::vector<FIMSearchHighlightRange> ranges_)
      : conversation(std::move(conversation_)),
        first_message(std::move(first_message_)), ranges(std::move(ranges_)) {}


};

} // namespace dps
} // namespace alibaba
