#pragma once
#include "fim_search_highlight_range.h"
#include "aim_search_highlight_range.h"
#include <vector> 

namespace alibaba {
namespace fim {


inline fim::FIMSearchHighlightRange FIMSearchHighlightRangeFrom(const dps::AIMSearchHighlightRange& aimSearchHighlightRange) {
    return fim::FIMSearchHighlightRange(aimSearchHighlightRange.start, aimSearchHighlightRange.length);
}

inline dps::AIMSearchHighlightRange AIMSearchHighlightRangeFrom(const fim::FIMSearchHighlightRange& fimSearchHighlightRange) {
    return dps::AIMSearchHighlightRange(fimSearchHighlightRange.start, fimSearchHighlightRange.length);
}

inline std::vector<fim::FIMSearchHighlightRange> FIMSearchHighlightRangeListFrom(const std::vector<dps::AIMSearchHighlightRange>& aimSearchHighlightRanges) {
    std::vector<fim::FIMSearchHighlightRange> fimSearchHighlightRanges;
    for (const auto& aimSearchHighlightRange : aimSearchHighlightRanges) {
        fimSearchHighlightRanges.push_back(FIMSearchHighlightRangeFrom(aimSearchHighlightRange));
    }
    return fimSearchHighlightRanges;
}

} // namespace fim
} // namespace alibaba