#pragma once

#include "aim_pub_search_chat_content_params.h"
#include "fim_search_chat_content_params.h"
#include "fim_message_content_type.hpp"

namespace alibaba {
namespace fim {
inline dps::AIMPubSearchChatContentParams AIMPubSearchChatContentParamsFrom(
    const FIMSearchChatContentParams &fimSearchChatContentParams) {
  return dps::AIMPubSearchChatContentParams(
      fimSearchChatContentParams.keyword, fimSearchChatContentParams.offset,
      fimSearchChatContentParams.max_num, fimSearchChatContentParams.start_time,
      fimSearchChatContentParams.end_time,
      fimSearchChatContentParams.is_auto_highlight,
      fimSearchChatContentParams.is_asc,
      AIMMsgContentTypeListFrom(fimSearchChatContentParams.support_msg_types),
      fimSearchChatContentParams.support_sub_types,
      fimSearchChatContentParams.biz_tags, fimSearchChatContentParams.appCids,
      fimSearchChatContentParams.sender_ids);
}

inline FIMSearchChatContentParams FIMSearchChatContentParamsFrom(
    const dps::AIMPubSearchChatContentParams &aimPubSearchChatContentParams) {
  return FIMSearchChatContentParams(
      aimPubSearchChatContentParams.keyword,
      aimPubSearchChatContentParams.offset,
      aimPubSearchChatContentParams.max_num,
      aimPubSearchChatContentParams.start_time,
      aimPubSearchChatContentParams.end_time,
      aimPubSearchChatContentParams.is_auto_highlight,
      aimPubSearchChatContentParams.is_asc,
      FIMMessageContentTypeListFrom(
          aimPubSearchChatContentParams.support_msg_types),
      aimPubSearchChatContentParams.support_sub_types,
      aimPubSearchChatContentParams.biz_tags,
      aimPubSearchChatContentParams.appCids,
      aimPubSearchChatContentParams.sender_ids);
}

} // namespace fim
} // namespace alibaba