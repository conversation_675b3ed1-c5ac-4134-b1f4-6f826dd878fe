// FIMSearchConversationResult
/**
 * 会话内容搜索返回结果
 */
// struct FIMSearchConversationResult final {

//   /**
//    * 搜索匹配的会话信息
//    */
//   FIMConversation conversation;
//   /**
//    * 该会话匹配的第一条消息
//    */
//   FIMMessage first_message;
//   /**
//    * 消息内容高亮位置
//    */
//   std::vector<FIMSearchHighlightRange> ranges;

//   FIMSearchConversationResult(FIMConversation conversation_,
//                                  FIMMessage first_message_,
//                                  std::vector<FIMSearchHighlightRange> ranges_)
//       : conversation(std::move(conversation_)),
//         first_message(std::move(first_message_)), ranges(std::move(ranges_)) {}

//   FIMSearchConversationResult() {}
// };
#pragma once

#include "fim_search_conversation_result.h"
#include "fim_conversation.hpp"
#include "fim_message.hpp"
#include "fim_search_highlight_range.hpp"
#include "aim_pub_search_conversation_result.h"
#include <vector>

namespace alibaba {
namespace fim {
    inline FIMSearchConversationResult FIMSearchConversationResultFrom(const dps::AIMPubSearchConversationResult& aimSearchConversationResult) {
        return FIMSearchConversationResult(
            FIMConversationFrom(aimSearchConversationResult.conversation),
            FIMMessageFrom(aimSearchConversationResult.first_message),
            FIMSearchHighlightRangeListFrom(aimSearchConversationResult.ranges)
        );
    }

    inline std::vector<FIMSearchConversationResult> FIMSearchConversationResultListFrom(const std::vector<dps::AIMPubSearchConversationResult>& aimSearchConversationResults) {
        std::vector<FIMSearchConversationResult> fimSearchConversationResults;
        for (const auto& aimSearchConversationResult : aimSearchConversationResults) {
            fimSearchConversationResults.push_back(FIMSearchConversationResultFrom(aimSearchConversationResult));
        }
        return fimSearchConversationResults;
    }

}
}