#pragma once
#include "fim_message.h"
#include "fim_search_highlight_range.h"
#include "fliggy_im_sdk.h"
#include <utility>
#include <vector>

namespace alibaba {
namespace fim {

/**
 * 消息内容搜索返回结果
 */
struct FIMSDK_API FIMSearchChatResult final {

  /**
   * 消息体
   */
  FIMMessage message;
  /**
   * 高亮信息
   */
  std::vector<FIMSearchHighlightRange> ranges;

  FIMSearchChatResult(FIMMessage message_,
                         std::vector<FIMSearchHighlightRange> ranges_)
      : message(std::move(message_)), ranges(std::move(ranges_)) {}

  FIMSearchChatResult() {}
};

} // namespace dps
} // namespace alibaba
