#pragma once
#include "aim_search_group_by_group_nick_params.h"
#include "fim_search_group_by_group_nick_params.h"

namespace alibaba {
namespace fim {
inline dps::AIMSearchGroupByGroupNickParams AIMSearchGroupByGroupNickParamsFrom(
    const FIMSearchGroupByGroupNickParams &fimSearchGroupByGroupNickParams) {
  return dps::AIMSearchGroupByGroupNickParams(
      fimSearchGroupByGroupNickParams.keyword,
      fimSearchGroupByGroupNickParams.offset,
      fimSearchGroupByGroupNickParams.max_num,
      fimSearchGroupByGroupNickParams.is_asc);
}

inline FIMSearchGroupByGroupNickParams FIMSearchGroupByGroupNickParamsFrom(
    const dps::AIMSearchGroupByGroupNickParams &aimSearchGroupByGroupNickParams) {
  return FIMSearchGroupByGroupNickParams(
      aimSearchGroupByGroupNickParams.keyword,
      aimSearchGroupByGroupNickParams.offset,
      aimSearchGroupByGroupNickParams.max_num,
      aimSearchGroupByGroupNickParams.is_asc);
}

} // namespace fim
} // namespace alibaba