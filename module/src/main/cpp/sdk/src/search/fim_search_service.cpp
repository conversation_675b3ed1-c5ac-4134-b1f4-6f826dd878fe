#include "fim_search_service.h"
#include "aim_pub_c_api.h"
#include "aim_pub_module.h"
#include "aim_pub_search_chat_content_params.h"
#include "aim_pub_search_chat_result.h"
#include "aim_pub_search_conv_by_content_listener.h"
#include "aim_pub_search_service.h"
#include "dps_error.h"
#include "fim_engine.h"
#include "fim_error.h"
#include "fim_error.hpp"
#include "fim_search_chat_content_params.hpp"
#include "fim_search_chat_result.hpp"
#include "fim_search_conversation_result.hpp"
#include "fim_search_group_by_group_nick_params.hpp"
#include "fim_search_group_params.hpp"
#include "fim_search_chat_content_params_json.h"
#include "fim_search_chat_result_json.h"

using json = nlohmann::json;

namespace alibaba {
namespace fim {

std::shared_ptr<AIMPubSearchService> getCurrentSearchService() {
  alibaba::dps::AIMPubModule *rawModulePtr =
      GetAIMPubModuleInstance((FIMEngine::GetInstance().fuid));
  if (rawModulePtr == nullptr) {
    return nullptr;
  }
  return rawModulePtr->GetSearchService();
}

/**
 * 搜索消息文本
 * @param params 搜索消息参数
 * @param listener 监听器
 * 如何分页:
 * 通过offset, max_num来做分页, total_cout 为返回结果
 * 1. 当offset为0时，total_count返回总的本地搜索匹配个数
 * 2. offset > 0 时, total_count为0，可以忽略
 * 示例，如何使用：
 * 1. 以20条每页进行分页，则
 * 2. offset = 0, max_num = 20, 返回 total_count为110，及第一页的20个消息
 * 3. 根据total_count计算总页码：110 / 20 + 1 = 6
 * 4. 获取第二页：offset = 20, max_num = 20, 返回下一个20条消息，total_count =
 * 0可忽略
 */
//   virtual void SearchChatContent(
//       const FIMSearchChatContentParams &params,
//       const std::shared_ptr<AIMPubSearchChatContentListener> &listener) = 0;
void FIMSearchService::SearchChatContent(
    const FIMSearchChatContentParams &params,
    const std::function<void(const std::vector<FIMSearchChatResult> &result,
                             int32_t total_count)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubSearchService> service = getCurrentSearchService();
  if (service == nullptr) {
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get searchService failure", "获取搜索服务失败");
    OnFailure(error);
    return;
  }
  auto aimParams = AIMPubSearchChatContentParamsFrom(params);
  service->SearchChatContent(
      aimParams,
      [OnSuccess](const std::vector<AIMPubSearchChatResult> &result,
                  int32_t total_count) {
        std::vector<FIMSearchChatResult> fimResult =
            FIMSearchChatResultListFrom(result);
        OnSuccess(fimResult, total_count);
      },
      [OnFailure](const dps::DPSError &error) {
        FIMError fimError = FIMErrorFrom(error);
        OnFailure(fimError);
      });
}

void FIMSearchService::SearchChatContentString(
    const std::string contentString,
    const std::function<void(const std::string &jsonString)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
    nlohmann::json json = nlohmann::json::parse(contentString);
    FIMSearchChatContentParams params = FIMSearchChatContentParamsFromJson(json);
    
    FIMSearchService::SearchChatContent(params, [OnSuccess] (const std::vector<FIMSearchChatResult> &result, int32_t total_count) {
        nlohmann::json jsonResult;

        jsonResult["result"] = nlohmann::json::array();
        for (const auto &item : result) {
            jsonResult["result"].push_back(FIMSearchChatResultToJson(item));
        }
        jsonResult["total_count"] = total_count;
        std::string jsonString = jsonResult.dump();
        OnSuccess(jsonString);
    }, [OnFailure] (const FIMError &error) {
        OnFailure(error);
    });
}

/**
 * 搜索消息文本，按照会话返回，每个会话返回一个消息
 * @param params 搜索参数，offset, maxnum以消息个数作为单位，非会话
 * @param listener 监听器
 * 如何分页:
 * 通过offset, max_num来做分页, total_cout 为返回结果
 * 1. 当offset为0时，total_count返回总的本地搜索匹配个数
 * 2. offset > 0 时，total_count为0，可以忽略
 * 示例，如何使用：
 * 1. 以20条每页进行分页，则
 * 2. offset = 0, max_num = 20, 返回 total_count为110，及第一页的20个消息
 * 3. 根据total_count计算总页码：110 / 20 + 1 = 6
 * 4. 获取第二页：offset = 20, max_num = 20, 返回下一个20条消息，total_count =
 * 0可忽略
 */
//   virtual void SearchConversationByContent(
//       const FIMSearchChatContentParams &params,
//       const std::shared_ptr<AIMPubSearchConvByContentListener> &listener) =
//       0;
void FIMSearchService::SearchConversationByContent(
    const FIMSearchChatContentParams &params,
    const std::function<
        void(const std::vector<FIMSearchConversationResult> &result,
             int32_t total_count)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubSearchService> service = getCurrentSearchService();
  if (service == nullptr) {
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get searchService failure", "获取搜索服务失败");
    OnFailure(error);
    return;
  }
  auto aimParams = AIMPubSearchChatContentParamsFrom(params);
  service->SearchConversationByContent(
      aimParams,
      [OnSuccess](const std::vector<AIMPubSearchConversationResult> &result,
                  int32_t total_count) {
        std::vector<FIMSearchConversationResult> fimResult =
            FIMSearchConversationResultListFrom(result);
        OnSuccess(fimResult, total_count);
      },
      [OnFailure](const dps::DPSError &error) {
        FIMError fimError = FIMErrorFrom(error);
        OnFailure(fimError);
      });
}

/**
 * 搜索会话名称
 * @param params 群名称搜索参数
 * @param listener 监听器
 * 如何分页:
 * 通过offset, max_num来做分页, total_cout 为返回结果
 * 1. 当offset为0时，total_count返回总的本地搜索匹配个数
 * 2. offset > 0 时，total_count为0，可以忽略
 * 示例，如何使用：
 * 1. 以20条每页进行分页，则
 * 2. offset = 0, max_num = 20, 返回 total_count为110，及第一页的20个消息
 * 3. 根据total_count计算总页码：110 / 20 + 1 = 6
 * 4. 获取第二页：offset = 20, max_num = 20, 返回下一个20条消息，total_count =
 * 0可忽略
 */
//   virtual void SearchGroupByName(
//       const AIMSearchGroupParams &params,
//       const std::shared_ptr<AIMPubSearchGroupByNameListener> &listener) = 0;
void FIMSearchService::SearchGroupByName(
    const FIMSearchGroupParams &params,
    const std::function<void(const std::vector<FIMConversation> &result,
                             int32_t total_count)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubSearchService> service = getCurrentSearchService();
  if (service == nullptr) {
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get searchService failure", "获取搜索服务失败");
    OnFailure(error);
    return;
  }
  auto aimParams = AIMSearchGroupParamsFrom(params);
  service->SearchGroupByName(
      aimParams,
      [OnSuccess](const std::vector<AIMPubConversation> &result,
                  int32_t total_count) {
        std::vector<FIMConversation> fimResult =
            FIMConversationListFrom(result);
        OnSuccess(fimResult, total_count);
      },
      [OnFailure](const dps::DPSError &error) {
        FIMError fimError = FIMErrorFrom(error);
        OnFailure(fimError);
      });
}

/**
 * 根据群昵称搜索相关群
 * @param params 群昵称搜索参数
 * @param listener 监听器
 * 如何分页:
 * 通过offset, max_num来做分页, total_cout 为返回结果
 * 1. 当offset为0时，total_count返回总的本地搜索匹配个数
 * 2. offset > 0 时，total_count为0，可以忽略
 * 示例，如何使用：
 * 1. 以20条每页进行分页，则
 * 2. offset = 0, max_num = 20, 返回 total_count为110，及第一页的20个消息
 * 3. 根据total_count计算总页码：110 / 20 + 1 = 6
 * 4. 获取第二页：offset = 20, max_num = 20, 返回下一个20条消息，total_count =
 * 0可忽略
 */
//   virtual void SearchGroupByGroupNick(
//       const AIMSearchGroupByGroupNickParams &params,
//       const std::shared_ptr<AIMPubSearchGroupByNameListener> &listener) = 0;
void FIMSearchService::SearchGroupByGroupNick(
    const FIMSearchGroupByGroupNickParams &params,
    const std::function<void(const std::vector<FIMConversation> &result,
                             int32_t total_count)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubSearchService> service = getCurrentSearchService();
  if (service == nullptr) {
    FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              "get searchService failure", "获取搜索服务失败");
    OnFailure(error);
    return;
  }
  auto aimParams = AIMSearchGroupByGroupNickParamsFrom(params);
  service->SearchGroupByGroupNick(
      aimParams,
      [OnSuccess](const std::vector<AIMPubConversation> &result,
                  int32_t total_count) {
        std::vector<FIMConversation> fimResult =
            FIMConversationListFrom(result);
        OnSuccess(fimResult, total_count);
      },
      [OnFailure](const dps::DPSError &error) {
        FIMError fimError = FIMErrorFrom(error);
        OnFailure(fimError);
      });
}

void FIMSearchService::SearchFromServer(
    const std::string &appCid, const std::string &fuid, const std::string &fcid,
    const std::string &searchKey, const int64_t &startTime,
    const int64_t &endTime, const FIMMessageContentType &msgType,
    const std::function<void(const std::string &response)> &OnSuccess,
        const std::function<void(const FIMError &error)> &OnFailure) {
          int msgTypeIntValue = static_cast<int>(msgType);
          json params = {
            {"appCid", appCid},
            {"fuid", fuid},
            {"fcid", fcid},
            {"searchKey", searchKey},
            {"startTime", std::to_string(startTime)},
            {"endTime", std::to_string(endTime)},
            {"msgType", std::to_string(msgTypeIntValue)}
          };
          FIMEngine::GetInstance().network_service->Mtop("api/message/searchByFcid", "1", params, [OnSuccess, OnFailure](const FIMHttpResponse& response) {
            // response.body 不为空
            if (response.body.empty()) {
              FIMError error = FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR,
                              response.error_message, "搜索失败");
              OnFailure(error);
              return;
            } else {
              OnSuccess(response.body);
            }
          });
    }

} // namespace fim
} // namespace alibaba
