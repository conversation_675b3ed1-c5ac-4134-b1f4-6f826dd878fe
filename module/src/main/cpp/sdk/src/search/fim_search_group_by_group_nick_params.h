// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <string>
#include <utility>

namespace alibaba {
namespace fim {

struct FIMSearchGroupByGroupNickParams final {

  /**
   * 搜索关键字（群昵称）
   */
  std::string keyword;
  /**
   * 返回结果起始位置，用于搜索结果分页，offset >= 0，默认为第一页
   */
  int32_t offset = 0;
  /**
   * 单次返回结果个数， 0 < max_num <= 1000, 默认为20个
   */
  int32_t max_num = 20;
  /**
   * 返回结果是否按昵称创建时间从小到大排列，默认为升序
   */
  bool is_asc = true;

  FIMSearchGroupByGroupNickParams(std::string keyword_, int32_t offset_,
                                  int32_t max_num_, bool is_asc_)
      : keyword(std::move(keyword_)), offset(std::move(offset_)),
        max_num(std::move(max_num_)), is_asc(std::move(is_asc_)) {}

  FIMSearchGroupByGroupNickParams() {}
};

} // namespace dps
} // namespace alibaba
