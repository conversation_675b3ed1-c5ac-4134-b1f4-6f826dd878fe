//
//  fim_conversation_service.hpp
//  FliggyIMSDK
//
//  Created by 蒙晨 on 2024/2/18.
//

#ifndef fim_conversation_service_h
#define fim_conversation_service_h

#include <functional>
#include <map>
#include <memory>
#include <mutex>
#include <string>
#include <vector>

#include "fim_conversation.h"
#include "fim_conversation_change_listener.h"
#include "fim_conversation_list_listener.h"
#include "fim_error.h"

namespace alibaba {
namespace dps {
class AIMPubConvCreateSingleConvListener;
class AIMPubConvGetConvListener;
class AIMPubConvGetSingleConvListener;
class AIMConvServiceCompleteListener;
enum class AIMConvTypingCommand;
enum class AIMConvTypingMessageContent;
class AIMConvSetTopListener;
class AIMQueryConvActiveStateListener;
class AIMPubConvService;
} // namespace dps
} // namespace alibaba

namespace alibaba {
namespace fim {

std::shared_ptr<AIMPubConvService> getCurrentConvService();

class FIMSDK_API FIMError;

using namespace alibaba::dps;

class FIMSDK_API FIMConversationService {
private:
  static FIMConversationService *instance;
  std::mutex conv_mutex;
  std::mutex conv_change_mutex;
  std::vector<std::shared_ptr<FIMConversationListListener>> conv_listeners;
  std::vector<std::shared_ptr<FIMConversationChangeListener>> conv_change_listeners;

public:
  static FIMConversationService *GetInstance();

  ~FIMConversationService() {}

  /**
   * 创建标准单聊会话。
   * @param uid 对方的fuid
   * @param ext 预留
   */
  // static void CreateSingleConversation(
  //                                      std::string uid,
  //                                      const
  //                                      std::shared_ptr<AIMPubConvCreateSingleConvListener>
  //                                      &listener);

  static void CreateStandardSingleConversation(
      std::string uid, std::map<std::string, std::string> ext,
      const std::function<void(const FIMConversation &conv)> &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);
  /**
   * 创建自定义单聊会话。和sellerId创建，会自动分配客服。
   * ext:  {
        sellerId：店铺id,
        sourceType：来源（1.商品)
        sourceText：来源文本（来自XXX商品详情）
}
   */
  static void CreateCustomSingleConversation(
      std::map<std::string, std::string> ext,
      const std::function<void(const FIMConversation &conv)> &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 创建自定义单聊会话。测试使用，后续会废弃。原因：现在服务端没法通过sellerId
   分发给不同的客服，为了不影响开发，先提供一个通过fuid创建的方式用来测试
   * ext:  {
        sellerId：店铺id,
        sourceType：来源（1.商品)
        sourceText：来源文本（来自XXX商品详情）
   }
   */
  static void TestCreateCustomSingleConversation(
      std::string uid, std::map<std::string, std::string> ext,
      const std::function<void(const FIMConversation &conv)> &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 获取会话列表，隐藏的会话不会返回（拉取会话不需要rpc请求,sdk内部有同步会话机制）
   * @param offset 从offset开始拉取（排序index，根据置顶及最后更新时间进行排序）
   * @param count     需要的Conversation个数
   * @param listener 监听器
   */
  // static void ListLocalConversationsWithOffset(
  //     int32_t offset, int32_t count,
  //     const std::shared_ptr<AIMPubConvGetConvListener> &listener);
  static void ListLocalConversationsWithOffset(
      int32_t offset, int32_t count,
      const std::function<void(const std::vector<FIMConversation> &result)>
          &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 获取本地会话列表，隐藏的会话不会返回（本地，不会有rpc请求）。
   * @param appCid
   * 从指定appCid开始拉取(当appCid为空时，表示拉首屏，会从第0个会话拉取)
   * @param count     需要的Conversation个数
   * @param listener 监听器
   */
  // static void ListLocalConversationsWithCid(
  //     const std::string &appCid, int32_t count,
  //     const std::shared_ptr<AIMPubConvGetConvListener> &listener);
  static void ListLocalConversationsWithCid(
      const std::string &appCid, int32_t count,
      const std::function<void(const std::vector<FIMConversation> &result)>
          &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 获取本地会话列表，包括所有状态的会话（也会返回隐藏的）。
   * @param offset 从offset开始拉取（排序index，根据置顶及最后更新时间进行排序）
   * @param count     需要拉取的Conversation个数
   * @param listener 监听器
   */
  // static void ListAllStatusLocalConvs(
  //     int32_t offset, int32_t count,
  //     const std::shared_ptr<AIMPubConvGetConvListener> &listener);
  static void ListAllStatusLocalConvs(
      int32_t offset, int32_t count,
            const std::function<void(const std::vector<FIMConversation> &result)>
          &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 获取会话Id对应的会话。
   * @param appCid    会话id
   * @param listener 监听器
   */
  // static void GetConversation(
  //     const std::string &appCid,
  //     const std::shared_ptr<AIMPubConvGetSingleConvListener> &listener);
  static void GetConversation(
      const std::string &appCid,
      const std::function<void(const FIMConversation &conv)> &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 获取会话appCid集合对应的会话列表。
   * @param appCids    会话集合id
   * @param listener 监听器
   */
  // static void GetConversations(
  //     const std::vector<std::string> &appCids,
  //     const std::shared_ptr<AIMPubConvGetConvListener> &listener);
  static void GetConversations(
      const std::vector<std::string> &appCids,
      const std::function<void(const std::vector<FIMConversation> &result)>
          &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 批量获取会话Id对应的本地会话（如果本地不存在则不会发送rpc）。
   * @param appCids    会话id
   * @param listener 监听器
   */
  // static void GetLocalConversations(
  //     const std::vector<std::string> &appCids,
  //     const std::shared_ptr<AIMPubConvGetConvListener> &listener);
  static void GetLocalConversations(
      const std::vector<std::string> &appCids,
      const std::function<void(const std::vector<FIMConversation> &result)>
          &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 根据user_id获取对应的会话列表集合。
   * @param user_id    用户id
   * @param listener 监听器
   */
  // static void GetSingleConversations(
  //     const std::string &user_id,
  //     const std::shared_ptr<AIMPubConvGetConvListener> &listener);
  static void GetSingleConversations(
      const std::string &user_id,
      const std::function<void(const std::vector<FIMConversation> &result)>
          &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 批量根据user_ids获取对应的会话列表集合。
   * @param user_ids    用户id集合
   * @param listener 监听器
   */
  // static void GetSingleConversationsWithUserIds(
  //     const std::vector<std::string> &user_ids,
  //     const std::shared_ptr<AIMPubConvGetConvListener> &listener);
  static void GetSingleConversationsWithUserIds(
      const std::vector<std::string> &user_ids,
      const std::function<void(const std::vector<FIMConversation> &result)>
          &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 从本地DB物理删除会话，并且会同步删除会话的所有本地消息(再次拉取到会话消息会再次同步)，群会删除本地群成员，没有rpc发送
   * @param appCid    会话id
   * @param listener 监听器
   */
  // static void RemoveLocalConversation(
  //     const std::string &appCid,
  //     const std::shared_ptr<AIMConvServiceCompleteListener> &listener);
  static void RemoveLocalConversation(
      const std::string &appCid, const std::function<void()> &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 发送正在输入消息事件给接收方，接收者需要精确receiverid，只对单聊会话有效。发送typing事件建议5s为间隔。
   * @param appCid   会话id
   * @param receiver_id   接受者id
   * @param command   TypingCommand
   * @param type TypingMessageContent
   * @param listener 监听器
   */
  // static void UpdateTypingStatus(
  //     const std::string &appCid, const std::string &receiver_id,
  //     AIMConvTypingCommand command, AIMConvTypingMessageContent type,
  //     const std::shared_ptr<AIMConvServiceCompleteListener> &listener);
  static void UpdateTypingStatus(
      const std::string &appCid, const std::string &receiver_id,
      AIMConvTypingCommand command, AIMConvTypingMessageContent type,
      const std::function<void()> &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 更新草稿，草稿只在本地存储。
   * @param appCid   会话id
   * @param draft   草稿内容
   * @param listener 监听器
   */
  // static void UpdateDraftMessage(
  //     const std::string &appCid, const std::string &draft,
  //     const std::shared_ptr<AIMConvServiceCompleteListener> &listener);
  static void UpdateDraftMessage(
      const std::string &appCid, const std::string &draft,
      const std::function<void()> &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 全量更新本地extension数据，只在本地存储。
   * @param appCid   会话id
   * @param local_ext 扩展信息
   * @param listener 监听器
   */
  // static void UpdateLocalExtension(
  //     const std::string &appCid,
  //     const std::map<std::string, std::string> &local_ext,
  //     const std::shared_ptr<AIMConvServiceCompleteListener> &listener);
  static void UpdateLocalExtension(
      const std::string &appCid,
      const std::map<std::string, std::string> &local_ext,
      const std::function<void()> &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 增量更新数据(add or update),不影响其他key
   * @param appCid   会话id
   * @param local_ext 扩展信息
   * @param listener 监听器
   */
  // static void UpdateLocalExtensionByKeys(
  //     const std::string &appCid,
  //     const std::map<std::string, std::string> &local_ext,
  //     const std::shared_ptr<AIMConvServiceCompleteListener> &listener);
  static void UpdateLocalExtensionByKeys(
      const std::string &appCid,
      const std::map<std::string, std::string> &local_ext,
      const std::function<void()> &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 批量增量更新数据(add or update),不影响其他key
   * @param appCid2ext appCid to localext的map
   * @param listener 监听器
   */
  // static void BulkUpdateLocalExtensionByKeys(
  //     const std::map<std::string, std::map<std::string, std::string>>
  //         &appCid2ext,
  //     const std::shared_ptr<AIMConvServiceCompleteListener> &listener);
  static void BulkUpdateLocalExtensionByKeys(
      const std::map<std::string, std::map<std::string, std::string>>
          &appCid2ext,
      const std::function<void()> &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 隐藏会话，会话将处于不可见状态，当会话状态变化时，例如会话收到消息，会话内数据变化时，将变为可见状态。
   * @param appCid   会话id
   * @param listener 监听器
   */
  // static void
  // Hide(const std::string &appCid,
  //      const std::shared_ptr<AIMConvServiceCompleteListener> &listener);
  static void Hide(const std::string &appCid,
                   const std::function<void()> &OnSuccess,
                   const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 更新会话是否免打扰
   * 这里只是更新会话状态的标示，会话变更和消息新增等还是会广播出来，是否通知用户由开发者决定
   * @param appCid   会话id
   * @param mute  是否免打扰
   * @param listener 监听器
   */
  // static void
  // Mute(const std::string &appCid, bool mute,
  //      const std::shared_ptr<AIMConvServiceCompleteListener> &listener);
  static void Mute(const std::string &appCid, bool mute,
                   const std::function<void()> &OnSuccess,
                   const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 会话置顶(返回值为会话置顶服务端时间戳，会同时更新会话的modify_time和top_rank字段)
   * @param appCid   会话id
   * @param top   置顶
   * @param listener 监听器
   */
  // static void
  // SetTop(const std::string &appCid, bool top,
  //        const std::shared_ptr<AIMConvSetTopListener> &listener);
  static void
  SetTop(const std::string &appCid, bool top,
         const std::function<void(int64_t top_rank)> &OnSuccess,
         const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 会话置顶带时间戳，kv的时间戳会存放在local_ext中
   * @param appCid   会话id
   * @param top   置顶
   * @param time_stamp   置顶时间戳字段的kv
   * @param listener 监听器
   */
  // static void SetTopWithTimeStamp(
  //     const std::string &appCid, bool top,
  //     const std::map<std::string, std::string> &time_stamp,
  //     const std::shared_ptr<AIMConvSetTopListener> &listener);
  static void SetTopWithTimeStamp(
      const std::string &appCid, bool top,
      const std::map<std::string, std::string> &time_stamp,
      const std::function<void(int64_t top_rank)> &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 清空会话未读消息
   * @param appCid 会话id
   * @param mid 会话最后一条消息的id
   * @param listener 监听器
   */
  // static void ClearRedPoint(
  //     const std::string &appCid, const std::string &mid,
  //     const std::shared_ptr<AIMConvServiceCompleteListener> &listener);
  static void
  ClearRedPoint(const std::string &appCid, const std::string &mid,
                const std::function<void()> &OnSuccess,
                const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 批量清空所有缓存会话的红点，上层显示或者有过操作的会话都会在缓存中（无notify推送）
   * @param listener 监听器
   */
  // static void ClearAllConvsRedPoint(
  //     const std::shared_ptr<AIMConvServiceCompleteListener> &listener);
  static void ClearAllConvsRedPoint(
      const std::function<void()> &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 清空会话所有消息
   * @param appCid   会话id
   * @param listener 监听器
   */
  // static void
  // Clear(const std::string &appCid,
  //       const std::shared_ptr<AIMConvServiceCompleteListener> &listener);
  static void
  Clear(const std::string &appCid, const std::function<void()> &OnSuccess,
        const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 激活会话，选择会话后需要调用，激活后的会话不会计算红点；如果不再选择会话，需要传入appCid为空字符串
   * 一些特殊情况，比如没有从服务端同步到群的解散和被移除，会补偿解散和移除事件
   * @param appCid    会话id
   */
  static void SetActiveCid(const std::string &appCid);

  /**
   * 注册会话全量变更的监听器
   * @param listener 监听器
   */
  static void AddConvListListener(
      const std::shared_ptr<FIMConversationListListener> &listener);

  /**
   * 删除全量变更的监听器
   * @param listener 监听器
   */
  static void RemoveConvListListener(
      const std::shared_ptr<FIMConversationListListener> &listener);

  /**
   * 注册会话增量变更的监听器
   * @param listener 监听器
   */
  static void AddConvChangeListener(
      const std::shared_ptr<FIMConversationChangeListener> &listener);

  /**
   * 删除会话增量变更的监听器
   * @param listener 监听器
   */
  static void RemoveConvChangeListener(
      const std::shared_ptr<FIMConversationChangeListener> &listener);

  /**
   * 删除所有会话的监听器
   */
  static void RemoveAllConvListListener();

  /**
   * 删除所有会话的变更监听器
   */
  static void RemoveAllConvChangeListener();

  /**
   * 判断会话是否激活
   */
  // static void IsConversationActive(
  //     const std::string &appCid,
  //     const std::shared_ptr<AIMQueryConvActiveStateListener> &listener);
  static void IsConversationActive(
      const std::string &appCid,
      const std::function<void(bool result)> &OnSuccess,
      const std::function<void(const FIMError &error)> &OnFailure);

  /**
   * 生成标准单聊cid
   * @param senderId 发送者id
   * @param receiverId 接收者id
   */
  /* static std::string GenerateStandardAppCid(const std::string &senderId,
                                             const std::string &receiverId);*/

  /**
   * 判断是否为标准单聊
   * @param appCid 会话id
   */
//  static bool IsStandard(const std::string &appCid);

  /**
   * 获取标准单聊targetUid
   * @param selfAppUid 自身uid
   * @param appCid 会话id
   */
//  static std::string GetTargetAppUid(const std::string &selfAppUid,
//                                     const std::string &appCid);

  /**
   * 生成标准appCid
   * @param uids userids
   */
  static std::string GenerateAppCid(std::vector<std::string> uids);
};

} // namespace fim
} // namespace alibaba

#endif /* fim_conversation_service_h */
