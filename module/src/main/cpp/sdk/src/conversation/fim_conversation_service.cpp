//
//  fim_conversation_service.cpp
//  FliggyIMSDK
//
//  Created by 蒙晨 on 2024/2/18.
//

#include "fim_conversation_service.h"
#include "aim_pub_c_api.h"
#include "aim_pub_module.h"
#include <algorithm>
#include <string>
// FIMEngine
#include "conv_listeners_manager.h"
#include "fim_engine.h"
// AIMPubConvService
#include "aim_pub_conv_service.h"
// AIMPubConvCreateSingleConvListener
#include "aim_pub_conv_create_single_conv_listener.h"
#include "aim_pub_conv_create_single_conv_param.h"
// FIMError
#include "fim_conversation.hpp"
#include "fim_error.hpp"
// #include "fim_conv_get_conv_listener.h"
// #include "fim_conv_get_conv_adapter.h"

#include "conv_listeners_manager.h"
#include "fim_error_code.h"
#include "log_service.h"
#include <iostream>
#include <string>
#include <vector>

using json = nlohmann::json;

namespace alibaba {
namespace fim {
using namespace alibaba::dps;



// void FIMConversationService::CreateSingleConversation(std::string uid,
//                                                       const
//                                                       std::shared_ptr<AIMPubConvCreateSingleConvListener>
//                                                       &listener) {
//     std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
//     if (convService) {
//         AIMPubConvCreateSingleConvParam param =
//         AIMPubConvCreateSingleConvParam(); std::vector<std::string> uids =
//         {FIMEngine::GetInstance().fuid, uid}; param.appCid =
//         GenerateAppCid(uids); param.uids = uids;
//         convService->CreateSingleConversation(param, listener);
//     }
// }

void CreateSingleConversation(
    AIMPubConvCreateSingleConvParam param,
    const std::function<void(const FIMConversation &conv)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    convService->CreateSingleConversation(
        param,
        [OnSuccess, OnFailure](const dps::AIMPubConversation &conv) {
          FIMConversation fimConv = FIMConversationFrom(conv);
          LogService::DebugLog([fimConv] {
            std::string result = "CreateSingleConversation: ";
            result += "<conv.appCid:" + fimConv.appCid + ">";
            return result;
          });
          OnSuccess(FIMConversationFrom(conv));
        },
        [OnFailure](const dps::DPSError &error) {
          LogService::ErrorLog([error] {
            std::string result = "CreateSingleConversation: ";
            result += "<error.code:" + std::to_string(error.code) + ">";
            result += "<error.message:" + error.developer_message + ">";
            return result;
          });
          OnFailure(FIMErrorFrom(error));
        });
  }
}

void FIMConversationService::CreateStandardSingleConversation(
    std::string uid, std::map<std::string, std::string> ext,
    const std::function<void(const FIMConversation &conv)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {

  LogService::DebugLog([uid, ext] {
    std::string result = "CreateStandardSingleConversation: ";
    result += "<uid:" + uid + ">";
    for (auto &kv : ext) {
      result += "<" + kv.first + ":" + kv.second + ">";
    }
    return result;
  });
  AIMPubConvCreateSingleConvParam param = AIMPubConvCreateSingleConvParam();
  param.ext = ext;
  std::vector<std::string> uids = {FIMEngine::GetInstance().fuid, uid};
  param.appCid = GenerateAppCid(uids);
  param.uids = uids;
  CreateSingleConversation(param, OnSuccess, OnFailure);
}

/**
 * 创建自定义单聊会话。和sellerId创建，会自动分配客服。
 * ext:  {
      sellerId：店铺id,
      sourceType：来源（1.商品)
      sourceText：来源文本（来自XXX商品详情）
 }
 * @param OnLocalData 这个时候没有头像和名称
 * @param OnServerData 这个时候获取到了头像和名称
 */
void FIMConversationService::CreateCustomSingleConversation(
    std::map<std::string, std::string> ext,
    const std::function<void(const FIMConversation &conv)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  LogService::DebugLog([ext] {
    std::string result = "CreateCustomSingleConversation: ";
    for (auto &kv : ext) {
      result += "<" + kv.first + ":" + kv.second + ">";
    }
    return result;
  });
  AIMPubConvCreateSingleConvParam param = AIMPubConvCreateSingleConvParam();
  param.ext = ext;
  CreateSingleConversation(param, OnSuccess, OnFailure);
}

/**
 * 创建自定义单聊会话。测试使用，后续会废弃。原因：现在服务端没法通过sellerId
 分发给不同的客服，为了不影响开发，先提供一个通过fuid创建的方式用来测试
 * ext:  {
      sellerId：店铺id,
      sourceType：来源（1.商品)
      sourceText：来源文本（来自XXX商品详情）
 }
 * @param OnLocalData 这个时候没有头像和名称
 * @param OnServerData 这个时候获取到了头像和名称
 */
void FIMConversationService::TestCreateCustomSingleConversation(
    std::string uid, std::map<std::string, std::string> ext,
    const std::function<void(const FIMConversation &conv)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  LogService::DebugLog([uid, ext] {
    std::string result = "TestCreateCustomSingleConversation: ";
    result += "<uid:" + uid + ">";
    for (auto &kv : ext) {
      result += "<" + kv.first + ":" + kv.second + ">";
    }
    return result;
  });
  AIMPubConvCreateSingleConvParam param = AIMPubConvCreateSingleConvParam();
  param.ext = ext;
  std::vector<std::string> uids = {FIMEngine::GetInstance().fuid, uid};
  param.uids = uids;
  CreateSingleConversation(param, OnSuccess, OnFailure);
}

// void FIMConversationService::ListLocalConversationsWithOffset(
//     int32_t offset, int32_t count,
//     const std::shared_ptr<AIMPubConvGetConvListener> &listener) {
//     std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
//     if (convService) {
//         convService->ListLocalConversationsWithOffset(offset, count,
//         listener);
//     }
// }

void FIMConversationService::ListLocalConversationsWithOffset(
    int32_t offset, int32_t count,
    const std::function<void(const std::vector<FIMConversation> &result)>
        &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  LogService::DebugLog([offset, count] {
    std::string result = "ListLocalConversationsWithOffset: ";
    result += "<offset:" + std::to_string(offset) + ">";
    result += "<count:" + std::to_string(count) + ">";
    return result;
  });
  if (convService) {
    convService->ListLocalConversationsWithOffset(
        offset, count,
        [OnSuccess,
         OnFailure](const std::vector<dps::AIMPubConversation> &result) {
          auto fimConvs = FIMConversationListFrom(result);
          LogService::DebugLog([fimConvs] {
            std::string result = "ListLocalConversationsWithOffset: ";
            for (auto conv : fimConvs) {
              result += "<conv.appCid:" + conv.appCid + ">";
            }
            return result;
          });
          OnSuccess(fimConvs);
        },
        [OnFailure](const dps::DPSError &error) {
          OnFailure(FIMErrorFrom(error));
        });
  }
}

/**
 * 获取本地会话列表，隐藏的会话不会返回（本地，不会有rpc请求）。
 * @param appCid
 * 从指定appCid开始拉取(当appCid为空时，表示拉首屏，会从第0个会话拉取)
 * @param count     需要的Conversation个数
 * @param listener 监听器
 */

// void FIMConversationService::ListLocalConversationsWithCid(
//     const std::string &appCid, int32_t count,
//     const std::shared_ptr<AIMPubConvGetConvListener> &listener) {
//     std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
//     if (convService) {
//         convService->ListLocalConversationsWithCid(appCid, count, listener);
//     }
// }

void FIMConversationService::ListLocalConversationsWithCid(
    const std::string &appCid, int32_t count,
    const std::function<void(const std::vector<FIMConversation> &result)>
        &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    convService->ListLocalConversationsWithCid(
        appCid, count,
        [OnSuccess,
         OnFailure](const std::vector<dps::AIMPubConversation> &result) {
          auto fimConvs = FIMConversationListFrom(result);
          OnSuccess(fimConvs);
        },
        [OnFailure](const dps::DPSError &error) {
          OnFailure(FIMErrorFrom(error));
        });
  }
}

/**
 * 获取本地会话列表，包括所有状态的会话（也会返回隐藏的）。
 * @param offset 从offset开始拉取（排序index，根据置顶及最后更新时间进行排序）
 * @param count     需要拉取的Conversation个数
 * @param listener 监听器
 */
// void FIMConversationService::ListAllStatusLocalConvs(
//     int32_t offset, int32_t count,
//     const std::shared_ptr<AIMPubConvGetConvListener> &listener) {
//     std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
//     if (convService) {
//         convService->ListAllStatusLocalConvs(offset, count, listener);
//     }
// }

void FIMConversationService::ListAllStatusLocalConvs(
    int32_t offset, int32_t count,
    const std::function<void(const std::vector<FIMConversation> &result)>
        &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  LogService::DebugLog([offset, count] {
    std::string result = "ListAllStatusLocalConvs: ";
    result += "<offset:" + std::to_string(offset) + ">";
    result += "<count:" + std::to_string(count) + ">";
    return result;
  });
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    convService->ListAllStatusLocalConvs(
        offset, count,
        [OnSuccess,
         OnFailure](const std::vector<dps::AIMPubConversation> &result) {
          auto fimConvs = FIMConversationListFrom(result);
          OnSuccess(fimConvs);
        },
        [OnFailure](const dps::DPSError &error) {
          OnFailure(FIMErrorFrom(error));
        });
  }
}

/**
 * 获取会话Id对应的会话。
 * @param appCid    会话id
 * @param listener 监听器
 */
// void FIMConversationService::GetConversation(
//     const std::string &appCid,
//     const std::shared_ptr<AIMPubConvGetSingleConvListener> &listener) {
//     std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
//     if (convService) {
//         convService->GetConversation(appCid, listener);
//     }
// }

void FIMConversationService::GetConversation(
    const std::string &appCid,
    const std::function<void(const FIMConversation &conv)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  LogService::DebugLog([appCid] {
    std::string result = "GetConversation: ";
    result += "<appCid:" + appCid + ">";
    return result;
  });
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    convService->GetConversation(
        appCid,
        [OnSuccess, OnFailure](const dps::AIMPubConversation &conv) {
          FIMConversation fimConv = FIMConversationFrom(conv);
          OnSuccess(FIMConversationFrom(conv));
        },
        [OnFailure](const dps::DPSError &error) {
          OnFailure(FIMErrorFrom(error));
        });
  }
}

/**
 * 获取会话appCid集合对应的会话列表。
 * @param appCids    会话集合id
 * @param listener 监听器
 */
// void FIMConversationService::GetConversations(
//     const std::vector<std::string> &appCids,
//     const std::shared_ptr<AIMPubConvGetConvListener> &listener) {
//     std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
//     if (convService) {
//         convService->GetConversations(appCids, listener);
//     }
// }

void FIMConversationService::GetConversations(
    const std::vector<std::string> &appCids,
    const std::function<void(const std::vector<FIMConversation> &result)>
        &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  LogService::DebugLog([appCids] {
    std::string result = "GetConversations: ";
    for (auto appCid : appCids) {
      result += "<appCid:" + appCid + ">";
    }
    return result;
  });
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    convService->GetConversations(
        appCids,
        [OnSuccess,
         OnFailure](const std::vector<dps::AIMPubConversation> &result) {
          auto fimConvs = FIMConversationListFrom(result);
          OnSuccess(fimConvs);
        },
        [OnFailure](const dps::DPSError &error) {
          OnFailure(FIMErrorFrom(error));
        });
  }
}

/**
 * 批量获取会话Id对应的本地会话（如果本地不存在则不会发送rpc）。
 * @param appCids    会话id
 * @param listener 监听器
 */
// void FIMConversationService::GetLocalConversations(
//     const std::vector<std::string> &appCids,
//     const std::shared_ptr<AIMPubConvGetConvListener> &listener) {
//     std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
//     if (convService) {
//         convService->GetLocalConversations(appCids, listener);
//     }
// }

void FIMConversationService::GetLocalConversations(
    const std::vector<std::string> &appCids,
    const std::function<void(const std::vector<FIMConversation> &result)>
        &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  LogService::DebugLog([appCids] {
    std::string result = "GetLocalConversations: ";
    for (auto appCid : appCids) {
      result += "<appCid:" + appCid + ">";
    }
    return result;
  });
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    convService->GetLocalConversations(
        appCids,
        [OnSuccess,
         OnFailure](const std::vector<dps::AIMPubConversation> &result) {
          auto fimConvs = FIMConversationListFrom(result);
          OnSuccess(fimConvs);
        },
        [OnFailure](const dps::DPSError &error) {
          OnFailure(FIMErrorFrom(error));
        });
  }
}

/**
 * 根据user_id获取对应的会话列表集合。
 * @param user_id    用户id
 * @param listener 监听器
 */

// void FIMConversationService::GetSingleConversations(
//     const std::string &user_id,
//     const std::shared_ptr<AIMPubConvGetConvListener> &listener) {
//     std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
//     if (convService) {
//         convService->GetSingleConversations(user_id, listener);
//     }
// }

void FIMConversationService::GetSingleConversations(
    const std::string &user_id,
    const std::function<void(const std::vector<FIMConversation> &result)>
        &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  LogService::DebugLog([user_id] {
    std::string result = "GetSingleConversations: ";
    result += "<user_id:" + user_id + ">";
    return result;
  });
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    convService->GetSingleConversations(
        user_id,
        [OnSuccess,
         OnFailure](const std::vector<dps::AIMPubConversation> &result) {
          auto fimConvs = FIMConversationListFrom(result);
          OnSuccess(fimConvs);
        },
        [OnFailure](const dps::DPSError &error) {
          OnFailure(FIMErrorFrom(error));
        });
  }
}

/**
 * 批量根据user_ids获取对应的会话列表集合。
 * @param user_ids    用户id集合
 * @param listener 监听器
 */
// void FIMConversationService::GetSingleConversationsWithUserIds(
//     const std::vector<std::string> &user_ids,
//     const std::shared_ptr<AIMPubConvGetConvListener> &listener) {
//     std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
//     if (convService) {
//         convService->GetSingleConversationsWithUserIds(user_ids, listener);
//     }
// }

void FIMConversationService::GetSingleConversationsWithUserIds(
    const std::vector<std::string> &user_ids,
    const std::function<void(const std::vector<FIMConversation> &result)>
        &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  LogService::DebugLog([user_ids] {
    std::string result = "GetSingleConversationsWithUserIds: ";
    for (auto user_id : user_ids) {
      result += "<user_id:" + user_id + ">";
    }
    return result;
  });

  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    convService->GetSingleConversationsWithUserIds(
        user_ids,
        [OnSuccess,
         OnFailure](const std::vector<dps::AIMPubConversation> &result) {
          auto fimConvs = FIMConversationListFrom(result);
          OnSuccess(fimConvs);
        },
        [OnFailure](const dps::DPSError &error) {
          OnFailure(FIMErrorFrom(error));
        });
  }
}

/**
 * 从本地DB物理删除会话，并且会同步删除会话的所有本地消息(再次拉取到会话消息会再次同步)，群会删除本地群成员，没有rpc发送
 * @param appCid    会话id
 * @param listener 监听器
 */
// void FIMConversationService::RemoveLocalConversation(
//     const std::string &appCid,
//     const std::shared_ptr<AIMConvServiceCompleteListener> &listener) {
//     std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
//     if (convService) {
//         convService->RemoveLocalConversation(appCid, listener);
//     }
// }

void FIMConversationService::RemoveLocalConversation(
    const std::string &appCid, const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  LogService::DebugLog([appCid] {
    std::string result = "RemoveLocalConversation: ";
    result += "<appCid:" + appCid + ">";
    return result;
  });
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    convService->RemoveLocalConversation(
        appCid, [OnSuccess]() { OnSuccess(); },
        [OnFailure](const dps::DPSError &error) {
          OnFailure(FIMErrorFrom(error));
        });
  }
}

/**
 * 发送正在输入消息事件给接收方，接收者需要精确receiverid，只对单聊会话有效。发送typing事件建议5s为间隔。
 * @param appCid   会话id
 * @param receiverid   接受者id
 * @param command   TypingCommand
 * @param type TypingMessageContent
 * @param listener 监听器
 */
// void FIMConversationService::UpdateTypingStatus(
//     const std::string &appCid, const std::string &receiver_id,
//     AIMConvTypingCommand command, AIMConvTypingMessageContent type,
//     const std::shared_ptr<AIMConvServiceCompleteListener> &listener) {
//     std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
//     if (convService) {
//         convService->UpdateTypingStatus(appCid, receiver_id, command, type,
//         listener);
//     }
// }

void FIMConversationService::UpdateTypingStatus(
    const std::string &appCid, const std::string &receiver_id,
    AIMConvTypingCommand command, AIMConvTypingMessageContent type,
    const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {

  LogService::DebugLog([appCid, receiver_id, command, type] {
    std::string result = "UpdateTypingStatus: ";
    result += "<appCid:" + appCid + ">";
    result += "<receiver_id:" + receiver_id + ">";
    result += "<command:" + std::to_string(static_cast<int32_t>(command)) +
              ">"; // Fix the 'to_string' error
    result += "<type:" + std::to_string(static_cast<int32_t>(type)) +
              ">"; // Fix the 'to_string' error
    return result;
  });
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    convService->UpdateTypingStatus(
        appCid, receiver_id, command, type, [OnSuccess]() { OnSuccess(); },
        [OnFailure](const dps::DPSError &error) {
          OnFailure(FIMErrorFrom(error));
        });
  }
}

/**
 * 更新草稿，草稿只在本地存储。
 * @param appCid   会话id
 * @param draft   草稿内容
 * @param listener 监听器
 */

// void FIMConversationService::UpdateDraftMessage(
//     const std::string &appCid, const std::string &draft,
//     const std::shared_ptr<AIMConvServiceCompleteListener> &listener) {
//     std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
//     if (convService) {
//         convService->UpdateDraftMessage(appCid, draft, listener);
//     }
// }

void FIMConversationService::UpdateDraftMessage(
    const std::string &appCid, const std::string &draft,
    const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  LogService::DebugLog([appCid, draft] {
    std::string result = "UpdateDraftMessage: ";
    result += "<appCid:" + appCid + ">";
    result += "<draft:" + draft + ">";
    return result;
  });
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    convService->UpdateDraftMessage(
        appCid, draft, [OnSuccess]() { OnSuccess(); },
        [OnFailure](const dps::DPSError &error) {
          OnFailure(FIMErrorFrom(error));
        });
  }
}

/**
 * 全量更新本地extension数据，只在本地存储。
 * @param appCid   会话id
 * @param local_ext 扩展信息
 * @param listener 监听器
 */

// void FIMConversationService::UpdateLocalExtension(
//     const std::string &appCid,
//     const std::map<std::string, std::string> &local_ext,
//     const std::shared_ptr<AIMConvServiceCompleteListener> &listener) {
//     std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
//     if (convService) {
//         convService->UpdateLocalExtension(appCid, local_ext, listener);
//     }
// }

void FIMConversationService::UpdateLocalExtension(
    const std::string &appCid,
    const std::map<std::string, std::string> &local_ext,
    const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  LogService::DebugLog([appCid, local_ext] {
    std::string result = "UpdateLocalExtension: ";
    result += "<appCid:" + appCid + ">";
    for (auto &kv : local_ext) {
      result += "<" + kv.first + ":" + kv.second + ">";
    }
    return result;
  });
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    convService->UpdateLocalExtension(
        appCid, local_ext, [OnSuccess]() { OnSuccess(); },
        [OnFailure](const dps::DPSError &error) {
          OnFailure(FIMErrorFrom(error));
        });
  }
}

/**
 * 增量更新数据(add or update),不影响其他key
 * @param appCid   会话id
 * @param local_ext 扩展信息
 * @param listener 监听器
 */
// void FIMConversationService::UpdateLocalExtensionByKeys(
//     const std::string &appCid,
//     const std::map<std::string, std::string> &local_ext,
//     const std::shared_ptr<AIMConvServiceCompleteListener> &listener) {
//     std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
//     if (convService) {
//         convService->UpdateLocalExtensionByKeys(appCid, local_ext, listener);
//     }
// }

void FIMConversationService::UpdateLocalExtensionByKeys(
    const std::string &appCid,
    const std::map<std::string, std::string> &local_ext,
    const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  LogService::DebugLog([appCid, local_ext] {
    std::string result = "UpdateLocalExtensionByKeys: ";
    result += "<appCid:" + appCid + ">";
    result += "local_ext:";
    for (auto &kv : local_ext) {
      result += "<" + kv.first + ":" + kv.second + ">";
    }
    return result;
  });
  if (convService) {
    convService->UpdateLocalExtensionByKeys(
        appCid, local_ext, [OnSuccess]() { OnSuccess(); },
        [OnFailure](const dps::DPSError &error) {
          OnFailure(FIMErrorFrom(error));
        });
  }
}

/**
 * 批量增量更新数据(add or update),不影响其他key
 * @param appCid2ext appCid to localext的map
 * @param listener 监听器
 */

// void FIMConversationService::BulkUpdateLocalExtensionByKeys(
//     const std::map<std::string, std::map<std::string, std::string>>
//     &appCid2ext, const std::shared_ptr<AIMConvServiceCompleteListener>
//     &listener) { std::shared_ptr<AIMPubConvService> convService =
//     getCurrentConvService(); if (convService) {
//         convService->BulkUpdateLocalExtensionByKeys(appCid2ext, listener);
//     }
// }

void FIMConversationService::BulkUpdateLocalExtensionByKeys(
    const std::map<std::string, std::map<std::string, std::string>> &appCid2ext,
    const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  LogService::DebugLog([appCid2ext] {
    std::string result = "BulkUpdateLocalExtensionByKeys: ";
    for (auto &kv : appCid2ext) {
      result += "<appCid:" + kv.first + ">";
      for (auto &kv2 : kv.second) {
        result += "<" + kv2.first + ":" + kv2.second + ">";
      }
    }
    return result;
  });
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    convService->BulkUpdateLocalExtensionByKeys(
        appCid2ext, [OnSuccess]() { OnSuccess(); },
        [OnFailure](const dps::DPSError &error) {
          OnFailure(FIMErrorFrom(error));
        });
  }
}

// void FIMConversationService::Hide(
//     const std::string &appCid,
//     const std::shared_ptr<AIMConvServiceCompleteListener> &listener) {
//     std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
//     if (convService) {
//         convService->Hide(appCid, listener);
//     }
// }

void FIMConversationService::Hide(
    const std::string &appCid, const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  LogService::DebugLog([appCid] {
    std::string result = "Hide: ";
    result += "<appCid:" + appCid + ">";
    return result;
  });
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    convService->Hide(
        appCid, [OnSuccess]() { OnSuccess(); },
        [OnFailure](const dps::DPSError &error) {
          OnFailure(FIMErrorFrom(error));
        });
  }
}

/**
 * 更新会话是否免打扰
 * 这里只是更新会话状态的标示，会话变更和消息新增等还是会广播出来，是否通知用户由开发者决定
 * @param appCid   会话id
 * @param mute  是否免打扰
 * @param listener 监听器
 */

// void FIMConversationService::Mute(
//     const std::string &appCid, bool mute,
//     const std::shared_ptr<AIMConvServiceCompleteListener> &listener) {
//     std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
//     if (convService) {
//         convService->Mute(appCid, mute, listener);
//     }
// }

void FIMConversationService::Mute(
    const std::string &appCid, bool mute,
    const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  LogService::DebugLog([appCid, mute] {
    std::string result = "Mute: ";
    result += "<appCid:" + appCid + ">";
    result += "<mute:" + std::to_string(mute) + ">";
    return result;
  });
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    convService->Mute(
        appCid, mute, [OnSuccess]() { OnSuccess(); },
        [OnFailure](const dps::DPSError &error) {
          OnFailure(FIMErrorFrom(error));
        });
  }
}

/**
 * 会话置顶(返回值为会话置顶服务端时间戳，会同时更新会话的modify_time和top_rank字段)
 * @param appCid   会话id
 * @param top   置顶
 * @param listener 监听器
 */
// void FIMConversationService::SetTop(
//     const std::string &appCid, bool top,
//     const std::shared_ptr<AIMConvSetTopListener> &listener) {
//     std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
//     if (convService) {
//         convService->SetTop(appCid, top, listener);
//     }
// }

void FIMConversationService::SetTop(
    const std::string &appCid, bool top,
    const std::function<void(int64_t top_rank)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  LogService::DebugLog([appCid, top] {
    std::string result = "SetTop: ";
    result += "<appCid:" + appCid + ">";
    result += "<top:" + std::to_string(top) + ">";
    return result;
  });
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    convService->SetTop(
        appCid, top, [OnSuccess](int64_t top_rank) { OnSuccess(top_rank); },
        [OnFailure](const dps::DPSError &error) {
          OnFailure(FIMErrorFrom(error));
        });
  }
}
/**
 * 会话置顶带时间戳，kv的时间戳会存放在local_ext中
 * @param appCid   会话id
 * @param top   置顶
 * @param time_stamp   置顶时间戳字段的kv
 * @param listener 监听器
 */

// void FIMConversationService::SetTopWithTimeStamp(
//     const std::string &appCid, bool top,
//     const std::map<std::string, std::string> &time_stamp,
//     const std::shared_ptr<AIMConvSetTopListener> &listener) {
//     std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
//     if (convService) {
//         convService->SetTopWithTimeStamp(appCid, top, time_stamp, listener);
//     }
// }

void FIMConversationService::SetTopWithTimeStamp(
    const std::string &appCid, bool top,
    const std::map<std::string, std::string> &time_stamp,
    const std::function<void(int64_t top_rank)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  LogService::DebugLog([appCid, top, time_stamp] {
    std::string result = "SetTopWithTimeStamp: ";
    result += "<appCid:" + appCid + ">";
    result += "<top:" + std::to_string(top) + ">";
    for (auto &kv : time_stamp) {
      result += "<" + kv.first + ":" + kv.second + ">";
    }
    return result;
  });
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    convService->SetTopWithTimeStamp(
        appCid, top, time_stamp,
        [OnSuccess](int64_t top_rank) { OnSuccess(top_rank); },
        [OnFailure](const dps::DPSError &error) {
          OnFailure(FIMErrorFrom(error));
        });
  }
}

/**
 * 清空会话未读消息
 * @param appCid 会话id
 * @param mid 会话最后一条消息的id
 * @param listener 监听器
 */

// void FIMConversationService::ClearRedPoint(
//     const std::string &appCid, const std::string &mid,
//     const std::shared_ptr<AIMConvServiceCompleteListener> &listener) {
//     std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
//     if (convService) {
//         convService->ClearRedPoint(appCid, mid, listener);
//     }
// }

void FIMConversationService::ClearRedPoint(
    const std::string &appCid, const std::string &mid,
    const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    convService->ClearRedPoint(
        appCid, mid, [OnSuccess]() { OnSuccess(); },
        [OnFailure](const dps::DPSError &error) {
          OnFailure(FIMErrorFrom(error));
        });
  }
}
/**
 * 批量清空所有缓存会话的红点，上层显示或者有过操作的会话都会在缓存中（无notify推送）
 * @param listener 监听器
 */

// void FIMConversationService::ClearAllConvsRedPoint(
//     const std::shared_ptr<AIMConvServiceCompleteListener> &listener) {
//     std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
//     if (convService) {
//         convService->ClearAllConvsRedPoint(listener);
//     }
// }

void FIMConversationService::ClearAllConvsRedPoint(
    const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    convService->ClearAllConvsRedPoint([OnSuccess]() { OnSuccess(); },
                                       [OnFailure](const dps::DPSError &error) {
                                         OnFailure(FIMErrorFrom(error));
                                       });
  }
}

/**
 * 清空会话所有消息
 * @param appCid   会话id
 * @param listener 监听器
 */

// void FIMConversationService::Clear(
//     const std::string &appCid,
//     const std::shared_ptr<AIMConvServiceCompleteListener> &listener) {
//     std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
//     if (convService) {
//         convService->Clear(appCid, listener);
//     }
// }

void FIMConversationService::Clear(
    const std::string &appCid, const std::function<void()> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    convService->Clear(
        appCid, [OnSuccess]() { OnSuccess(); },
        [OnFailure](const dps::DPSError &error) {
          OnFailure(FIMErrorFrom(error));
        });
  }
}
/**
 * 激活会话，选择会话后需要调用，激活后的会话不会计算红点；如果不再选择会话，需要传入appCid为空字符串
 * 一些特殊情况，比如没有从服务端同步到群的解散和被移除，会补偿解散和移除事件
 * @param appCid    会话id
 */

void FIMConversationService::SetActiveCid(const std::string &appCid) {
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  // LogService::DebugLog(std::function<std::string ()> logStringProducer)
  LogService::DebugLog([appCid] { return "SetActiveCid: " + appCid; });
  if (convService) {
    convService->SetActiveCid(appCid);
    LogService::DebugLog(
        [] { return "SetActiveCid: convService is not null"; });
  } else {
    LogService::ErrorLog([] { return "SetActiveCid: convService is null"; });
  }
}

/**
 * 注册会话全量变更的监听器
 * @param listener 监听器
 */

void FIMConversationService::AddConvListListener(
    const std::shared_ptr<FIMConversationListListener> &listener) {
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    LogService::DebugLog([] { return "AddConvListListener"; });
    convService->AddConvListListener(listener);
  } else {
    LogService::ErrorLog(
        [] { return "AddConvListListener: convService is null"; });
  }
}

/**
 * 删除全量变更的监听器
 * @param listener 监听器
 */

void FIMConversationService::RemoveConvListListener(
    const std::shared_ptr<FIMConversationListListener> &listener) {
  LogService::DebugLog([] { return "RemoveConvListListener"; });
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    LogService::DebugLog([] { return "RemoveConvListListener"; });
    convService->RemoveConvListListener(listener);
  } else {
    LogService::ErrorLog(
        [] { return "RemoveConvListListener: convService is null"; });
  }
}

/**
 * 注册会话增量变更的监听器
 * @param listener 监听器
 */

void FIMConversationService::AddConvChangeListener(
    const std::shared_ptr<FIMConversationChangeListener> &listener) {
  LogService::DebugLog([]() { return "AddConvChangeListener"; });
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    LogService::DebugLog([]() { return "AddConvChangeListener"; });
    convService->AddConvChangeListener(listener);
  } else {
    LogService::ErrorLog(
        []() { return "AddConvChangeListener: convService is null"; });
  }
}

/**
 * 删除会话增量变更的监听器
 * @param listener 监听器
 */

void FIMConversationService::RemoveConvChangeListener(
    const std::shared_ptr<FIMConversationChangeListener> &listener) {
  LogService::DebugLog([]() { return "RemoveConvChangeListener"; });
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    LogService::DebugLog([]() { return "FIM RemoveConvChangeListener"; });
    convService->RemoveConvChangeListener(listener);
  } else {
    LogService::ErrorLog(
        []() { return "RemoveConvChangeListener: convService is null"; });
  }
}

/**
 * 删除所有会话的监听器
 */

void FIMConversationService::RemoveAllConvListListener() {
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    LogService::DebugLog([]() { return "FIM RemoveAllConvListListener"; });
    convService->RemoveAllConvListListener();
  } else {
    LogService::ErrorLog(
        []() { return "RemoveAllConvListListener: convService is null"; });
  }
}

/**
 * 删除所有会话的变更监听器
 */

void FIMConversationService::RemoveAllConvChangeListener() {
  LogService::DebugLog([]() { return "RemoveAllConvChangeListener"; });
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    LogService::DebugLog([]() { return "RemoveAllConvChangeListener"; });
    convService->RemoveAllConvChangeListener();
  } else {
    LogService::ErrorLog(
        []() { return "RemoveAllConvChangeListener: convService is null"; });
  }
}

/**
 * 判断会话是否激活
 */

// void FIMConversationService::IsConversationActive(
//     const std::string &appCid,
//     const std::shared_ptr<AIMQueryConvActiveStateListener> &listener) {
//     std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
//     if (convService) {
//         convService->IsConversationActive(appCid, listener);
//     }
// }

void FIMConversationService::IsConversationActive(
    const std::string &appCid,
    const std::function<void(bool result)> &OnSuccess,
    const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<AIMPubConvService> convService = getCurrentConvService();
  if (convService) {
    convService->IsConversationActive(
        appCid, [OnSuccess](bool result) { OnSuccess(result); },
        [OnFailure](const dps::DPSError &error) {
          OnFailure(FIMErrorFrom(error));
        });
  }
}
/**
 * 生成标准单聊cid
 * @param senderId 发送者id
 * @param receiverId 接收者id
 */
//
// static std::string GenerateStandardAppCid(const std::string &senderId,
//                                          const std::string &receiverId) {
//    return AIMPubConvService::GenerateStandardAppCid(senderId, receiverId);
//}

/**
 * 判断是否为标准单聊
 * @param appCid 会话id
 */

//bool FIMConversationService::IsStandard(const std::string &appCid) {
//  return AIMPubConvService::IsStandard(appCid);
//}

/**
 * 获取标准单聊targetUid
 * @param selfAppUid 自身uid
 * @param appCid 会话id
 */
//std::string FIMConversationService::GetTargetAppUid(const std::string &selfAppUid,
//                                   const std::string &appCid) {
//  return AIMPubConvService::GetTargetAppUid(selfAppUid, appCid);
//}

#pragma mark - tools
std::string
FIMConversationService::GenerateAppCid(std::vector<std::string> uids) {
  //$1$UserId1:UserId2:UserId3...  其中（字典序小的userid靠前）
  std::sort(uids.begin(), uids.end());
  std::string appCid = "$1$";
  for (auto uid : uids) {
    appCid += uid + ":";
  }
  appCid.pop_back();
  return appCid;
}

std::shared_ptr<AIMPubConvService> getCurrentConvService() {
  alibaba::dps::AIMPubModule *rawModulePtr =
      GetAIMPubModuleInstance(FIMEngine::GetInstance().fuid);
  if (rawModulePtr) {
    std::shared_ptr<AIMPubConvService> convService =
        rawModulePtr->GetConvService();
    if (convService) {
      return convService;
    } else {
      return nullptr;
    }
  } else {
    return nullptr;
  }
}

} // namespace fim
} // namespace alibaba
