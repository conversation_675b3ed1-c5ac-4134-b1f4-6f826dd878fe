#pragma once
#include "aim_pub_msg_change_listener.h"
#include "aim_pub_msg_listener.h"
#include "fim_conversation_change_listener.h"
#include "fim_conversation_list_listener.h"
#include "fim_message_change_listener.h"
#include "fim_message_listener.h"
#include "aim_pub_conv_change_listener.h"
#include "aim_pub_conv_list_listener.h"
#include <mutex>
#include <type_traits>

namespace alibaba {
namespace fim {
class ConvListenerProxy : public dps::AIMPubConvListListener,
                             public dps::AIMPubConvChangeListener {
#pragma mark - 实现AIMPubConvListListener
  /**
   * 新增会话
   * @param convs 新增的会话集合
   */
  void OnAddedConversations(const std::vector<AIMPubConversation> &convs);

  /**
   * 删除会话
   * @param appCids 删除的会话appCid集合
   */
  void OnRemovedConversations(const std::vector<std::string> &appCids);

  /**
   * 所有会话被更新替换
   * @param convs 更新后的会话集合
   */
  void OnRefreshedConversations(const std::vector<AIMPubConversation> &convs);
#pragma mark - 实现AIMPubConvChangeListener
  /**
   * 会话状态变更
   * @param convs 全量的会话结构
   */
  void OnConvStatusChanged(const std::vector<AIMPubConversation> &convs);

  /**
   * 会话最后一条消息变更
   * @param convs 全量的会话结构
   * 特殊场景:消息撤回时,last_msg中只有recall和mid有效。
   */
  void OnConvLastMessageChanged(const std::vector<AIMPubConversation> &convs);

  /**
   * 会话未读消息数变更
   * @param convs 全量的会话结构
   */
  void OnConvUnreadCountChanged(const std::vector<AIMPubConversation> &convs);

  /**
   * 会话extension变更
   * @param convs 全量的会话结构
   */
  void OnConvExtensionChanged(const std::vector<AIMPubConversation> &convs);

  /**
   * 会话local extension变更
   * @param convs 全量的会话结构
   */
  void
  OnConvLocalExtensionChanged(const std::vector<AIMPubConversation> &convs);

  /**
   * 会话user extension变更
   * @param convs 全量的会话结构
   */
  void OnConvUserExtensionChanged(const std::vector<AIMPubConversation> &convs);

  /**
   * 会话是否通知的状态变更
   * @param convs 全量的会话结构
   */
  void OnConvNotificationChanged(const std::vector<AIMPubConversation> &convs);

  /**
   * 会话置顶状态变更
   * @param convs 全量的会话结构
   */
  void OnConvTopChanged(const std::vector<AIMPubConversation> &convs);

  /**
   * 会话草稿变更
   * @param convs 全量的会话结构
   */
  void OnConvDraftChanged(const std::vector<AIMPubConversation> &convs);

  /**
   * 接收到正在输入事件
   * @param appCid  	会话id
   * @param command   TypingCommand
   * @param type TypingMessageContent
   */
  void OnConvTypingEvent(const std::string &appCid,
                         AIMConvTypingCommand command,
                         AIMConvTypingMessageContent type);

  /**
   * 会话消息被清空
   * @param convs 有效字段appCid
   */
  void OnConvClearMessage(const std::vector<AIMPubConversation> &convs);
};

} // namespace fim
} // namespace alibaba