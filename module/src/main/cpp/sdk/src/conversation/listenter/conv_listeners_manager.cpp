
#include "conv_listeners_manager.h"
#include "aim_pub_c_api.h"
#include "aim_pub_module.h"
#include "conv_listener_proxy.h"
#include "fim_conversation_list_listener.h"
#include "fim_engine.h"
#include "fim_message.h"
#include "fim_message.hpp"
#include "fim_message_change_listener.h"
#include "fim_message_listener.h"
#include "fim_new_message.hpp"
#include <memory>
#include <mutex>
// AIMPubMsgService
#include "aim_msg_change_listener_proxy.h"
#include "aim_msg_listener_proxy.h"
#include "aim_pub_conv_service.h"
#include "aim_pub_msg_service.h"
// FIMConversationService
#include "fim_conversation_service.h"
#include "log_service.h"

static std::shared_ptr<alibaba::dps::AIMPubConvService>
getCurrentConvService() {
  alibaba::dps::AIMPubModule *rawModulePtr =
      GetAIMPubModuleInstance((alibaba::fim::FIMEngine::GetInstance().fuid));
  if (rawModulePtr == nullptr) {
    return nullptr;
  }
  return rawModulePtr->GetConvService();
}

namespace alibaba {
namespace fim {

ConvListenersManager *ConvListenersManager::GetInstance() {
  static ConvListenersManager instance;
  return &instance;
}


/**
 * 注册会话全量变更的监听器
 * @param listener 监听器
 */
void ConvListenersManager::ConvListenersManager::AddConvListListener(
    const std::shared_ptr<FIMConversationListListener> &listener) {

  std::shared_ptr<alibaba::dps::AIMPubConvService> convService =
      getCurrentConvService();
  if (convService == nullptr) {
    return;
  }
  std::lock_guard<std::mutex> lock(listeners_mutex);
  conv_listeners.push_back(listener);
  static std::once_flag flag;
  std::call_once(flag, [convService]() {
    std::shared_ptr<ConvListenerProxy> listenerProxy =
        std::make_shared<ConvListenerProxy>(ConvListenerProxy());
    convService->AddConvListListener(listenerProxy);
  });
  LogService::DebugLog([this]() {
    std::string result = "AddConvListListener: ";
    std::string length = std::to_string(conv_listeners.size());
    result += length;
    return result;
  });
}

/**
 * 删除全量变更的监听器
 * @param listener 监听器
 */
void ConvListenersManager::ConvListenersManager::RemoveConvListListener(
    const std::shared_ptr<FIMConversationListListener> &listener) {
  std::lock_guard<std::mutex> lock(listeners_mutex);
  auto it = std::find(conv_listeners.begin(), conv_listeners.end(), listener);
  if (it != conv_listeners.end()) {
    conv_listeners.erase(it);
  }
  LogService::DebugLog([this]() {
    std::string result = "RemoveConvListListener: ";
    std::string length = std::to_string(conv_listeners.size());
    result += length;
    return result;
  });
}

/**
 * 注册会话增量变更的监听器
 * @param listener 监听器
 */
void ConvListenersManager::ConvListenersManager::AddConvChangeListener(
    const std::shared_ptr<FIMConversationChangeListener> &listener) {
  std::shared_ptr<alibaba::dps::AIMPubConvService> convService =
      getCurrentConvService();
  if (convService == nullptr) {
    return;
  }
  std::lock_guard<std::mutex> lock(change_listeners_mutex);
  message_change_listeners.push_back(listener);
  static std::once_flag flag;
  std::call_once(flag, [convService]() {
    std::shared_ptr<ConvListenerProxy> listenerProxy =
        std::make_shared<ConvListenerProxy>(ConvListenerProxy());
    convService->AddConvChangeListener(listenerProxy);
  });
  LogService::DebugLog([this]() {
    std::string result = "AddConvChangeListener: ";
    std::string length = std::to_string(conv_listeners.size());
    result += length;
    return result;
  });
}

/**
 * 删除会话增量变更的监听器
 * @param listener 监听器
 */
void ConvListenersManager::RemoveConvChangeListener(
    const std::shared_ptr<FIMConversationChangeListener> &listener) {
  std::lock_guard<std::mutex> lock(change_listeners_mutex);
  auto it = std::find(message_change_listeners.begin(),
                      message_change_listeners.end(), listener);
  if (it != message_change_listeners.end()) {
    message_change_listeners.erase(it);
  }
  LogService::DebugLog([this]() {
    std::string result = "RemoveConvChangeListener: ";
    std::string length = std::to_string(conv_listeners.size());
    result += length;
    return result;
  });
}

/**
 * 删除所有会话的监听器
 */
void ConvListenersManager::ConvListenersManager::RemoveAllConvListListener() {
  std::lock_guard<std::mutex> lock(listeners_mutex);
  conv_listeners.clear();
  std::shared_ptr<alibaba::dps::AIMPubConvService> convService =
      getCurrentConvService();
  if (convService == nullptr) {
    return;
  }
  convService->RemoveAllConvListListener();
  LogService::DebugLog([this]() {
    std::string result = "RemoveAllConvListListener: ";
    std::string length = std::to_string(conv_listeners.size());
    result += length;
    return result;
  });
}

/**
 * 删除所有会话的变更监听器
 */
void ConvListenersManager::ConvListenersManager::RemoveAllConvChangeListener() {
  std::lock_guard<std::mutex> lock(change_listeners_mutex);
  message_change_listeners.clear();
  std::shared_ptr<alibaba::dps::AIMPubConvService> convService =
      getCurrentConvService();
  if (convService == nullptr) {
    return;
  }
  convService->RemoveAllConvChangeListener();
  LogService::DebugLog([this]() {
    std::string result = "RemoveAllConvChangeListener: ";
    std::string length = std::to_string(message_change_listeners.size());
    result += length;
    return result;
  });
}

#pragma mark - 实现AIMPubConvListListener
/**
 * 新增会话
 * @param convs 新增的会话集合
 */
void ConvListenersManager::ConvListenersManager::OnAddedConversations(
    const std::vector<FIMConversation> &convs) {
  std::vector<std::shared_ptr<FIMConversationListListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(listeners_mutex);
  listeners_copy = conv_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      listener->OnAddedConversations(convs);
    }
  }
  LogService::DebugLog([this]() {
    std::string result = "OnAddedConversations: ";
    std::string length = std::to_string(conv_listeners.size());
    result += length;
    return result;
  });
}

/**
 * 删除会话
 * @param appCids 删除的会话appCid集合
 */
void ConvListenersManager::OnRemovedConversations(
    const std::vector<std::string> &appCids) {
  std::vector<std::shared_ptr<FIMConversationListListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(listeners_mutex);
  listeners_copy = conv_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      listener->OnRemovedConversations(appCids);
    }
  }
}

/**
 * 所有会话被更新替换
 * @param convs 更新后的会话集合
 */
void ConvListenersManager::OnRefreshedConversations(
    const std::vector<FIMConversation> &convs) {
  std::vector<std::shared_ptr<FIMConversationListListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(listeners_mutex);
  listeners_copy = conv_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      listener->OnRefreshedConversations(convs);
    }
  }
}
#pragma mark - 实现AIMPubConvChangeListener
/**
 * 会话状态变更
 * @param convs 全量的会话结构
 */
void ConvListenersManager::OnConvStatusChanged(
    const std::vector<FIMConversation> &convs) {
  std::vector<std::shared_ptr<FIMConversationChangeListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(change_listeners_mutex);
  listeners_copy = message_change_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      listener->OnConvStatusChanged(convs);
    }
  }
}

/**
 * 会话最后一条消息变更
 * @param convs 全量的会话结构
 * 特殊场景:消息撤回时,last_msg中只有recall和mid有效。
 */
void ConvListenersManager::OnConvLastMessageChanged(
    const std::vector<FIMConversation> &convs) {
  std::vector<std::shared_ptr<FIMConversationChangeListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(change_listeners_mutex);
  listeners_copy = message_change_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      listener->OnConvLastMessageChanged(convs);
    }
  }
}

/**
 * 会话未读消息数变更
 * @param convs 全量的会话结构
 */
void ConvListenersManager::OnConvUnreadCountChanged(
    const std::vector<FIMConversation> &convs) {
  std::vector<std::shared_ptr<FIMConversationChangeListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(change_listeners_mutex);
  listeners_copy = message_change_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      listener->OnConvUnreadCountChanged(convs);
    }
  }
}

/**
 * 会话extension变更
 * @param convs 全量的会话结构
 */
void ConvListenersManager::OnConvExtensionChanged(
    const std::vector<FIMConversation> &convs) {
  std::vector<std::shared_ptr<FIMConversationChangeListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(change_listeners_mutex);
  listeners_copy = message_change_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      listener->OnConvExtensionChanged(convs);
    }
  }
}

/**
 * 会话local extension变更
 * @param convs 全量的会话结构
 */
void ConvListenersManager::OnConvLocalExtensionChanged(
    const std::vector<FIMConversation> &convs) {
  std::vector<std::shared_ptr<FIMConversationChangeListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(change_listeners_mutex);
  listeners_copy = message_change_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      listener->OnConvLocalExtensionChanged(convs);
    }
  }
}

/**
 * 会话user extension变更
 * @param convs 全量的会话结构
 */
void ConvListenersManager::OnConvUserExtensionChanged(
    const std::vector<FIMConversation> &convs) {
  std::vector<std::shared_ptr<FIMConversationChangeListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(change_listeners_mutex);
  listeners_copy = message_change_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      listener->OnConvUserExtensionChanged(convs);
    }
  }
}

/**
 * 会话是否通知的状态变更
 * @param convs 全量的会话结构
 */
void ConvListenersManager::OnConvNotificationChanged(
    const std::vector<FIMConversation> &convs) {
  std::vector<std::shared_ptr<FIMConversationChangeListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(change_listeners_mutex);
  listeners_copy = message_change_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      listener->OnConvNotificationChanged(convs);
    }
  }
}

/**
 * 会话置顶状态变更
 * @param convs 全量的会话结构
 */
void ConvListenersManager::OnConvTopChanged(
    const std::vector<FIMConversation> &convs) {
  std::vector<std::shared_ptr<FIMConversationChangeListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(change_listeners_mutex);
  listeners_copy = message_change_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      listener->OnConvTopChanged(convs);
    }
  }
}

/**
 * 会话草稿变更
 * @param convs 全量的会话结构
 */
void ConvListenersManager::OnConvDraftChanged(
    const std::vector<FIMConversation> &convs) {
  std::vector<std::shared_ptr<FIMConversationChangeListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(change_listeners_mutex);
  listeners_copy = message_change_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      listener->OnConvDraftChanged(convs);
    }
  }
}

/**
 * 接收到正在输入事件
 * @param appCid  	会话id
 * @param command   TypingCommand
 * @param type TypingMessageContent
 */
void ConvListenersManager::OnConvTypingEvent(const std::string &appCid,
                                             AIMConvTypingCommand command,
                                             AIMConvTypingMessageContent type) {
  std::vector<std::shared_ptr<FIMConversationChangeListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(change_listeners_mutex);
  listeners_copy = message_change_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      listener->OnConvTypingEvent(appCid, command, type);
    }
  }
}

/**
 * 会话消息被清空
 * @param convs 有效字段appCid
 */
void ConvListenersManager::OnConvClearMessage(
    const std::vector<FIMConversation> &convs) {
  std::vector<std::shared_ptr<FIMConversationChangeListener>> listeners_copy;
  std::lock_guard<std::mutex> lock(change_listeners_mutex);
  listeners_copy = message_change_listeners;
  for (const auto &listener : listeners_copy) {
    if (listener) {
      listener->OnConvClearMessage(convs);
    }
  }
}

} // namespace fim
} // namespace alibaba
