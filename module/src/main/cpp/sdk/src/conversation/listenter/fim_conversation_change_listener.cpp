// //
// //  fim_conversation_change_list_listener.cpp
// //  FliggyIMSDK
// //
// //  Created by 蒙晨 on 2024/2/19.
// //

#include "fim_conversation_change_listener.h"
#include "aim_pub_conv_change_listener.h"
#include "fim_conversation.hpp"
#include "aim_pub_conversation.h"
#include "log_service.h"
#include <string>

namespace alibaba {
namespace fim {
      /**
   * 会话状态变更
   * @param convs 全量的会话结构
   */
void
  FIMConversationChangeListener::OnConvStatusChanged(const std::vector<AIMPubConversation> &convs) {
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
        LogService::DebugLog([convs] {
        std::string convsStr = "FIMConversationChangeListener::OnConvStatusChanged:";
        for (auto conv : convs) {
            convsStr += conv.appCid + ",";
        }
        return convsStr;
    });
    OnConvStatusChanged(fimConvs);
  }

  /**
   * 会话最后一条消息变更
   * @param convs 全量的会话结构
   * 特殊场景:消息撤回时,last_msg中只有recall和mid有效。
   */
void
  FIMConversationChangeListener::OnConvLastMessageChanged(const std::vector<AIMPubConversation> &convs) {
    LogService::DebugLog([convs] {
        std::string convsStr = "FIMConversationChangeListener::OnConvLastMessageChanged:";
        for (auto conv : convs) {
            convsStr += conv.appCid + ",";
        }
        return convsStr;
    });
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    OnConvLastMessageChanged(fimConvs);
  }

  /**
   * 会话未读消息数变更
   * @param convs 全量的会话结构
   */
void
  FIMConversationChangeListener::OnConvUnreadCountChanged(const std::vector<AIMPubConversation> &convs) {
    LogService::DebugLog([convs] {
        std::string convsStr = "FIMConversationChangeListener::OnConvUnreadCountChanged:";
        for (auto conv : convs) {
            convsStr += conv.appCid + ",";
        }
        return convsStr;
    });
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    OnConvUnreadCountChanged(fimConvs);
  }

  /**
   * 会话extension变更
   * @param convs 全量的会话结构
   */
void
  FIMConversationChangeListener::OnConvExtensionChanged(const std::vector<AIMPubConversation> &convs) {
    LogService::DebugLog([convs] {
        std::string convsStr = "FIMConversationChangeListener::OnConvExtensionChanged:";
        for (auto conv : convs) {
            convsStr += conv.appCid + ",";
        }
        return convsStr;
    });
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    OnConvExtensionChanged(fimConvs);
  }

  /**
   * 会话local extension变更
   * @param convs 全量的会话结构
   */
void
  FIMConversationChangeListener::OnConvLocalExtensionChanged(const std::vector<AIMPubConversation> &convs) {
    LogService::DebugLog([convs] {
        std::string convsStr = "FIMConversationChangeListener::OnConvLocalExtensionChanged:";
        for (auto conv : convs) {
            convsStr += conv.appCid + ",";
        }
        return convsStr;
    });
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    OnConvLocalExtensionChanged(fimConvs);
  }

  /**
   * 会话user extension变更
   * @param convs 全量的会话结构
   */
void
  FIMConversationChangeListener::OnConvUserExtensionChanged(const std::vector<AIMPubConversation> &convs) {
    LogService::DebugLog([convs] {
        std::string convsStr = "FIMConversationChangeListener::OnConvUserExtensionChanged:";
        for (auto conv : convs) {
            convsStr += conv.appCid + ",";
        }
        return convsStr;
    });
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    OnConvUserExtensionChanged(fimConvs);
  }

  /**
   * 会话是否通知的状态变更
   * @param convs 全量的会话结构
   */
void
  FIMConversationChangeListener::OnConvNotificationChanged(const std::vector<AIMPubConversation> &convs) {
    LogService::DebugLog([convs] {
        std::string convsStr = "FIMConversationChangeListener::OnConvNotificationChanged:";
        for (auto conv : convs) {
            convsStr += conv.appCid + ",";
        }
        return convsStr;
    });
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    OnConvNotificationChanged(fimConvs);
  }

  /**
   * 会话置顶状态变更
   * @param convs 全量的会话结构
   */
void
  FIMConversationChangeListener::OnConvTopChanged(const std::vector<AIMPubConversation> &convs) {
    LogService::DebugLog([convs] {
        std::string convsStr = "FIMConversationChangeListener::OnConvTopChanged:";
        for (auto conv : convs) {
            convsStr += conv.appCid + ",";
        }
        return convsStr;
    });
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    OnConvTopChanged(fimConvs);
  }

  /**
   * 会话草稿变更
   * @param convs 全量的会话结构
   */
void
  FIMConversationChangeListener::OnConvDraftChanged(const std::vector<AIMPubConversation> &convs) {
    LogService::DebugLog([convs] {
        std::string convsStr = "FIMConversationChangeListener::OnConvDraftChanged:";
        for (auto conv : convs) {
            convsStr += conv.appCid + ",";
        }
        return convsStr;
    });
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    OnConvDraftChanged(fimConvs);
  }


  /**
   * 会话消息被清空
   * @param convs 有效字段appCid
   */
void
  FIMConversationChangeListener::OnConvClearMessage(const std::vector<AIMPubConversation> &convs) {
    LogService::DebugLog([convs] {
        std::string convsStr = "FIMConversationChangeListener::OnConvClearMessage:";
        for (auto conv : convs) {
            convsStr += conv.appCid + ",";
        }
        return convsStr;
    });
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    OnConvClearMessage(fimConvs);
  }
}
}
