// //
// //  fim_conversation_list_listener.cpp
// //  FliggyIMSDK
// //
// //  Created by 蒙晨 on 2024/2/19.
// //

#include "fim_conversation.hpp"
#include "aim_pub_conversation.h"
#include "fim_conversation_list_listener.h"
#include "log_service.h"
#include <cmath>
namespace alibaba {
namespace fim {
    /**
   * 新增会话
   * @param convs 新增的会话集合
   */
void FIMConversationListListener::OnAddedConversations(const std::vector<dps::AIMPubConversation> &convs) {
    LogService::DebugLog([convs] {
        std::string convsStr = "FIMConversationListListener::OnAddedConversations:";
        for (auto conv : convs) {
            convsStr += conv.appCid + ",";
        }
        return convsStr;
    });
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    OnAddedConversations(fimConvs);
  }

  /**
   * 所有会话被更新替换
   * @param convs 更新后的会话集合
   */
void FIMConversationListListener::OnRefreshedConversations(const std::vector<dps::AIMPubConversation> &convs) {
    LogService::DebugLog([convs] {
        std::string convsStr = "FIMConversationListListener::OnRefreshedConversations:";
        for (auto conv : convs) {
            convsStr += conv.appCid + ",";
        }
        return convsStr;
    });
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    OnRefreshedConversations(fimConvs);
  }
}
};
