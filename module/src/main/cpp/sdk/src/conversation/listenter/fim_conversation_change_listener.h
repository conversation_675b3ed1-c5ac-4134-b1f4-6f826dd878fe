//
//  fim_conversation_change_list_listener.hpp
//  FliggyIMSDK
//
//  Created by 蒙晨 on 2024/2/19.
//

#ifndef fim_conversation_change_listener_h
#define fim_conversation_change_listener_h

#include <stdio.h>
#include <vector>
#include <memory>
#include <string>
#include "fliggy_im_sdk.h"
#include "fim_conversation.h"
#include "aim_pub_conv_change_listener.h"

namespace alibaba {
namespace dps {

class AIMPubConvChangeListener;
enum class AIMConvTypingCommand;
enum class AIMConvTypingMessageContent;
}
}

namespace alibaba {
namespace fim {

class FIMSDK_API FIMConversationChangeListener : public dps::AIMPubConvChangeListener {
public:
    virtual ~FIMConversationChangeListener() {}

    /**
     * 会话状态变更
     * @param convs 全量的会话结构
     */
    virtual void
    OnConvStatusChanged(const std::vector<FIMConversation> &convs) = 0;

    /**
     * 会话最后一条消息变更
     * @param convs 全量的会话结构
     * 特殊场景:消息撤回时,last_msg中只有recall和mid有效。
     */
    virtual void
    OnConvLastMessageChanged(const std::vector<FIMConversation> &convs) = 0;

    /**
     * 会话未读消息数变更
     * @param convs 全量的会话结构
     */
    virtual void
    OnConvUnreadCountChanged(const std::vector<FIMConversation> &convs) = 0;

    /**
     * 会话extension变更
     * @param convs 全量的会话结构
     */
    virtual void
    OnConvExtensionChanged(const std::vector<FIMConversation> &convs) = 0;

    /**
     * 会话local extension变更
     * @param convs 全量的会话结构
     */
    virtual void
    OnConvLocalExtensionChanged(const std::vector<FIMConversation> &convs) = 0;

    /**
     * 会话user extension变更
     * @param convs 全量的会话结构
     */
    virtual void
    OnConvUserExtensionChanged(const std::vector<FIMConversation> &convs) = 0;

    /**
     * 会话是否通知的状态变更
     * @param convs 全量的会话结构
     */
    virtual void
    OnConvNotificationChanged(const std::vector<FIMConversation> &convs) = 0;

    /**
     * 会话置顶状态变更
     * @param convs 全量的会话结构
     */
    virtual void
    OnConvTopChanged(const std::vector<FIMConversation> &convs) = 0;

    /**
     * 会话草稿变更
     * @param convs 全量的会话结构
     */
    virtual void
    OnConvDraftChanged(const std::vector<FIMConversation> &convs) = 0;

    /**
     * 接收到正在输入事件
     * @param appCid      会话id
     * @param command   TypingCommand
     * @param type TypingMessageContent
     */
    virtual void OnConvTypingEvent(const std::string &appCid,
                                   AIMConvTypingCommand command,
                                   AIMConvTypingMessageContent type) = 0;

    /**
     * 会话消息被清空
     * @param convs 有效字段appCid
     */
    virtual void
    OnConvClearMessage(const std::vector<FIMConversation> &convs) = 0;
private:
      /**
   * 会话状态变更
   * @param convs 全量的会话结构
   */
void
  OnConvStatusChanged(const std::vector<AIMPubConversation> &convs);

  /**
   * 会话最后一条消息变更
   * @param convs 全量的会话结构
   * 特殊场景:消息撤回时,last_msg中只有recall和mid有效。
   */
void
  OnConvLastMessageChanged(const std::vector<AIMPubConversation> &convs);

  /**
   * 会话未读消息数变更
   * @param convs 全量的会话结构
   */
void
  OnConvUnreadCountChanged(const std::vector<AIMPubConversation> &convs);

  /**
   * 会话extension变更
   * @param convs 全量的会话结构
   */
void
  OnConvExtensionChanged(const std::vector<AIMPubConversation> &convs);

  /**
   * 会话local extension变更
   * @param convs 全量的会话结构
   */
void
  OnConvLocalExtensionChanged(const std::vector<AIMPubConversation> &convs);

  /**
   * 会话user extension变更
   * @param convs 全量的会话结构
   */
void
  OnConvUserExtensionChanged(const std::vector<AIMPubConversation> &convs);

  /**
   * 会话是否通知的状态变更
   * @param convs 全量的会话结构
   */
void
  OnConvNotificationChanged(const std::vector<AIMPubConversation> &convs);

  /**
   * 会话置顶状态变更
   * @param convs 全量的会话结构
   */
void
  OnConvTopChanged(const std::vector<AIMPubConversation> &convs);

  /**
   * 会话草稿变更
   * @param convs 全量的会话结构
   */
void
  OnConvDraftChanged(const std::vector<AIMPubConversation> &convs);


  /**
   * 会话消息被清空
   * @param convs 有效字段appCid
   */
void
  OnConvClearMessage(const std::vector<AIMPubConversation> &convs);
};

} // namespace dps
} //

#endif
