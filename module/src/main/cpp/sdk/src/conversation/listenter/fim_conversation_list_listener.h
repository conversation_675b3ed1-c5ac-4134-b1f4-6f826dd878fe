//
//  fim_conv_list_listener.h
//  FliggyIMSDK
//
//  Created by 蒙晨 on 2024/2/19.
//
#pragma once


#ifndef fim_conversation_list_listener_h
#define fim_conversation_list_listener_h

#include <memory>
#include <vector>
#include <string>
#include "fliggy_im_sdk.h"
#include "fim_conversation.h"
#include "aim_pub_conv_list_listener.h"

namespace alibaba {
namespace fim {
class FIMSDK_API FIMConversationListListener : public dps::AIMPubConvListListener {
public:
    virtual ~FIMConversationListListener() {}

    /**
     * 新增会话
     * @param convs 新增的会话集合
     */
    virtual void OnAddedConversations(const std::vector<FIMConversation> &convs) = 0;
    /**
     * 删除会话
     * @param appCids 删除的会话appCid集合
     */
    virtual void OnRemovedConversations(const std::vector<std::string> &appCids) = 0;
    /**
     * 所有会话被更新替换
     * @param convs 更新后的会话集合
     */
    virtual void OnRefreshedConversations(const std::vector<FIMConversation> &convs) = 0;
    private:
    /**
   * 新增会话
   * @param convs 新增的会话集合
   */
void
  OnAddedConversations(const std::vector<dps::AIMPubConversation> &convs);

  /**
   * 所有会话被更新替换
   * @param convs 更新后的会话集合
   */
void
  OnRefreshedConversations(const std::vector<dps::AIMPubConversation> &convs);
};

} // namespace dps
} // namespace alibaba


#endif /* fim_conv_list_listener_h */
