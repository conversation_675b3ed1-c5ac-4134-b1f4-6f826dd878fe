#include "conv_listener_proxy.h"
#include "conv_listeners_manager.h"
#include "fim_conversation.hpp"
#include <vector>
namespace alibaba {
namespace fim {
#pragma mark - 实现AIMPubConvListListener
  /**
   * 新增会话
   * @param convs 新增的会话集合
   */
  void ConvListenerProxy::OnAddedConversations(const std::vector<AIMPubConversation> &convs) {
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    ConvListenersManager::GetInstance()->OnAddedConversations(fimConvs);
  }

  /**
   * 删除会话
   * @param appCids 删除的会话appCid集合
   */
  void ConvListenerProxy:: OnRemovedConversations(const std::vector<std::string> &appCids) {
    ConvListenersManager::GetInstance()->OnRemovedConversations(appCids);
  }

  /**
   * 所有会话被更新替换
   * @param convs 更新后的会话集合
   */
  void ConvListenerProxy:: OnRefreshedConversations(const std::vector<AIMPubConversation> &convs) {
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    ConvListenersManager::GetInstance()->OnRefreshedConversations(fimConvs);
  }
#pragma mark - 实现AIMPubConvChangeListener
  /**
   * 会话状态变更
   * @param convs 全量的会话结构
   */
  void ConvListenerProxy:: OnConvStatusChanged(const std::vector<AIMPubConversation> &convs) {
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    ConvListenersManager::GetInstance()->OnConvStatusChanged(fimConvs);
  }

  /**
   * 会话最后一条消息变更
   * @param convs 全量的会话结构
   * 特殊场景:消息撤回时,last_msg中只有recall和mid有效。
   */
  void ConvListenerProxy:: OnConvLastMessageChanged(const std::vector<AIMPubConversation> &convs) {
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    ConvListenersManager::GetInstance()->OnConvLastMessageChanged(fimConvs);
  }

  /**
   * 会话未读消息数变更
   * @param convs 全量的会话结构
   */
  void ConvListenerProxy:: OnConvUnreadCountChanged(const std::vector<AIMPubConversation> &convs) {
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    ConvListenersManager::GetInstance()->OnConvUnreadCountChanged(fimConvs);
  }

  /**
   * 会话extension变更
   * @param convs 全量的会话结构
   */
  void ConvListenerProxy:: OnConvExtensionChanged(const std::vector<AIMPubConversation> &convs) {
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    ConvListenersManager::GetInstance()->OnConvExtensionChanged(fimConvs);
  }

  /**
   * 会话local extension变更
   * @param convs 全量的会话结构
   */
  void ConvListenerProxy::
  OnConvLocalExtensionChanged(const std::vector<AIMPubConversation> &convs) {
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    ConvListenersManager::GetInstance()->OnConvLocalExtensionChanged(fimConvs);
  }

  /**
   * 会话user extension变更
   * @param convs 全量的会话结构
   */
  void ConvListenerProxy:: OnConvUserExtensionChanged(const std::vector<AIMPubConversation> &convs) {
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    ConvListenersManager::GetInstance()->OnConvUserExtensionChanged(fimConvs);
  }

  /**
   * 会话是否通知的状态变更
   * @param convs 全量的会话结构
   */
  void ConvListenerProxy:: OnConvNotificationChanged(const std::vector<AIMPubConversation> &convs) {
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    ConvListenersManager::GetInstance()->OnConvNotificationChanged(fimConvs);
  }

  /**
   * 会话置顶状态变更
   * @param convs 全量的会话结构
   */
  void ConvListenerProxy:: OnConvTopChanged(const std::vector<AIMPubConversation> &convs) {
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    ConvListenersManager::GetInstance()->OnConvTopChanged(fimConvs);
  }

  /**
   * 会话草稿变更
   * @param convs 全量的会话结构
   */
  void ConvListenerProxy:: OnConvDraftChanged(const std::vector<AIMPubConversation> &convs) {
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    ConvListenersManager::GetInstance()->OnConvDraftChanged(fimConvs);
  }

  /**
   * 接收到正在输入事件
   * @param appCid  	会话id
   * @param command   TypingCommand
   * @param type TypingMessageContent
   */
  void ConvListenerProxy:: OnConvTypingEvent(const std::string &appCid,
                         AIMConvTypingCommand command,
                         AIMConvTypingMessageContent type) {
    ConvListenersManager::GetInstance()->OnConvTypingEvent(appCid, command, type);
                         }

  /**
   * 会话消息被清空
   * @param convs 有效字段appCid
   */
  void ConvListenerProxy:: OnConvClearMessage(const std::vector<AIMPubConversation> &convs) {
    std::vector<FIMConversation> fimConvs = FIMConversationListFrom(convs);
    ConvListenersManager::GetInstance()->OnConvClearMessage(fimConvs);
  }
}
}