#pragma once
#include "aim_pub_msg_change_listener.h"
#include "aim_pub_msg_listener.h"
#include "fim_conversation_change_listener.h"
#include "fim_conversation_list_listener.h"
#include "fim_message_change_listener.h"
#include "fim_message_listener.h"
#include "aim_pub_conv_change_listener.h"
#include <mutex>

namespace alibaba {
namespace fim {
class ConvListenersManager {
private:
  static ConvListenersManager *instance;
  std::mutex listeners_mutex;
  std::mutex change_listeners_mutex;
  std::vector<std::shared_ptr<FIMConversationListListener>> conv_listeners;
  std::vector<std::shared_ptr<FIMConversationChangeListener>>
      message_change_listeners;

public:
  static ConvListenersManager *GetInstance();

#pragma mark 管理listeners

  /**
   * 注册会话全量变更的监听器
   * @param listener 监听器
   */
  void AddConvListListener(
      const std::shared_ptr<FIMConversationListListener> &listener);

  /**
   * 删除全量变更的监听器
   * @param listener 监听器
   */
  void RemoveConvListListener(
      const std::shared_ptr<FIMConversationListListener> &listener);

  /**
   * 注册会话增量变更的监听器
   * @param listener 监听器
   */
  void AddConvChangeListener(
      const std::shared_ptr<FIMConversationChangeListener> &listener);

  /**
   * 删除会话增量变更的监听器
   * @param listener 监听器
   */
  void RemoveConvChangeListener(
      const std::shared_ptr<FIMConversationChangeListener> &listener);

  /**
   * 删除所有会话的监听器
   */
  void RemoveAllConvListListener();

  /**
   * 删除所有会话的变更监听器
   */
  void RemoveAllConvChangeListener();

#pragma mark - 实现AIMPubConvListListener
  /**
   * 新增会话
   * @param convs 新增的会话集合
   */
  void OnAddedConversations(const std::vector<FIMConversation> &convs);

  /**
   * 删除会话
   * @param appCids 删除的会话appCid集合
   */
  void OnRemovedConversations(const std::vector<std::string> &appCids);

  /**
   * 所有会话被更新替换
   * @param convs 更新后的会话集合
   */
  void OnRefreshedConversations(const std::vector<FIMConversation> &convs);
#pragma mark - 实现AIMPubConvChangeListener
  /**
   * 会话状态变更
   * @param convs 全量的会话结构
   */
  void OnConvStatusChanged(const std::vector<FIMConversation> &convs);

  /**
   * 会话最后一条消息变更
   * @param convs 全量的会话结构
   * 特殊场景:消息撤回时,last_msg中只有recall和mid有效。
   */
  void OnConvLastMessageChanged(const std::vector<FIMConversation> &convs);

  /**
   * 会话未读消息数变更
   * @param convs 全量的会话结构
   */
  void OnConvUnreadCountChanged(const std::vector<FIMConversation> &convs);

  /**
   * 会话extension变更
   * @param convs 全量的会话结构
   */
  void OnConvExtensionChanged(const std::vector<FIMConversation> &convs);

  /**
   * 会话local extension变更
   * @param convs 全量的会话结构
   */
  void
  OnConvLocalExtensionChanged(const std::vector<FIMConversation> &convs);

  /**
   * 会话user extension变更
   * @param convs 全量的会话结构
   */
  void OnConvUserExtensionChanged(const std::vector<FIMConversation> &convs);

  /**
   * 会话是否通知的状态变更
   * @param convs 全量的会话结构
   */
  void OnConvNotificationChanged(const std::vector<FIMConversation> &convs);

  /**
   * 会话置顶状态变更
   * @param convs 全量的会话结构
   */
  void OnConvTopChanged(const std::vector<FIMConversation> &convs);

  /**
   * 会话草稿变更
   * @param convs 全量的会话结构
   */
  void OnConvDraftChanged(const std::vector<FIMConversation> &convs);

  /**
   * 接收到正在输入事件
   * @param appCid  	会话id
   * @param command   TypingCommand
   * @param type TypingMessageContent
   */
  void OnConvTypingEvent(const std::string &appCid,
                         AIMConvTypingCommand command,
                         AIMConvTypingMessageContent type);

  /**
   * 会话消息被清空
   * @param convs 有效字段appCid
   */
  void OnConvClearMessage(const std::vector<FIMConversation> &convs);
#pragma mark - 头像
  private:
    void FetchConvInfo(const std::vector<FIMConversation> &convs);
};

} // namespace fim
} // namespace alibaba