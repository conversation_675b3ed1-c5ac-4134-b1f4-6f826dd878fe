//
//  fim_conv_get_conv_listener.h
//  FliggyIMSDK
//
//  Created by 蒙晨 on 2024/2/18.
//

// fim_conv_get_conv_listener.h
#ifndef fim_conv_get_conv_listener_h
#define fim_conv_get_conv_listener_h

#include "fim_conversation.h"
#include "fim_error.h"
#include <memory>
#include <vector>

namespace alibaba {
namespace fim {

/**
 * 获取会话列表监听
 */
class FIMSDK_API FIMConvGetConvListener {
public:
  virtual ~FIMConvGetConvListener() {}
  /**
   * 获取成功
   */
  virtual void OnSuccess(const std::vector<FIMConversation> &result) = 0;
  /**
   * 获取失败
   */
  virtual void OnFailure(const FIMError &error) = 0;
};

using FIMConvGetConvListenerPtr = std::shared_ptr<FIMConvGetConvListener>;
using FIMConvGetConvListenerWeakPtr = std::weak_ptr<FIMConvGetConvListener>;

} // namespace fim
} // namespace alibaba

#endif /* fim_conv_get_conv_listener_h */
