//
//  fim_conv_get_conv_adapter.h
//  FliggyIMSDK
//
//  Created by 蒙晨 on 2024/2/18.
//

// fim_conv_get_conv_adapter.h
#ifndef fim_conv_get_conv_adapter_h
#define fim_conv_get_conv_adapter_h

#include "fim_conv_get_conv_listener.h"
//AIMPubConvGetConvListener
#include "aim_pub_conv_get_conv_listener.h"
#include "fim_conversation.hpp"
#include "fim_error.hpp"

namespace alibaba {
namespace fim {

class FIMConvGetConvAdapter : public dps::AIMPubConvGetConvListener {
public:
  // 构造函数需要传入 FIMConvGetConvListener 引用
  explicit FIMConvGetConvAdapter(const FIMConvGetConvListenerPtr& listener)
    : listener_(listener) {}

  // 获取成功的回调转换
  virtual void OnSuccess(const std::vector<dps::AIMPubConversation> &result) override {
    if (auto fimListener = listener_.lock()) {
      auto fimConvVector = FIMConversationListFrom(result);
      fimListener->OnSuccess(fimConvVector);
    }
  }

  // 获取失败的回调转换
  virtual void OnFailure(const dps::DPSError &error) override {
    if (auto fimListener = listener_.lock()) {
      FIMError fimError = FIMErrorFrom(error);
      fimListener->OnFailure(fimError);
    }
  }

private:
  FIMConvGetConvListenerWeakPtr listener_;
};

} // namespace fim
} // namespace alibaba

#endif /* fim_conv_get_conv_adapter_h */
