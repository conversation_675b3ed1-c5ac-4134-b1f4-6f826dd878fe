//
//  fim_conversation.hpp
//  FliggyIMSDK
//
//  Created by 蒙晨 on 2024/2/18.
//

#ifndef fim_conversation_h
#define fim_conversation_h

#include "fim_group_permission.h"
#include "fim_message.h"
#include "fliggy_im_sdk.h"
#include <map>
#include <stdio.h>
#include <string>
#include <vector>

namespace alibaba {
namespace fim {

// 定义与AIM*相同的FIM*枚举类型
enum class FIMConvType {
  /**
   * 未知类型
   */
  CONV_TYPE_UNKNOW = -1,
  /**
   * 单聊
   */
  CONV_TYPE_SINGLE = 1,
  /**
   * 群聊
   */
  CONV_TYPE_GROUP = 2,

};

enum class FIMConvStatus {
  /**
   * 未知类型
   */
  CONV_STATUS_UNKNOW = -1,
  /**
   * 隐藏
   */
  CONV_STATUS_HIDE = 0,
  /**
   * 正常状态，一般会话都在该状态
   */
  CONV_STATUS_NORMAL = 1,
  /**
   * 会话处于离线状态，未同步入云端
   */
  CONV_STATUS_OFFLINE = 2,
  /**
   * 群会话被踢
   */
  CONV_STATUS_KICKED = 3,
  /**
   * 群会话被解散
   */
  CONV_STATUS_DISMISSED = 4,

};

enum class FIMGroupSilencedStatus {
  /**
   * 既不在白名单也不在黑名单
   */
  GROUP_SILENCE_STATUS_NORMAL = 0,
  /**
   * 白名单
   */
  GROUP_SILENCE_STATUS_IN_WHITELIST = 1,
  /**
   * 黑名单
   */
  GROUP_SILENCE_STATUS_IN_BLACKLIST = 2,
};

struct FIMSDK_API FIMConversation final {
  /**
   * 会话名称
   */
  std::string fim_title() const;

  /**
   * 会话头像
   */
  std::string fim_avatar() const;

  /**
   * 会话最近消息概览
   */
  std::string fim_summary() const;

  /**
   * user_exteions中 at_all的内容
   */
  std::string at_all() const;

  /**
   * user_exteions中 at我的内容
   */
  std::string at_me() const;
  /**
   * user_exteions中 回复的内容
   */
  std::string reply_me() const;
  /**
  * 互动消息提示
  */
  std::string interactionText() const;

  /**
   * 会话ID
   */
  std::string appCid;
  /**
   * 会话类型
   */
  FIMConvType type;
  /**
   * 业务类型
   */
  std::string biz_type;
  /**
   * 会话状态
   */
  FIMConvStatus status;
  /**
   * 会话用户ID列表
   */
  std::vector<std::string> userids;
  /**
   * 会话创建时间
   */
  int64_t created_at;
  /**
   * 会话修改时间
   */
  int64_t modify_time;
  /**
   * 未读消息数
   */
  int32_t red_point;
  /**
   * 草稿
   */
  std::string draft;
  /**
   * 是否免打扰
   */
  bool mute_notification = false;
  /**
   * 置顶 （>0表示置顶，越大越靠前）
   */
  int64_t top_rank = 0;
  /**
   * 扩展信息
   */
  std::map<std::string, std::string> extension;
  /**
   * user扩展信息
   */
  std::map<std::string, std::string> user_extension;
  /**
   * 本地扩展信息
   */
  std::map<std::string, std::string> local_extension;
  /**
   * 会话是否有最后一条消息
   */
  bool has_last_msg = false;
  /**
   * 会话最后一条消息（如果其中mid为空，则没有最后一条消息）
   */
  FIMMessage last_msg;

  /**
   * 会话加入时间
   */
  int64_t join_time = 0;
  /**
   * 群主uid
   */
  std::string owner_uid;
  /**
   * 群标题
   */
  std::string title;
  /**
   * 群头像
   */
  std::string icon;
  /**
   * 群人数
   */
  int32_t member_count = 0;
  /**
   * 群人数限制
   */
  int32_t member_limit = 0;
  /**
   * 群禁言
   */
  bool silence_all = false;
  /**
   * 禁言状态
   */
  FIMGroupSilencedStatus silenced_status;
  /**
   * 禁言截止时间（ms）
   */
  int64_t silenced_endtime;
  /**
   * 群管理员uid
   */
  std::vector<std::string> admins;
  /**
   * 群权限群成员权限
   */
  std::vector<FIMGroupPermission> member_permissions;
  /**
   * 是否支持已读回执
   */
  bool read_receipts_enabled;
  
  // 初始化方法
  FIMConversation(std::string appCid, FIMConvType type, std::string biz_type,
                  FIMConvStatus status, std::vector<std::string> userids,
                  int64_t created_at, int64_t modify_time, int32_t red_point,
                  std::string draft, bool mute_notification, int64_t top_rank,
                  std::map<std::string, std::string> extension,
                  std::map<std::string, std::string> user_extension,
                  std::map<std::string, std::string> local_extension,
                  bool has_last_msg, FIMMessage last_msg, int64_t join_time,
                  std::string owner_uid, std::string title, std::string icon,
                  int32_t member_count, int32_t member_limit, bool silence_all,
                  FIMGroupSilencedStatus silenced_status,
                  int64_t silenced_endtime, std::vector<std::string> admins,
                  std::vector<FIMGroupPermission> member_permissions,
                  bool read_receipts_enabled, std::string avatar,
                  std::string name)
      : appCid(std::move(appCid)), type(type), biz_type(std::move(biz_type)),
        status(status), userids(std::move(userids)), created_at(created_at),
        modify_time(modify_time), red_point(red_point), draft(std::move(draft)),
        mute_notification(mute_notification), top_rank(top_rank),
        extension(std::move(extension)),
        user_extension(std::move(user_extension)),
        local_extension(std::move(local_extension)), has_last_msg(has_last_msg),
        last_msg(last_msg), join_time(join_time),
        owner_uid(std::move(owner_uid)), title(std::move(title)),
        icon(std::move(icon)), member_count(member_count),
        member_limit(member_limit), silence_all(silence_all),
        silenced_status(silenced_status), silenced_endtime(silenced_endtime),
        admins(std::move(admins)),
        member_permissions(std::move(member_permissions)),
        read_receipts_enabled(read_receipts_enabled) {}
};

} // namespace fim
} // namespace alibaba

#endif /* fim_conversation_h */
