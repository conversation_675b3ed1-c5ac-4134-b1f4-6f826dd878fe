#pragma once
#include <stdio.h>
#include <string>

#include "fliggy_im_sdk.h"

namespace alibaba {
namespace fim {

struct FIMSDK_API FIMGroupPermission final {
    std::string permission_group;
    int32_t status = 0;

    FIMGroupPermission(std::string permission_group_, int32_t status_)
        : permission_group(std::move(permission_group_)),
          status(std::move(status_)) {}

    FIMGroupPermission() {}
};

} // namespace fim
} // namespace alibaba