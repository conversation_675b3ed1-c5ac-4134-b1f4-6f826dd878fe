#include "fim_conversation.h"
#include "fim_engine.h"
#include "json.hpp"

using json = nlohmann::json;

namespace alibaba {
    namespace fim {
    
        // 常量 interaction
        const std::string INTERACTION_KEY = "interaction";

        std::pair<std::string, std::string>
        getUserInfo(const std::map<std::string, std::string> &extension) {
            // 查找current_fuid对应的用户信息
            auto it = extension.find(FIMEngine::GetInstance().fuid);
            if (it != extension.end()) {
                // 解析JSON字符串
                json user_info = json::parse(it->second);
                // 获取avatar和nickName
                std::string avatar = user_info.value("avatar", "");
                std::string nickName = user_info.value("nickName", "");
                return {avatar, nickName};
            }

            // 如果找不到对应的fuid，返回空字符串
            return {"", ""};
        }

        /**
        * 是否是自己发的消息
        */
        std::string FIMConversation::fim_title() const {
            if (type == FIMConvType::CONV_TYPE_GROUP) {
                return title;
            }
            std::string avatar, nickName;
            std::tie(avatar, nickName) = getUserInfo(extension);
            return nickName.empty() ? title : nickName;
        }

        /**
        * 飞猪头像
        */
        std::string FIMConversation::fim_avatar() const {
            if (type == FIMConvType::CONV_TYPE_GROUP) {
                return icon;
            }
            std::string avatar, nickName;
            std::tie(avatar, nickName) = getUserInfo(extension);
            return avatar.empty() ? icon : avatar;
        }

        /**
         * 会话最近消息概览
         */
        std::string FIMConversation::fim_summary() const {
            if (last_msg.mid.empty()) {
                return "";
            }
            if (last_msg.is_recall) {
                return last_msg.is_self_message() ? "你撤回了一条消息" : "消息被撤回";
            } else if (last_msg.is_delete) {
                return "消息被删除";
            }
            FIMMessageContent content = last_msg.content;
            FIMMessageContentType content_type = content.content_type;
            switch (content_type) {
                case FIMMessageContentType::CONTENT_TYPE_TEXT:
                case FIMMessageContentType::CONTENT_TYPE_LINK: {
                    return content.text_content.text;
                }
                case FIMMessageContentType::CONTENT_TYPE_IMAGE: {
                    return "[图片]";
                }
                case FIMMessageContentType::CONTENT_TYPE_AUDIO: {
                    return "[音频]";
                }
                case FIMMessageContentType::CONTENT_TYPE_VIDEO: {
                    return "[视频]";
                }
                case FIMMessageContentType::CONTENT_TYPE_GEO: {
                    return "[位置]";
                }
                case FIMMessageContentType::CONTENT_TYPE_STRUCT:
                case FIMMessageContentType::CONTENT_TYPE_AT: {
                    std::vector<FIMMessageStructElement> elements = content.struct_content.elements;
                    std::string summary;
                    for (FIMMessageStructElement element: elements) {
                        if (element.element_type ==
                            FIMMessageStructElementType::ELEMENT_TYPE_TEXT) {
                            summary += element.text_content.text;
                        } else if (element.element_type ==
                                   FIMMessageStructElementType::ELEMENT_TYPE_UID
                                   || element.element_type ==
                                      FIMMessageStructElementType::ELEMENT_TYPE_AT) {
                            summary += "@";
                            if (element.at_element.is_at_all) {
                                summary += "所有人 ";
                            } else {
                                summary += element.at_element.default_nick + " ";
                            }
                        }
                    }
                    return summary;
                }
                case FIMMessageContentType::CONTENT_TYPE_FILE: {
                    return "[文件]";
                }
                case FIMMessageContentType::CONTENT_TYPE_COMBINE_FORWARD: {
                    return "[转发消息]";
                }
                case FIMMessageContentType::CONTENT_TYPE_CUSTOM: {
                    return content.custom_content.summary;
                }
                case FIMMessageContentType::CONTENT_TYPE_REPLY: {
                    // 回复消息，SimpleContent
                    FIMMessageInnerReplyContent reply_content = content.reply_content.reply_content;
                    switch (reply_content.content_type) {
                        case FIMMessageContentType::CONTENT_TYPE_TEXT:
                        case FIMMessageContentType::CONTENT_TYPE_LINK: {
                            return reply_content.content.text_content.text;
                        }
                        case FIMMessageContentType::CONTENT_TYPE_IMAGE: {
                            return "[图片]";
                        }
                        case FIMMessageContentType::CONTENT_TYPE_AUDIO: {
                            return "[音频]";
                        }
                        case FIMMessageContentType::CONTENT_TYPE_VIDEO: {
                            return "[视频]";
                        }
                        case FIMMessageContentType::CONTENT_TYPE_GEO: {
                            return "[位置]";
                        }
                        case FIMMessageContentType::CONTENT_TYPE_STRUCT:
                        case FIMMessageContentType::CONTENT_TYPE_AT: {
                            std::vector<FIMMessageStructElement> elements = reply_content.content.struct_content.elements;
                            std::string summary;
                            for (FIMMessageStructElement element: elements) {
                                if (element.element_type ==
                                    FIMMessageStructElementType::ELEMENT_TYPE_TEXT) {
                                    summary += element.text_content.text;
                                } else if (element.element_type ==
                                           FIMMessageStructElementType::ELEMENT_TYPE_UID
                                           || element.element_type ==
                                              FIMMessageStructElementType::ELEMENT_TYPE_AT) {
                                    summary += "@";
                                    if (element.at_element.is_at_all) {
                                        summary += "所有人 ";
                                    } else {
                                        summary += element.at_element.default_nick + " ";
                                    }
                                }
                            }
                            return summary;
                        }
                        case FIMMessageContentType::CONTENT_TYPE_FILE: {
                            return "[文件]";
                        }
                        case FIMMessageContentType::CONTENT_TYPE_COMBINE_FORWARD: {
                            return "[转发消息]";
                        }
                        case FIMMessageContentType::CONTENT_TYPE_CUSTOM: {
                            return reply_content.content.custom_content.summary;
                        }
                        case FIMMessageContentType::CONTENT_TYPE_UNKNOW:
                        default:
                            return "";
                    }
                }
                case FIMMessageContentType::CONTENT_TYPE_UNKNOW:
                default:
                    return "";
            }
        }
    
    json interactionJson(const std::map<std::string, std::string> user_extension, const std::string key) {
        auto it = user_extension.find(INTERACTION_KEY);
        if (it != user_extension.end() && !it->second.empty()) {
            try {
                json interaction = json::parse(it->second);
                json value = interaction[key];
                if (value.is_array()) {
                    return value;
                } else {
                    return json::array();
                }
                
            } catch (const std::exception &e) {
                return json::array();
            }
        }
        
        return json::array();
    }
    
    int64_t interactionCount(const std::map<std::string, std::string> user_extension, const std::string key) {
        json result = interactionJson(user_extension, key);
        if (result.is_array()) {
            std::size_t size = result.size();
            return static_cast<int64_t>(size);
        } else {
            return 0;
        }
    }
    
    std::string userExtensionInteractionText(const std::map<std::string, std::string> user_extension) {
        try {
            return user_extension.at("interaction_text");
        } catch (const std::out_of_range&) {
            return "";
        }
    }

    std::string FIMConversation::interactionText() const {
        std::string text = userExtensionInteractionText(user_extension);
        if (!text.empty()) {
            return text;
        }
        int64_t reply_me_count = interactionCount(user_extension, "replyMe");
        if (reply_me_count > 0) {
            return "[有人回复我]";
        }
        int64_t at_me_count = interactionCount(user_extension, "atMe");
        if (at_me_count > 0) {
            return "[有人@我]";
        }
        int64_t at_all_count = interactionCount(user_extension, "atAll");
        if (at_all_count > 0) {
            return "[@所有人]";
        }
        return "";
    }
    
    
    /**
     * user_exteions中 at_all的内容
     */
    std::string FIMConversation::at_all() const {
        return interactionJson(user_extension, "at_all").dump();
    }

    /**
     * user_exteions中 at我的内容
     */
    std::string FIMConversation::at_me() const {
        return interactionJson(user_extension, "at_me").dump();
    }
    /**
     * user_exteions中 回复的内容
     */
    std::string FIMConversation::reply_me() const {
        return interactionJson(user_extension, "reply_me").dump();
    }

    }
}
