#pragma once
#include "fim_group_permission.h"
#include "aim_group_permission.h"
#include <vector> 

namespace alibaba {
namespace fim {
    inline FIMGroupPermission FIMGroupPermissionFrom(const dps::AIMGroupPermission& aimGroupPermission) {
        FIMGroupPermission fimGroupPermission;
        fimGroupPermission.permission_group = aimGroupPermission.permission_group;
        fimGroupPermission.status = aimGroupPermission.status;
        return fimGroupPermission;
    }

    inline std::vector<FIMGroupPermission> FIMGroupPermissionListFrom(const std::vector<dps::AIMGroupPermission>& aimGroupPermissionList) {
        std::vector<FIMGroupPermission> fimGroupPermissionList;
        for (const auto& aimGroupPermission : aimGroupPermissionList) {
            fimGroupPermissionList.push_back(FIMGroupPermissionFrom(aimGroupPermission));
        }
        return fimGroupPermissionList;
    }

    inline  dps::AIMGroupPermission AIMGroupPermissionFrom(const FIMGroupPermission& fimGroupPermission) {
        dps::AIMGroupPermission aimGroupPermission;
        aimGroupPermission.permission_group = fimGroupPermission.permission_group;
        aimGroupPermission.status = fimGroupPermission.status;
        return aimGroupPermission;
    }

    inline std::vector<dps::AIMGroupPermission> AIMGroupPermissionListFrom(const std::vector<FIMGroupPermission>& fimGroupPermissionList) {
        std::vector<dps::AIMGroupPermission> aimGroupPermissionList;
        for (const auto& fimGroupPermission : fimGroupPermissionList) {
            aimGroupPermissionList.push_back(AIMGroupPermissionFrom(fimGroupPermission));
        }
        return aimGroupPermissionList;
    }
} // namespace fim
} // namespace alibaba