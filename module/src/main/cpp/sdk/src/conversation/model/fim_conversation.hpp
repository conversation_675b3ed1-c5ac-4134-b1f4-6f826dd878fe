//
//  fim_conversation_internal.h
//  FliggyIMSDK
//
//  Created by 蒙晨 on 2024/2/18.
//

// fim_conversation_internal.h
#ifndef fim_conversation_internal_h
#define fim_conversation_internal_h

#include "aim_pub_conversation.h"
#include "fim_conversation.h"
#include "fim_group_permission.hpp"
#include "fim_message.h"
#include "fim_message.hpp"
namespace alibaba {
namespace fim {

inline FIMConversation
FIMConversationFrom(const dps::AIMPubConversation &aimConv) {
    FIMMessage last_msg = FIMMessageFrom(aimConv.last_msg);
  return FIMConversation(
      aimConv.appCid, static_cast<FIMConvType>(aimConv.type), aimConv.biz_type,
      static_cast<FIMConvStatus>(aimConv.status), aimConv.userids,
      aimConv.created_at, aimConv.modify_time, aimConv.red_point, aimConv.draft,
      aimConv.mute_notification, aimConv.top_rank, aimConv.extension,
      aimConv.user_extension, aimConv.local_extension, aimConv.has_last_msg, last_msg,
      aimConv.join_time, aimConv.owner_uid, aimConv.title, aimConv.icon,
      aimConv.member_count, aimConv.member_limit, aimConv.silence_all,
      static_cast<FIMGroupSilencedStatus>(aimConv.silenced_status),
      aimConv.silenced_endtime, aimConv.admins,
      FIMGroupPermissionListFrom(aimConv.member_permissions),
      aimConv.read_receipts_enabled, "", "");
}

inline std::vector<FIMConversation>
FIMConversationListFrom(const std::vector<dps::AIMPubConversation> &aimConvs) {
  std::vector<FIMConversation> fimConvs;
  for (const auto &aimConv : aimConvs) {
    fimConvs.push_back(FIMConversationFrom(aimConv));
  }
  return fimConvs;
}

} // namespace fim
} // namespace alibaba

#endif /* fim_conversation_internal_h */
