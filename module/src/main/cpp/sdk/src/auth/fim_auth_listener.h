// fim_auth_listener.h

#ifndef fim_auth_listener_h
#define fim_auth_listener_h


// fim_auth_listener.h

#include "fliggy_im_sdk.h"
#pragma once

#include <cstdint>
#include <memory>
#include <string>
#include "dps_auth_listener.h"



/**
 // 使用 FIMAuthListenerAdapter
 FIMAuthListenerPtr myFIMListener = {创建 FIMAuthListener 的实现 };
 std::shared_ptr<dps::DPSAuthListener> adapter(new fim::FIMAuthListenerAdapter(myFIMListener));

 AddListener(adapter); // AddListener 需要 dps::DPSAuthListener 的 shared_ptr
 */

namespace alibaba {
  namespace dps {
    enum class DPSConnectionStatus;
  }

namespace fim {

enum class FIMConnectionStatus : int {
  UNCONNECTED = 0, // 未连接
  CONNECTING = 1,  // 正在连接
  CONNECTED = 2,   // 已连接
  AUTHING = 3,     // 正在登录
  AUTHED = 4,      // 登录成功
};

class FIMSDK_API FIMAuthListener: public dps::DPSAuthListener {
public:
  virtual ~FIMAuthListener() {}

    /**
     * 连接状态事件
     * @param status      网络状态
     */
    virtual void OnConnectionStatusChanged(FIMConnectionStatus status) = 0;

    /**
     * 登录token获取失败事件
     * @param error_code  获取登录token失败错误值
     * @param error_msg   获取登录token失败错误信息
     */
    virtual void OnGetAuthCodeFailed(int32_t error_code,
                                     const std::string& error_msg) = 0;

    /**
     * 本地登录事件
     * 如果本地已有登录信息，调用Login接口后会立即回调；反之会等待网络登录成功之后回调
     */
    virtual void OnLocalLogin() = 0;

    /**
     * 被踢事件
     * @param message     被踢下线时附带的消息
     */
    virtual void OnKickout(const std::string& message) = 0;

    /**
     * 其他端设备在（离）线情况
     * @param type 事件类型（1：事件通知，包括上下线，2：状态通知，在线状态）
     * @param device_type 设备类型
     * （0:default,1:web,2:Android,3:iOS,4:Mac,5:Windows,6:iPad）
     * @param status      设备状态（1：上线或在线，2：下线或离线）
     * @param time        时间（上线或下线时间）
     */
    virtual void OnDeviceStatus(int32_t type,
                                int32_t device_type,
                                int32_t status,
                                int64_t time) = 0;

    /**
     * 下载资源cookie变更事件
     * @param cookie      新cookie
     */
    virtual void OnMainServerCookieRefresh(const std::string& cookie) = 0;
private:
      /**
   * 连接状态事件
   * @param status      网络状态
   */
void OnConnectionStatusChanged(dps::DPSConnectionStatus status);

};

using FIMAuthListenerPtr = std::shared_ptr<FIMAuthListener>;
using FIMAuthListenerWeakPtr = std::weak_ptr<FIMAuthListener>;

} // namespace fim
} // namespace alibaba

#endif // ALIBABA_FIM_AUTH_LISTENER_H_
