//
//  json_to_AIMPubMsgLocalExtensionUpdateInfo.cpp
//  FliggyIM
//
//  Created by 蒙晨 on 2024/8/6.
//

#include "json_to_AIMPubMsgLocalExtensionUpdateInfo.hpp"
//
//struct AIMPubMsgLocalExtensionUpdateInfo final {
//
//    /**
//     * 会话id
//     */
//    std::string appCid;
//    /**
//     * 消息localid
//     */
//    std::string localid;
//    /**
//     * 扩展内容
//     */
//    std::map<std::string, std::string> extension;
//}

alibaba::dps::AIMPubMsgLocalExtensionUpdateInfo AIMPubMsgLocalExtensionUpdateInfoFromJson(const nlohmann::json& j) {
    alibaba::dps::AIMPubMsgLocalExtensionUpdateInfo info;
    if (j.contains("appCid") && !j["appCid"].is_null()) {
        info.appCid = j["appCid"].get<std::string>();
    }
    if (j.contains("localid") && !j["localid"].is_null()) {
        info.localid = j["localid"].get<std::string>();
    }
    if (j.contains("extension") && !j["extension"].is_null()) {
        info.extension = j["extension"].get<std::map<std::string, std::string>>();
    }
    return info;
}
