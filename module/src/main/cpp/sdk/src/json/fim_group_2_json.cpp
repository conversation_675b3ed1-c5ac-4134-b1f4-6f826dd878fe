//
// Created by 照松 on 2024/4/16.
//

#include "fim_group_2_json.h"
#include "fim_group_role_changed_notify.h"
#include "fim_message_2_json.h"
#include <string>
#include <vector>

nlohmann::json
groupMember2Json(const alibaba::fim::FIMGroupMember &groupMember) {
    nlohmann::json json;

    json["app_cid"] = groupMember.appCid;
    json["uid"] = groupMember.uid;
    json["role"] = groupMemberRole2Json(groupMember.role);
    json["created_at"] = groupMember.created_at;
    json["group_nick"] = groupMember.group_nick;
    json["extension"] = groupMember.extension;

    return json;
}

nlohmann::json
groupMemberRole2Json(const alibaba::fim::FIMGroupMemberRole &memberRole) {
    nlohmann::json json;

    json["role"] = memberRole.role;
    json["custom_role"] = memberRole.custom_role;

    return json;
}

nlohmann::json
groupAnnouncement2Json(const alibaba::fim::FIMGroupAnnouncement &announcement) {
    nlohmann::json json;

    json["announcement"] = announcement.announcement;
    json["operator_uid"] = announcement.operator_uid;
    json["modify_time"] = announcement.modify_time;

    return json;
}

nlohmann::json
groupMemberRoleChangedNotify2Json(const alibaba::fim::FIMGroupRoleChangedNotify &notify) {
    nlohmann::json json;
    json["app_cid"] = notify.appCid;
    json["role"] = groupMemberRole2Json(notify.role);
    json["uids"] = stringVector2JsonArray(notify.uids);
    return json;
}
