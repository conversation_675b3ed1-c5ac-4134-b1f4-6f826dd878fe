//
// Created by 照松 on 2024/4/4.
//

#include "fim_conv_2_json.h"

nlohmann::json fimConv2Json(const alibaba::fim::FIMConversation &conversation) {
    nlohmann::json json;

    json["app_cid"] = conversation.appCid;
    json["type"] = conversation.type;
    json["biz_type"] = conversation.biz_type;
    json["status"] = conversation.status;
    json["userids"] = conversation.userids;
    json["created_at"] = conversation.created_at;
    json["modify_time"] = conversation.modify_time;
    json["red_point"] = conversation.red_point;
    json["draft"] = conversation.draft;
    json["mute_notification"] = conversation.mute_notification;
    json["top_rank"] = conversation.top_rank;
    json["extension"] = conversation.extension;
    json["user_extension"] = conversation.user_extension;
    json["local_extension"] = conversation.local_extension;
    json["has_last_msg"] = conversation.has_last_msg;
    json["last_msg"] = fimMessage2Json(conversation.last_msg);
    json["join_time"] = conversation.join_time;
    json["owner_uid"] = conversation.owner_uid;
    json["title"] = conversation.title;
    json["icon"] = conversation.icon;
    json["member_count"] = conversation.member_count;
    json["member_limit"] = conversation.member_limit;
    json["silence_all"] = conversation.silence_all;
    json["silenced_status"] = conversation.silenced_status;
    json["silenced_endtime"] = conversation.silenced_endtime;
    json["admins"] = conversation.admins;
    json["member_permissions"] = fimGroupPermission2Json(conversation.member_permissions);
    json["read_receipts_enabled"] = conversation.read_receipts_enabled;
    json["fim_title"] = conversation.fim_title();
    json["fim_avatar"] = conversation.fim_avatar();
    json["fim_summary"] = conversation.fim_summary();
    json["interaction_text"] = conversation.interactionText();

    return json;
}

nlohmann::json fimGroupPermission2Json(const alibaba::fim::FIMGroupPermission &permission) {
    nlohmann::json json;

    json["permission_group"] = permission.permission_group;
    json["status"] = permission.status;

    return json;
}

nlohmann::json fimGroupPermission2Json(const std::vector<alibaba::fim::FIMGroupPermission> &permissions) {
    nlohmann::json permissionJsonArray = nlohmann::json::array();

    for (const alibaba::fim::FIMGroupPermission &permission: permissions) {
        permissionJsonArray.push_back(fimGroupPermission2Json(permission));
    }

    return permissionJsonArray;
}
