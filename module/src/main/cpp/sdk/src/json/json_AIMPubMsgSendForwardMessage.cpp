//
//  json_AIMPubMsgSendForwardMessage.cpp
//  FliggyIM
//
//  Created by 蒙晨 on 2024/8/6.
//

#include "json_AIMPubMsgSendForwardMessage.hpp"

//struct AIMPubMsgSendForwardMessage final {
//    
//    std::string appCid;
//    /**
//     * 需转发消息的mid列表
//     */
//    std::vector<std::string> mids;
//    std::string to_appCid;
//    std::vector<std::string> receivers;
//    /**
//     * 如不为空，则替换转发消息的 extension，否则复用原extension
//     */
//    std::map<std::string, std::string> extension;
//    std::map<std::string, std::string> callback_ctx;
//}

alibaba::dps::AIMPubMsgSendForwardMessage AIMPubMsgSendForwardMessageFromJson(const nlohmann::json& j) {
    alibaba::dps::AIMPubMsgSendForwardMessage info;
    if (j.contains("appCid") && !j["appCid"].is_null()) {
        info.appCid = j["appCid"].get<std::string>();
    }
    if (j.contains("mids") && !j["mids"].is_null()) {
        info.mids = j["mids"].get<std::vector<std::string>>();
    }
    if (j.contains("to_appCid") && !j["to_appCid"].is_null()) {
        info.to_appCid = j["to_appCid"].get<std::string>();
    }
    if (j.contains("receivers") && !j["receivers"].is_null()) {
        info.receivers = j["receivers"].get<std::vector<std::string>>();
    }
    if (j.contains("extension") && !j["extension"].is_null()) {
        info.extension = j["extension"].get<std::map<std::string, std::string>>();
    }
    if (j.contains("callback_ctx") && !j["callback_ctx"].is_null()) {
        info.callback_ctx = j["callback_ctx"].get<std::map<std::string, std::string>>();
    }
    return info;
}

nlohmann::json JsonFromAIMPubMsgSendForwardMessage(const alibaba::dps::AIMPubMsgSendForwardMessage& forwardMessage) {
    nlohmann::json j;
    if (!forwardMessage.appCid.empty()) {
        j["appCid"] = forwardMessage.appCid;
    }
    if (!forwardMessage.mids.empty()) {
        j["mids"] = forwardMessage.mids;
    }
    if (!forwardMessage.to_appCid.empty()) {
        j["to_appCid"] = forwardMessage.to_appCid;
    }
    if (!forwardMessage.receivers.empty()) {
        j["receivers"] = forwardMessage.receivers;
    }
    if (!forwardMessage.extension.empty()) {
        j["extension"] = forwardMessage.extension;
    }
    if (!forwardMessage.callback_ctx.empty()) {
        j["callback_ctx"] = forwardMessage.callback_ctx;
    }
    return j;
}
