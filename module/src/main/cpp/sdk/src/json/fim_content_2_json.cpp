//
// Created by 照松 on 2024/4/3.
//

#include "fim_content_2_json.h"

nlohmann::json messageContent2Json(const alibaba::fim::FIMMessageContent &content) {
    nlohmann::json json;
    json["content_type"] = static_cast<int>(content.content_type);
    switch (content.content_type) {
        case alibaba::fim::FIMMessageContentType::CONTENT_TYPE_UNKNOW:
            break;
        case alibaba::fim::FIMMessageContentType::CONTENT_TYPE_TEXT:
        case alibaba::fim::FIMMessageContentType::CONTENT_TYPE_LINK: {
            json["text_content"] = textContent2Json(content.text_content);
        }
            break;
        case alibaba::fim::FIMMessageContentType::CONTENT_TYPE_IMAGE: {
            json["image_content"] = imageContent2Json(content.image_content);
        }
            break;
        case alibaba::fim::FIMMessageContentType::CONTENT_TYPE_AUDIO: {
            json["audio_content"] = audioContent2Json(content.audio_content);
        }
            break;
        case alibaba::fim::FIMMessageContentType::CONTENT_TYPE_VIDEO: {
            json["video_content"] = videoContent2Json(content.video_content);
        }
            break;
        case alibaba::fim::FIMMessageContentType::CONTENT_TYPE_GEO: {
            json["geo_content"] = geoContent2Json(content.geo_content);
        }
            break;
        case alibaba::fim::FIMMessageContentType::CONTENT_TYPE_STRUCT:
        case alibaba::fim::FIMMessageContentType::CONTENT_TYPE_AT: {
            json["struct_content"] = structContent2Json(content.struct_content);
        }
            break;
        case alibaba::fim::FIMMessageContentType::CONTENT_TYPE_FILE: {
            json["file_content"] = fileContent2Json(content.file_content);
        }
            break;
        case alibaba::fim::FIMMessageContentType::CONTENT_TYPE_REPLY: {
            json["reply_content"] = replyContent2Json(content.reply_content);
        }
            break;
        case alibaba::fim::FIMMessageContentType::CONTENT_TYPE_COMBINE_FORWARD: {
            json["combine_forward_content"] = combineForwardContent2Json(
                    content.combine_forward_content);
        }
            break;
        case alibaba::fim::FIMMessageContentType::CONTENT_TYPE_CUSTOM: {
            json["custom_content"] = customContent2Json(content.custom_content);
        }
            break;
    }

    return json;
}


nlohmann::json textContent2Json(const alibaba::fim::FIMMessageTextContent &textContent) {
    nlohmann::json json;

    json["text"] = textContent.text;
    json["encrypted_text"] = textContent.encrypted_text;
    json["extension"] = textContent.extension;

    return json;
}

alibaba::fim::FIMMessageTextContent json2TextContent(const nlohmann::json &jsonObj) {
    alibaba::fim::FIMMessageTextContent textContent;
    if (jsonHasKey(jsonObj, "text")) {
        textContent.text = jsonObj["text"];
    }
    if (jsonHasKey(jsonObj, "encrypted_text")) {
        textContent.encrypted_text = jsonObj["encrypted_text"];
    }
    if (jsonHasKey(jsonObj, "extension")) {
        textContent.extension = json2StrStrMap(jsonObj["extension"]);
    }
    return textContent;
}

nlohmann::json imageContent2Json(const alibaba::fim::FIMMessageImageContent &imageContent) {
    nlohmann::json json;

    json["local_path"] = imageContent.local_path;
    json["upload_path"] = imageContent.upload_path;
    json["mime_type"] = imageContent.mime_type;
    json["original_url"] = imageContent.original_url;
    json["thumbnail_url"] = imageContent.thumbnail_url;
    json["media_id"] = imageContent.media_id;
    json["file_name"] = imageContent.file_name;
    json["height"] = imageContent.height;
    json["width"] = imageContent.width;
    json["size"] = imageContent.size;
    json["type"] = static_cast<int>(imageContent.type);
    json["file_type"] = static_cast<int>(imageContent.file_type);
    json["orientation"] = static_cast<int>(imageContent.orientation);
    json["extension"] = imageContent.extension;

    return json;
}

nlohmann::json audioContent2Json(const alibaba::fim::FIMMessageAudioContent &audioContent) {
    nlohmann::json json;

    json["local_path"] = audioContent.local_path;
    json["upload_path"] = audioContent.upload_path;
    json["mime_type"] = audioContent.mime_type;
    json["url"] = audioContent.url;
    json["media_id"] = audioContent.media_id;
    json["binary_data"] = audioContent.binary_data;
    json["audio_type"] = static_cast<int>(audioContent.audio_type);
    json["duration"] = audioContent.duration;

    return json;
}

nlohmann::json videoContent2Json(const alibaba::fim::FIMMessageVideoContent &videoContent) {
    nlohmann::json json;

    json["local_path"] = videoContent.local_path;
    json["upload_path"] = videoContent.upload_path;
    json["mime_type"] = videoContent.mime_type;
    json["url"] = videoContent.url;
    json["media_id"] = videoContent.media_id;
    json["cover_local_path"] = videoContent.cover_local_path;
    json["cover_upload_path"] = videoContent.cover_upload_path;
    json["cover_url"] = videoContent.cover_url;
    json["cover_media_id"] = videoContent.cover_media_id;
    json["cover_file_type"] = static_cast<int>(videoContent.cover_file_type);
    json["cover_mime_type"] = videoContent.cover_mime_type;
    json["file_name"] = videoContent.file_name;
    json["file_type"] = videoContent.file_type;
    json["file_size"] = videoContent.file_size;
    json["duration"] = videoContent.duration;
    json["height"] = videoContent.height;
    json["width"] = videoContent.width;
    json["cover_width"] = videoContent.cover_width;
    json["cover_height"] = videoContent.cover_height;

    return json;
}

nlohmann::json geoContent2Json(const alibaba::fim::FIMMessageGeoContent &geoContent) {
    nlohmann::json json;

    json["pic_local_path"] = geoContent.pic_local_path;
    json["pic_upload_path"] = geoContent.pic_upload_path;
    json["mime_type"] = geoContent.mime_type;
    json["pic_url"] = geoContent.pic_url;
    json["pic_media_id"] = geoContent.pic_media_id;
    json["pic_file_type"] = static_cast<int>(geoContent.pic_file_type); // 枚举到 NSNumber
    json["pic_width"] = geoContent.pic_width;
    json["pic_height"] = geoContent.pic_height;
    json["latitude"] = geoContent.latitude;
    json["longitude"] = geoContent.longitude;
    json["location_name"] = geoContent.location_name;

    return json;
}


nlohmann::json fileContent2Json(const alibaba::fim::FIMMessageFileContent &fileContent) {
    nlohmann::json json;

    // Convert std::string to NSString
    json["local_path"] = fileContent.local_path;
    json["file_name"] = fileContent.file_name;
    json["file_type"] = fileContent.file_type;
    json["media_id"] = fileContent.media_id;
    json["mime_type"] = fileContent.mime_type;
    json["url"] = fileContent.url;

    // Convert int64_t to NSNumber
    json["file_size"] = fileContent.file_size;

    return json; // Return an immutable dictionary
}

nlohmann::json replyContent2Json(const alibaba::fim::FIMMessageReplyContent &replyContent) {
    nlohmann::json json;

    json["reference_msg"] = reference2Json(replyContent.reference_msg);
    json["reply_content"] = innerReplyContent2Json(replyContent.reply_content);

    return json;
}

nlohmann::json
innerCombineContent2Json(const alibaba::fim::FIMMessageInnerCombineContent &innerCombineContent) {
    nlohmann::json json;

    json["content_type"] = innerCombineContent.content_type;
    json["simple_content"] = simpleContent2Json(innerCombineContent.simple_content);
    json["reply_content"] = replyContent2Json(innerCombineContent.reply_content);

    return json;
}


nlohmann::json combineForward2Json(const alibaba::fim::FIMMessageCombineForward &combineForward) {
    nlohmann::json json;

    json["sender"] = combineForward.sender;
    json["appCid"] = combineForward.appCid;
    json["mid"] = combineForward.mid;
    json["created_at"] = combineForward.created_at;
    json["combine_content"] = innerCombineContent2Json(combineForward.combine_content);
    json["extension"] = combineForward.extension;

    return json;
}

nlohmann::json combineForwardContent2Json(
        const alibaba::fim::FIMMessageCombineForwardContent &combineForwardContent) {
    nlohmann::json json = nlohmann::json::array();

    for (const auto &combineForward: combineForwardContent.combine_forward) {
        json.push_back(combineForward2Json(combineForward));
    }

    return json;
}


nlohmann::json customContent2Json(const alibaba::fim::FIMMessageCustomContent &customContent) {
    nlohmann::json json;

    // Convert int32_t to NSNumber
    json["type"] = customContent.type;

    // Convert std::string to NSString
    json["title"] = customContent.title;
    json["summary"] = customContent.summary;
    json["degrade"] = customContent.degrade;
    json["data"] = customContent.data;

    return json;
}