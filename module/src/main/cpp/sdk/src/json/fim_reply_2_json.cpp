//
// Created by 照松 on 2024/4/3.
//

#include "fim_reply_2_json.h"

nlohmann::json reference2Json(const alibaba::fim::FIMMessageReference &reference) {
    nlohmann::json json;

    json["sender"] = reference.sender;
    json["appCid"] = reference.appCid;
    json["mid"] = reference.mid;
    json["created_at"] = reference.created_at;
    json["reference_content"] = referenceContent2Json(reference.reference_content);
    json["extension"] = reference.extension;

    return json;
}

nlohmann::json referenceContent2Json(const alibaba::fim::FIMMessageReferenceContent &reference) {
    nlohmann::json json;

    json["content_type"] = reference.content_type;
    json["content"] = simpleContent2Json(reference.content);

    return json;
}

nlohmann::json innerReplyContent2Json(const alibaba::fim::FIMMessageInnerReplyContent &innerReplyContent) {
    nlohmann::json json;

    json["content_type"] = innerReplyContent.content_type;
    json["content"] = simpleContent2Json(innerReplyContent.content);

    return json;
}

nlohmann::json simpleContent2Json(const alibaba::fim::FIMMessageSimpleContent &reference) {
    nlohmann::json json;

    json["text_content"] = textContent2Json(reference.text_content);
    json["image_content"] = imageContent2Json(reference.image_content);
    json["audio_content"] = audioContent2Json(reference.audio_content);
    json["video_content"] = videoContent2Json(reference.video_content);
    json["geo_content"] = geoContent2Json(reference.geo_content);
    json["custom_content"] = customContent2Json(reference.custom_content);
    json["struct_content"] = structContent2Json(reference.struct_content);
    json["file_content"] = fileContent2Json(reference.file_content);

    return json;
}