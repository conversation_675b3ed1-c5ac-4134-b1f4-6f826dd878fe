//
//  json_AIMPubMsgSendReplyMessage.cpp
//  FliggyIM
//
//  Created by 蒙晨 on 2024/8/6.
//

#include "json_AIMPubMsgSendReplyMessage.hpp"
#include "json_to_pub_msg_send_message.h"


alibaba::dps::AIMPubMsgSendReplyMessage AIMPubMsgSendReplyMessageFromJson(const nlohmann::json& j) {
    alibaba::dps::AIMPubMsgSendReplyMessage info;
    if (j.contains("appCid") && !j["appCid"].is_null()) {
        info.appCid = j["appCid"].get<std::string>();
    }
    if (j.contains("reference_mid") && !j["reference_mid"].is_null()) {
        info.reference_mid = j["reference_mid"].get<std::string>();
    }
    if (j.contains("reply_content") && !j["reply_content"].is_null()) {
        info.reply_content = convertToPubMsgContent(j["reply_content"]);
    }
    if (j.contains("receivers") && !j["receivers"].is_null()) {
        info.receivers = j["receivers"].get<std::vector<std::string>>();
    }
    if (j.contains("extension") && !j["extension"].is_null()) {
        info.extension = j["extension"].get<std::map<std::string, std::string>>();
    }
    if (j.contains("local_extension") && !j["local_extension"].is_null()) {
        info.local_extension = j["local_extension"].get<std::map<std::string, std::string>>();
    }
    if (j.contains("callback_ctx") && !j["callback_ctx"].is_null()) {
        info.callback_ctx = j["callback_ctx"].get<std::map<std::string, std::string>>();
    }
    return info;
}
