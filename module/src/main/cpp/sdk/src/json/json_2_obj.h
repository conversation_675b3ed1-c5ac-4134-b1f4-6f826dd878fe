//
// Created by 照松 on 2024/4/4.
//

#ifndef NEED4SPEED_FIM_JSON_2_OBJ_H
#define NEED4SPEED_FIM_JSON_2_OBJ_H

#include "fim_message.h"
#include "fliggy_im_sdk.h"

std::map<std::string, std::string>  FIMSDK_API 
json2StrStrMap(const nlohmann::json &json);

/**
 * 判断json中是否包含某个key
 * @return true 包含
 */
bool  FIMSDK_API jsonHasKey(const nlohmann::json &json, const std::string &key);

#endif //NEED4SPEED_FIM_JSON_2_OBJ_H
