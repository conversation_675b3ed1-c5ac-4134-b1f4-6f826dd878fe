//
//  json_aim_pub_conv_create_single_conv_param.cpp
//  FliggyIM
//
//  Created by 蒙晨 on 2024/10/10.
//

#include "json_aim_pub_conv_create_single_conv_param.hpp"

alibaba::dps::AIMPubConvCreateSingleConvParam AIMPubConvCreateSingleConvParamFromJson(const nlohmann::json& j) {
    alibaba::dps::AIMPubConvCreateSingleConvParam param;
    try {
        if (j.contains("appCid") && !j["appCid"].is_null()) {
            param.appCid = j["appCid"].get<std::string>();
        }
        if (j.contains("biz_type") && !j["biz_type"].is_null()) {
            param.biz_type = j["biz_type"].get<std::string>();
        }
        if (j.contains("ext") && !j["ext"].is_null() && j["ext"].is_object()) {
            param.ext = j["ext"].get<std::map<std::string, std::string>>();
        }
        if (j.contains("uids") && !j["uids"].is_null()) {
            param.uids = j["uids"].get<std::vector<std::string>>();
        }
        if (j.contains("ctx") && !j["ctx"].is_null() && j["ctx"].is_object()) {
            param.ctx = j["ctx"].get<std::map<std::string, std::string>>();
        }
        if (j.contains("is_local") && !j["is_local"].is_null()) {
            param.is_local = j["is_local"].get<bool>();
        }
    } catch (std::exception& e) {
        
    }
    return param;
    
}
