//
//  json_from_AIMPubMsgReadStatus.hpp
//  FliggyIM
//
//  Created by 蒙晨 on 2024/8/6.
//

#ifndef json_from_AIMPubMsgReadStatus_hpp
#define json_from_AIMPubMsgReadStatus_hpp
#include "aim_pub_msg_read_status.h"
#include "json.hpp"
#include <stdio.h>

alibaba::dps::AIMPubMsgReadStatus AIMPubMsgReadStatusFromJson(const nlohmann::json& j);
nlohmann::json JsonFromAIMPubMsgReadStatus(const alibaba::dps::AIMPubMsgReadStatus& readStatus);
//nlohmann::json AIMPubMsgReadStatusToJson(const alibaba::dps::AIMPubMsgReadStatus& readStatus);

#endif /* json_from_AIMPubMsgReadStatus_hpp */
