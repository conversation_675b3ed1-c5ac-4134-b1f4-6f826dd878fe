//
// Created by 照松 on 2024/4/3.
//

#ifndef NEED4SPEED_FIM_STRUCT_2_JSON_H
#define NEED4SPEED_FIM_STRUCT_2_JSON_H

#include "fim_content_2_json.h"
#include "fliggy_im_sdk.h"

nlohmann::json FIMSDK_API  structContent2Json(const alibaba::fim::FIMMessageStructContent &structContent);

nlohmann::json FIMSDK_API  structElements2Json(std::vector<alibaba::fim::FIMMessageStructElement> elements);

nlohmann::json FIMSDK_API  structElement2Json(const alibaba::fim::FIMMessageStructElement &structElement);

alibaba::fim::FIMMessageStructElement json2StructElement(nlohmann::json  jsonObj);

nlohmann::json FIMSDK_API  structElementUid2Json(const alibaba::fim::FIMMessageStructElementUid &structElement);

alibaba::fim::FIMMessageStructElementUid json2StructElementUid(nlohmann::json  jsonObj);

nlohmann::json FIMSDK_API  structElementAt2Json(const alibaba::fim::FIMMessageStructElementAt &structElement);

alibaba::fim::FIMMessageStructElementAt json2StructElementAt(nlohmann::json  jsonObj);

#endif //NEED4SPEED_FIM_STRUCT_2_JSON_H
