//
// Created by 照松 on 2024/4/16.
//

#ifndef NEED4SPEED_FIM_GROUP_2_JSON_H
#define NEED4SPEED_FIM_GROUP_2_JSON_H

#include "fim_message.h"
#include "fim_group_member.h"
#include "fim_group_announcement.h"
#include "fim_group_role_changed_notify.h"

nlohmann::json FIMSDK_API 
groupMember2Json(const alibaba::fim::FIMGroupMember &groupMember);

nlohmann::json FIMSDK_API 
groupMemberRole2Json(const alibaba::fim::FIMGroupMemberRole &memberRole);

nlohmann::json FIMSDK_API 
groupMemberRoleChangedNotify2Json(const alibaba::fim::FIMGroupRoleChangedNotify &notify);

nlohmann::json FIMSDK_API 
groupAnnouncement2Json(const alibaba::fim::FIMGroupAnnouncement &announcement);

#endif //NEED4SPEED_FIM_GROUP_2_JSON_H
