//
// Created by 照松 on 2024/4/3.
//

#include "fim_message_2_json.h"

nlohmann::json fimMessage2Json(const alibaba::fim::FIMMessage &message) {
    nlohmann::json json;

    json["app_cid"] = message.appCid;
    json["mid"] = message.mid;
    json["localid"] = message.localid;
    json["sender"] = message.sender;
    json["sender_tag"] = message.sender_tag;
    json["created_at"] = message.created_at;
    json["unread_count"] = message.unread_count;
    json["receiver_count"] = message.receiver_count;
    json["receivers"] = message.receivers;
    json["is_read"] = message.is_read;
    json["extension"] = message.extension;
    json["local_extension"] = message.local_extension;
    json["user_extension"] = message.user_extension;
    json["content"] = messageContent2Json(message.content);
    json["status"] = message.status;
    json["is_delete"] = message.is_delete;
    json["is_recall"] = message.is_recall;
    json["is_disable_read"] = message.is_disable_read;
    json["is_local"] = message.is_local;
    json["biz_info"] = msgBizInfo2Json(message.biz_info);
    json["display_style"] = message.display_style;
    json["recall_feature"] = msgRecallFeature2Json(message.recall_feature);
    json["is_self_message"] = message.is_self_message();

    return json;
}

nlohmann::json msgBizInfo2Json(const alibaba::dps::AIMMsgBizInfo &bizInfo) {
    nlohmann::json json;

    json["biz_tag"] = bizInfo.biz_tag;
    json["biz_text"] = bizInfo.biz_text;

    return json;
}

nlohmann::json msgRecallFeature2Json(const alibaba::fim::AIMPubMsgRecallFeature &recallFeature) {
    nlohmann::json json;

    json["operator_type"] = static_cast<int>(recallFeature.operator_type);
    json["code"] = recallFeature.code;
    json["operator_uid"] = recallFeature.operator_uid;
    json["extension"] = recallFeature.extension;

    return json;
}


/**
 * string vector 转 json 数组
 * @param vector
 * @return json 数组
 */
nlohmann::json stringVector2JsonArray(std::vector<std::string> vector) {
    nlohmann::json jsonArray = nlohmann::json::array();
    for (const std::string &item: vector) {
        jsonArray.push_back(item);
    }
    return jsonArray;
}

nlohmann::json fimNewMessage2Json(const alibaba::fim::FIMNewMessage &newMessage) {
    nlohmann::json  jsonObject;

    jsonObject["msg"] = fimMessage2Json(newMessage.msg);
    jsonObject["type"] = newMessage.type;

    return jsonObject;
}

nlohmann::json sendMediaProgress2Json(const alibaba::dps::AIMMsgSendMediaProgress &progress) {
    nlohmann::json  jsonObject;

    jsonObject["cid"] = progress.cid;
    jsonObject["localid"] = progress.localid;
    jsonObject["progress"] = progress.progress;

    return jsonObject;
}