//
//  json_to_AIMPubMsgBizUpdateInfo.cpp
//  FliggyIM
//
//  Created by 蒙晨 on 2024/8/6.
//

#include "json_to_AIMPubMsgBizUpdateInfo.hpp"

//struct AIMMsgBizInfo final {
//    
//    /**
//     * tag
//     */
//    std::string biz_tag;
//    /**
//     * 内容
//     */
//    std::string biz_text;
//}
//struct AIMPubMsgBizUpdateInfo final {
//    
//    /**
//     * 需要更新的消息appCid
//     */
//    std::string appCid;
//    /**
//     * 需要更新的消息localid
//     */
//    std::string localid;
//    /**
//     * 消息更新模式
//     */
//    AIMMsgUpdateMode update_mode = AIMMsgUpdateMode::UPDATE_ALL;
//    /**
//     * 需更新的消息biz属性
//     */
//    AIMMsgBizInfo biz_info;
//}
//AIMMsgBizInfo
alibaba::dps::AIMMsgBizInfo AIMMsgBizInfoFromJson(const nlohmann::json& j) {
    alibaba::dps::AIMMsgBizInfo info;
    if (j.contains("biz_tag") && !j["biz_tag"].is_null()) {
        info.biz_tag = j["biz_tag"].get<std::string>();
    }
    if (j.contains("biz_text") && !j["biz_text"].is_null()) {
        info.biz_text = j["biz_text"].get<std::string>();
    }
    return info;
}
//AIMPubMsgBizUpdateInfo
alibaba::dps::AIMPubMsgBizUpdateInfo AIMPubMsgBizUpdateInfoFromJson(const nlohmann::json& j) {
    alibaba::dps::AIMPubMsgBizUpdateInfo info;
    if (j.contains("appCid") && !j["appCid"].is_null()) {
        info.appCid = j["appCid"].get<std::string>();
    }
    if (j.contains("localid") && !j["localid"].is_null()) {
        info.localid = j["localid"].get<std::string>();
    }
    if (j.contains("update_mode") && !j["update_mode"].is_null()) {
        info.update_mode = j["update_mode"].get<alibaba::dps::AIMMsgUpdateMode>();
    }
    if (j.contains("biz_info") && !j["biz_info"].is_null()) {
        info.biz_info = AIMMsgBizInfoFromJson(j["biz_info"]);
    }
    return info;
}

