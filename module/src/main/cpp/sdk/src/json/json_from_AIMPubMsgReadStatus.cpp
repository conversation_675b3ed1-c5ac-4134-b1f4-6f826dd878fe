//
//  json_from_AIMPubMsgReadStatus.cpp
//  FliggyIM
//
//  Created by 蒙晨 on 2024/8/6.
//

#include "json_from_AIMPubMsgReadStatus.hpp"
//struct AIMPubMsgReadStatus final {
//
//  std::string appCid;
//  std::string mid;
//  std::vector<std::string> read_uids;
//  std::vector<std::string> unread_uids;
//
//  AIMPubMsgReadStatus(std::string appCid_, std::string mid_,
//                      std::vector<std::string> read_uids_,
//                      std::vector<std::string> unread_uids_)
//      : appCid(std::move(appCid_)), mid(std::move(mid_)),
//        read_uids(std::move(read_uids_)), unread_uids(std::move(unread_uids_)) {
//  }
//
//  AIMPubMsgReadStatus() {}
//};
alibaba::dps::AIMPubMsgReadStatus AIMPubMsgReadStatusFromJson(const nlohmann::json& j) {
    alibaba::dps::AIMPubMsgReadStatus info;
    if (j.contains("appCid") && !j["appCid"].is_null()) {
        info.appCid = j["appCid"].get<std::string>();
    }
    if (j.contains("mid") && !j["mid"].is_null()) {
        info.mid = j["mid"].get<std::string>();
    }
    if (j.contains("read_uids") && !j["read_uids"].is_null()) {
        info.read_uids = j["read_uids"].get<std::vector<std::string>>();
    }
    if (j.contains("unread_uids") && !j["unread_uids"].is_null()) {
        info.unread_uids = j["unread_uids"].get<std::vector<std::string>>();
    }
    return info;
}

nlohmann::json JsonFromAIMPubMsgReadStatus(const alibaba::dps::AIMPubMsgReadStatus& readStatus) {
    nlohmann::json j;
    if (!readStatus.appCid.empty()) {
        j["appCid"] = readStatus.appCid;
    }
    if (!readStatus.mid.empty()) {
        j["mid"] = readStatus.mid;
    }
    if (!readStatus.read_uids.empty()) {
        j["read_uids"] = readStatus.read_uids;
    }
    if (!readStatus.unread_uids.empty()) {
        j["unread_uids"] = readStatus.unread_uids;
    }
    return j;
}

