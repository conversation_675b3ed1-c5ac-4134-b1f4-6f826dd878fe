//
// Created by 照松 on 2024/4/3.
//

#ifndef NEED4SPEED_FIM_REPLY_2_JSON_H
#define NEED4SPEED_FIM_REPLY_2_JSON_H

#include "fim_content_2_json.h"
#include "fliggy_im_sdk.h"

nlohmann::json FIMSDK_API  reference2Json(const alibaba::fim::FIMMessageReference &structContent);

nlohmann::json FIMSDK_API  referenceContent2Json(const alibaba::fim::FIMMessageReferenceContent &reference);

nlohmann::json FIMSDK_API  innerReplyContent2Json(const alibaba::fim::FIMMessageInnerReplyContent &innerReplyContent);

nlohmann::json FIMSDK_API  simpleContent2Json(const alibaba::fim::FIMMessageSimpleContent &reference);

#endif //NEED4SPEED_FIM_REPLY_2_JSON_H
