//
// Created by 照松 on 2024/4/4.
//


#include "fim_error_2_json.h"

nlohmann::json fimError2Json(const alibaba::fim::FIMError &error) {
    nlohmann::json json;

    json["domain"] = error.domain;
    json["code"] = error.code;
    json["developer_message"] = error.developer_message;
    json["reason"] = error.reason;
    json["extra_info"] = error.extra_info;
    json["scope"] = error.scope;

    return json;
}