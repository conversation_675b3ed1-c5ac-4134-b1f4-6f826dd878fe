//
// Created by 照松 on 2024/4/3.
//

#include "fim_struct_2_json.h"

nlohmann::json structContent2Json(const alibaba::fim::FIMMessageStructContent &structContent) {
    nlohmann::json json;
    json["elements"] = structElements2Json(structContent.elements);
    return json;
}

nlohmann::json structElements2Json(std::vector<alibaba::fim::FIMMessageStructElement> elements) {
    nlohmann::json json = nlohmann::json::array();

    for (const auto &element: elements) {
        json.push_back(structElement2Json(element));
    }

    return json;
}

nlohmann::json structElement2Json(const alibaba::fim::FIMMessageStructElement &structElement) {
    nlohmann::json json;

    json["element_type"] = static_cast<int>(structElement.element_type);
    json["text_content"] = textContent2Json(structElement.text_content);
    json["uid_element"] = structElementUid2Json(structElement.uid_element);
    json["at_element"] = structElementAt2Json(structElement.at_element);

    return json;
}

alibaba::fim::FIMMessageStructElement json2StructElement(nlohmann::json jsonObj) {
    alibaba::fim::FIMMessageStructElement structElement;

    if (jsonHasKey(jsonObj, "element_type")) {
        structElement.element_type = jsonObj["element_type"];
    }

    if (jsonHasKey(jsonObj, "text_content")) {
        structElement.text_content = json2TextContent(jsonObj["text_content"]);
    }
    if (jsonHasKey(jsonObj, "at_element")) {
        structElement.at_element = json2StructElementAt(jsonObj["at_element"]);
    }
    if (jsonHasKey(jsonObj, "uid_element")) {
        structElement.uid_element = json2StructElementUid(jsonObj["uid_element"]);
    }

    return structElement;
}

nlohmann::json
structElementUid2Json(const alibaba::fim::FIMMessageStructElementUid &structElement) {
    nlohmann::json json;

    json["uid"] = structElement.uid;
    json["default_nick"] = structElement.default_nick;
    json["prefix"] = structElement.prefix;

    return json;
}

alibaba::fim::FIMMessageStructElementUid json2StructElementUid(nlohmann::json jsonObj) {
    alibaba::fim::FIMMessageStructElementUid structElement;

    if (jsonHasKey(jsonObj, "uid")) {
        structElement.uid = jsonObj["uid"];
    }
    if (jsonHasKey(jsonObj, "default_nick")) {
        structElement.default_nick = jsonObj["default_nick"];
    }
    if (jsonHasKey(jsonObj, "prefix")) {
        structElement.prefix = jsonObj["prefix"];
    }

    return structElement;
}

nlohmann::json structElementAt2Json(const alibaba::fim::FIMMessageStructElementAt &structElement) {
    nlohmann::json json;

    json["is_at_all"] = structElement.is_at_all;
    json["uid"] = structElement.uid;
    json["default_nick"] = structElement.default_nick;

    return json;
}

alibaba::fim::FIMMessageStructElementAt json2StructElementAt(nlohmann::json jsonObj) {
    alibaba::fim::FIMMessageStructElementAt structElement;

    if (jsonHasKey(jsonObj, "is_at_all")) {
        structElement.is_at_all = jsonObj["is_at_all"];
    }
    if (jsonHasKey(jsonObj, "uid")) {
        structElement.uid = jsonObj["uid"];
    }
    if (jsonHasKey(jsonObj, "default_nick")) {
        structElement.default_nick = jsonObj["default_nick"];
    }

    return structElement;
}