//
// Created by 照松 on 2024/4/3.
//

#ifndef NEED4SPEED_FIM_MESSAGE_2_JSON_H
#define NEED4SPEED_FIM_MESSAGE_2_JSON_H

#include <cstdio>
#include <iostream>
#include <map>
#include "fim_message.h"
#include "fim_new_message.h"
#include "aim_msg_send_media_progress.h"
#include "fim_message_content_type.h"
#include "fim_message_struct_content.h"
#include "fim_content_2_json.h"

nlohmann::json FIMSDK_API  fimMessage2Json(const alibaba::fim::FIMMessage &message);

nlohmann::json FIMSDK_API  messageContent2Json(const alibaba::fim::FIMMessageContent &content);

nlohmann::json FIMSDK_API  msgBizInfo2Json(const alibaba::dps::AIMMsgBizInfo &bizInfo);

nlohmann::json FIMSDK_API  msgRecallFeature2Json(const alibaba::fim::AIMPubMsgRecallFeature &recallFeature);

nlohmann::json FIMSDK_API  stringVector2JsonArray(std::vector<std::string> vector);

nlohmann::json FIMSDK_API  fimNewMessage2Json(const alibaba::fim::FIMNewMessage &newMessage);

nlohmann::json FIMSDK_API  sendMediaProgress2Json(const alibaba::dps::AIMMsgSendMediaProgress &progress);

#endif //NEED4SPEED_FIM_MESSAGE_2_JSON_H
