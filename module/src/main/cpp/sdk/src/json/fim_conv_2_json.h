//
// Created by 照松 on 2024/4/4.
//

#ifndef NEED4SPEED_FIM_CONV_2_JSON_H
#define NEED4SPEED_FIM_CONV_2_JSON_H

#include "fim_message_2_json.h"
#include "fim_conversation.h"

nlohmann::json FIMSDK_API 
fimConv2Json(const alibaba::fim::FIMConversation &conversation);

nlohmann::json FIMSDK_API 
fimGroupPermission2Json(const alibaba::fim::FIMGroupPermission &permission);

nlohmann::json FIMSDK_API 
fimGroupPermission2Json(const std::vector<alibaba::fim::FIMGroupPermission> &permissions);

#endif //NEED4SPEED_FIM_CONV_2_JSON_H
