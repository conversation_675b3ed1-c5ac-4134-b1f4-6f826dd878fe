//
//  json_to_message_re_send_message.cpp
//  FliggyIM
//
//  Created by 蒙晨 on 2024/8/6.
//

#include "json_to_message_re_send_message.hpp"
//struct FIMMessageReSendMessage {
//    std::string appCid;
//    std::string localid;
//    std::map<std::string, std::string> callback_ctx;
//}
alibaba::fim::FIMMessageReSendMessage FIMMessageReSendMessageFromJson(const nlohmann::json& j) {
    alibaba::fim::FIMMessageReSendMessage msg;
    if (j.contains("appCid") && !j["appCid"].is_null()) {
        msg.appCid = j["appCid"].get<std::string>();
    }
    if (j.contains("localid") && !j["localid"].is_null()) {
        msg.localid = j["localid"].get<std::string>();
    }
    if (j.contains("callback_ctx") && !j["callback_ctx"].is_null()) {
        msg.callback_ctx = j["callback_ctx"].get<std::map<std::string, std::string>>();
    }
    return msg;
}
