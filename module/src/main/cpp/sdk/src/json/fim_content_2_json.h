//
// Created by 照松 on 2024/4/3.
//

#ifndef NEED4SPEED_FIM_CONTENT_2_JSON_H
#define NEED4SPEED_FIM_CONTENT_2_JSON_H

#include <cstdio>
#include <iostream>
#include <map>
#include "fim_message.h"
#include "fim_message_content_type.h"
#include "fim_message_struct_content.h"
#include "fim_struct_2_json.h"
#include "fim_reply_2_json.h"
#include "json_2_obj.h"
#include "fliggy_im_sdk.h"

nlohmann::json FIMSDK_API 
messageContent2Json(const alibaba::fim::FIMMessageContent &content);

nlohmann::json FIMSDK_API 
textContent2Json(const alibaba::fim::FIMMessageTextContent &textContent);

alibaba::fim::FIMMessageTextContent FIMSDK_API 
json2TextContent(const nlohmann::json FIMSDK_API & jsonObj);

nlohmann::json FIMSDK_API 
imageContent2Json(const alibaba::fim::FIMMessageImageContent &imageContent);

nlohmann::json FIMSDK_API 
audioContent2Json(const alibaba::fim::FIMMessageAudioContent &audioContent);

nlohmann::json FIMSDK_API 
videoContent2Json(const alibaba::fim::FIMMessageVideoContent &videoContent);

nlohmann::json FIMSDK_API 
geoContent2Json(const alibaba::fim::FIMMessageGeoContent &geoContent);

nlohmann::json FIMSDK_API 
fileContent2Json(const alibaba::fim::FIMMessageFileContent &fileContent);

nlohmann::json FIMSDK_API 
replyContent2Json(const alibaba::fim::FIMMessageReplyContent &replyContent);

nlohmann::json FIMSDK_API 
innerCombineContent2Json(const alibaba::fim::FIMMessageInnerCombineContent &innerCombineContent);

nlohmann::json FIMSDK_API 
combineForward2Json(const alibaba::fim::FIMMessageCombineForward &combineForward);

nlohmann::json FIMSDK_API 
combineForwardContent2Json(
        const alibaba::fim::FIMMessageCombineForwardContent &combineForwardContent);

nlohmann::json FIMSDK_API 
customContent2Json(const alibaba::fim::FIMMessageCustomContent &customContent);


#endif //NEED4SPEED_FIM_CONTENT_2_JSON_H
