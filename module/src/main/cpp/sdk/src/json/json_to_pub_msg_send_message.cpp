//
//  json_to_pub_msg_send_message.cpp
//  FliggyIM
//
//  Created by 蒙晨 on 2024/8/5.
//

#include "json_to_pub_msg_send_message.h"
#include "json.hpp"
#include "aim_pub_msg_send_message.h"
//AIMMsgImageCompressType
#include "aim_msg_image_compress_type.h"
//AIMMsgContentType
#include "aim_msg_content_type.h"
//AIMPubMsgReplyContent
#include "aim_pub_msg_reply_content.h"
//AIMPubMsgReference
#include "aim_pub_msg_reference.h"
//AIMPubMsgCombineForwardContent
#include "aim_pub_msg_combine_forward_content.h"
//AIMPubMsgCombineForward
#include "aim_pub_msg_combine_forward.h"
//AIMPubMsgStructElement
#include "aim_pub_msg_struct_element.h"
//AIMPubMsgReplyContent
#include "aim_pub_msg_reply_content.h"
//AIMPubMsgCombineForwardContent
#include "aim_pub_msg_combine_forward_content.h"
//AIMPubMsgInnerReplyContent
#include "aim_pub_msg_inner_reply_content.h"
//AIMPubMsgReference
#include "aim_pub_msg_reference.h"
//AIMPubMsgStructContent
#include "aim_pub_msg_struct_content.h"

using namespace alibaba::dps;

AIMPubMsgTextContent convertToTextContent(const nlohmann::json& j) {
    AIMPubMsgTextContent content;
    if (j.contains("text") && !j["text"].is_null()) {
        content.text = j["text"].get<std::string>();
    }
    if (j.contains("encrypted_text") && !j["encrypted_text"].is_null()) {
        content.encrypted_text = j["encrypted_text"].get<std::string>();
    }
    if (j.contains("extension") && j["extension"].is_object()) {
        content.extension = j["extension"].get<std::map<std::string, std::string>>();
    }
    return content;
}

AIMMsgImageContent convertToImageContent(const nlohmann::json& j) {
    AIMMsgImageContent content;
    if (j.contains("local_path") && !j["local_path"].is_null()) {
        content.local_path = j["local_path"].get<std::string>();
    }
    if (j.contains("upload_path") && !j["upload_path"].is_null()) {
        content.upload_path = j["upload_path"].get<std::string>();
    }
    if (j.contains("mime_type") && !j["mime_type"].is_null()) {
        content.mime_type = j["mime_type"].get<std::string>();
    }
    if (j.contains("original_url") && !j["original_url"].is_null()) {
        content.original_url = j["original_url"].get<std::string>();
    }
    if (j.contains("thumbnail_url") && !j["thumbnail_url"].is_null()) {
        content.thumbnail_url = j["thumbnail_url"].get<std::string>();
    }
    if (j.contains("blurred_data") && j["blurred_data"].is_array()) {
        content.blurred_data = j["blurred_data"].get<std::vector<uint8_t>>();
    }
    if (j.contains("media_id") && !j["media_id"].is_null()) {
        content.media_id = j["media_id"].get<std::string>();
    }
    if (j.contains("file_name") && !j["file_name"].is_null()) {
        content.file_name = j["file_name"].get<std::string>();
    }
    if (j.contains("height") && !j["height"].is_null()) {
        content.height = j["height"].get<int32_t>();
    }
    if (j.contains("width") && !j["width"].is_null()) {
        content.width = j["width"].get<int32_t>();
    }
    if (j.contains("size") && !j["size"].is_null()) {
        content.size = j["size"].get<int32_t>();
    }
    if (j.contains("type") && !j["type"].is_null()) {
        content.type = static_cast<AIMMsgImageCompressType>(j["type"].get<int>());
    }
    if (j.contains("file_type") && !j["file_type"].is_null()) {
        content.file_type = static_cast<AIMMsgImageFileType>(j["file_type"].get<int>());
    }
    if (j.contains("orientation") && !j["orientation"].is_null()) {
        content.orientation = static_cast<AIMMsgOrientation>(j["orientation"].get<int>());
    }
    if (j.contains("extension") && j["extension"].is_object()) {
        content.extension = j["extension"].get<std::map<std::string, std::string>>();
    }
    return content;
}

AIMMsgAudioContent convertToAudioContent(const nlohmann::json& j) {
    AIMMsgAudioContent content;
    if (j.contains("local_path") && !j["local_path"].is_null()) {
        content.local_path = j["local_path"].get<std::string>();
    }
    if (j.contains("upload_path") && !j["upload_path"].is_null()) {
        content.upload_path = j["upload_path"].get<std::string>();
    }
    if (j.contains("mime_type") && !j["mime_type"].is_null()) {
        content.mime_type = j["mime_type"].get<std::string>();
    }
    if (j.contains("url") && !j["url"].is_null()) {
        content.url = j["url"].get<std::string>();
    }
    if (j.contains("media_id") && !j["media_id"].is_null()) {
        content.media_id = j["media_id"].get<std::string>();
    }
    if (j.contains("binary_data") && j["binary_data"].is_array()) {
        content.binary_data = j["binary_data"].get<std::vector<uint8_t>>();
    }
    if (j.contains("audio_type") && !j["audio_type"].is_null()) {
        content.audio_type = static_cast<AIMMsgAudioType>(j["audio_type"].get<int>());
    }
    if (j.contains("duration") && !j["duration"].is_null()) {
        content.duration = j["duration"].get<int64_t>();
    }
    return content;
}

AIMMsgVideoContent convertToVideoContent(const nlohmann::json& j) {
    AIMMsgVideoContent content;
    if (j.contains("local_path") && !j["local_path"].is_null()) {
        content.local_path = j["local_path"].get<std::string>();
    }
    if (j.contains("upload_path") && !j["upload_path"].is_null()) {
        content.upload_path = j["upload_path"].get<std::string>();
    }
    if (j.contains("mime_type") && !j["mime_type"].is_null()) {
        content.mime_type = j["mime_type"].get<std::string>();
    }
    if (j.contains("url") && !j["url"].is_null()) {
        content.url = j["url"].get<std::string>();
    }
    if (j.contains("media_id") && !j["media_id"].is_null()) {
        content.media_id = j["media_id"].get<std::string>();
    }
    if (j.contains("cover_local_path") && !j["cover_local_path"].is_null()) {
        content.cover_local_path = j["cover_local_path"].get<std::string>();
    }
    if (j.contains("cover_upload_path") && !j["cover_upload_path"].is_null()) {
        content.cover_upload_path = j["cover_upload_path"].get<std::string>();
    }
    if (j.contains("cover_url") && !j["cover_url"].is_null()) {
        content.cover_url = j["cover_url"].get<std::string>();
    }
    if (j.contains("cover_media_id") && !j["cover_media_id"].is_null()) {
        content.cover_media_id = j["cover_media_id"].get<std::string>();
    }
    if (j.contains("cover_file_type") && !j["cover_file_type"].is_null()) {
        content.cover_file_type = static_cast<AIMMsgImageFileType>(j["cover_file_type"].get<int>());
    }
    if (j.contains("cover_mime_type") && !j["cover_mime_type"].is_null()) {
        content.cover_mime_type = j["cover_mime_type"].get<std::string>();
    }
    if (j.contains("file_name") && !j["file_name"].is_null()) {
        content.file_name = j["file_name"].get<std::string>();
    }
    if (j.contains("file_type") && !j["file_type"].is_null()) {
        content.file_type = j["file_type"].get<std::string>();
    }
    if (j.contains("file_size") && !j["file_size"].is_null()) {
        content.file_size = j["file_size"].get<int64_t>();
    }
    if (j.contains("duration") && !j["duration"].is_null()) {
        content.duration = j["duration"].get<int64_t>();
    }
    if (j.contains("height") && !j["height"].is_null()) {
        content.height = j["height"].get<int32_t>();
    }
    if (j.contains("width") && !j["width"].is_null()) {
        content.width = j["width"].get<int32_t>();
    }
    if (j.contains("cover_width") && !j["cover_width"].is_null()) {
        content.cover_width = j["cover_width"].get<int32_t>();
    }
    if (j.contains("cover_height") && !j["cover_height"].is_null()) {
        content.cover_height = j["cover_height"].get<int32_t>();
    }
    return content;
}

AIMMsgGeoContent convertToGeoContent(const nlohmann::json& j) {
    AIMMsgGeoContent content;
    if (j.contains("pic_local_path") && !j["pic_local_path"].is_null()) {
        content.pic_local_path = j["pic_local_path"].get<std::string>();
    }
    if (j.contains("pic_upload_path") && !j["pic_upload_path"].is_null()) {
        content.pic_upload_path = j["pic_upload_path"].get<std::string>();
    }
    if (j.contains("mime_type") && !j["mime_type"].is_null()) {
        content.mime_type = j["mime_type"].get<std::string>();
    }
    if (j.contains("pic_url") && !j["pic_url"].is_null()) {
        content.pic_url = j["pic_url"].get<std::string>();
    }
    if (j.contains("pic_media_id") && !j["pic_media_id"].is_null()) {
        content.pic_media_id = j["pic_media_id"].get<std::string>();
    }
    if (j.contains("pic_file_type") && !j["pic_file_type"].is_null()) {
        content.pic_file_type = static_cast<AIMMsgImageFileType>(j["pic_file_type"].get<int>());
    }
    if (j.contains("pic_width") && !j["pic_width"].is_null()) {
        content.pic_width = j["pic_width"].get<int32_t>();
    }
    if (j.contains("pic_height") && !j["pic_height"].is_null()) {
        content.pic_height = j["pic_height"].get<int32_t>();
    }
    if (j.contains("latitude") && !j["latitude"].is_null()) {
        content.latitude = j["latitude"].get<double>();
    }
    if (j.contains("longitude") && !j["longitude"].is_null()) {
        content.longitude = j["longitude"].get<double>();
    }
    if (j.contains("location_name") && !j["location_name"].is_null()) {
        content.location_name = j["location_name"].get<std::string>();
    }
    return content;
}

AIMMsgCustomContent convertToCustomContent(const nlohmann::json& j) {
    AIMMsgCustomContent content;
    if (j.contains("type") && !j["type"].is_null()) {
        content.type = j["type"].get<int32_t>();
    }
    if (j.contains("binary_data") && j["binary_data"].is_array()) {
        content.binary_data = j["binary_data"].get<std::vector<uint8_t>>();
    }
    if (j.contains("title") && !j["title"].is_null()) {
        content.title = j["title"].get<std::string>();
    }
    if (j.contains("summary") && !j["summary"].is_null()) {
        content.summary = j["summary"].get<std::string>();
    }
    if (j.contains("degrade") && !j["degrade"].is_null()) {
        content.degrade = j["degrade"].get<std::string>();
    }
    return content;
}

AIMMsgFileContent convertToFileContent(const nlohmann::json& j) {
    AIMMsgFileContent content;
    if (j.contains("local_path") && !j["local_path"].is_null()) {
        content.local_path = j["local_path"].get<std::string>();
    }
    if (j.contains("file_name") && !j["file_name"].is_null()) {
        content.file_name = j["file_name"].get<std::string>();
    }
    if (j.contains("file_type") && !j["file_type"].is_null()) {
        content.file_type = j["file_type"].get<std::string>();
    }
    if (j.contains("media_id") && !j["media_id"].is_null()) {
        content.media_id = j["media_id"].get<std::string>();
    }
    if (j.contains("mime_type") && !j["mime_type"].is_null()) {
        content.mime_type = j["mime_type"].get<std::string>();
    }
    if (j.contains("url") && !j["url"].is_null()) {
        content.url = j["url"].get<std::string>();
    }
    if (j.contains("file_size") && !j["file_size"].is_null()) {
        content.file_size = j["file_size"].get<int64_t>();
    }
    return content;
}

AIMPubMsgStructElementUid convertToStructElementUid(const nlohmann::json& j) {
    AIMPubMsgStructElementUid uid_element;
    if (j.contains("uid") && !j["uid"].is_null()) {
        uid_element.uid = j["uid"].get<std::string>();
    }
    if (j.contains("default_nick") && !j["default_nick"].is_null()) {
        uid_element.default_nick = j["default_nick"].get<std::string>();
    }
    if (j.contains("prefix") && !j["prefix"].is_null()) {
        uid_element.prefix = j["prefix"].get<std::string>();
    }
    return uid_element;
}

AIMPubMsgStructElementAt convertToStructElementAt(const nlohmann::json& j) {
    AIMPubMsgStructElementAt at_element;
    if (j.contains("is_at_all") && !j["is_at_all"].is_null()) {
        at_element.is_at_all = j["is_at_all"].get<bool>();
    }
    if (j.contains("uid") && !j["uid"].is_null()) {
        at_element.uid = j["uid"].get<std::string>();
    }
    if (j.contains("default_nick") && !j["default_nick"].is_null()) {
        at_element.default_nick = j["default_nick"].get<std::string>();
    }
    return at_element;
}

// 更新 convertToStructElement 函数
AIMPubMsgStructElement convertToStructElement(const nlohmann::json& j) {
    AIMPubMsgStructElement element;
    if (j.contains("element_type") && !j["element_type"].is_null()) {
        element.element_type = static_cast<AIMMsgStructElementType>(j["element_type"].get<int>());
    }
    
    switch (element.element_type) {
        case AIMMsgStructElementType::ELEMENT_TYPE_TEXT:
            if (j.contains("text_content") && j["text_content"].is_object()) {
                element.text_content = convertToTextContent(j["text_content"]);
            }
            break;
        case AIMMsgStructElementType::ELEMENT_TYPE_UID:
            if (j.contains("uid_element") && j["uid_element"].is_object()) {
                element.uid_element = convertToStructElementUid(j["uid_element"]);
            }
            break;
        case AIMMsgStructElementType::ELEMENT_TYPE_AT:
            if (j.contains("at_element") && j["at_element"].is_object()) {
                element.at_element = convertToStructElementAt(j["at_element"]);
            }
            break;
        default:
            // 处理未知类型
            break;
    }
    
    return element;
}


AIMPubMsgStructContent convertToStructContent(const nlohmann::json& j) {
    AIMPubMsgStructContent content;
    if (j.contains("elements") && j["elements"].is_array()) {
        for (const auto& element : j["elements"]) {
            content.elements.push_back(convertToStructElement(element));
        }
    }
    return content;
}

// 更新 convertToSimpleContent 函数
AIMPubMsgSimpleContent convertToSimpleContent(const nlohmann::json& j, AIMMsgContentType content_type) {
    AIMPubMsgSimpleContent content;
    switch (content_type) {
        case AIMMsgContentType::CONTENT_TYPE_TEXT:
            if (j.contains("text_content") && j["text_content"].is_object()) {
                content.text_content = convertToTextContent(j["text_content"]);
            }
            break;
        case AIMMsgContentType::CONTENT_TYPE_IMAGE:
            if (j.contains("image_content") && j["image_content"].is_object()) {
                content.image_content = convertToImageContent(j["image_content"]);
            }
            break;
        case AIMMsgContentType::CONTENT_TYPE_AUDIO:
            if (j.contains("audio_content") && j["audio_content"].is_object()) {
                content.audio_content = convertToAudioContent(j["audio_content"]);
            }
            break;
        case AIMMsgContentType::CONTENT_TYPE_VIDEO:
            if (j.contains("video_content") && j["video_content"].is_object()) {
                content.video_content = convertToVideoContent(j["video_content"]);
            }
            break;
        case AIMMsgContentType::CONTENT_TYPE_GEO:
            if (j.contains("geo_content") && j["geo_content"].is_object()) {
                content.geo_content = convertToGeoContent(j["geo_content"]);
            }
            break;
        case AIMMsgContentType::CONTENT_TYPE_CUSTOM:
            if (j.contains("custom_content") && j["custom_content"].is_object()) {
                content.custom_content = convertToCustomContent(j["custom_content"]);
            }
            break;
        case AIMMsgContentType::CONTENT_TYPE_STRUCT:
            if (j.contains("struct_content") && j["struct_content"].is_object()) {
                content.struct_content = convertToStructContent(j["struct_content"]);
            }
            break;
        case AIMMsgContentType::CONTENT_TYPE_FILE:
            if (j.contains("file_content") && j["file_content"].is_object()) {
                content.file_content = convertToFileContent(j["file_content"]);
            }
            break;
        default:
            // 处理未知类型
            break;
    }
    return content;
}

AIMPubMsgReferenceContent convertToReferenceContent(const nlohmann::json& j) {
    AIMPubMsgReferenceContent referenceContent;

    if (j.contains("content_type") && !j["content_type"].is_null()) {
        referenceContent.content_type = static_cast<AIMMsgContentType>(j["content_type"].get<int>());
    }

    if (j.contains("content") && j["content"].is_object()) {
        referenceContent.content = convertToSimpleContent(j["content"], referenceContent.content_type);
    }

    return referenceContent;
}

AIMPubMsgReference convertToReference(const nlohmann::json& j) {
    AIMPubMsgReference reference;

    if (j.contains("sender") && !j["sender"].is_null()) {
        reference.sender = j["sender"].get<std::string>();
    }

    if (j.contains("appCid") && !j["appCid"].is_null()) {
        reference.appCid = j["appCid"].get<std::string>();
    }

    if (j.contains("mid") && !j["mid"].is_null()) {
        reference.mid = j["mid"].get<std::string>();
    }

    if (j.contains("created_at") && !j["created_at"].is_null()) {
        reference.created_at = j["created_at"].get<int64_t>();
    }

    if (j.contains("reference_content") && j["reference_content"].is_object()) {
        reference.reference_content = convertToReferenceContent(j["reference_content"]);
    }

    if (j.contains("extension") && j["extension"].is_object()) {
        for (auto it = j["extension"].begin(); it != j["extension"].end(); ++it) {
            reference.extension[it.key()] = it.value().get<std::string>();
        }
    }

    return reference;
}

AIMPubMsgInnerReplyContent convertToInnerReplyContent(const nlohmann::json& j) {
    AIMPubMsgInnerReplyContent innerReplyContent;

    if (j.contains("content_type") && !j["content_type"].is_null()) {
        innerReplyContent.content_type = static_cast<AIMMsgContentType>(j["content_type"].get<int>());
    }

    if (j.contains("content") && j["content"].is_object()) {
        innerReplyContent.content = convertToSimpleContent(j["content"], innerReplyContent.content_type);
    }

    return innerReplyContent;
}

AIMPubMsgReplyContent convertToReplyContent(const nlohmann::json& j) {
    AIMPubMsgReplyContent replyContent;

    if (j.contains("reference_msg") && j["reference_msg"].is_object()) {
        replyContent.reference_msg = convertToReference(j["reference_msg"]);
    }

    if (j.contains("reply_content") && j["reply_content"].is_object()) {
        replyContent.reply_content = convertToInnerReplyContent(j["reply_content"]);
    }

    return replyContent;
}

AIMPubMsgInnerCombineContent convertToInnerCombineContent(const nlohmann::json& j) {
    AIMPubMsgInnerCombineContent innerCombineContent;

    if (j.contains("content_type") && !j["content_type"].is_null()) {
        innerCombineContent.content_type = static_cast<AIMMsgContentType>(j["content_type"].get<int>());
    }

    if (j.contains("simple_content") && j["simple_content"].is_object()) {
        innerCombineContent.simple_content = convertToSimpleContent(j["simple_content"], innerCombineContent.content_type);
    }

    if (j.contains("reply_content") && j["reply_content"].is_object()) {
        innerCombineContent.reply_content = convertToReplyContent(j["reply_content"]);
    }

    return innerCombineContent;
}

AIMPubMsgCombineForward convertToCombineForward(const nlohmann::json& j) {
    AIMPubMsgCombineForward combineForward;

    if (j.contains("sender") && !j["sender"].is_null()) {
        combineForward.sender = j["sender"].get<std::string>();
    }

    if (j.contains("appCid") && !j["appCid"].is_null()) {
        combineForward.appCid = j["appCid"].get<std::string>();
    }

    if (j.contains("mid") && !j["mid"].is_null()) {
        combineForward.mid = j["mid"].get<std::string>();
    }

    if (j.contains("created_at") && !j["created_at"].is_null()) {
        combineForward.created_at = j["created_at"].get<int64_t>();
    }

    if (j.contains("combine_content") && j["combine_content"].is_object()) {
        combineForward.combine_content = convertToInnerCombineContent(j["combine_content"]);
    }

    if (j.contains("extension") && j["extension"].is_object()) {
        for (auto it = j["extension"].begin(); it != j["extension"].end(); ++it) {
            combineForward.extension[it.key()] = it.value().get<std::string>();
        }
    }

    return combineForward;
}

AIMPubMsgContent convertToPubMsgContent(const nlohmann::json& content_json) {
    AIMPubMsgContent content;

    if (content_json.contains("content_type") && !content_json["content_type"].is_null()) {
        content.content_type = static_cast<AIMMsgContentType>(content_json["content_type"].get<int>());
    }

    // 根据content_type初始化相应的内容
    switch (content.content_type) {
        case AIMMsgContentType::CONTENT_TYPE_TEXT:
            if (content_json.contains("text_content") && content_json["text_content"].is_object()) {
                const auto& text_json = content_json["text_content"];
                AIMPubMsgTextContent text_content;
                if (text_json.contains("text") && !text_json["text"].is_null()) {
                    text_content.text = text_json["text"].get<std::string>();
                }
                if (text_json.contains("encrypted_text") && !text_json["encrypted_text"].is_null()) {
                    text_content.encrypted_text = text_json["encrypted_text"].get<std::string>();
                }
                if (text_json.contains("extension") && text_json["extension"].is_object()) {
                    text_content.extension = text_json["extension"].get<std::map<std::string, std::string>>();
                }
                content.text_content = std::move(text_content);
            }
            break;

        case AIMMsgContentType::CONTENT_TYPE_IMAGE:
            if (content_json.contains("image_content") && content_json["image_content"].is_object()) {
                const auto& image_json = content_json["image_content"];
                AIMMsgImageContent image_content;
                if (image_json.contains("local_path") && !image_json["local_path"].is_null()) {
                    image_content.local_path = image_json["local_path"].get<std::string>();
                }
                if (image_json.contains("upload_path") && !image_json["upload_path"].is_null()) {
                    image_content.upload_path = image_json["upload_path"].get<std::string>();
                }
                if (image_json.contains("mime_type") && !image_json["mime_type"].is_null()) {
                    image_content.mime_type = image_json["mime_type"].get<std::string>();
                }
                if (image_json.contains("original_url") && !image_json["original_url"].is_null()) {
                    image_content.original_url = image_json["original_url"].get<std::string>();
                }
                if (image_json.contains("thumbnail_url") && !image_json["thumbnail_url"].is_null()) {
                    image_content.thumbnail_url = image_json["thumbnail_url"].get<std::string>();
                }
                if (image_json.contains("blurred_data") && image_json["blurred_data"].is_array()) {
                    image_content.blurred_data = image_json["blurred_data"].get<std::vector<uint8_t>>();
                }
                if (image_json.contains("media_id") && !image_json["media_id"].is_null()) {
                    image_content.media_id = image_json["media_id"].get<std::string>();
                }
                if (image_json.contains("file_name") && !image_json["file_name"].is_null()) {
                    image_content.file_name = image_json["file_name"].get<std::string>();
                }
                if (image_json.contains("height") && !image_json["height"].is_null()) {
                    image_content.height = image_json["height"].get<int32_t>();
                }
                if (image_json.contains("width") && !image_json["width"].is_null()) {
                    image_content.width = image_json["width"].get<int32_t>();
                }
                if (image_json.contains("size") && !image_json["size"].is_null()) {
                    image_content.size = image_json["size"].get<int32_t>();
                }
                if (image_json.contains("type") && !image_json["type"].is_null()) {
                    image_content.type = static_cast<AIMMsgImageCompressType>(image_json["type"].get<int>());
                }
                if (image_json.contains("file_type") && !image_json["file_type"].is_null()) {
                    image_content.file_type = static_cast<AIMMsgImageFileType>(image_json["file_type"].get<int>());
                }
                if (image_json.contains("orientation") && !image_json["orientation"].is_null()) {
                    image_content.orientation = static_cast<AIMMsgOrientation>(image_json["orientation"].get<int>());
                }
                if (image_json.contains("extension") && image_json["extension"].is_object()) {
                    image_content.extension = image_json["extension"].get<std::map<std::string, std::string>>();
                }
                content.image_content = std::move(image_content);
            }
            break;

        case AIMMsgContentType::CONTENT_TYPE_AUDIO:
            if (content_json.contains("audio_content") && content_json["audio_content"].is_object()) {
                const auto& audio_json = content_json["audio_content"];
                AIMMsgAudioContent audio_content;
                if (audio_json.contains("local_path") && !audio_json["local_path"].is_null()) {
                    audio_content.local_path = audio_json["local_path"].get<std::string>();
                }
                if (audio_json.contains("upload_path") && !audio_json["upload_path"].is_null()) {
                    audio_content.upload_path = audio_json["upload_path"].get<std::string>();
                }
                if (audio_json.contains("mime_type") && !audio_json["mime_type"].is_null()) {
                    audio_content.mime_type = audio_json["mime_type"].get<std::string>();
                }
                if (audio_json.contains("url") && !audio_json["url"].is_null()) {
                    audio_content.url = audio_json["url"].get<std::string>();
                }
                if (audio_json.contains("media_id") && !audio_json["media_id"].is_null()) {
                    audio_content.media_id = audio_json["media_id"].get<std::string>();
                }
                if (audio_json.contains("binary_data") && audio_json["binary_data"].is_array()) {
                    audio_content.binary_data = audio_json["binary_data"].get<std::vector<uint8_t>>();
                }
                if (audio_json.contains("audio_type") && !audio_json["audio_type"].is_null()) {
                    audio_content.audio_type = static_cast<AIMMsgAudioType>(audio_json["audio_type"].get<int>());
                }
                if (audio_json.contains("duration") && !audio_json["duration"].is_null()) {
                    audio_content.duration = audio_json["duration"].get<int64_t>();
                }
                content.audio_content = std::move(audio_content);
            }
            break;

        case AIMMsgContentType::CONTENT_TYPE_VIDEO:
            if (content_json.contains("video_content") && content_json["video_content"].is_object()) {
                const auto& video_json = content_json["video_content"];
                AIMMsgVideoContent video_content;
                if (video_json.contains("local_path") && !video_json["local_path"].is_null()) {
                    video_content.local_path = video_json["local_path"].get<std::string>();
                }
                if (video_json.contains("upload_path") && !video_json["upload_path"].is_null()) {
                    video_content.upload_path = video_json["upload_path"].get<std::string>();
                }
                if (video_json.contains("mime_type") && !video_json["mime_type"].is_null()) {
                    video_content.mime_type = video_json["mime_type"].get<std::string>();
                }
                if (video_json.contains("url") && !video_json["url"].is_null()) {
                    video_content.url = video_json["url"].get<std::string>();
                }
                if (video_json.contains("media_id") && !video_json["media_id"].is_null()) {
                    video_content.media_id = video_json["media_id"].get<std::string>();
                }
                if (video_json.contains("cover_local_path") && !video_json["cover_local_path"].is_null()) {
                    video_content.cover_local_path = video_json["cover_local_path"].get<std::string>();
                }
                if (video_json.contains("cover_upload_path") && !video_json["cover_upload_path"].is_null()) {
                    video_content.cover_upload_path = video_json["cover_upload_path"].get<std::string>();
                }
                if (video_json.contains("cover_url") && !video_json["cover_url"].is_null()) {
                    video_content.cover_url = video_json["cover_url"].get<std::string>();
                }
                if (video_json.contains("cover_media_id") && !video_json["cover_media_id"].is_null()) {
                    video_content.cover_media_id = video_json["cover_media_id"].get<std::string>();
                }
                if (video_json.contains("cover_file_type") && !video_json["cover_file_type"].is_null()) {
                    video_content.cover_file_type = static_cast<AIMMsgImageFileType>(video_json["cover_file_type"].get<int>());
                }
                if (video_json.contains("cover_mime_type") && !video_json["cover_mime_type"].is_null()) {
                    video_content.cover_mime_type = video_json["cover_mime_type"].get<std::string>();
                }
                if (video_json.contains("file_name") && !video_json["file_name"].is_null()) {
                    video_content.file_name = video_json["file_name"].get<std::string>();
                }
                if (video_json.contains("file_type") && !video_json["file_type"].is_null()) {
                    video_content.file_type = video_json["file_type"].get<std::string>();
                }
                if (video_json.contains("file_size") && !video_json["file_size"].is_null()) {
                    video_content.file_size = video_json["file_size"].get<int64_t>();
                }
                if (video_json.contains("duration") && !video_json["duration"].is_null()) {
                    video_content.duration = video_json["duration"].get<int64_t>();
                }
                if (video_json.contains("height") && !video_json["height"].is_null()) {
                    video_content.height = video_json["height"].get<int32_t>();
                }
                if (video_json.contains("width") && !video_json["width"].is_null()) {
                    video_content.width = video_json["width"].get<int32_t>();
                }
                if (video_json.contains("cover_width") && !video_json["cover_width"].is_null()) {
                    video_content.cover_width = video_json["cover_width"].get<int32_t>();
                }
                if (video_json.contains("cover_height") && !video_json["cover_height"].is_null()) {
                    video_content.cover_height = video_json["cover_height"].get<int32_t>();
                }
                content.video_content = std::move(video_content);
            }
            break;

        case AIMMsgContentType::CONTENT_TYPE_GEO:
            if (content_json.contains("geo_content") && content_json["geo_content"].is_object()) {
                const auto& geo_json = content_json["geo_content"];
                AIMMsgGeoContent geo_content;
                if (geo_json.contains("pic_local_path") && !geo_json["pic_local_path"].is_null()) {
                    geo_content.pic_local_path = geo_json["pic_local_path"].get<std::string>();
                }
                if (geo_json.contains("pic_upload_path") && !geo_json["pic_upload_path"].is_null()) {
                    geo_content.pic_upload_path = geo_json["pic_upload_path"].get<std::string>();
                }
                if (geo_json.contains("mime_type") && !geo_json["mime_type"].is_null()) {
                    geo_content.mime_type = geo_json["mime_type"].get<std::string>();
                }
                if (geo_json.contains("pic_url") && !geo_json["pic_url"].is_null()) {
                    geo_content.pic_url = geo_json["pic_url"].get<std::string>();
                }
                if (geo_json.contains("pic_media_id") && !geo_json["pic_media_id"].is_null()) {
                    geo_content.pic_media_id = geo_json["pic_media_id"].get<std::string>();
                }
                if (geo_json.contains("pic_file_type") && !geo_json["pic_file_type"].is_null()) {
                    geo_content.pic_file_type = static_cast<AIMMsgImageFileType>(geo_json["pic_file_type"].get<int>());
                }
                if (geo_json.contains("pic_width") && !geo_json["pic_width"].is_null()) {
                    geo_content.pic_width = geo_json["pic_width"].get<int32_t>();
                }
                if (geo_json.contains("pic_height") && !geo_json["pic_height"].is_null()) {
                    geo_content.pic_height = geo_json["pic_height"].get<int32_t>();
                }
                if (geo_json.contains("latitude") && !geo_json["latitude"].is_null()) {
                    geo_content.latitude = geo_json["latitude"].get<double>();
                }
                if (geo_json.contains("longitude") && !geo_json["longitude"].is_null()) {
                    geo_content.longitude = geo_json["longitude"].get<double>();
                }
                if (geo_json.contains("location_name") && !geo_json["location_name"].is_null()) {
                    geo_content.location_name = geo_json["location_name"].get<std::string>();
                }
                content.geo_content = std::move(geo_content);
            }
            break;

        case AIMMsgContentType::CONTENT_TYPE_CUSTOM:
            if (content_json.contains("custom_content") && content_json["custom_content"].is_object()) {
                const auto& custom_json = content_json["custom_content"];
                AIMMsgCustomContent custom_content;
                if (custom_json.contains("type") && !custom_json["type"].is_null()) {
                    custom_content.type = custom_json["type"].get<int32_t>();
                }
                if (custom_json.contains("binary_data") && custom_json["binary_data"].is_array()) {
                    custom_content.binary_data = custom_json["binary_data"].get<std::vector<uint8_t>>();
                }
                if (custom_json.contains("title") && !custom_json["title"].is_null()) {
                    custom_content.title = custom_json["title"].get<std::string>();
                }
                if (custom_json.contains("summary") && !custom_json["summary"].is_null()) {
                    custom_content.summary = custom_json["summary"].get<std::string>();
                }
                if (custom_json.contains("degrade") && !custom_json["degrade"].is_null()) {
                    custom_content.degrade = custom_json["degrade"].get<std::string>();
                }
                content.custom_content = std::move(custom_content);
            }
            break;

        case AIMMsgContentType::CONTENT_TYPE_STRUCT:
            if (content_json.contains("struct_content") && content_json["struct_content"].is_object()) {
                                const auto& struct_json = content_json["struct_content"];
                                AIMPubMsgStructContent struct_content;
                                if (struct_json.contains("elements") && struct_json["elements"].is_array()) {
                                    struct_content.elements.reserve(struct_json["elements"].size());
                                    for (const auto& element : struct_json["elements"]) {
                                        struct_content.elements.push_back(convertToStructElement(element));
                                    }
                                }
                                content.struct_content = std::move(struct_content);
                            }
            break;

        case AIMMsgContentType::CONTENT_TYPE_FILE:
            if (content_json.contains("file_content") && content_json["file_content"].is_object()) {
                const auto& file_json = content_json["file_content"];
                AIMMsgFileContent file_content;
                if (file_json.contains("local_path") && !file_json["local_path"].is_null()) {
                    file_content.local_path = file_json["local_path"].get<std::string>();
                }
                if (file_json.contains("file_name") && !file_json["file_name"].is_null()) {
                    file_content.file_name = file_json["file_name"].get<std::string>();
                }
                if (file_json.contains("file_type") && !file_json["file_type"].is_null()) {
                    file_content.file_type = file_json["file_type"].get<std::string>();
                }
                if (file_json.contains("media_id") && !file_json["media_id"].is_null()) {
                    file_content.media_id = file_json["media_id"].get<std::string>();
                }
                if (file_json.contains("mime_type") && !file_json["mime_type"].is_null()) {
                    file_content.mime_type = file_json["mime_type"].get<std::string>();
                }
                if (file_json.contains("url") && !file_json["url"].is_null()) {
                    file_content.url = file_json["url"].get<std::string>();
                }
                if (file_json.contains("file_size") && !file_json["file_size"].is_null()) {
                    file_content.file_size = file_json["file_size"].get<int64_t>();
                }
                content.file_content = std::move(file_content);
            }
            break;

        case AIMMsgContentType::CONTENT_TYPE_REPLY:
            if (content_json.contains("reply_content") && content_json["reply_content"].is_object()) {
                const auto& reply_json = content_json["reply_content"];
                AIMPubMsgReplyContent reply_content;
                if (reply_json.contains("reference_msg") && reply_json["reference_msg"].is_object()) {
                    reply_content.reference_msg = convertToReference(reply_json["reference_msg"]);
                }
                if (reply_json.contains("reply_content") && reply_json["reply_content"].is_object()) {
                    reply_content.reply_content = convertToInnerReplyContent(reply_json["reply_content"]);
                }
                content.reply_content = std::move(reply_content);
            }
            break;

        case AIMMsgContentType::CONTENT_TYPE_COMBINE_FORWARD:
            if (content_json.contains("combine_forward_content") && content_json["combine_forward_content"].is_object()) {
                const auto& combine_json = content_json["combine_forward_content"];
                AIMPubMsgCombineForwardContent combine_content;
                if (combine_json.contains("combine_forward") && combine_json["combine_forward"].is_array()) {
                    combine_content.combine_forward.reserve(combine_json["combine_forward"].size());
                    for (const auto& forward : combine_json["combine_forward"]) {
                        combine_content.combine_forward.push_back(convertToCombineForward(forward));
                    }
                }
                content.combine_forward_content = std::move(combine_content);
            }
            break;

        default:
            // 处理未知的内容类型
            break;
    }
    return content;
}


AIMPubMsgSendMessage AIMPubMsgSendMessageFromJson(const nlohmann::json& j) {
    AIMPubMsgSendMessage message;

    // 初始化基本字段
    if (j.contains("appCid") && !j["appCid"].is_null()) {
        message.appCid = j["appCid"].get<std::string>();
    }

    if (j.contains("receivers") && j["receivers"].is_array()) {
        message.receivers = j["receivers"].get<std::vector<std::string>>();
    }

    if (j.contains("extension") && j["extension"].is_object()) {
        message.extension = j["extension"].get<std::map<std::string, std::string>>();
    }

    if (j.contains("local_extension") && j["local_extension"].is_object()) {
        message.local_extension = j["local_extension"].get<std::map<std::string, std::string>>();
    }

    if (j.contains("callback_ctx") && j["callback_ctx"].is_object()) {
        message.callback_ctx = j["callback_ctx"].get<std::map<std::string, std::string>>();
    }

    if (j.contains("custom_localid") && !j["custom_localid"].is_null()) {
        message.custom_localid = j["custom_localid"].get<std::string>();
    }

    // 初始化 content
    if (j.contains("content") && j["content"].is_object()) {
        const auto& content_json = j["content"];
        AIMPubMsgContent content = convertToPubMsgContent(content_json);
        message.content = std::move(content);
    }

    return message;
}
