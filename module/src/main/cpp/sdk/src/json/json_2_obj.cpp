//
// Created by 照松 on 2024/4/4.
//


#include "json_2_obj.h"

std::map<std::string, std::string> json2StrStrMap(const nlohmann::json &json) {
    std::map<std::string, std::string> map;

    // 遍历 JSON 对象中的每个键值对
    for (auto it = json.begin(); it != json.end(); ++it) {
        // 检查键是否是字符串
        if ((typeid(it.key()) == typeid(std::string))
            && (typeid(it.key()) == typeid(std::string))) {
            map.insert({it.key(), it.value()});
        }
    }

    return map;
}

bool json<PERSON>as<PERSON>ey(const nlohmann::json &json, const std::string &key) {
    return json.find(key) != json.end();
}