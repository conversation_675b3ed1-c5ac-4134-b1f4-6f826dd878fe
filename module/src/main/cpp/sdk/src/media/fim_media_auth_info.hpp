#pragma once
#include "fim_media_auth_info.h"
#include "aim_media_auth_info.h"
#include "fim_msg_media_auth_info.hpp"
#include "fim_group_avator_media_auth_info.hpp"

namespace alibaba {
namespace fim {

// 假设有从 AIM 版本到 FIM 版本的转换函数
inline FIMMediaAuthInfo FIMMediaAuthInfoFrom(const dps::AIMMediaAuthInfo& aimAuthInfo) {
  // 这里需要提供 FIM 版本的枚举和结构体转换函数
  return FIMMediaAuthInfo(
    static_cast<FIMMediaAuthScene>(aimAuthInfo.scene),
    aimAuthInfo.biz_type,
    FIMGroupAvatorMediaAuthInfoFrom(aimAuthInfo.group_avator_auth),
    FIMMsgMediaAuthInfoFrom(aimAuthInfo.msg_auth)
  );
}

// 假设有从 FIM 版本到 AIM 版本的转换函数
inline dps::AIMMediaAuthInfo AIMMediaAuthInfoFrom(const FIMMediaAuthInfo& fimAuthInfo) {
  // 这里需要提供 AIM 版本的枚举和结构体转换函数
  return alibaba::dps::AIMMediaAuthInfo(
    static_cast<alibaba::dps::AIMMediaAuthScene>(fimAuthInfo.scene),
    fimAuthInfo.biz_type,
    AIMGroupAvatorMediaAuthInfoFrom(fimAuthInfo.group_avator_auth),
    AIMMsgMediaAuthInfoFrom(fimAuthInfo.msg_auth)
  );
}

} // namespace fim
} // namespace alibaba
