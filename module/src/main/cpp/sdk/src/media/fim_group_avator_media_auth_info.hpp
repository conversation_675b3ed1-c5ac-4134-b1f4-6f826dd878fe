#pragma once
#include "fim_group_avator_media_auth_info.h"
#include "aim_group_avator_media_auth_info.h" // Assuming this is the header file for the AIM version of the struct

namespace alibaba {
namespace fim {

inline FIMGroupAvatorMediaAuthInfo FIMGroupAvatorMediaAuthInfoFrom(const dps::AIMGroupAvatorMediaAuthInfo& aimAuthInfo) {
  return FIMGroupAvatorMediaAuthInfo(
    aimAuthInfo.cid
  );
}

inline dps::AIMGroupAvatorMediaAuthInfo AIMGroupAvatorMediaAuthInfoFrom(const FIMGroupAvatorMediaAuthInfo& fimAuthInfo) {
  return dps::AIMGroupAvatorMediaAuthInfo(
    fimAuthInfo.cid
  );
}

} // namespace fim
} // namespace alibaba
