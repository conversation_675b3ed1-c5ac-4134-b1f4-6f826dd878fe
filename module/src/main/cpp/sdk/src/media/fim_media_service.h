// Copyright (c) 2019 The Alibaba DingTalk Authors. All rights reserved.

#pragma once

#include <cstdint>
#include <functional>
#include <memory>
#include <string>
#include "fim_image_size_type.h"
#include "fim_media_thumbnail_file_type.h"
#include "fim_upload_file_param.h"
#include "fim_download_file_param.h"
#include "fim_image_dimensions.h"
#include "fim_media_auth_info.h"
#include "fim_error.h"
#include "fliggy_im_sdk.h"


namespace alibaba {
namespace fim {


class FIMSDK_API FIMMediaService {
public:

  /**
   * 上传文件
   * @param param 发送内容
   * @param listener 回调
   */

  static void
  UploadFile(const FIMUploadFileParam &param,
             const std::function<void(const std::string &task_id)> &OnCreate,
             const std::function<void()> &OnStart,
             const std::function<void(int64_t current_size, int64_t total_size)>
                 &OnProgress,
             const std::function<void(const std::string &media_id)> &OnSuccess,
             const std::function<void(const FIMError &error)>
                 &OnFailure);

  /**
   * 下载文件
   * @param param 下载内容
   * @param listener 回调
   */
  static void DownloadFile(
      const FIMDownloadFileParam &param,
      const std::function<void(const std::string &task_id)> &OnCreate,
      const std::function<void()> &OnStart,
      const std::function<void(int64_t current_size, int64_t total_size)>
          &OnProgress,
      const std::function<void(const std::string &path)> &OnSuccess,
      const std::function<void(const FIMError &error)>
          &OnFailure);

  /**
   * 取消文件任务
   * @param task_id 任务id
   */
  static void CancelFileTask(const std::string &task_id);

  /**
   * media 解析(image)
   * @param media_id 媒体id
   * @param size 图片大小
   */
  static std::string TransferMediaIdToImageUrl(const std::string &media_id,
                                                FIMImageSizeType size);

  /**
   * media 解析(image)
   * @param media_id 媒体id
   * @param size 图片大小
   * @param biz_type 业务类型
   */
  static std::string
  TransferMediaIdToImageUrlWithBizType(const std::string &media_id,
                                       FIMImageSizeType size,
                                       const std::string &biz_type);

  /**
   * media 解析(file)
   * @param media_id 媒体id
   */
  static std::string TransferMediaIdToUrl(const std::string &media_id);

  /**
   * media 解析(file)
   * @param media_id 媒体id
   * @param biz_type 业务类型
   */
  static std::string
  TransferMediaIdToUrlWithBizType(const std::string &media_id,
                                  const std::string &biz_type);

  /**
   * media 解析鉴权(image)
   * @param media_id 媒体id
   * @param size 图片大小
   * @param auth_info 鉴权信息
   */
  static std::string
  TransferMediaIdToAuthImageUrl(const std::string &media_id,
                                FIMImageSizeType size,
                                const FIMMediaAuthInfo &auth_info);

  /**
   * media 解析鉴权(image)
   * @param media_id 媒体id
   * @param size 图片大小
   * @param auth_info 鉴权信息
   * @param biz_type 业务类型
   */
  static std::string TransferMediaIdToAuthImageUrlBizType(
      const std::string &media_id, FIMImageSizeType size,
      const FIMMediaAuthInfo &auth_info, const std::string &biz_type);

  /**
   * media 解析鉴权(file)
   * @param media_id 媒体id
   * @param auth_info 鉴权信息
   */
  static std::string
  TransferMediaIdToAuthUrl(const std::string &media_id,
                           const FIMMediaAuthInfo &auth_info);

  /**
   * media 解析鉴权(file)
   * @param media_id 媒体id
   * @param auth_info 鉴权信息
   * @param biz_type 业务类型
   */
  static std::string
  TransferMediaIdToAuthUrlBizType(const std::string &media_id,
                                  const FIMMediaAuthInfo &auth_info,
                                  const std::string &biz_type);

  /**
   * 解析url常量部分
   * @param url 下载url
   */
  static std::string GetUrlConstantPart(const std::string &url);

  /**
   * 获取图片尺寸
   */
  static FIMImageDimensions
  TransferMediaIdToImageDimensions(const std::string &media_id);

  /**
   * media 根据文件格式解析缩略图(image),默认JPG格式,支持GIF
   * @param media_id 媒体id
   * @param file_type 文件格式
   * @param biz_type 业务类型
   */
  static std::string TransferMediaIdToThumbnailUrlWithFileType(
      const std::string &media_id, const std::string &biz_type,
      FIMMediaThumbnailFileType file_type);

  /**
   * media 根据文件格式解析鉴权缩略图(image),默认JPG格式,支持GIF
   * @param media_id 媒体id
   * @param auth_info 鉴权信息
   * @param file_type 文件格式
   * @param biz_type 业务类型
   */
  static std::string TransferMediaIdToAuthThumbnailUrlWithFileType(
      const std::string &media_id, const FIMMediaAuthInfo &auth_info,
      const std::string &biz_type, FIMMediaThumbnailFileType file_type);
};

using FIMMediaServicePtr = std::shared_ptr<FIMMediaService>;
using FIMMediaServiceWeakPtr = std::weak_ptr<FIMMediaService>;

} // namespace dps
} // namespace alibaba
