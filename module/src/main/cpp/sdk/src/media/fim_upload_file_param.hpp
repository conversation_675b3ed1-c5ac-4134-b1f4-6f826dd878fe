#pragma once
#include "fim_upload_file_param.h"
#include "aim_upload_file_param.h"
#include "aim_file_auth_type.h"

namespace alibaba {
namespace fim {

inline FIMUploadFileParam FIMUploadFileParamFrom(const dps::AIMUploadFileParam& aimParam) {
  // 此处假设存在一个将AIMFileAuthType转换为FIMFileAuthType的函数FIMFileAuthTypeFrom
  return FIMUploadFileParam(
    aimParam.path,
    aimParam.mime_type,
    aimParam.biz_type,
    static_cast<FIMFileAuthType>(aimParam.auth_type),
    aimParam.expired_time
  );
}

inline dps::AIMUploadFileParam AIMUploadFileParamFrom(const FIMUploadFileParam& fimParam) {
  // 此处假设存在一个将FIMFileAuthType转换为AIMFileAuthType的函数AIMFileAuthTypeFrom
  return alibaba::dps::AIMUploadFileParam(
    fimParam.path,
    fimParam.mime_type,
    fimParam.biz_type,
    static_cast<alibaba::dps::AIMFileAuthType>(fimParam.auth_type),
    fimParam.expired_time
  );
}

} // namespace fim
} // namespace alibaba
