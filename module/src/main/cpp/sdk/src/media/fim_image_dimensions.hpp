#pragma once
#include "fim_image_dimensions.h"
#include "aim_image_dimensions.h"

namespace alibaba {
namespace fim {

inline FIMImageDimensions FIMImageDimensionsFrom(const dps::AIMImageDimensions& aimDimensions) {
  return FIMImageDimensions(
    aimDimensions.width,
    aimDimensions.height
  );
}

inline dps::AIMImageDimensions AIMImageDimensionsFrom(const FIMImageDimensions& fimDimensions) {
  return dps::AIMImageDimensions(
    fimDimensions.width,
    fimDimensions.height
  );
}

} // namespace fim
} // namespace alibaba
