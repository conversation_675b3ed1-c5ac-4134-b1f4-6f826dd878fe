#pragma once
#include "fim_download_file_param.h"
#include "aim_download_file_param.h"

namespace alibaba {
namespace fim {

inline FIMDownloadFileParam FIMDownloadFileParamFrom(const dps::AIMDownloadFileParam& aimParam) {
  return FIMDownloadFileParam(
    aimParam.download_url,
    aimParam.path
  );
}

inline dps::AIMDownloadFileParam AIMDownloadFileParamFrom(const FIMDownloadFileParam& fimParam) {
  return dps::AIMDownloadFileParam(
    fimParam.download_url,
    fimParam.path
  );
}

} // namespace fim
} // namespace alibaba
