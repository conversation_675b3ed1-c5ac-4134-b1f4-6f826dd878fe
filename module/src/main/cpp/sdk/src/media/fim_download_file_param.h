#pragma once
#include "fliggy_im_sdk.h"
#include <string>

namespace alibaba {
namespace fim {

/**
 * 多媒体下载相关参数
 */
struct FIMSDK_API FIMDownloadFileParam final {
  std::string download_url;
  std::string path;
  FIMDownloadFileParam(std::string download_url_, std::string path_)
      : download_url(std::move(download_url_)), path(std::move(path_)) {}
  FIMDownloadFileParam() {}
};

} // namespace fim
} // namespace alibaba
