#include "fim_media_service.h"
#include "aim_image_dimensions.h"
#include "aim_media_service.h"
#include "aim_pub_c_api.h"
#include "aim_upload_file_param.h"
#include "dps_error.h"
#include "fim_download_file_param.hpp"
#include "fim_engine.h"
#include "fim_error.h"
#include "fim_error.hpp"
#include "fim_group_avator_media_auth_info.hpp"
#include "fim_image_dimensions.hpp"
#include "fim_media_auth_info.hpp"
#include "fim_media_service.h"
#include "fim_msg_media_auth_info.hpp"
#include "fim_upload_file_param.hpp"

namespace alibaba {
namespace fim {
using namespace alibaba::dps;
/**
 * 上传文件
 * @param param 发送内容
 * @param listener 回调
 */
std::shared_ptr<dps::AIMMediaService> getCurrentMediaService() {
  alibaba::dps::AIMPubModule *rawModulePtr =
      GetAIMPubModuleInstance((FIMEngine::GetInstance().fuid));
  if (rawModulePtr == nullptr) {
    return nullptr;
  }
  return rawModulePtr->GetMediaService();
}

void FIMMediaService::UploadFile(const FIMUploadFileParam &param,
           const std::function<void(const std::string &task_id)> &OnCreate,
           const std::function<void()> &OnStart,
           const std::function<void(int64_t current_size, int64_t total_size)>
               &OnProgress,
           const std::function<void(const std::string &media_id)> &OnSuccess,
           const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<dps::AIMMediaService> mediaService = getCurrentMediaService();
  if (mediaService == nullptr) {
    FIMError error =
        FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR, "get messageService failure",
                 "获取media service失败");
    OnFailure(error);
    return;
  }
  AIMUploadFileParam aimParam = AIMUploadFileParamFrom(param);
  mediaService->UploadFile(aimParam, OnCreate, OnStart, OnProgress, OnSuccess,
                           [OnFailure](const DPSError &dpsError) {
                             FIMError error = FIMErrorFrom(dpsError);
                             OnFailure(error);
                           });
}

/**
 * 下载文件
 * @param param 下载内容
 * @param listener 回调
 */
void FIMMediaService::
DownloadFile(const FIMDownloadFileParam &param,
             const std::function<void(const std::string &task_id)> &OnCreate,
             const std::function<void()> &OnStart,
             const std::function<void(int64_t current_size, int64_t total_size)>
                 &OnProgress,
             const std::function<void(const std::string &path)> &OnSuccess,
             const std::function<void(const FIMError &error)> &OnFailure) {
  std::shared_ptr<dps::AIMMediaService> mediaService = getCurrentMediaService();
  if (mediaService == nullptr) {
    FIMError error =
        FIMError(FIMErrorCode::FIM_ERR_CONV_ERROR, "get messageService failure",
                 "获取media service失败");
    OnFailure(error);
  }
  AIMDownloadFileParam aimParam = AIMDownloadFileParamFrom(param);
  mediaService->DownloadFile(aimParam, OnCreate, OnStart, OnProgress, OnSuccess,
                             [OnFailure](const DPSError &dpsError) {
                               FIMError error = FIMErrorFrom(dpsError);
                               OnFailure(error);
                             });
}

/**
 * 取消文件任务
 * @param task_id 任务id
 */
void FIMMediaService:: CancelFileTask(const std::string &task_id) {
  std::shared_ptr<dps::AIMMediaService> mediaService = getCurrentMediaService();
  if (mediaService == nullptr) {
    return;
  }
  mediaService->CancelFileTask(task_id);
}

/**
 * media 解析(image)
 * @param media_id 媒体id
 * @param size 图片大小
 */
std::string FIMMediaService::TransferMediaIdToImageUrl(const std::string &media_id,
                                             FIMImageSizeType size) {
  std::shared_ptr<dps::AIMMediaService> mediaService = getCurrentMediaService();
  if (mediaService == nullptr) {
    return "";
  }
  AIMImageSizeType aimSize = static_cast<AIMImageSizeType>(size);
  return mediaService->TransferMediaIdToImageUrl(media_id, aimSize);
}

/**
 * media 解析(image)
 * @param media_id 媒体id
 * @param size 图片大小
 * @param biz_type 业务类型
 */
std::string
FIMMediaService::TransferMediaIdToImageUrlWithBizType(const std::string &media_id,
                                     FIMImageSizeType size,
                                     const std::string &biz_type) {
  std::shared_ptr<dps::AIMMediaService> mediaService = getCurrentMediaService();
  if (mediaService == nullptr) {
    return "";
  }
  AIMImageSizeType aimSize = static_cast<AIMImageSizeType>(size);
  return mediaService->TransferMediaIdToImageUrlWithBizType(media_id, aimSize,
                                                            biz_type);
}

/**
 * media 解析(file)
 * @param media_id 媒体id
 */
std::string FIMMediaService::TransferMediaIdToUrl(const std::string &media_id) {
  std::shared_ptr<dps::AIMMediaService> mediaService = getCurrentMediaService();
  if (mediaService == nullptr) {
    return "";
  }
  return mediaService->TransferMediaIdToUrl(media_id);
}

/**
 * media 解析(file)
 * @param media_id 媒体id
 * @param biz_type 业务类型
 */
std::string
FIMMediaService::TransferMediaIdToUrlWithBizType(const std::string &media_id,
                                const std::string &biz_type) {
  std::shared_ptr<dps::AIMMediaService> mediaService = getCurrentMediaService();
  if (mediaService == nullptr) {
    return "";
  }
  return mediaService->TransferMediaIdToUrlWithBizType(media_id, biz_type);
}

/**
 * media 解析鉴权(image)
 * @param media_id 媒体id
 * @param size 图片大小
 * @param auth_info 鉴权信息
 */
std::string
FIMMediaService::TransferMediaIdToAuthImageUrl(const std::string &media_id,
                              FIMImageSizeType size,
                              const FIMMediaAuthInfo &auth_info) {
  std::shared_ptr<dps::AIMMediaService> mediaService = getCurrentMediaService();
  if (mediaService == nullptr) {
    return "";
  }
  AIMImageSizeType aimSize = static_cast<AIMImageSizeType>(size);
  AIMMediaAuthInfo aimAuth = AIMMediaAuthInfoFrom(auth_info);
  return mediaService->TransferMediaIdToAuthImageUrl(media_id, aimSize,
                                                     aimAuth);
}

/**
 * media 解析鉴权(image)
 * @param media_id 媒体id
 * @param size 图片大小
 * @param auth_info 鉴权信息
 * @param biz_type 业务类型
 */
std::string FIMMediaService::TransferMediaIdToAuthImageUrlBizType(
    const std::string &media_id, FIMImageSizeType size,
    const FIMMediaAuthInfo &auth_info, const std::string &biz_type) {
  std::shared_ptr<dps::AIMMediaService> mediaService = getCurrentMediaService();
  if (mediaService == nullptr) {
    return "";
  }
  AIMImageSizeType aimSize = static_cast<AIMImageSizeType>(size);
  AIMMediaAuthInfo aimAuth = AIMMediaAuthInfoFrom(auth_info);
  return mediaService->TransferMediaIdToAuthImageUrlBizType(media_id, aimSize,
                                                            aimAuth, biz_type);
}

/**
 * media 解析鉴权(file)
 * @param media_id 媒体id
 * @param auth_info 鉴权信息
 */
std::string FIMMediaService::TransferMediaIdToAuthUrl(const std::string &media_id,
                                            const FIMMediaAuthInfo &auth_info) {
  std::shared_ptr<dps::AIMMediaService> mediaService = getCurrentMediaService();
  if (mediaService == nullptr) {
    return "";
  }
  AIMMediaAuthInfo aimAuth = AIMMediaAuthInfoFrom(auth_info);
  return mediaService->TransferMediaIdToAuthUrl(media_id, aimAuth);
}

/**
 * media 解析鉴权(file)
 * @param media_id 媒体id
 * @param auth_info 鉴权信息
 * @param biz_type 业务类型
 */
std::string
FIMMediaService::TransferMediaIdToAuthUrlBizType(const std::string &media_id,
                                const FIMMediaAuthInfo &auth_info,
                                const std::string &biz_type) {
  std::shared_ptr<dps::AIMMediaService> mediaService = getCurrentMediaService();
  if (mediaService == nullptr) {
    return "";
  }
  AIMMediaAuthInfo aimAuth = AIMMediaAuthInfoFrom(auth_info);
  return mediaService->TransferMediaIdToAuthUrlBizType(media_id, aimAuth,
                                                       biz_type);
}

/**
 * 解析url常量部分
 * @param url 下载url
 */
std::string FIMMediaService::GetUrlConstantPart(const std::string &url) {
  std::shared_ptr<dps::AIMMediaService> mediaService = getCurrentMediaService();
  if (mediaService == nullptr) {
    return "";
  }
  return mediaService->GetUrlConstantPart(url);
}

/**
 * 获取图片尺寸
 */
FIMImageDimensions
FIMMediaService::TransferMediaIdToImageDimensions(const std::string &media_id) {
  std::shared_ptr<dps::AIMMediaService> mediaService = getCurrentMediaService();
  if (mediaService == nullptr) {
    return FIMImageDimensions();
  }
  AIMImageDimensions aimDimensions =
      mediaService->TransferMediaIdToImageDimensions(media_id);
  return FIMImageDimensionsFrom(aimDimensions);
}

/**
 * media 根据文件格式解析缩略图(image),默认JPG格式,支持GIF
 * @param media_id 媒体id
 * @param file_type 文件格式
 * @param biz_type 业务类型
 */
std::string
FIMMediaService::TransferMediaIdToThumbnailUrlWithFileType(const std::string &media_id,
                                          const std::string &biz_type,
                                          FIMMediaThumbnailFileType file_type) {
  std::shared_ptr<dps::AIMMediaService> mediaService = getCurrentMediaService();
  if (mediaService == nullptr) {
    return "";
  }
  AIMMediaThumbnailFileType aimFileType =
      static_cast<AIMMediaThumbnailFileType>(file_type);
  return mediaService->TransferMediaIdToThumbnailUrlWithFileType(
      media_id, biz_type, aimFileType);
}

/**
 * media 根据文件格式解析鉴权缩略图(image),默认JPG格式,支持GIF
 * @param media_id 媒体id
 * @param auth_info 鉴权信息
 * @param file_type 文件格式
 * @param biz_type 业务类型
 */
std::string FIMMediaService::TransferMediaIdToAuthThumbnailUrlWithFileType(
    const std::string &media_id, const FIMMediaAuthInfo &auth_info,
    const std::string &biz_type, FIMMediaThumbnailFileType file_type) {
  std::shared_ptr<dps::AIMMediaService> mediaService = getCurrentMediaService();
  if (mediaService == nullptr) {
    return "";
  }
  AIMMediaAuthInfo aimAuth = AIMMediaAuthInfoFrom(auth_info);
  AIMMediaThumbnailFileType aimFileType =
      static_cast<AIMMediaThumbnailFileType>(file_type);
  return mediaService->TransferMediaIdToAuthThumbnailUrlWithFileType(
      media_id, aimAuth, biz_type, aimFileType);
}

} // namespace fim
} // namespace alibaba