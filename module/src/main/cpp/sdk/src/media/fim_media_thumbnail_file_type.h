#pragma once

#include <functional>

namespace alibaba {
namespace fim {

enum class FIMMediaThumbnailFileType : int {
  IMAGE_FILE_TYPE_UNKNOWN = -1,
  IMAGE_FILE_TYPE_JPG = 0,
  IMAGE_FILE_TYPE_GIF = 1,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::fim::FIMMediaThumbnailFileType> {
  size_t operator()(::alibaba::fim::FIMMediaThumbnailFileType type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
