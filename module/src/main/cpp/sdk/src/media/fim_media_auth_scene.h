#pragma once

#include <functional>

namespace alibaba {
namespace fim {

enum class FIMMediaAuthScene : int {
  /**
   * 未知类型
   */
  MAC_UNKNOWN = 0,
  /**
   *会话头像
   */
  MAC_GROUP_AVATOR = 1,
  /**
   *会话消息
   */
  MAC_MSG = 2,
};

}
} // namespace alibaba

namespace std {

template <> struct hash<::alibaba::fim::FIMMediaAuthScene> {
  size_t operator()(::alibaba::fim::FIMMediaAuthScene type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};

} // namespace std
