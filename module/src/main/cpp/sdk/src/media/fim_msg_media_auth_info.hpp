#pragma once
#include "fim_msg_media_auth_info.h"
#include "aim_msg_media_auth_info.h" // Assuming this is the header file for the AIM version of the struct

namespace alibaba {
namespace fim {

inline FIMMsgMediaAuthInfo FIMMsgMediaAuthInfoFrom(const dps::AIMMsgMediaAuthInfo& aimAuthInfo) {
  return FIMMsgMediaAuthInfo(
    aimAuthInfo.cid,
    aimAuthInfo.mid
  );
}

inline dps::AIMMsgMediaAuthInfo AIMMsgMediaAuthInfoFrom(const FIMMsgMediaAuthInfo& fimAuthInfo) {
  return dps::AIMMsgMediaAuthInfo(
    fimAuthInfo.cid,
    fimAuthInfo.mid
  );
}

} // namespace fim
} // namespace alibaba
