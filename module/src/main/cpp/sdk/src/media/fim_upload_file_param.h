#pragma once
#include <string>
#include <cstdint>
#include "fim_file_auth_type.h" // 假设已经定义了对应AIMFileAuthType的FIM版本
#include "fliggy_im_sdk.h"

namespace alibaba {
namespace fim {

/**
 * 多媒体上传相关参数
 */
struct FIMSDK_API FIMUploadFileParam final {
  std::string path;
  std::string mime_type;
  std::string biz_type;
  FIMFileAuthType auth_type = FIMFileAuthType::STRICT_AUTH;
  /**
   * 过期时间，单位分钟（取决于服务端实现），默认值为0，
   */
  int32_t expired_time = 0;
  FIMUploadFileParam(std::string path_, std::string mime_type_,
                     std::string biz_type_, FIMFileAuthType auth_type_,
                     int32_t expired_time_)
      : path(std::move(path_)), mime_type(std::move(mime_type_)),
        biz_type(std::move(biz_type_)), auth_type(std::move(auth_type_)),
        expired_time(std::move(expired_time_)) {}
  FIMUploadFileParam() {}
};

} // namespace fim
} // namespace alibaba
