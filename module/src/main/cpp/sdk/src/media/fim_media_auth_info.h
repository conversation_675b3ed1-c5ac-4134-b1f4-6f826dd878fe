#pragma once
#include <string>
#include "fim_media_auth_scene.h" 
#include "fim_group_avator_media_auth_info.h"
#include "fim_msg_media_auth_info.h"
#include "fliggy_im_sdk.h"

namespace alibaba {
namespace fim {

/**
 * 鉴权信息
 */
struct FIMSDK_API FIMMediaAuthInfo final {
  FIMMediaAuthScene scene = FIMMediaAuthScene::MAC_UNKNOWN;
  std::string biz_type;
  FIMGroupAvatorMediaAuthInfo group_avator_auth;
  FIMMsgMediaAuthInfo msg_auth;
  FIMMediaAuthInfo(FIMMediaAuthScene scene_, std::string biz_type_,
                   FIMGroupAvatorMediaAuthInfo group_avator_auth_,
                   FIMMsgMediaAuthInfo msg_auth_)
      : scene(scene_), biz_type(std::move(biz_type_)),
        group_avator_auth(std::move(group_avator_auth_)),
        msg_auth(std::move(msg_auth_)) {}
  FIMMediaAuthInfo() {}
};

} // namespace fim
} // namespace alibaba
