#pragma once
#include <memory>
#include <string>
#include "fim_log_level.h"
#include "fliggy_im_sdk.h"

namespace alibaba {
namespace fim {


/**
 * Log 监听
 */
class FIMSDK_API FIMLogHandler {
 public:
  virtual ~FIMLogHandler() {}

  virtual void OnLog(FIMLogLevel log_level, const std::string& log_content) = 0;
};

using FIMLogHandlerPtr = std::shared_ptr<FIMLogHandler>;
using FIMLogHandlerWeakPtr = std::weak_ptr<FIMLogHandler>;

}  // namespace fim
}  // namespace alibaba
