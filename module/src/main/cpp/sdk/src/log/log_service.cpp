#include "log_service.h"
#include "dps_log_level.h"
#include "dps_logger_proxy.h"
#include "dps_pub_engine.h"
#include <iostream>

namespace alibaba {
namespace fim {
LogService *LogService::instance = nullptr;

void LogService::Log(FIMLogLevel log_level, const std::string &log_content) {
  FIMLogLevel fim_log_level = static_cast<FIMLogLevel>(log_level);
  if (LogService::GetInstance()->fim_logger &&
      log_level >= LogService::GetInstance()->log_level) {
    LogService::GetInstance()->fim_logger->OnLog(fim_log_level, log_content);
  }
}

void LogService::DebugLog(std::function<std::string()> logStringProducer) {
  FIMLogLevel log_level = LogService::GetInstance()->log_level;
  FIMLogHandlerPtr fim_logger = LogService::GetInstance()->fim_logger;
  bool shouldLogDebug =
      log_level <= FIMLogLevel::FIM_LOG_LEVEL_DEBUG && fim_logger != nullptr;
  if (shouldLogDebug) {
    Log(FIMLogLevel::FIM_LOG_LEVEL_DEBUG, logStringProducer());
  }
}

void LogService::InfoLog(std::function<std::string()> logStringProducer) {
  FIMLogLevel log_level = LogService::GetInstance()->log_level;
  FIMLogHandlerPtr fim_logger = LogService::GetInstance()->fim_logger;
  bool shouldLogInfo =
      log_level <= FIMLogLevel::FIM_LOG_LEVEL_INFO && fim_logger != nullptr;
  if (shouldLogInfo) {
    Log(FIMLogLevel::FIM_LOG_LEVEL_INFO, logStringProducer());
  }
}

void LogService::WarningLog(std::function<std::string()> logStringProducer) {
  FIMLogLevel log_level = LogService::GetInstance()->log_level;
  FIMLogHandlerPtr fim_logger = LogService::GetInstance()->fim_logger;
  bool shouldLogWarning =
      log_level <= FIMLogLevel::FIM_LOG_LEVEL_WARNING && fim_logger != nullptr;
  if (shouldLogWarning) {
    Log(FIMLogLevel::FIM_LOG_LEVEL_WARNING, logStringProducer());
  }
}

void LogService::ErrorLog(std::function<std::string()> logStringProducer) {
  FIMLogLevel log_level = LogService::GetInstance()->log_level;
  FIMLogHandlerPtr fim_logger = LogService::GetInstance()->fim_logger;
  bool shouldLogError =
      log_level <= FIMLogLevel::FIM_LOG_LEVEL_ERROR && fim_logger != nullptr;
  if (shouldLogError) {
    Log(FIMLogLevel::FIM_LOG_LEVEL_ERROR, logStringProducer());
  }
}

void LogService::FatalLog(const std::string &log_content) {
  FIMLogLevel log_level = LogService::GetInstance()->log_level;
  FIMLogHandlerPtr fim_logger = LogService::GetInstance()->fim_logger;
  bool shouldLogFatal =
      log_level <= FIMLogLevel::FIM_LOG_LEVEL_FATAL && fim_logger != nullptr;
  if (shouldLogFatal) {
    Log(FIMLogLevel::FIM_LOG_LEVEL_FATAL, log_content);
  }
}

void LogService::SetLogHandler(FIMLogLevel min_log_level,
                               const std::shared_ptr<FIMLogHandler> &handler) {
  log_level = min_log_level;
  fim_logger = handler;
  dps::DPSLogLevel dps_log_level = static_cast<dps::DPSLogLevel>(log_level);

  std::shared_ptr<alibaba::fim::DPSLogServiceProxy> logServiceProxy =
      std::make_shared<alibaba::fim::DPSLogServiceProxy>();
  dps::DPSPubEngine::GetDPSEngine()->SetLogHandler(dps_log_level,
                                                   logServiceProxy);
}

} // namespace fim
} // namespace alibaba