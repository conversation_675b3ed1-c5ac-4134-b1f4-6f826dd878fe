#pragma once
#include "dps_log_handler.h"
#include "dps_log_level.h"
#include "log_service.h"
#include "fim_log_level.h"
#include <iostream>

namespace alibaba {
namespace fim {

class DPSLogServiceProxy : public dps::DPSLogHandler {
public:
  virtual void OnLog(dps::DPSLogLevel log_level,
                     const std::string &log_content) override {
    FIMLogLevel fim_log_level = static_cast<FIMLogLevel>(log_level);
    LogService::GetInstance()->Log(fim_log_level, log_content);
    std::cout << log_content << std::endl;
  }
};

} // namespace fim
} // namespace alibaba
