#pragma once
#include "dps_log_level.h"
#include "fim_log_handler.h"
#include "fim_log_level.h"

namespace alibaba {
namespace fim {

class LogService {
private:
  static LogService *instance;
  FIMLogHandlerPtr fim_logger;
  FIMLogLevel log_level = FIMLogLevel::FIM_LOG_LEVEL_NEVER;

  LogService() = default;

  bool ShouldLogDebug() {
    return log_level <= FIMLogLevel::FIM_LOG_LEVEL_DEBUG;
  }
  bool ShouldLogInfo() { return log_level <= FIMLogLevel::FIM_LOG_LEVEL_INFO; }
  bool ShouldLogWarning() {
    return log_level <= FIMLogLevel::FIM_LOG_LEVEL_WARNING;
  }
  bool ShouldLogError() {
    return log_level <= FIMLogLevel::FIM_LOG_LEVEL_ERROR;
  }
  bool ShouldLogFatal() {
    return log_level <= FIMLogLevel::FIM_LOG_LEVEL_FATAL;
  }

public:
  static LogService *GetInstance() {
    if (instance == nullptr) {
      instance = new LogService();
    }
    return instance;
  }


  static void Log(FIMLogLevel log_level, const std::string &log_content);

  static void DebugLog(std::function<std::string()> logStringProducer);

  static void InfoLog(std::function<std::string()> logStringProducer);

  static void WarningLog(std::function<std::string()> logStringProducer);

  static void ErrorLog(std::function<std::string()> logStringProducer);

  static void FatalLog(const std::string &log_content);

  void SetLogHandler(FIMLogLevel min_log_level,
                     const std::shared_ptr<FIMLogHandler> &handler);
};

} // namespace fim
} // namespace alibaba
