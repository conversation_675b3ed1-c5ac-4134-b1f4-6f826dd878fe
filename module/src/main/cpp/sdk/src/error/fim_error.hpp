#pragma once
#include "fim_error.h"
#include "dps_error.h"
#include "fim_error_code.h"
#include "fim_error_domain.h"

namespace alibaba {
namespace fim {
using namespace dps;
// 注意这里是函数的定义，不是声明，所以应该包含函数体
inline FIMError FIMErrorFrom(const dps::DPSError &dpsError) {
  FIMErrorDomain domain = static_cast<FIMErrorDomain>(dpsError.domain);
  FIMErrorCode code = static_cast<FIMErrorCode>(dpsError.code);
  return FIMError(domain, code, dpsError.developer_message, dpsError.reason, dpsError.extra_info, dpsError.scope);
}
}
}
