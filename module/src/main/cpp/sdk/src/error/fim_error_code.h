#pragma once
//
//  fim_error_code.h
//  FIMSDKFramework
//
//  Created by 蒙晨 on 2024/1/25.
//

#ifndef fim_error_code_h
#define fim_error_code_h

#include <cstdint>
#include <functional>


namespace alibaba {
namespace fim {
// 定义 FIM 类型的错误代码枚举
enum class FIMErrorCode : int32_t {
  /**
   * [DPS]成功
   */
  DPS_ERR_SUCCESS = 0,
  /**
   * [DPS]禁止调用
   */
  DPS_ERR_NOTENABLED,
  /**
   * [DPS]功能不支持
   */
  DPS_ERR_NOTSUPPORT,
  /**
   * [DPS]参数错误
   */
  DPS_ERR_INVALIDARG,
  /**
   * [DPS]DPSEngine实例不存在
   */
  DPS_ENGINE_IS_NULL = 10,
  /**
   * [DPS]DPSEngine重复创建
   */
  DPS_ENGINE_ALREADY_EXIST,
  /**
   * [DPS]DPSEngine未启动
   */
  DPS_ENGINE_NOT_START,
  /**
   * [DPS]DPSSettingService有必填值未填写
   */
  DPS_SETTING_NOT_READY,
  /**
   * [DPS]DPSManager创建失败
   */
  DPS_CREATE_MANAGER_FAILED,
  /**
   * [DPS]DPSManager初始化失败
   */
  DPS_MANAGER_INIT_FAILED,
  /**
   * [DPS]DPSManager实例不存在
   */
  DPS_MANAGER_NOT_EXIST,
  /**
   * [DPS]DPSAuthService实例不存在
   */
  DPS_AUTH_IS_NULL,
  /**
   * [DPS]DPSSyncService实例不存在
   */
  DPS_SYNC_IS_NULL,
  /**
   * [DPS]DPSUtService实例不存在
   */
  DPS_UT_IS_NULL,
  /**
   * [DPS]DPSRpcService实例不存在
   */
  DPS_RPC_IS_NULL,
  /**
   * [AUTH]实例不存在
   */
  AUTH_AUTH_IS_NULL = 50,
  /**
   * [AUTH]网络实例不存在
   */
  AUTH_UA_IS_NULL,
  /**
   * [AUTH]AuthTokenCallback回调为空
   */
  AUTH_GET_TOKEN_CB_IS_NULL,
  /**
   * [AUTH]AuthTokenCallback回调方法返回错误
   */
  AUTH_GET_TOKEN_FAILED,
  /**
   * [AUTH]AuthTokenCallback调用太频繁被流控
   */
  AUTH_GET_TOKEN_TOO_FREQUENTLY,
  /**
   * [AUTH]token为空
   */
  AUTH_TOKEN_EMPTY,
  /**
   * [AUTH]token已过期
   */
  AUTH_TOKEN_EXPIRED,
  /**
   * [AUTH]未初始化
   */
  AUTH_NOT_INIT,
  /**
   * [AUTH]未登录
   */
  AUTH_NOT_AUTH_YET,
  /**
   * [DB]实例不存在
   */
  DB_MANAGER_NOT_EXIST = 100,
  /**
   * [DB]获取数据版本异常
   */
  DB_GET_VERSION_FAILED,
  /**
   * [DB]异步执行超时
   */
  DB_ASYNC_EXEC_TIMEOUT,
  /**
   * [DB]SQL执行失败
   */
  DB_SQL_ERROR,
  /**
   * [DB]数据库文件损坏
   */
  DB_OPEN_MALFORMED,
  /**
   * [DB]磁盘已满，数据写入失败
   */
  DB_FULL,
  /**
   * [DB]数据库损坏且无法自动恢复
   */
  DB_MAILFORMED,
  /**
   * [DB]数据库运行期间内存不足
   */
  DB_NO_MEMORY,
  /**
   *  [DB]数据库密钥错误
   */
  DB_ENCRYPTION_KEY_ERROR,
  /**
   * [USERDATA]清理用户数据参数错误
   */
  RESET_USER_DATA_INVALID_PARAM = 150,
  /**
   * [USERDATA]账号正在使用
   */
  RESET_USER_DATA_USER_IS_RUNNING,
  /**
   * [USERDATA]用户目录删除失败，可能被占用或无法删除
   */
  RESET_USER_DATA_FAILED_TO_RESET,
  /**
   * [NET]网络参数错误
   */
  NET_ERR_INVALID_PARAM = 100001,
  /**
   * [NET]网络操作异常
   */
  NET_ERR_INVALID_OPERATION,
  /**
   * [NET]网络超时
   */
  NET_ERR_TIMEOUT,
  /**
   * [NET]网络异常
   */
  NET_ERR_NETWORK_EXCEPTION,
  /**
   * [NET]找不到token
   */
  NET_ERR_NO_AUTHTOKEN,
  /**
   * [NET]网络未连接
   */
  NET_ERR_NOT_CONNECTED,
  /**
   * [NET]正在登录
   */
  NET_ERR_IS_AUTHING,
  /**
   * [NET]已经登录
   */
  NET_ERR_IS_AUTHED,
  /**
   * [NET]token为空
   */
  NET_ERR_TOKEN_EMPTY,
  /**
   * [NET]未知错误
   */
  NET_ERR_UNKNOWN,
  /**
   * [NET]网络包错误
   */
  NET_ERR_UNPACK_EXCEPTION,
  /**
   * [NET]服务端未指定错误原因
   */
  NET_ERR_CODE_REASON_NOT_SET,
  /**
   * [NET]账号已退出
   */
  NET_ERR_LOGOUT,
  /**
   * [NET]当前网络不可用
   */
  NET_ERR_NETWORK_UNAVAILABLE,
  /**
   * [NET]发送请求失败
   */
  NET_ERR_SEND_ERROR,
  /**
   * [SQLITE] SQL执行成功
   */
  SQLITE_ERR_SQLITE_OK = 200000,
  /**
   * [SQLITE] SQL错误 或 丢失数据库
   */
  SQLITE_ERR_SQLITE_ERROR,
  /**
   * [SQLITE] SQLite 内部逻辑错误
   */
  SQLITE_ERR_SQLITE_INTERNAL,
  /**
   * [SQLITE] 拒绝访问
   */
  SQLITE_ERR_SQLITE_PERM,
  /**
   * [SQLITE] 回调函数请求取消操作
   */
  SQLITE_ERR_SQLITE_ABORT,
  /**
   * [SQLITE] 数据库文件被锁定
   */
  SQLITE_ERR_SQLITE_BUSY,
  /**
   * [SQLITE] 数据库中的一个表被锁定
   */
  SQLITE_ERR_SQLITE_LOCKED,
  /**
   * [SQLITE] 某次 malloc() 函数调用失败
   */
  SQLITE_ERR_SQLITE_NOMEM,
  /**
   * [SQLITE] 尝试写入一个只读数据库
   */
  SQLITE_ERR_SQLITE_READONLY,
  /**
   * [SQLITE] 操作被 sqlite3_interupt() 函数中断
   */
  SQLITE_ERR_SQLITE_INTERRUPT,
  /**
   * [SQLITE] 发生某些磁盘 I/O 错误
   */
  SQLITE_ERR_SQLITE_IO_ERR,
  /**
   * [SQLITE] 数据库磁盘映像不正确
   */
  SQLITE_ERR_SQLITE_CORRUPT,
  /**
   * [SQLITE] sqlite3_file_control()中出现未知操作数
   */
  SQLITE_ERR_SQLITE_NOT_FOUND,
  /**
   * [SQLITE] 因为数据库满导致插入失败
   */
  SQLITE_ERR_SQLITE_FULL,
  /**
   * [SQLITE] 无法打开数据库文件
   */
  SQLITE_ERR_SQLITE_CANT_OPEN,
  /**
   * [SQLITE] 数据库锁定协议错误
   */
  SQLITE_ERR_SQLITE_PROTOCOL,
  /**
   * [SQLITE] 数据库为空
   */
  SQLITE_ERR_SQLITE_EMPTY,
  /**
   * [SQLITE] 数据结构发生改变
   */
  SQLITE_ERR_SQLITE_SCHEMA,
  /**
   * [SQLITE] 字符串或二进制数据超过大小限制
   */
  SQLITE_ERR_SQLITE_TOO_BIG,
  /**
   * [SQLITE] 由于约束违例而取消
   */
  SQLITE_ERR_SQLITE_CONSTRAINT,
  /**
   * [SQLITE] 数据类型不匹配
   */
  SQLITE_ERR_SQLITE_MISMATCH,
  /**
   * [SQLITE] 不正确的库使用
   */
  SQLITE_ERR_SQLITE_MISUSE,
  /**
   * [SQLITE] 使用了操作系统不支持的功能
   */
  SQLITE_ERR_SQLITE_NOLFS,
  /**
   * [SQLITE] 授权失败
   */
  SQLITE_ERR_SQLITE_AUTH,
  /**
   * [SQLITE] 附加数据库格式错误
   */
  SQLITE_ERR_SQLITE_FORMAT,
  /**
   * [SQLITE] 传递给sqlite3_bind()的第二个参数超出范围
   */
  SQLITE_ERR_SQLITE_RANGE,
  /**
   * [SQLITE] 被打开的文件不是一个数据库文件
   */
  SQLITE_ERR_SQLITE_NOT_A_DB,
  /**
   * [SQLITE] 来自sqlite3_log()的提示
   */
  SQLITE_ERR_SQLITE_NOTICE,
  /**
   * [SQLITE] 来自sqlite3_log()的warning
   */
  SQLITE_ERR_SQLITE_WARNING,
  /**
   * [SQLITE] sqlite3_step() 已经产生一个行结果
   */
  SQLITE_ERR_SQLITE_ROW = 200100,
  /**
   * [SQLITE] sqlite3_step() 已经结束执行
   */
  SQLITE_ERR_SQLITE_DONE = 200101,
  /**
   * [FIM] Engine 未成功启动
   */
  FIM_ERR_NO_ENGINE = 600001,
  /**
   * [FIM]  网络请求报错
   */
  FIM_ERR_NETWORK_ERROR = 600002,
  /**
   * [FIM]  会话报错
   */
  FIM_ERR_CONV_ERROR = 600003,

  FIM_ERR_SETTING_SERVICE = 600004,
    
  FIM_ERR_FFI = 600005,
};

} // namespace fim
} // namespace alibaba

// 特化 std::hash 模板用于 FIMErrClientCode 枚举
namespace std {
template <> struct hash<::alibaba::fim::FIMErrorCode> {
  size_t operator()(::alibaba::fim::FIMErrorCode type) const {
    return std::hash<int>()(static_cast<int>(type));
  }
};
} // namespace std

#endif /* fim_error_code_h */
