//
//  fim_error_damain.h
//  FIMSDKFramework
//
//  Created by �ɳ� on 2024/1/26.
//

#ifndef fim_error_damain_h
#define fim_error_damain_h

#include <cstdint>
#include <functional>

namespace alibaba {
    namespace fim {

        enum class FIMErrorDomain : int {
            /**
             * DPS错误
             */
            FIM_ERR_DOMAIN_DPS = 0,
            /**
             * AIM错误
             */
            FIM_ERR_DOMAIN_AIM,
            /**
             * FIMSDK错误
             */
            FIM_ERR_DOMAIN_FIMSDK,
            /**
             * 外部错误
             */
            FIM_ERR_DOMAIN_EXTERNAL,
        };

    }
}  // namespace alibaba

namespace std {

    template <>
    struct hash<::alibaba::fim::FIMErrorDomain> {
        size_t operator()(::alibaba::fim::FIMErrorDomain type) const {
            return std::hash<int>()(static_cast<int>(type));
        }
    };

}  // namespace std


#endif /* fim_error_damain_h */
