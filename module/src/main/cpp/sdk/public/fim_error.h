//
//  fim_error.h
//  FIMSDKFramework
//
//  Created by �ɳ� on 2024/1/25.
//

#ifndef fim_error_h
#define fim_error_h

#include <string>
#include "fim_error_code.h"
#include "fim_error_domain.h"
#include "fliggy_im_sdk.h"


namespace alibaba {
    namespace fim {
        struct FIMSDK_API FIMError {
            FIMErrorDomain domain;
            FIMErrorCode code;
            std::string developer_message;
            std::string reason;
            std::string extra_info;
            std::string scope;

            // 构造函数，接受所有六个参数
            FIMError(FIMErrorDomain domain_, FIMErrorCode code_, const std::string& developer_message_,
                     const std::string& reason_, const std::string& extra_info_, const std::string& scope_)
                : domain(domain_), code(code_), developer_message(developer_message_),
                  reason(reason_), extra_info(extra_info_), scope(scope_) {}

            // 构造函数，接受四个参数
            FIMError(FIMErrorDomain domain_, FIMErrorCode code_, const std::string& developer_message_,
                     const std::string& reason_)
                : domain(domain_), code(code_), developer_message(developer_message_),
                  reason(reason_), extra_info(""), scope("") {}

                            // 构造函数，接受三个参数
            FIMError(FIMErrorCode code_, const std::string& developer_message_,
                    const std::string& reason_)
                : domain(FIMErrorDomain::FIM_ERR_DOMAIN_FIMSDK), // 为错误域提供默认值
                code(code_),
                developer_message(developer_message_),
                reason(reason_),
                extra_info(""), // 为额外信息提供空字符串
                scope("") {}    // 为范围提供空字符串

            // 默认构造函数
            FIMError()
                : domain(FIMErrorDomain::FIM_ERR_DOMAIN_FIMSDK), code(FIMErrorCode::DPS_ERR_SUCCESS),
                  developer_message(""), reason(""), extra_info(""), scope("") {}
        };
    } // namespace fim
} // namespace alibaba


#endif
