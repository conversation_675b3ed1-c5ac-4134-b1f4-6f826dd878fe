//
//  fliggy_im_sdk.h
//  FliggyIMSDK
//
//  Created by 蒙晨 on 2024/2/21.
//

#ifndef fliggy_im_sdk_h
#define fliggy_im_sdk_h

// #if defined(_WIN32) || defined(_WIN64)
//     #ifdef FLIGGYSDK_EXPORTS
//     #define FIMSDK_API __declspec(dllexport)
//     #else
//     #define FIMSDK_API __declspec(dllexport)
//     #endif
// #else
//     #define FIMSDK_API __attribute__((visibility("default")))
// #endif
#if defined(_WIN32) || defined(_WIN64)
    #ifdef FLIGGYIMSDK_EXPORTS
        // 使用此宏导出到 DLL
        #define FIMSDK_API __declspec(dllexport)
    #else
        // 使用此宏从 DLL 导入
        #define FIMSDK_API __declspec(dllimport)
    #endif
#else
    #define FIMSDK_API __attribute__((visibility("default")))
#endif


#endif /* fliggy_im_sdk_h */
