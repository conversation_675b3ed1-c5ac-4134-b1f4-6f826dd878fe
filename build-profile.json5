{
  "app": {
    "signingConfigs": [
      {
        "name": "release",
        "type": "HarmonyOS",
        "material": {
          "storePassword": "0000001C385B4544C8A1CE3F1F490F8C6AF10A7342EE9B83BB86F837790051853D82BC0C88F7903F8BEFFCB6",
          "certpath": "./signature/release/fliggy_harmony_release.cer",
          "keyAlias": "fliggy",
          "keyPassword": "0000001C77FC5A847A3A85B71C657CE0B2327A48A631C62FC9285923BA89E1418198C87E751851EEB730F40F",
          "profile": "./signature/release/fliggy_harmony_release.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "./signature/release/fliggy_harmony_release.p12"
        }
      },
      {
        "name": "default",
        "type": "HarmonyOS",
        "material": {
          "storePassword": "0000001CCF8B8A5993FB0B8675B97A9190713287EA1BB76CAE1ECE5E1B9246014DE528758D15B01E51CAF612",
          "certpath": "./signature/debug/fliggy_harmony_debug.cer",
          "keyAlias": "fliggy",
          "keyPassword": "0000001C358F6130D8BD4313A7684AA370139B47F9CA7C47B2F002211BF3441F0CD26561252817B5A5F38F86",
          "profile": "./signature/debug/fliggy_harmony_debug.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "./signature/debug/fliggy_harmony_debug.p12"
        }
      },
      {
        "name": "internal",
        "type": "HarmonyOS",
        "material": {
          "storePassword": "0000001C628A2580FAF84B69CF035733888EAC0F3B208C7275F171BB5D6BF5E3A0C89217C0480620F3421F72",
          "certpath": "./signature/internal/fliggy_harmony_internal.cer",
          "keyAlias": "fliggy",
          "keyPassword": "0000001CBB8BFC980EC232DB95E0E33BF4402B0653CADAF0331BBD32B05FC1252F3DE1659280C58E92AD203D",
          "profile": "./signature/internal/fliggy_harmony_internal.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "./signature/internal/fliggy_harmony_internal.p12"
        }
      },
      {
        "name": "inhouse",
        "type": "HarmonyOS",
        "material": {
          "certpath": "./signature/inhouse/fliggy_harmony_inhouse.cer",
          "storePassword": "0000001C57B0C7CCC4E57F5FFA7C3C6B8BFF83D282CB1AFC06D4B4C004F386F1BAB861E87EA89AAB7C7FA065",
          "keyAlias": "fliggy",
          "keyPassword": "0000001CA722F18523E7E1C4207634FAA350BC53DFFB72141267EFB9D67B272BDF3FE831106A0E1FADB85CE9",
          "profile": "./signature/inhouse/fliggy_harmony_inhouse.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "./signature/inhouse/fliggy_harmony_inhouse.p12"
        }
      }
    ],
    "products": [
      {
        "name": "default",
        "signingConfig": "default",
        "compatibleSdkVersion": "5.0.0(12)",
        "runtimeOS": "HarmonyOS",
        "compatibleSdkVersionStage": "beta3",
        "buildOption": {
          "strictMode": {
            "useNormalizedOHMUrl": true
          }
        }
      },
      {
        "name": "release",
        "signingConfig": "release",
        "compatibleSdkVersion": "5.0.0(12)",
        "runtimeOS": "HarmonyOS",
        "compatibleSdkVersionStage": "beta3",
        "buildOption": {
          "strictMode": {
            "useNormalizedOHMUrl": true
          }
        }
      }
    ],
    "buildModeSet": [
      {
        "name": "debug"
      },
      {
        "name": "release"
      },
      {
        "name": "debugWithSymbol"
      },
      {
        "name": "releaseWithSymbol"
      }
    ]
  },
  "modules": [
    {
      "name": "entry",
      "srcPath": "./entry",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default",
            "release",
          ]
        }
      ]
    },
    {
      "name": "cpp_nexus",
      "srcPath": "./cpp_nexus"
    }
  ]
}