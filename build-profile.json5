{"app": {"signingConfigs": [{"name": "default", "type": "HarmonyOS", "material": {"certpath": "/Users/<USER>/.ohos/config/default_fliggy_im_eb9HssDpTsbNDwifrqbFX5xpVwbjAKL7YXCQv68vMKo=.cer", "keyAlias": "debugKey", "keyPassword": "0000001A549DB6F4051405143561BC94EFF220766C805D7FBACFB4B7B860369519C2F5806685E7AC92F4", "profile": "/Users/<USER>/.ohos/config/default_fliggy_im_eb9HssDpTsbNDwifrqbFX5xpVwbjAKL7YXCQv68vMKo=.p7b", "signAlg": "SHA256withECDSA", "storeFile": "/Users/<USER>/.ohos/config/default_fliggy_im_eb9HssDpTsbNDwifrqbFX5xpVwbjAKL7YXCQv68vMKo=.p12", "storePassword": "0000001A888A5B6146726D5677B62CF0802BA30B4215CA79A295D04E911152C045C98C6E115A12E0FB05"}}], "products": [{"name": "default", "signingConfig": "default", "compatibleSdkVersion": "5.0.0(12)", "targetSdkVersion": "5.0.0(12)", "runtimeOS": "HarmonyOS", "compatibleSdkVersionStage": "beta3", "buildOption": {"strictMode": {"useNormalizedOHMUrl": true}}}], "buildModeSet": [{"name": "debug"}, {"name": "release"}]}, "modules": [{"name": "entry", "srcPath": "./entry", "targets": [{"name": "default", "applyToProducts": ["default"]}]}, {"name": "module", "srcPath": "./module"}]}